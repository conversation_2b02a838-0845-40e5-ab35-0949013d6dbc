package main

import (
	"context"
	"database/sql"
	"kernel/kernel-common/runtime"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"kernel/plugins/modules/userserver/bo"
	"kernel/plugins/pb"
	"time"

	json "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
)

// @Summary 获取皮肤列表
// @Description 获取皮肤列表
// @Tags userserver
// @Accept json
// @Produce json
// @Success 200 {object} RespSkinList "返回结果"
// @Router /v2/rpc/userserver.skin.list [post]
func (c *Controller) SkinList(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		return json.MarshalToString(&RespSkinList{Code: int64(models.FAILED), Message: "session error"})
	}

	skins := bo.Skins{}
	obj := c.server.dbagent.Create(ctx, &skins, models.WithKeys(map[string]interface{}{skins.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err := obj.Query(ctx).As(&skins)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query skins for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespSkinList{Code: int64(models.FAILED), Message: err.Error()})
		}
		skins.Items = "{}"
	}
	items := map[int64]*Skin{} // item_id -> item
	err = json.Unmarshal([]byte(skins.Items), &items)
	if err != nil {
		logger.Error("Failed to unmarshal skins items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespSkinList{Code: int64(models.FAILED), Message: err.Error()})
	}

	// 检查装扮是否过期，并更新
	isupdate := false
	now := time.Now()
	for itemId, item := range items {
		if item.ExpireTime > 0 && item.ExpireTime < now.Unix() {
			delete(items, itemId)
			isupdate = true
		}
	}
	if isupdate {
		skins.UpdatedAt = now
		skins.Items, err = json.MarshalToString(items)
		if err != nil {
			logger.Error("Failed to marshal skins items for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespSkinList{Code: int64(models.FAILED), Message: err.Error()})
		}
		resp, err := obj.WithReqOption(models.WithValues(map[string]interface{}{"items": skins.Items, "updated_at": skins.UpdatedAt})).Update(ctx).Result()
		if err != nil {
			logger.Error("Failed to update skins for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespSkinList{Code: int64(models.FAILED), Message: err.Error()})
		}
		if resp.RowsAffected > 0 {
			// nothing to do
		} else {
			return json.MarshalToString(&RespSkinList{Code: int64(models.FAILED), Message: "update failed"})
		}
	}

	list := pb.SkinList{}
	list.List = make([]*pb.SkinInfo, 0, len(items))
	for _, item := range items {
		id := uint32(item.SkinId)
		expireTime := item.ExpireTime
		list.List = append(list.List, &pb.SkinInfo{
			Id:         &id,
			ExpireTime: &expireTime,
		})
	}
	bytes, err := proto.Marshal(&list)
	if err != nil {
		logger.Error("Failed to marshal skin list for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespSkinList{Code: int64(models.FAILED), Message: err.Error()})
	}

	return json.MarshalToString(&RespSkinList{Code: int64(models.OK), Message: "success", Data: string(bytes)})
}

// @Summary 添加皮肤
// @Description 添加皮肤
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqSkinOpt true "请求参数"
// @Success 200 {object} models.CommonResp "返回结果"
// @Router /v2/rpc/userserver.skin.add [post]
func (c *Controller) SkinAdd(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if c.server.common.GetUserID(ctx) != "" {
		logger.Error("session not allowed, add skin")
		return json.MarshalToString(&models.CommonResp{Code: models.ACCESS_DENIED, Msg: "session not allowed"})
	}

	var reqSkin ReqSkinOpt
	err := json.Unmarshal([]byte(payload), &reqSkin)
	if err != nil {
		logger.Error("Failed to unmarshal skin add: %v", err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}

	if reqSkin.UserId == "" || reqSkin.Skin == nil {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "param is invalid"})
	}

	user_id := reqSkin.UserId
	isInsert := false
	skins := bo.Skins{}
	obj := c.server.dbagent.Create(ctx, &skins, models.WithKeys(map[string]interface{}{skins.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx).As(&skins)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query skins for user_id %s: %v", user_id, err)
			return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
		}
		skins.Items = "{}"
		isInsert = true
	}
	items := map[int64]*Skin{} // item_id -> item
	err = json.Unmarshal([]byte(skins.Items), &items)
	if err != nil {
		logger.Error("Failed to unmarshal skins items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}

	// 检查装扮是否过期，并更新
	isupdate := false
	now := time.Now()
	for itemId, item := range items {
		if item.ExpireTime > 0 && item.ExpireTime < now.Unix() {
			delete(items, itemId)
			isupdate = true
		}
	}

	// 检查是否已经存在
	if _, ok := items[reqSkin.Skin.SkinId]; ok {
		if isupdate {
			skins.UpdatedAt = now
			_, err := obj.WithReqOption(models.WithValues(map[string]interface{}{"items": skins.Items, "updated_at": skins.UpdatedAt})).Update(ctx).Result()
			if err != nil {
				logger.Error("Failed to update skins for user_id %s: %v", user_id, err)
				return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
			}
		}
		return json.MarshalToString(&models.CommonResp{Code: models.DATA_EXIST, Msg: "skin already exists"})
	}

	// 添加新的装扮
	items[reqSkin.Skin.SkinId] = reqSkin.Skin

	skins.UpdatedAt = now
	skins.Items, err = json.MarshalToString(items)
	if err != nil {
		logger.Error("Failed to marshal skins items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}
	var resp *models.DBAgentResp
	obj.WithReqOption(models.WithValues(map[string]interface{}{"items": skins.Items, "updated_at": skins.UpdatedAt}))
	if isInsert {
		resp, err = obj.Insert(ctx).Result()
	} else {
		resp, err = obj.Update(ctx).Result()
	}
	if err != nil {
		logger.Error("Failed to update skins for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}
	if resp.RowsAffected > 0 {
		// nothing to do
	} else {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "update failed"})
	}

	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "success"})
}

// @Summary 删除皮肤
// @Description 删除皮肤
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqSkinOpt true "请求参数"
// @Success 200 {object} models.CommonResp "返回结果"
// @Router /v2/rpc/userserver.skin.delete [post]
func (c *Controller) SkinDelete(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if c.server.common.GetUserID(ctx) != "" {
		logger.Error("session not allowed, delete skin")
		return json.MarshalToString(&models.CommonResp{Code: models.ACCESS_DENIED, Msg: "session not allowed"})
	}

	var reqSkin ReqSkinOpt
	err := json.Unmarshal([]byte(payload), &reqSkin)
	if err != nil {
		logger.Error("Failed to unmarshal skin delete: %v", err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}

	if reqSkin.UserId == "" || reqSkin.Skin == nil {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "param is invalid"})
	}

	user_id := reqSkin.UserId
	skins := bo.Skins{}
	obj := c.server.dbagent.Create(ctx, &skins, models.WithKeys(map[string]interface{}{skins.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx).As(&skins)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query skins for user_id %s: %v", user_id, err)
			return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
		}
		skins.Items = "{}"
	}
	items := map[int64]*Skin{} // item_id -> item
	err = json.Unmarshal([]byte(skins.Items), &items)
	if err != nil {
		logger.Error("Failed to unmarshal skins items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}

	// 检查装扮是否存在
	if _, ok := items[reqSkin.Skin.SkinId]; !ok {
		return json.MarshalToString(&models.CommonResp{Code: models.DATA_NOT_EXIST, Msg: "skin not exists"})
	}

	// 检查装扮是否过期
	now := time.Now()
	for itemId, item := range items {
		if item.ExpireTime > 0 && item.ExpireTime < now.Unix() {
			delete(items, itemId)
		}
	}

	// 添加新的装扮
	delete(items, reqSkin.Skin.SkinId)

	skins.UpdatedAt = now
	skins.Items, err = json.MarshalToString(items)
	if err != nil {
		logger.Error("Failed to marshal skins items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}
	resp, err := obj.WithReqOption(models.WithValues(map[string]interface{}{"items": skins.Items, "updated_at": skins.UpdatedAt})).Update(ctx).Result()
	if err != nil {
		logger.Error("Failed to update skins for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}
	if resp.RowsAffected > 0 {
		// nothing to do
	} else {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "update failed"})
	}

	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "success"})
}

// @Summary 查询皮肤
// @Description 查询皮肤
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqSkinOpt true "请求参数"
// @Success 200 {object} RespSkinQuery "返回结果"
// @Router /v2/rpc/userserver.skin.query [post]
func (c *Controller) SkinQuery(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if c.server.common.GetUserID(ctx) != "" {
		logger.Error("session not allowed, query skin")
		return json.MarshalToString(&RespSkinQuery{Code: int64(models.ACCESS_DENIED), Message: "session not allowed", Data: nil})
	}

	var reqSkin ReqSkinOpt
	err := json.Unmarshal([]byte(payload), &reqSkin)
	if err != nil {
		logger.Error("Failed to unmarshal skin query: %v", err)
		return json.MarshalToString(&RespSkinQuery{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	if reqSkin.UserId == "" || reqSkin.Skin == nil {
		return json.MarshalToString(&RespSkinQuery{Code: int64(models.FAILED), Message: "param is invalid", Data: nil})
	}

	user_id := reqSkin.UserId
	skins := bo.Skins{}
	obj := c.server.dbagent.Create(ctx, &skins, models.WithKeys(map[string]interface{}{skins.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx).As(&skins)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query skins for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespSkinQuery{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
		}
		skins.Items = "{}"
	}
	items := map[int64]*Skin{} // item_id -> item
	err = json.Unmarshal([]byte(skins.Items), &items)
	if err != nil {
		logger.Error("Failed to unmarshal skins items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespSkinQuery{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	// 检查装扮是否存在
	if _, ok := items[reqSkin.Skin.SkinId]; !ok {
		return json.MarshalToString(&RespSkinQuery{Code: int64(models.DATA_NOT_EXIST), Message: "skin not exists", Data: nil})
	} else {
		return json.MarshalToString(&RespSkinQuery{Code: int64(models.OK), Message: "success", Data: items[reqSkin.Skin.SkinId]})
	}
}

// @Summary 更新皮肤
// @Description 更新皮肤
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqSkinOpt true "请求参数"
// @Success 200 {object} RespSkinUpdate "返回结果"
// @Router /v2/rpc/userserver.skin.update [post]
func (c *Controller) SkinUpdate(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if c.server.common.GetUserID(ctx) != "" {
		logger.Error("session not allowed, update skin")
		return json.MarshalToString(&RespSkinUpdate{Code: int64(models.ACCESS_DENIED), Message: "session not allowed", Data: nil})
	}

	var reqSkin ReqSkinOpt
	err := json.Unmarshal([]byte(payload), &reqSkin)
	if err != nil {
		logger.Error("Failed to unmarshal skin update: %v", err)
		return json.MarshalToString(&RespSkinUpdate{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	if reqSkin.UserId == "" || reqSkin.Skin == nil {
		return json.MarshalToString(&RespSkinUpdate{Code: int64(models.FAILED), Message: "param is invalid", Data: nil})
	}

	user_id := reqSkin.UserId
	skins := bo.Skins{}
	obj := c.server.dbagent.Create(ctx, &skins, models.WithKeys(map[string]interface{}{skins.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx).As(&skins)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query skins for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespSkinUpdate{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
		}
		skins.Items = "{}"
	}
	items := map[int64]*Skin{} // item_id -> item
	err = json.Unmarshal([]byte(skins.Items), &items)
	if err != nil {
		logger.Error("Failed to unmarshal skins items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespSkinUpdate{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	// 检查装扮是否存在
	if _, ok := items[reqSkin.Skin.SkinId]; !ok {
		return json.MarshalToString(&RespSkinUpdate{Code: int64(models.DATA_NOT_EXIST), Message: "skin not exists", Data: nil})
	}

	// 检查装扮是否过期
	now := time.Now()
	for itemId, item := range items {
		if item.ExpireTime > 0 && item.ExpireTime < now.Unix() {
			delete(items, itemId)
		}
	}

	// 更新新的装扮
	items[reqSkin.Skin.SkinId].ExpireTime = reqSkin.Skin.ExpireTime

	skins.UpdatedAt = now
	skins.Items, err = json.MarshalToString(items)
	if err != nil {
		logger.Error("Failed to marshal skins items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespSkinUpdate{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}
	resp, err := obj.WithReqOption(models.WithValues(map[string]interface{}{"items": skins.Items, "updated_at": skins.UpdatedAt})).Update(ctx).Result()
	if err != nil {
		logger.Error("Failed to update skins for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespSkinUpdate{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}
	if resp.RowsAffected > 0 {
		// nothing to do
	} else {
		return json.MarshalToString(&RespSkinUpdate{Code: int64(models.FAILED), Message: "update failed", Data: nil})
	}

	return json.MarshalToString(&RespSkinUpdate{Code: int64(models.OK), Message: "success", Data: items[reqSkin.Skin.SkinId]})
}
