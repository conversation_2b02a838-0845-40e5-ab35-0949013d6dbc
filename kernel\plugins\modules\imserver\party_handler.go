package main

import (
	"context"
	"fmt"
	"kernel/kernel-common/rtapi"
	"kernel/plugins/common"
	"kernel/plugins/models"
	"time"

	"github.com/gofrs/uuid/v5"
	json "github.com/json-iterator/go"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

type PartyHandler struct {
	PartyID      string                        `json:"party_id"`
	Leader       string                        `json:"leader,omitempty"` // uin of leader
	Open         bool                          `json:"open,omitempty"`   // whether the party is open
	PartyName    string                        `json:"party_name,omitempty"`
	CreateTime   time.Time                     `json:"create_time,omitempty"`
	MaxSize      int                           `json:"max_size,omitempty"`
	Presences    map[string]*PartyPresenceItem `json:"presences,omitempty"`     // 成员列表 uin -> presence
	InvitedMap   map[string]int64              `json:"inviteds,omitempty"`      // 邀请列表 uin -> timestamp
	JoinRequests []*models.Presence            `json:"join_requests,omitempty"` // 加入请求列表
	PartyDesc    string                        `json:"party_desc,omitempty"`    // 招募描述
	PartyLabels  string                        `json:"party_labels,omitempty"`  // 招募标签

	controller *PartyController `json:"-"`
	dirty      bool             `json:"-"`
}

type PartyPresenceItem struct {
	Presence *models.Presence `json:"presence,omitempty"`
	JoinTime time.Time        `json:"join_time,omitempty"`
}

func NewPartyHandler(c *PartyController, leaderUin string, open bool, partyName string, maxSize int) *PartyHandler {
	partyID := uuid.Must(uuid.NewV4()).String() + ".0"
	party := c.pool.Get().(*PartyHandler)
	party.PartyID = partyID
	party.CreateTime = time.Now()
	party.Leader = leaderUin
	party.Open = open
	party.PartyName = partyName
	party.MaxSize = maxSize
	party.dirty = true
	return party
}

func LoadPartyHandler(c *PartyController, partyID string) (*PartyHandler, error) {
	// 从redis中获取party信息
	redis := c.server.GetCommon().Redis
	key := fmt.Sprintf("party:%s", partyID)
	partyStr, err := redis.Get(context.Background(), key).Result()
	if err != nil {
		return nil, err
	}
	if partyStr == "" {
		return nil, nil
	}
	party := c.pool.Get().(*PartyHandler)
	err = json.Unmarshal([]byte(partyStr), party)
	if err != nil {
		c.pool.Put(party)
		return nil, err
	}
	return party, nil
}

func LoadPartyHandlerBatch(c *PartyController, partyIDs []string) ([]*PartyHandler, error) {
	if len(partyIDs) == 0 {
		return nil, nil
	}
	// 从redis中获取party信息
	redis := c.server.GetCommon().Redis
	logger := c.server.GetCommon().Logger
	keys := make([]string, 0, len(partyIDs))
	for _, partyID := range partyIDs {
		keys = append(keys, fmt.Sprintf("party:%s", partyID))
	}
	partyStrs, err := redis.MGet(context.Background(), keys...).Result()
	if err != nil {
		return nil, err
	}
	parties := make([]*PartyHandler, 0, len(partyIDs))
	for _, partyStr := range partyStrs {
		if partyStr == nil {
			continue
		}
		party := c.pool.Get().(*PartyHandler)
		err = json.Unmarshal([]byte(partyStr.(string)), party)
		if err != nil {
			logger.Error("unmarshal party failed, %s, %s", partyStr, err.Error())
			party.Reset()
			c.pool.Put(party)
			continue
		}
		parties = append(parties, party)
	}
	return parties, nil
}

func (m *PartyHandler) Release() {
	if err := m.Save(); err != nil {
		m.controller.server.GetCommon().Logger.Error("save party failed, %s, %s", m.PartyID, err.Error())
	}
	m.Reset()
	m.controller.pool.Put(m)
}

func (m *PartyHandler) Reset() {
	m.PartyID = ""
	m.Leader = ""
	m.Open = false
	m.PartyName = ""
	m.CreateTime = time.Time{}
	m.MaxSize = 0
	m.PartyDesc = ""
	m.PartyLabels = ""
	for uid := range m.Presences {
		delete(m.Presences, uid)
	}
	for uid := range m.InvitedMap {
		delete(m.InvitedMap, uid)
	}
	m.JoinRequests = m.JoinRequests[:0]
	m.dirty = false
}

// 申请加入
func (p *PartyHandler) JoinRequest(ctx context.Context, presence *models.Presence) (bool, *models.ErrorMsg) {
	execFunc := func() (bool, *models.ErrorMsg) {
		// Check if party is full.
		if p.MemberSize() >= p.MaxSize {
			return false, ErrMsgPartyFull
		}
		// Check if party is open, and therefore automatically accepts join requests.
		// if p.Open {
		// 	_, err := p.MemberJoin([]*models.Presence{presence})
		// 	if err != nil {
		// 		return false, err
		// 	}
		// 	// The party membership has changed, stop any ongoing matchmaking processes.
		// 	return true, nil
		// }

		// 邀请成员，同意自动加入
		if _, found := p.InvitedMap[presence.GetUserId()]; found {
			return true, models.ErrMsgOK
		}

		// Check if party has room for more join requests.
		if len(p.JoinRequests) >= PartyJoinRequestMaxSize {
			return false, ErrMsgPartyJoinRequestsFull
		}
		// Check if party has already received join request from this user.
		for _, joinRequest := range p.JoinRequests {
			if joinRequest.GetUserId() == presence.GetUserId() {
				return false, ErrMsgPartyJoinRequestDuplicate
			}
		}
		// Check if party already has this user.
		if _, ok := p.Presences[presence.GetUserId()]; ok {
			return false, ErrMsgPartyJoinRequestAlreadyMember
		}

		p.JoinRequests = append(p.JoinRequests, presence)
		p.dirty = true
		return false, models.ErrMsgOK
	}
	if result, errMsg := execFunc(); models.IsErrOK(errMsg) && result {
		if err := p.Save(); err != nil {
			return false, models.NewErrorMsg(int32(models.FAILED), err.Error())
		}
		return result, models.ErrMsgOK
	} else if !models.IsErrOK(errMsg) {
		return false, errMsg
	}

	// Send message to party leader.
	if p.Leader != "" {
		userPresence := &rtapi.UserPresence{
			UserId:   presence.GetUserId(),
			Username: presence.GetUsername(),
		}

		envelope := &rtapi.Envelope{
			Message: &rtapi.Envelope_PartyJoinRequest{
				PartyJoinRequest: &rtapi.PartyJoinRequest{
					PartyId:   p.PartyID,
					Presences: []*rtapi.UserPresence{userPresence},
				},
			},
		}
		p.controller.server.stream.StreamSendRawToUserIds(ctx, []string{p.Leader}, envelope)

	}

	return false, models.ErrMsgOK
}

// 加入
func (p *PartyHandler) Join(ctx context.Context, presences []*models.Presence) *models.ErrorMsg {
	if len(presences) == 0 {
		return models.ErrMsgOK
	}

	var leaderPresence *rtapi.UserPresence
	execFunc := func() *models.ErrorMsg {
		// Assign the party leader if this is the first join.
		if p.Leader == "" {
			for _, presence := range presences {
				// The initial leader is joining the party at creation time.
				p.Leader = presence.GetUserId()
				break
			}
		}

		_, errMsg := p.MemberJoin(presences)
		if !models.IsErrOK(errMsg) {
			// Should not happen, this process is just a confirmation.
			return errMsg
		}
		p.dirty = true
		return models.ErrMsgOK
	}
	if err := execFunc(); models.IsErrOK(err) {
		if err1 := p.Save(); err1 != nil {
			p.controller.server.GetLogger().Error("error in party join %s", err1.Error())
			return models.NewErrorMsg(int32(models.FAILED), err1.Error())
		}
	} else {
		p.controller.server.GetLogger().Error("error in party join %s", err.Error())
		return err
	}

	// 查询一遍状态
	p.QueryMemberOnlineStatus(ctx)

	memberUserPresences := make([]*rtapi.UserPresence, 0, len(p.Presences))
	for _, member := range p.Presences {
		userPresence := &rtapi.UserPresence{
			UserId:   member.Presence.GetUserId(),
			Username: member.Presence.GetUsername(),
			Status:   &wrapperspb.StringValue{Value: member.Presence.GetStatus()},
		}
		memberUserPresences = append(memberUserPresences, userPresence)
		if member.Presence.GetUserId() == p.Leader {
			leaderPresence = userPresence
		}
	}

	presenceIDs := make(map[string]*rtapi.Envelope, len(presences))
	joins := make([]*rtapi.UserPresence, 0, len(presences))
	for _, presence := range presences {
		memberUserPresence := &rtapi.UserPresence{
			UserId:   presence.GetUserId(),
			Username: presence.GetUsername(),
			Status:   &wrapperspb.StringValue{Value: p.Presences[presence.GetUserId()].Presence.GetStatus()},
		}
		joins = append(joins, memberUserPresence)

		presenceIDs[presence.GetUserId()] = &rtapi.Envelope{
			Message: &rtapi.Envelope_Party{
				Party: &rtapi.Party{
					PartyId: p.PartyID,
					Open:    p.Open,
					MaxSize: int32(p.MaxSize),
					Self:    memberUserPresence,
					Leader:  leaderPresence,
					// Presences assigned below.
				},
			},
		}
	}

	// Send party info to the new joiners.
	for user_id, envelope := range presenceIDs {
		envelope.GetParty().Presences = memberUserPresences
		p.controller.server.stream.StreamSendRawToUserIds(ctx, []string{user_id}, envelope)
	}

	// Send party presence event to the old members.
	oldUids := make([]string, 0, len(p.Presences))
	for memberUid, _ := range p.Presences {
		if presenceIDs[memberUid] == nil {
			oldUids = append(oldUids, memberUid)
		}
	}
	if len(oldUids) > 0 {
		envelope := &rtapi.Envelope{Message: &rtapi.Envelope_PartyPresenceEvent{PartyPresenceEvent: &rtapi.PartyPresenceEvent{
			PartyId: p.PartyID,
			Joins:   joins,
		}}}
		p.controller.server.stream.StreamSendRawToUserIds(ctx, oldUids, envelope)
	}

	return models.ErrMsgOK
}

func (p *PartyHandler) Leave(ctx context.Context, presences []*models.Presence) {
	if len(presences) == 0 {
		return
	}

	var updateLeaderMsg *rtapi.Envelope
	execFunc := func() {
		presences, _ = p.MemberLeave(presences)
		if len(presences) == 0 {
			return
		}

		// Remove the leader if they've left.
		for _, presence := range presences {
			if p.Leader != "" && p.Leader == presence.GetUserId() {
				// Check is only meaningful if a leader exists. Leader may temporarily be nil here until a new
				// one is assigned below, when multiple presences leave concurrently and one was just the leader.
				p.Leader = ""

				oldestPresence := p.MemberOldest()
				if oldestPresence == nil {
					// Party is now empty, close it.
					p.Open = false
					return
				}

				// Leader has left, but there are other party members. Promote the oldest presence as the new party leader.
				p.Leader = oldestPresence.GetUserId()

				// Send any new leader promotion message to party members.
				updateLeaderMsg = &rtapi.Envelope{
					Message: &rtapi.Envelope_PartyLeader{
						PartyLeader: &rtapi.PartyLeader{
							PartyId: p.PartyID,
							Presence: &rtapi.UserPresence{
								UserId:   oldestPresence.GetUserId(),
								Username: oldestPresence.GetUsername(),
							},
						},
					},
				}

				break
			}
		}
	}
	execFunc()
	if err := p.Save(); err != nil {
		p.controller.server.GetLogger().Error("error in party leave %s", err.Error())
		return
	}

	if updateLeaderMsg != nil {
		p.SendToMembers(ctx, updateLeaderMsg, nil)
	}

	leaves := make([]*rtapi.UserPresence, 0, len(presences))
	for _, presence := range presences {
		leaves = append(leaves, &rtapi.UserPresence{
			UserId:   presence.GetUserId(),
			Username: presence.GetUsername(),
		})
	}
	if len(leaves) > 0 {
		envelope := &rtapi.Envelope{Message: &rtapi.Envelope_PartyPresenceEvent{PartyPresenceEvent: &rtapi.PartyPresenceEvent{
			PartyId: p.PartyID,
			Leaves:  leaves,
		}}}
		p.SendToMembers(ctx, envelope, nil)
	}

	// The party membership has changed, stop any ongoing matchmaking processes.
	//_ = p.matchmaker.RemovePartyAll(p.IDStr)
}

// 提升为领导
func (p *PartyHandler) Promote(ctx context.Context, optUin string, presence *rtapi.UserPresence) *models.ErrorMsg {
	// Only the party leader may promote.
	if p.Leader == "" || p.Leader != optUin {
		return ErrMsgPartyNotLeader
	}

	var envelope *rtapi.Envelope
	for _, member := range p.Presences {
		if member.Presence.GetUserId() == presence.GetUserId() {
			// Found the party member being promoted.
			p.Leader = member.Presence.GetUserId()

			envelope = &rtapi.Envelope{
				Message: &rtapi.Envelope_PartyLeader{
					PartyLeader: &rtapi.PartyLeader{
						PartyId: p.PartyID,
						Presence: &rtapi.UserPresence{
							UserId:   member.Presence.GetUserId(),
							Username: member.Presence.GetUsername(),
						},
					},
				},
			}

			break
		}
	}

	// Attempted to promote a party member that did not exist.
	if envelope == nil {
		return ErrMsgPartyNotMember
	}
	p.dirty = true
	if err := p.Save(); err != nil {
		p.controller.server.GetLogger().Error("error in party promote %s", err.Error())
		return models.NewErrorMsg(int32(models.FAILED), err.Error())
	}

	p.SendToMembers(ctx, envelope, nil)

	return models.ErrMsgOK
}

// 接受加入
func (p *PartyHandler) Accept(ctx context.Context, optUin string, presence *rtapi.UserPresence, singleParty bool) *models.ErrorMsg {
	if singleParty {
		// 检测是否已经在其他party中
		partyId, err := p.controller.GetUserPartyId(presence.GetUserId())
		if err != nil {
			return models.NewErrorMsg(int32(models.FAILED), err.Error())
		}
		if partyId != "" {
			return ErrMsgPartyAlreadyInParty
		}
	}

	// Only the party leader may promote.
	if p.Leader == "" || p.Leader != optUin {
		return ErrMsgPartyNotLeader
	}

	// Check if there's room to accept the new party member.
	if p.MemberSize() >= p.MaxSize {
		return ErrMsgPartyFull
	}

	// Check if the presence has actually requested to join.
	var idx int
	var joinRequestPresence *models.Presence
	for i, joinRequest := range p.JoinRequests {
		if joinRequest.GetUserId() == presence.GetUserId() {
			idx = i
			joinRequestPresence = joinRequest
			break
		}
	}
	if joinRequestPresence == nil {
		return ErrMsgPartyNotRequest
	}

	copy(p.JoinRequests[idx:], p.JoinRequests[idx+1:])
	p.JoinRequests[len(p.JoinRequests)-1] = nil
	p.JoinRequests = p.JoinRequests[:len(p.JoinRequests)-1]

	// Add the presence to the party stream, which will trigger the Join() hook above.
	if _, errMsg := p.MemberJoin([]*models.Presence{joinRequestPresence}); !models.IsErrOK(errMsg) {
		return errMsg
	}

	if err := p.Save(); err != nil {
		p.controller.server.GetLogger().Error("error in party accept %s", err.Error())
		return models.NewErrorMsg(int32(models.FAILED), err.Error())
	}

	// 查询一遍状态
	p.QueryMemberOnlineStatus(ctx)

	var leaderPresence *rtapi.UserPresence
	memberUserPresences := make([]*rtapi.UserPresence, 0, len(p.Presences))
	for uin, member := range p.Presences {
		userPresence := &rtapi.UserPresence{
			UserId:   member.Presence.GetUserId(),
			Username: member.Presence.GetUsername(),
			Status:   &wrapperspb.StringValue{Value: member.Presence.GetStatus()},
		}
		memberUserPresences = append(memberUserPresences, userPresence)
		if member.Presence.GetUserId() == p.Leader {
			leaderPresence = userPresence
		}

		if presence.GetUserId() == uin {
			presence.Status = &wrapperspb.StringValue{Value: member.Presence.GetStatus()}
		}
	}

	// 发送消息给加入者
	p.controller.server.stream.StreamSendRawToUserIds(ctx, []string{presence.GetUserId()}, &rtapi.Envelope{
		Message: &rtapi.Envelope_Party{
			Party: &rtapi.Party{
				PartyId:   p.PartyID,
				Open:      p.Open,
				MaxSize:   int32(p.MaxSize),
				Self:      presence,
				Leader:    leaderPresence,
				Presences: memberUserPresences,
			},
		},
	})

	// 发送消息给其他成员
	filterUins := map[string]struct{}{presence.GetUserId(): {}}
	envelope := &rtapi.Envelope{Message: &rtapi.Envelope_PartyPresenceEvent{PartyPresenceEvent: &rtapi.PartyPresenceEvent{
		PartyId: p.PartyID,
		Joins:   []*rtapi.UserPresence{presence},
	}}}
	p.SendToMembers(ctx, envelope, filterUins)

	// The party membership has changed, stop any ongoing matchmaking processes.
	//_ = p.matchmaker.RemovePartyAll(p.IDStr)

	return models.ErrMsgOK
}

func (p *PartyHandler) Remove(ctx context.Context, optUin string, presence *rtapi.UserPresence) *models.ErrorMsg {
	// Only the party leader may remove.
	if p.Leader == "" || p.Leader != optUin {
		return ErrMsgPartyNotLeader
	}

	// Check if the leader is attempting to remove its own presence.
	if p.Leader == presence.GetUserId() {
		return ErrMsgPartyRemoveSelf
	}

	// Remove the party member, if found.
	removeMember := p.Presences[presence.GetUserId()]
	if removeMember == nil {
		// Wasn't a party member, check if it's actually a rejected join request.
		for i, joinRequest := range p.JoinRequests {
			if joinRequest.GetUserId() == presence.GetUserId() {
				// Rejected join requests do not require stream removal, they were never part of the stream to begin with.
				copy(p.JoinRequests[i:], p.JoinRequests[i+1:])
				p.JoinRequests[len(p.JoinRequests)-1] = nil
				p.JoinRequests = p.JoinRequests[:len(p.JoinRequests)-1]
				p.dirty = true

				if err := p.Save(); err != nil {
					p.controller.server.GetLogger().Error("error in party remove %s", err.Error())
					return models.NewErrorMsg(int32(models.FAILED), err.Error())
				}
				return nil
			}
		}
	}

	if removeMember == nil {
		return ErrMsgPartyNotMember
	}

	// The party membership has changed, stop any ongoing matchmaking processes.
	//_ = p.matchmaker.RemovePartyAll(p.IDStr)

	p.MemberLeave([]*models.Presence{removeMember.Presence})

	if err := p.Save(); err != nil {
		p.controller.server.GetLogger().Error("error in party remove %s", err.Error())
		return models.NewErrorMsg(int32(models.FAILED), err.Error())
	}

	presence.Username = removeMember.Presence.GetUsername()
	leaves := []*rtapi.UserPresence{presence}
	envelope := &rtapi.Envelope{Message: &rtapi.Envelope_PartyPresenceEvent{PartyPresenceEvent: &rtapi.PartyPresenceEvent{
		PartyId: p.PartyID,
		Leaves:  leaves,
	}}}

	user_ids := make([]string, 0, len(p.Presences)+1)
	for user_id, _ := range p.Presences {
		user_ids = append(user_ids, user_id)
	}
	user_ids = append(user_ids, presence.GetUserId())
	p.controller.server.stream.StreamSendRawToUserIds(ctx, user_ids, envelope)

	return models.ErrMsgOK
}

func (p *PartyHandler) Close(ctx context.Context, optUin string) *models.ErrorMsg {
	// Only the party leader may close the party.
	if p.Leader == "" || p.Leader != optUin {
		return ErrMsgPartyNotLeader
	}

	user_ids := make([]string, 0, len(p.Presences))
	leaves := make([]*models.Presence, 0, len(p.Presences))
	for _, presence := range p.Presences {
		leaves = append(leaves, presence.Presence)
		user_ids = append(user_ids, presence.Presence.GetUserId())
	}
	p.MemberLeave(leaves)

	p.Open = false
	p.dirty = true

	if err := p.Save(); err != nil {
		p.controller.server.GetLogger().Error("error in party close %s", err.Error())
		return models.NewErrorMsg(int32(models.FAILED), err.Error())
	}

	p.controller.server.stream.StreamSendRawToUserIds(ctx, user_ids, &rtapi.Envelope{Message: &rtapi.Envelope_PartyClose{PartyClose: &rtapi.PartyClose{
		PartyId: p.PartyID,
	}}})

	return models.ErrMsgOK
}

func (p *PartyHandler) Update(ctx context.Context, optUin string, desc string, labels string, open bool) *models.ErrorMsg {
	// Only the party leader may update the party.
	if p.Leader == "" || p.Leader != optUin {
		return ErrMsgPartyNotLeader
	}

	p.PartyDesc = desc
	p.PartyLabels = labels
	p.Open = open
	p.dirty = true

	if err := p.Save(); err != nil {
		p.controller.server.GetLogger().Error("error in party update %s", err.Error())
		return models.NewErrorMsg(int32(models.FAILED), err.Error())
	}

	party_data := &PartyDetailData{
		PartyId:     p.PartyID,
		PartyLeader: p.Leader,
		PartyDesc:   p.PartyDesc,
		PartyLabels: p.PartyLabels,
	}

	party_data_bytes, err := json.Marshal(party_data)
	if err != nil {
		p.controller.server.GetLogger().Error("error in party update %s", err.Error())
		return models.NewErrorMsg(int32(models.FAILED), err.Error())
	}

	p.DataSend(ctx, "", int64(PartyOpCodeUpdate), party_data_bytes)

	return models.ErrMsgOK
}

// 邀请成员加入party
func (p *PartyHandler) Invite(ctx context.Context, optUin string, desUin string) *models.ErrorMsg {
	if p.Leader == "" || p.Leader != optUin {
		return ErrMsgPartyNotLeader
	}

	if invite_time, found := p.InvitedMap[desUin]; found {
		if time.Now().Unix()-invite_time < 30 {
			p.controller.server.GetLogger().Error("party invite party %s already invited %s", p.PartyID, desUin)
			return ErrMsgPartyAlreadyInvited
		}
	}
	if len(p.Presences) >= p.MaxSize {
		return ErrMsgPartyFull
	}
	if len(p.InvitedMap) >= PartyInviteMaxSize {
		return ErrMsgPartyInviteTooMany
	}
	if _, found := p.Presences[desUin]; found {
		return ErrMsgPartyAlreadyInParty
	}
	p.InvitedMap[desUin] = time.Now().Unix()
	p.dirty = true

	if err := p.Save(); err != nil {
		p.controller.server.GetLogger().Error("error in party invite %s", err.Error())
		return models.NewErrorMsg(int32(models.FAILED), err.Error())
	}

	party_data := &PartyDetailData{
		PartyId:          p.PartyID,
		PartyLeader:      p.Leader,
		PartyDesc:        p.PartyDesc,
		PartyLabels:      p.PartyLabels,
		PartyMemberCount: len(p.Presences),
		PartyMemberList:  []*models.Presence{p.Presences[optUin].Presence},
	}
	party_data_bytes, err := json.MarshalToString(party_data)
	if err != nil {
		p.controller.server.GetLogger().Error("error in party update %s", err.Error())
		return models.NewErrorMsg(int32(models.FAILED), err.Error())
	}

	// 发送邀请消息
	p.controller.server.send.Push(context.Background(), desUin, "party_invite", party_data_bytes, "", 0)

	return models.ErrMsgOK
}

// 拒绝邀请
func (p *PartyHandler) InviteReject(presence *models.Presence) *models.ErrorMsg {
	if _, found := p.InvitedMap[presence.GetUserId()]; !found {
		return models.ErrMsgOK
	}
	delete(p.InvitedMap, presence.GetUserId())
	p.dirty = true
	if err := p.Save(); err != nil {
		p.controller.server.GetLogger().Error("error in party invite reject %s", err.Error())
		return models.NewErrorMsg(int32(models.FAILED), err.Error())
	}

	return models.ErrMsgOK
}

func (p *PartyHandler) JoinRequestList(ctx context.Context, optUin string) ([]*models.Presence, *models.ErrorMsg) {
	// Only the party leader may request a list of pending join requests.
	if p.Leader == "" || p.Leader != optUin {
		return nil, ErrMsgPartyNotLeader
	}

	user_ids := make([]int64, 0, len(p.JoinRequests))
	for _, joinRequest := range p.JoinRequests {
		user_ids = append(user_ids, common.Interface2Int64(joinRequest.GetUserId()))
	}
	if len(user_ids) == 0 {
		return nil, models.ErrMsgOK
	}
	online_info, err := p.controller.server.online.QueryOnlineUsersInfoByUin(ctx, user_ids)
	if err != nil {
		p.controller.server.GetLogger().Error("error in party join request list %s", err.Error())
		return nil, models.NewErrorMsg(int32(models.FAILED), err.Error())
	}
	for _, joinRequest := range p.JoinRequests {
		onlineInfo, ok := online_info[common.Interface2Int64(joinRequest.GetUserId())]
		if ok && onlineInfo.SessionId != "" {
			joinRequest.Meta.Status = "online"
		} else {
			joinRequest.Meta.Status = "offline"
		}
	}
	return p.JoinRequests, models.ErrMsgOK
}

func (p *PartyHandler) MatchmakerAdd(sessionID, node, query string, minCount, maxCount, countMultiple int, stringProperties map[string]string, numericProperties map[string]float64) (string, []*models.PresenceID, error) {
	return "", nil, nil
}

func (p *PartyHandler) MatchmakerRemove(sessionID, node, ticket string) error {
	return nil
}

func (p *PartyHandler) DataSend(ctx context.Context, sendUin string, opCode int64, data []byte) *models.ErrorMsg {
	// Check if the sender is a party member.
	var sender *PartyPresenceItem
	if sendUin != "" {
		sender = p.Presences[sendUin]
		if sender == nil {
			return ErrMsgPartyNotMember
		}
	}

	recipients := make([]string, 0, len(p.Presences))
	for memberUid, _ := range p.Presences {
		recipients = append(recipients, memberUid)
	}

	var senderUserPresence *rtapi.UserPresence = nil
	if sender != nil {
		senderUserPresence = &rtapi.UserPresence{
			UserId:   sender.Presence.GetUserId(),
			Username: sender.Presence.GetUsername(),
		}
	}

	// Sender was a party member, construct and send the correct envelope.
	envelope := &rtapi.Envelope{
		Message: &rtapi.Envelope_PartyData{
			PartyData: &rtapi.PartyData{
				PartyId:  p.PartyID,
				Presence: senderUserPresence,
				OpCode:   opCode,
				Data:     data,
			},
		},
	}
	p.controller.server.stream.StreamSendRawToUserIds(ctx, recipients, envelope)

	return models.ErrMsgOK
}

func (p *PartyHandler) SendToMembers(ctx context.Context, envelope *rtapi.Envelope, filterUins map[string]struct{}) {
	user_ids := make([]string, 0, len(p.Presences))
	for user_id, _ := range p.Presences {
		if len(filterUins) > 0 {
			if _, ok := filterUins[user_id]; ok {
				continue
			}
		}
		user_ids = append(user_ids, user_id)
	}
	p.controller.server.stream.StreamSendRawToUserIds(ctx, user_ids, envelope)
}

func (m *PartyHandler) MemberJoin(joins []*models.Presence) ([]*models.Presence, *models.ErrorMsg) {
	processed := make([]*models.Presence, 0, len(joins))
	var newPresences int
	for _, join := range joins {
		_, presenceFound := m.Presences[join.GetUserId()]
		if !presenceFound {
			newPresences++
		}
	}
	if newPresences > 0 && len(m.Presences)+newPresences > m.MaxSize {
		return nil, ErrMsgPartyFull
	}
	redis := m.controller.server.GetCommon().Redis
	logger := m.controller.server.GetCommon().Logger
	for _, join := range joins {
		delete(m.InvitedMap, join.GetUserId())
		if _, ok := m.Presences[join.GetUserId()]; !ok {
			// 设置用户加入的party
			key := fmt.Sprintf("userparty:%s", join.GetUserId())
			err := redis.Set(context.Background(), key, m.PartyID, 0).Err()
			if err != nil {
				logger.Error("set user party failed, ", err.Error())
				continue
			}

			m.Presences[join.GetUserId()] = &PartyPresenceItem{
				Presence: join,
				JoinTime: time.Now(),
			}
			processed = append(processed, join)
			m.dirty = true
		}
	}
	return processed, models.ErrMsgOK
}

func (m *PartyHandler) MemberLeave(leaves []*models.Presence) ([]*models.Presence, []*models.Presence) {
	processed := make([]*models.Presence, 0, len(leaves))
	reservations := make([]*models.Presence, 0, len(leaves))

	redis := m.controller.server.GetCommon().Redis
	logger := m.controller.server.GetCommon().Logger

	keys := make([]string, 0, len(leaves))
	for _, leave := range leaves {
		// 删除用户加入的party
		key := fmt.Sprintf("userparty:%s", leave.GetUserId())
		keys = append(keys, key)

		if _, ok := m.Presences[leave.GetUserId()]; ok {
			delete(m.Presences, leave.GetUserId())
			processed = append(processed, leave)
			m.dirty = true
		}
	}

	if len(keys) > 0 {
		err := redis.Del(context.Background(), keys...).Err()
		if err != nil {
			logger.Error("delete user party failed, ", err.Error())
		}
	}

	return processed, reservations
}

func (m *PartyHandler) MemberList() map[string]*PartyPresenceItem {
	return m.Presences
}

func (m *PartyHandler) MemberSize() int {
	return len(m.Presences)
}

func (m *PartyHandler) MemberOldest() *models.Presence {
	if len(m.Presences) == 0 {
		return nil
	}
	oldtime := time.Now()
	var oldestPresence *models.Presence
	for _, presence := range m.Presences {
		if oldtime.After(presence.JoinTime) {
			oldtime = presence.JoinTime
			oldestPresence = presence.Presence
		}
	}
	return oldestPresence
}

func (m *PartyHandler) Save() error {
	if !m.dirty {
		return nil
	}

	redis := m.controller.server.GetCommon().Redis
	key := fmt.Sprintf("party:%s", m.PartyID)

	if len(m.Presences) == 0 {
		redis.Del(context.Background(), key)
		m.controller.partyList.DeleteParty(m.PartyID)
		return nil
	}

	// 移除超时邀请(30秒)
	for user_id, invite_time := range m.InvitedMap {
		if time.Now().Unix()-invite_time > 30 {
			delete(m.InvitedMap, user_id)
		}
	}

	partyBytes, err := json.Marshal(m)
	if err != nil {
		return err
	}
	err = redis.Set(context.Background(), key, string(partyBytes), 0).Err()
	if err == nil {
		m.dirty = false
	}

	// 更新party成员数量
	if !m.Open || len(m.Presences) >= m.MaxSize {
		m.controller.partyList.DeleteParty(m.PartyID)
	} else {
		m.controller.partyList.UpdatePartyMembers(m.PartyID, len(m.Presences))
	}

	return err
}

// 查询队伍成员在线状态
func (m *PartyHandler) QueryMemberOnlineStatus(ctx context.Context) error {
	user_ids := make([]int64, 0, len(m.Presences))
	for user_id := range m.Presences {
		user_ids = append(user_ids, common.Interface2Int64(user_id))
	}
	if len(user_ids) == 0 {
		return nil
	}
	online_info, err := m.controller.server.online.QueryOnlineUsersInfoByUin(ctx, user_ids)
	if err != nil {
		return err
	}
	for user_id := range m.Presences {
		onlineInfo, ok := online_info[common.Interface2Int64(user_id)]
		if ok && onlineInfo.SessionId != "" {
			m.Presences[user_id].Presence.Meta.Status = "online"
		} else {
			m.Presences[user_id].Presence.Meta.Status = "offline"
		}
	}
	return nil
}

// 更新队伍成员在线状态
func (m *PartyHandler) UpdateMemberOnlineStatus(uin int64, online bool) error {
	tmpStatus := "offline"
	if online {
		tmpStatus = "online"
	}
	party_data := &PartyDetailData{
		PartyId: m.PartyID,
		PartyMemberList: []*models.Presence{&models.Presence{
			UserID: common.Interface2String(uin),
			Meta:   models.PresenceMeta{Status: tmpStatus},
		}},
	}

	party_data_bytes, err := json.Marshal(party_data)
	if err != nil {
		m.controller.server.GetLogger().Error("error in party update %s", err.Error())
		return err
	}

	m.DataSend(context.Background(), "", int64(PartyOpCodeOnline), party_data_bytes)
	return nil
}
