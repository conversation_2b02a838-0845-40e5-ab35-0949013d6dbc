## Gateway 的核心设计理念

这个项目中的 Gateway 是一个**智能 API 网关**，它在 Nakama 游戏服务器的基础上构建了一个完整的微服务架构入口。Gateway 的主要作用是：

### 1. **统一入口管理**

- 所有客户端请求都通过 Gateway 进入系统
- 提供 HTTP、WebSocket、gRPC 多种协议支持
- 统一的路由管理和请求分发

### 2. **核心组件架构**

```go path=kernel/plugins/modules/gateserver/main.go mode=EXCERPT
type GateServerModule struct {
    name            string
    logger          runtime.Logger
    db              *sql.DB
    nk              runtime.NakamaModule
    initializer     runtime.Initializer
    config          runtime.Config // nakama 配置
    CustomConfig    *CustomConfig  // 自定义配置
    isRegisterSelf  bool
    ProxyPassConfig sync.Map
    controller      *Controller
    proxy           *httputil.ReverseProxy
    limiterm        *common.LimiterManager
    ipwm            *common.IPBlackWhiteListManager
```

### 3. **路由配置系统**

Gateway 使用灵活的路由配置，支持多种代理模式：

```yaml path=kernel/data/gateserver101.yaml mode=EXCERPT
routes:
  "/v2/account/authenticate":
    type: svc
    data: loginserver@@omo
    lbtype: rr
    scheme: http
    prefix: true
  "/api/v1/report_callback":
    type: svc
    data: logicserver@@omo
    lbtype: rr
    scheme: http
    authkey: "defaulthttpkey"
    routepath: /v2/rpc/logicserver.reportcallback
    unwrap: true
```

### 4. **负载均衡策略**

Gateway 支持多种负载均衡算法：

```go path=kernel/plugins/models/const.go mode=EXCERPT
// 反向代理负载均衡算法
const (
    PROXYLBRR       string = "rr"       // 轮循
    PROXYLBWRR      string = "wrr"      // 加权轮循
    PROXYLBHASH     string = "hash"     // 一致性hash
    PROXYLBRAND     string = "rand"     // 随机
    PROXYLBLEASSNUM string = "leastnum" // 最小连接数,暂不支持
)
```

### 5. **服务发现与注册**

Gateway 集成了 Nacos 作为服务发现中心：

```go path=kernel/plugins/common/watchsvr.go mode=EXCERPT
type WatchNodeList struct {
    WatchSvrList   sync.Map        // map[string]*SvrCfgList = make(map[string]*SvrCfgList) key: svcname@@nacosgroup
    watchSvrTypes  map[string]bool // key: svcname@@nacosgroup
    watchEventLock sync.Mutex
    logger         runtime.Logger
    serverName     string
    nacosClient    *NacosClient
}
```

### 6. **安全与限流机制**

Gateway 提供了完整的安全防护：

- **IP 白名单/黑名单**：控制访问来源
- **限流器**：防止服务过载
- **认证机制**：支持 AuthKey 验证
- **访问日志**：完整的请求追踪

### 7. **关键特性**

1. **动态路由**：支持运行时路由配置更新
2. **健康检查**：自动检测后端服务状态
3. **故障转移**：自动切换到健康的服务实例
4. **请求解包**：支持 unwrap 功能，简化客户端处理
5. **多协议支持**：HTTP、WebSocket、gRPC 统一处理

### 8. **与 Nakama 的集成**

Gateway 巧妙地利用了 Nakama 的插件系统：

- 作为 Nakama 的一个模块运行
- 利用 Nakama 的 HTTP 处理能力
- 扩展了 Nakama 的路由功能
- 保持了与 Nakama 生态的兼容性

### 9. **微服务架构支持**

Gateway 支持完整的微服务架构：

- **服务注册**：自动向 Nacos 注册服务
- **服务发现**：动态发现可用服务实例
- **负载均衡**：智能分发请求到最优实例
- **配置管理**：集中化的配置管理

这个 Gateway 设计的优势在于它既保持了 Nakama 游戏服务器的强大功能，又扩展了企业级的微服务网关能力，为构建大规模的游戏后端系统提供了坚实的基础架构。
