<ngb-alert [dismissible]="false" type="danger" class="mb-3" *ngIf="error">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">An error occurred: {{error}}</h6>
</ngb-alert>

<div class="row no-gutters">
  <table class="table table-sm table-hover table-bordered">
    <thead class="thead-light">
      <tr>
        <th style="width: 320px">Group ID</th>
        <th>Name</th>
        <th style="width: 300px">State</th>
        <th style="width: 180px">Update Time</th>
        <th style="width: 90px" *ngIf="deleteAllowed()">Remove</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngIf="groups.length === 0">
        <td colSpan="5" class="text-muted">No group membership found.</td>
      </tr>

      <tr *ngFor="let g of groups; index as i">
        <td (click)="viewAccount(g)">{{g.group.id}}</td>
        <td (click)="viewAccount(g)">{{g.group.name}}</td>
        <td (click)="viewAccount(g)">
          <span *ngIf="g.state === 0">Superadmin (0)</span>
          <span *ngIf="g.state === 1">Admin (1)</span>
          <span *ngIf="g.state === 2">Member (2)</span>
          <span *ngIf="g.state === 3">Join Request (3)</span>
          <span *ngIf="g.state === 4">Banned (4)</span>
        </td>
        <td (click)="viewAccount(g)">{{g.group.update_time}}</td>
        <td *ngIf="deleteAllowed()"><button type="button" class="btn btn-sm btn-danger" (click)="deleteGroupUser($event, i, g);">Delete</button></td>
      </tr>
    </tbody>
  </table>
</div>
