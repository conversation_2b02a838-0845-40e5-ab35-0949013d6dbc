package main

import (
	"context"
	"database/sql"
	"kernel/kernel-common/api"
	"kernel/kernel-common/runtime"
	commbo "kernel/plugins/bo"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	sysruntime "runtime"
	"strings"
	"sync"
	"time"

	"github.com/gofrs/uuid/v5"
	json "github.com/json-iterator/go"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type Controller struct {
	server *UserServerModule
}

func NewController(s *UserServerModule) *Controller {
	return &Controller{
		server: s,
	}
}

// @Summary 查询用户信息
// @Description 查询用户信息 userserver.query
// @Tags userserver
// @Produce  json
// @Accept	 application/json
// @Param request body ReqQueryBase true "请求参数"
// @Success 200 {object} RespQueryBase "返回结果"
// @Router /v2/rpc/userserver.query [post]
func (c *Controller) Query(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if ctx == nil {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "context is nil", Data: nil})
	}
	if payload == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "payload is empty", Data: nil})
	}

	uid := c.server.common.GetStrUin(ctx)

	var req ReqQueryBase
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}

	// 解析user_ids
	if len(req.UserIDs) == 0 && len(req.Uins) == 0 {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "user_ids or uins is required", Data: nil})
	}

	// 解析fields
	if len(req.Fields) == 0 {
		req.Fields = []string{"*"}
	}

	tmp_set_user_func := func(user *commbo.User, apiuser *models.User) {
		if user == nil || apiuser == nil {
			return
		}

		if len(req.Fields) == 1 && req.Fields[0] == "*" {
			apiuser.Id = user.UserId
			apiuser.Username = user.Username
			apiuser.DisplayName = user.DisplayName
			apiuser.AvatarUrl = user.AvatarUrl
			apiuser.LangTag = user.LangTag
			apiuser.Location = user.Location
			apiuser.Timezone = user.Timezone
			apiuser.Metadata = user.Metadata
			apiuser.EdgeCount = int32(user.EdgeCount)
			apiuser.CreateTime = &timestamppb.Timestamp{Seconds: user.CreateTime.Unix()}
			apiuser.UpdateTime = &timestamppb.Timestamp{Seconds: user.UpdateTime.Unix()}
			apiuser.Online = c.server.online.IsOnline(user.UserId)
			apiuser.DisableTime = &timestamppb.Timestamp{Seconds: user.DisableTime.Unix()}
		} else {
			for _, field := range req.Fields {
				switch strings.TrimSpace(field) {
				case "id":
					apiuser.Id = user.UserId
				case "username":
					apiuser.Username = user.Username
				case "display_name":
					apiuser.DisplayName = user.DisplayName
				case "avatar_url":
					apiuser.AvatarUrl = user.AvatarUrl
				case "lang_tag":
					apiuser.LangTag = user.LangTag
				case "location":
					apiuser.Location = user.Location
				case "timezone":
					apiuser.Timezone = user.Timezone
				case "metadata":
					apiuser.Metadata = user.Metadata
				case "edge_count":
					apiuser.EdgeCount = int32(user.EdgeCount)
				case "create_time":
					apiuser.CreateTime = &timestamppb.Timestamp{Seconds: user.CreateTime.Unix()}
				case "update_time":
					apiuser.UpdateTime = &timestamppb.Timestamp{Seconds: user.UpdateTime.Unix()}
				case "online":
					apiuser.Online = c.server.online.IsOnline(user.UserId)
				case "disable_time":
					apiuser.DisableTime = &timestamppb.Timestamp{Seconds: user.DisableTime.Unix()}
				default:
					logger.Error("Invalid field: %s", field)
				}
			}
		}
	}

	users := make(map[string]*models.User)
	var total int32
	if len(req.UserIDs) == 1 && req.UserIDs[0] == "*" {
		if uid != "" {
			logger.Error("session not allowed, query all users")
			return json.MarshalToString(&models.CommonResp{Code: models.ACCESS_DENIED, Msg: "session not allowed", Data: nil})
		}

		// 查询所有用户
		if req.Page < 1 {
			req.Page = 1
		}
		if req.Size <= 0 || req.Size > 100 {
			req.Size = 20
		}
		offset := (req.Page - 1) * req.Size
		limit := req.Size

		// 创建一个数据库对象
		obj := c.server.dbagent.Create(ctx, &commbo.User{}, models.WithNotOptCache(true))
		defer c.server.dbagent.Release(obj)

		// 优化：同时查询用户列表和总数，减少一次数据库查询
		list := []*commbo.User{}
		var totalResults []struct {
			Total int32 `json:"total"`
		}

		// 并行执行两个查询
		var wg sync.WaitGroup
		var listErr, countErr error
		wg.Add(2)
		c.server.common.GroutinePool.Submit(func() {
			defer wg.Done()
			listErr = obj.RawQuery(ctx, "SELECT * FROM users ORDER BY create_time DESC LIMIT %d OFFSET %d", limit, offset).As(&list)
		})
		c.server.common.GroutinePool.Submit(func() {
			defer wg.Done()
			countErr = obj.RawQuery(ctx, "SELECT COUNT(*) AS total FROM users").As(&totalResults)
		})
		wg.Wait()

		// 处理用户列表查询错误
		if listErr != nil {
			logger.Error("Failed to query all users: %v", listErr)
			return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: listErr.Error(), Data: nil})
		}

		// 处理用户总数查询错误
		if countErr != nil {
			logger.Error("Failed to query user count: %v", countErr)
			return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: countErr.Error(), Data: nil})
		}

		// 处理用户列表数据
		for _, user := range list {
			apiuser := &models.User{}
			tmp_set_user_func(user, apiuser)
			users[user.UserId] = apiuser
		}

		// 设置总数
		if len(totalResults) > 0 {
			total = totalResults[0].Total
		}
	} else {
		// 合并用户ID和UIN查询任务
		type queryTask struct {
			isUserID bool
			id       interface{} // 可以是userID或uin
		}

		var tasks []queryTask
		for _, userID := range req.UserIDs {
			tasks = append(tasks, queryTask{isUserID: true, id: userID})
		}
		for _, uin := range req.Uins {
			tasks = append(tasks, queryTask{isUserID: false, id: uin})
		}

		// 使用互斥锁保护users map的并发写入
		var usersMutex sync.Mutex

		// 设置并发数量
		concurrency := sysruntime.NumCPU()
		if len(tasks) < concurrency {
			concurrency = len(tasks)
		}

		// 使用WaitGroup等待所有查询完成
		var wg sync.WaitGroup
		taskCh := make(chan queryTask, len(tasks))

		// 启动工作协程
		for i := 0; i < concurrency; i++ {
			wg.Add(1)
			c.server.common.GroutinePool.Submit(func() {
				defer wg.Done()
				for task := range taskCh {
					user := commbo.User{}
					var keyMap map[string]interface{}

					if task.isUserID {
						userID := task.id.(string)
						keyMap = map[string]interface{}{user.GetKeyName(): userID}
					} else {
						uin := task.id.(int32)
						keyMap = map[string]interface{}{"uin": uin}
					}

					var err error
					obj := c.server.dbagent.Create(ctx, &user, models.WithKeys(keyMap)).WithOption(logic.WithAutoRLock())
					if len(req.Fields) == 1 && req.Fields[0] == "*" {
						err = obj.Query(ctx).As(&user)
					} else {
						err = obj.Query(ctx, req.Fields...).As(&user)
					}
					c.server.dbagent.Release(obj)

					if err != nil {
						if task.isUserID {
							logger.Error("Failed to query user for user_id %v: %v", task.id, err)
						} else {
							logger.Error("Failed to query user for uin %v: %v", task.id, err)
						}
						continue
					}

					apiuser := &models.User{}
					tmp_set_user_func(&user, apiuser)

					usersMutex.Lock()
					users[common.Interface2String(task.id)] = apiuser
					usersMutex.Unlock()
				}
			})
		}

		// 发送任务到通道
		for _, task := range tasks {
			taskCh <- task
		}
		close(taskCh)

		// 等待所有查询完成
		wg.Wait()
		total = int32(len(users))
	}

	// 返回结果
	resp := RespQueryBase{
		Users: users,
		Total: total,
	}
	return json.MarshalToString(resp)
}

// @Summary 修改用户信息
// @Description 修改用户信息
// @Tags userserver
// @Produce  json
// @Accept	 application/json
// @Param request body ReqModifyBase true "请求参数"
// @Success 200 {object} models.CommonResp "返回结果"
// @Router /v2/rpc/userserver.modify [post]
func (c *Controller) Modify(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uid := c.server.common.GetStrUin(ctx)
	if uid != "" {
		logger.Error("session not allowed, modify user")
		return json.MarshalToString(&models.CommonResp{Code: models.ACCESS_DENIED, Msg: "session not allowed", Data: nil})
	}

	var req ReqModifyBase
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}

	updateMap := make(map[string]interface{})
	for userKey, userValue := range req.Fields {
		switch strings.TrimSpace(userKey) {
		case "username":
			updateMap["username"] = userValue
		case "display_name":
			updateMap["display_name"] = userValue
		case "avatar_url":
			updateMap["avatar_url"] = userValue
		case "lang_tag":
			updateMap["lang_tag"] = userValue
		case "location":
			updateMap["location"] = userValue
		case "timezone":
			updateMap["timezone"] = userValue
		case "metadata":
			updateMap["metadata"] = userValue
		case "email":
			updateMap["email"] = userValue
		case "password":
			if v, ok := userValue.(string); ok {
				if len(v) < 8 {
					return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "Password must be at least 8 characters long.", Data: nil})
				}
				hashedPassword, err := bcrypt.GenerateFromPassword([]byte(v), bcrypt.DefaultCost)
				if err != nil {
					logger.Error("Error hashing password.", zap.Error(err))
					return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "Error updating user account password.", Data: nil})
				}
				updateMap["password"] = string(hashedPassword)
			}
		}
	}

	userID := req.UserID
	user := commbo.User{}
	obj := c.server.dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{user.GetKeyName(): userID})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err := obj.Query(ctx).As(&user)
	if err != nil {
		logger.Error("Failed to query user for user_id %s: %v", userID, err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "Failed to query user.", Data: userID})
	}

	//  ISO 8601格式
	updateMap["update_time"] = time.Now()
	obj.WithReqOption(models.WithValues(updateMap))
	resp, err := obj.Update(ctx).Result()
	if err != nil {
		logger.Error("Failed to update user for user_id %s: %v", userID, err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "Failed to update user.", Data: userID})
	}

	if resp.RowsAffected == 0 {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "Failed to update user.", Data: userID})
	}

	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "success", Data: userID})
}

// 修改用户性别
// @Summary 修改用户性别
// @Description 修改用户性别
// @Tags userserver
// @Produce  json
// @Accept	 application/json
// @Param request body ReqModifyGender true "请求参数"
// @Success 200 {object} models.CommonResp "返回结果"
// @Router /v2/rpc/userserver.modifygender [post]
func (c *Controller) ModifyGender(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uid := c.server.common.GetStrUin(ctx)
	if uid != "" {
		logger.Error("session not allowed, modify user gender")
		return json.MarshalToString(&models.CommonResp{Code: models.ACCESS_DENIED, Msg: "session not allowed", Data: nil})
	}

	var req ReqModifyGender
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}

	userID := req.UserID
	user := commbo.User{}
	obj := c.server.dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{user.GetKeyName(): userID})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err := obj.Query(ctx, "metadata").As(&user)
	if err != nil {
		logger.Error("Failed to query user for user_id %s: %v", userID, err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "Failed to query user.", Data: userID})
	}

	metadata := models.UserMetaData{}
	err = json.Unmarshal([]byte(user.Metadata), &metadata)
	if err != nil {
		logger.Error("Failed to unmarshal metadata for user_id %s: %v", userID, err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}

	roleData := models.RoleData{}
	err = json.Unmarshal([]byte(metadata.RoleData), &roleData)
	if err != nil {
		logger.Error("Failed to unmarshal role_data for user_id %s: %v", userID, err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}
	roleData.Gender = req.Gender
	metadata.RoleData, _ = json.MarshalToString(roleData)
	user.Metadata, _ = json.MarshalToString(metadata)
	user.UpdateTime = time.Now()
	_, err = obj.WithReqOption(models.WithValues(map[string]interface{}{"metadata": user.Metadata, "update_time": user.UpdateTime})).Update(ctx).Result()
	if err != nil {
		logger.Error("Failed to update user for user_id %s: %v", userID, err)
	}

	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "success", Data: userID})
}

// @Summary 封禁用户
// @Description 封禁用户
// @Tags userserver
// @Produce  json
// @Accept	 application/json
// @Param request body ReqBanUser true "请求参数"
// @Success 200 {object} models.CommonResp "返回结果"
// @Router /v2/rpc/userserver.ban [post]
func (c *Controller) Ban(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uid := c.server.common.GetStrUin(ctx)
	if uid != "" {
		logger.Error("session not allowed, ban user")
		return json.MarshalToString(&models.CommonResp{Code: models.ACCESS_DENIED, Msg: "session not allowed", Data: nil})
	}

	var req ReqBanUser
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}

	for _, userID := range req.UserIDs {
		user := commbo.User{}
		obj := c.server.dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{user.GetKeyName(): userID})).WithOption(logic.WithAutoLock())
		defer func() {
			c.server.dbagent.Release(obj)
		}()
		err := obj.Query(ctx).As(&user)
		if err != nil {
			logger.Error("Failed to query user for user_id %s: %v", userID, err)
			return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "Failed to query user.", Data: userID})
		}

		user.DisableTime = time.Now().Add(time.Duration(req.Time) * time.Second)
		user.UpdateTime = time.Now()
		_, err = obj.WithReqOption(models.WithValues(map[string]interface{}{"disable_time": user.DisableTime, "update_time": user.UpdateTime})).Update(ctx).Result()
		if err != nil {
			logger.Error("Failed to update ban user for user_id %s: %v", userID, err)
		}

		c.server.send.PushClose(ctx, []string{userID}, runtime.PresenceReasonDisconnect, &api.Notification{
			Id:         uuid.Must(uuid.NewV4()).String(),
			Subject:    "banned",
			Content:    "{}",
			Code:       int32(models.NotificationCodeUserBanned),
			SenderId:   "",
			CreateTime: &timestamppb.Timestamp{Seconds: time.Now().Unix()},
			Persistent: false,
		})
	}

	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "success", Data: req.UserIDs})
}

// @Summary 解封用户
// @Description 解封用户
// @Tags userserver
// @Produce  json
// @Accept	 application/json
// @Param request body ReqUnbanUser true "请求参数"
// @Success 200 {object} models.CommonResp "返回结果"
// @Router /v2/rpc/userserver.unban [post]
func (c *Controller) Unban(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uid := c.server.common.GetStrUin(ctx)
	if uid != "" {
		logger.Error("session not allowed, unban user")
		return json.MarshalToString(&models.CommonResp{Code: models.ACCESS_DENIED, Msg: "session not allowed", Data: nil})
	}

	var req ReqUnbanUser
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}

	for _, userID := range req.UserIDs {
		user := commbo.User{}
		obj := c.server.dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{user.GetKeyName(): userID})).WithOption(logic.WithAutoLock())
		defer func() {
			c.server.dbagent.Release(obj)
		}()
		err := obj.Query(ctx).As(&user)
		if err != nil {
			logger.Error("Failed to query user for user_id %s: %v", userID, err)
			return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "Failed to query user.", Data: userID})
		}

		user.DisableTime = time.Unix(0, 0)
		_, err = obj.WithReqOption(models.WithValues(map[string]interface{}{"disable_time": user.DisableTime})).Update(ctx).Result()
		if err != nil {
			logger.Error("Failed to update unban user for user_id %s: %v", userID, err)
		}
	}

	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "success", Data: req.UserIDs})
}
