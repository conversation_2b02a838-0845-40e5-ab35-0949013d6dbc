{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ui": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["file-saver", "js-yaml"], "outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": true, "assets": ["src/favicon.ico", "src/static"], "styles": ["src/bootstrap.min.css", "src/styles.scss"], "scripts": [], "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"local": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "production": {"outputPath": "dist/prod", "baseHref": "/", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}]}, "production-nt": {"outputPath": "dist/prod-nt", "baseHref": "/", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod-nt.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}]}}, "defaultConfiguration": "local"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "ui:build"}, "configurations": {"local": {"browserTarget": "ui:build:local"}, "production": {"browserTarget": "ui:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "ui:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/bootstrap.min.css", "src/styles.scss"], "scripts": []}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "ui:serve"}, "configurations": {"production": {"devServerTarget": "ui:serve:production"}}}}}}, "cli": {"analytics": false}}