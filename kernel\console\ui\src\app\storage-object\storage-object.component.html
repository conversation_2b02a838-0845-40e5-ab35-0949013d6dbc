<h2 class="pb-1">Storage Object</h2>
<div class="storage-top-nav d-flex justify-content-between align-items-baseline mb-3">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb">
      <li class="breadcrumb-item">Storage</li>
      <li class="breadcrumb-item"><a [routerLink]="['/storage']" [queryParams]="{'collection': this.object.collection}">{{this.object.collection}}</a></li>
      <li class="breadcrumb-item"><a [routerLink]="['/storage']" [queryParams]="{'collection': this.object.collection, 'key': this.object.key}">{{this.object.key}}</a></li>
      <li class="breadcrumb-item"><a [routerLink]="['/storage']" [queryParams]="{'collection': this.object.collection, 'key': this.object.key, 'user_id': this.object.user_id}">{{this.object.user_id}}</a></li>
    </ol>
  </nav>
</div>

<ngb-alert [dismissible]="false" type="danger" class="mb-3" *ngIf="error">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">Error while modifying storage object: {{error}}</h6>
</ngb-alert>

<ngb-alert [dismissible]="false" type="success" class="mb-3" *ngIf="updated">
  <img src="/static/svg/green-tick.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">Storage object was modified successfully.</h6>
</ngb-alert>

<form [formGroup]="objectForm" (ngSubmit)="updateObject()" class="add-border">
  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="collection">Collection</label>
      </div>
      <input type="text" id="collection" class="form-control form-control-sm my-2" placeholder="Collection" required formControlName="collection" [ngClass]="{'is-invalid': f.collection.dirty && f.collection.errors}">
      <div class="invalid-tooltip" [hidden]="f.collection.disabled || f.collection.valid || f.collection.pristine">Collection is required</div>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="key">Key</label>
      </div>
      <input type="text" id="key" class="form-control form-control-sm my-2" placeholder="Key" required formControlName="keyname" [ngClass]="{'is-invalid': f.keyname.dirty && f.keyname.errors}">
      <div class="invalid-tooltip" [hidden]="f.keyname.disabled || f.keyname.valid || f.keyname.pristine">Key is required</div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="user_id">User ID</label>
      </div>
      <input type="text" id="user_id" class="form-control form-control-sm my-2" placeholder="User ID" required formControlName="user_id" [ngClass]="{'is-invalid': f.user_id.dirty && f.user_id.errors}">
      <div class="invalid-tooltip" [hidden]="f.user_id.disabled || f.user_id.valid || f.user_id.pristine">User ID is required</div>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline align-self-center">
      <div class="col-3 pl-0">
        <label class="d-inline" for="permission_read">Read Permission</label>
      </div>
      <div class="btn-group btn-group-toggle" id="permission_read" name="permission_read">
        <label class="btn btn-outline-secondary" [ngClass]="{'active': f.permission_read.value == 0}">
          <input formControlName="permission_read" type="radio" [value]="0"> No Read
        </label>
        <label class="btn btn-outline-secondary" [ngClass]="{'active': f.permission_read.value == 1}">
          <input formControlName="permission_read" type="radio" [value]="1"> Owner Read
        </label>
        <label class="btn btn-outline-secondary" [ngClass]="{'active': f.permission_read.value == 2}">
          <input formControlName="permission_read" type="radio" [value]="2"> Public Read
        </label>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="version">Version</label>
      </div>
      <input type="text" id="version" [value]="object.version" class="form-control-plaintext form-control-sm my-2" placeholder="Version" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline align-self-center">
      <div class="col-3 pl-0">
        <label class="d-inline" for="permission_write">Write Permission</label>
      </div>
      <div class="btn-group btn-group-toggle" id="permission_write" name="permission_write">
        <label class="btn btn-outline-secondary" [ngClass]="{'active': f.permission_write.value == 0}">
          <input formControlName="permission_write" type="radio" [value]="0"> No Write
        </label>
        <label class="btn btn-outline-secondary" [ngClass]="{'active': f.permission_write.value == 1}">
          <input formControlName="permission_write" type="radio" [value]="1"> Owner Write
        </label>
      </div>
    </div>
  </div>

  <div class="row add-border-single-row-bottom mb-3">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="create_time">Create Time</label>
      </div>
      <input type="text" id="create_time" [value]="object.create_time" class="form-control-plaintext form-control-sm my-2" placeholder="Create Time" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="update_time">Update Time</label>
      </div>
      <input type="text" id="update_time" [value]="object.update_time" class="form-control-plaintext form-control-sm my-2" placeholder="Update Time" disabled readonly>
    </div>
  </div>

  <div class="card p-2 mt-3 mb-3 jsoneditor" style="height: 518px">
    <div #editor style="height: 500px"></div>
  </div>
  <button type="submit" class="btn btn-primary" [disabled]="updating" *ngIf="updateAllowed()">
    <span *ngIf="object.collection !== f.collection.value
      || object.key !== f.keyname.value
      || object.user_id !== f.user_id.value; else elseBlock">Save as a copy</span>
    <ng-template #elseBlock>Save</ng-template>
  </button>
</form>
