Before you submit an issue we recommend you drop into the [community forum](https://forum.heroiclabs.com) and ask any questions you have or mention any problems you've had getting started with the server.

Please provide as much information as you can with this issue report. If you believe it may be an issue with one of the client libraries please report it on their [own trackers](https://github.com/heroiclabs?utf8=%E2%9C%93&q=nakama%20AND%20sdk&type=&language=).

## Description
<!--- Example: Error thrown when Unity client fetches a user's friends. -->

## Steps to Reproduce
<!---
Example:
1. Register a new user
2. Connect to server socket
3. Send message to fetch current user's friends
4. Errors reported to client and in server logs
-->

## Expected Result
<!--- Example: No error is thrown and results are returned by server. -->

## Actual Result
<!--- Example: Error is thrown with stacktrace in logs. -->

## Context
<!-- Which client did you use? -->
- [ ] Unity
- [ ] Unreal
- [ ] Other

## Your Environment
<!---
`nakama --version` will show the version of the server.
-->
- Nakama: X.X.X
- Database: X.X.X
- Environment name and version:
- Operating System and version:
