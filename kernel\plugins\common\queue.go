package common

import (
	"sync"
)

// 生产者-消费者队列
type Queue[T comparable] struct {
	mu      sync.Mutex     // 互斥锁
	cond    *sync.Cond     // 条件变量
	queue   []T            // 存储
	closed  bool           // 是否关闭
	leavers map[T]struct{} // 记录主动离开的key
}

// 创建队列
func NewQueue[T comparable]() *Queue[T] {
	q := &Queue[T]{
		leavers: make(map[T]struct{}),
		queue:   make([]T, 0),
	}
	q.cond = sync.NewCond(&q.mu)
	return q
}

// 生产者：添加数据（key 入队）
func (q *Queue[T]) Enqueue(key T) {
	q.mu.Lock()
	defer q.mu.Unlock()

	if q.closed {
		return
	}

	delete(q.leavers, key) // key 入队时清除离队标记

	q.queue = append(q.queue, key)
	q.cond.Signal() // 唤醒一个等待的消费者
}

// 消费者：获取数据（支持 `key` 离队）
func (q *Queue[T]) Dequeue() (T, bool) {
	q.mu.Lock()
	defer q.mu.Unlock()

	var zero T
	for {
		if q.closed {
			return zero, false
		}

		// 查找一个没有主动离队的 key
		for len(q.queue) > 0 {
			key := q.queue[0]
			q.queue = q.queue[1:]

			if _, left := q.leavers[key]; !left { // 未离队
				delete(q.leavers, key) // 清理已使用的key，防止内存泄漏
				return key, true
			}
			delete(q.leavers, key) // 清理已处理的离队key
		}

		// 队列空了，等待数据
		q.cond.Wait()
	}
}

// 消费者：主动离队
func (q *Queue[T]) Leave(key T) {
	q.mu.Lock()
	defer q.mu.Unlock()

	q.leavers[key] = struct{}{} // 标记 key 已离队
}

// 关闭队列
func (q *Queue[T]) Close() {
	q.mu.Lock()
	defer q.mu.Unlock()

	q.closed = true
	q.cond.Broadcast() // 通知所有消费者退出
}

// 检查队列是否已关闭
func (q *Queue[T]) Closed() bool {
	q.mu.Lock()
	defer q.mu.Unlock()
	return q.closed
}

// 清空队列
func (q *Queue[T]) Clear() {
	q.mu.Lock()
	defer q.mu.Unlock()
	q.queue = make([]T, 0)
	q.leavers = make(map[T]struct{})
}
