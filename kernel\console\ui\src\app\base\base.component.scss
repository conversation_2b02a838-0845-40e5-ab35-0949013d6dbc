/* Colors */
$brand-purple: #7668ED;
$brand-light-purple: #A69BFF;
$brand-grey: #FAFAFC;
$brand-dark-blue: #333564;

.nav-link.no-active {
  background-color: transparent !important;
}

.nav-item hr {
  border-top-color: slate<PERSON>rey;
}

/* Nav sidebar */
.sidebar {
  background-color: $brand-dark-blue;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100; /* Behind the nav header */
  font-family: 'Montserrat', sans-serif;
  overflow-y: auto; /* Scrollable contents if viewport is shorter than content. */

  .nav-item {
    /*background-color: red;*/
  }

  .nav-link {
    color: #fff;
    font-size: 12px;
    letter-spacing: 0.1em;
    opacity: 0.6;
    font-weight: 500 !important;

    &:hover {
      opacity: 1;
      background-color: rgba(225,225,225,0.1);
    }
  }

  hr {
    border-color: #fff;
    opacity: 0.2;
  }
}

.active {
  opacity: 1;
}

.nav-link.active {
  opacity: 1;
  color: $brand-light-purple;
}

.navbar-collapse {
  letter-spacing: 0;
  text-transform: capitalize;
}

.logo-icon {
  display: none;
}

.link-text {
  padding-left: 4px;
}

@media screen and (max-width: 1200px) {
  .main-extended {
    width: 94.333333% !important;
    max-width: 94.333333% !important;
    flex: 0 0 94.333333% !important;
  }
  .sidebar {
    min-width: 65px !important;
    width: 5.66% !important;
    max-width: 5.66% !important;

      .logo-full {
        display: none;
      }

      .logo-icon {
        display: block;
      }

      .link-icon {
        width: 30px;
        color: #fff;
      }

      .link-text {
        display: none;
      }

      .logged-in-as {
        display: none;
      }

    .nav-link {

    }
  }
}
