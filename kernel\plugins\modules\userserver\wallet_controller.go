package main

import (
	"context"
	"database/sql"
	"kernel/kernel-common/runtime"
	commbo "kernel/plugins/bo"
	"kernel/plugins/logic"
	"kernel/plugins/models"

	json "github.com/json-iterator/go"
)

// @Summary 获取钱包信息
// @Description 获取钱包信息
// @Tags userserver
// @Accept json
// @Produce json
// @Success 200 {object} RespWallet "返回结果"
// @Router /v2/rpc/userserver.wallet.query [post]
func (c *Controller) WalletQuery(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if c.server.common.GetUserID(ctx) != "" {
		logger.Error("session not allowed, query wallet")
		return json.MarshalToString(&RespWallet{Code: int64(models.ACCESS_DENIED), Message: "session not allowed", Data: nil})
	}

	var req ReqWalletQuery
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&RespWallet{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	user_id := req.UserID
	if user_id == "" {
		return json.MarshalToString(&RespWallet{Code: int64(models.PARAM_ERR), Message: "user_id is empty", Data: nil})
	}

	wallet := PlayerWallet{}
	fields, err := c.server.common.QueryUserFileds(ctx, user_id, "wallet")
	if err != nil {
		logger.Error("Failed to query wallet for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespWallet{Code: int64(models.EXCEPTION), Message: err.Error(), Data: nil})
	}
	err = json.UnmarshalFromString(fields["wallet"].(string), &wallet)
	if err != nil {
		logger.Error("Failed to unmarshal wallet for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespWallet{Code: int64(models.EXCEPTION), Message: err.Error(), Data: nil})
	}
	return json.MarshalToString(&RespWallet{Code: int64(models.OK), Message: "success", Data: &wallet})
}

// @Summary 添加钻石，金币
// @Description 添加钻石，金币
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqWalletAdd true "请求参数"
// @Success 200 {object} RespWallet "返回结果"
// @Router /v2/rpc/userserver.wallet.add [post]
func (c *Controller) WalletAdd(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if c.server.common.GetUserID(ctx) != "" {
		logger.Error("session not allowed, add wallet")
		return json.MarshalToString(&RespWallet{Code: int64(models.ACCESS_DENIED), Message: "session not allowed", Data: nil})
	}

	req := ReqWalletAdd{}
	err := json.UnmarshalFromString(payload, &req)
	if err != nil {
		logger.Error("Failed to unmarshal payload for user_id %s: %v", req.UserID, err)
		return json.MarshalToString(&RespWallet{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	if req.UserID == "" || (req.Diamond <= 0 && req.DiamondGift <= 0 && req.Gold <= 0) {
		return json.MarshalToString(&RespWallet{Code: int64(models.PARAM_ERR), Message: "param error", Data: nil})
	}

	user := commbo.User{}
	obj := c.server.dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{user.GetKeyName(): req.UserID})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx, "wallet").As(&user)
	if err != nil {
		logger.Error("Failed to query wallet for user_id %s: %v", req.UserID, err)
		return json.MarshalToString(&RespWallet{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	wallet := PlayerWallet{}
	err = json.UnmarshalFromString(user.Wallet, &wallet)
	if err != nil {
		logger.Error("Failed to unmarshal wallet for user_id %s: %v", req.UserID, err)
		return json.MarshalToString(&RespWallet{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	wallet.Diamond += req.Diamond
	wallet.DiamondGift += req.DiamondGift
	wallet.Gold += req.Gold

	user.Wallet, _ = json.MarshalToString(wallet)
	_, err = obj.WithReqOption(models.WithValues(map[string]interface{}{"wallet": user.Wallet})).Update(ctx).Result()
	if err != nil {
		c.server.logger.Error("Failed to update wallet for user_id %s: %v", req.UserID, err)
		return json.MarshalToString(&RespWallet{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	return json.MarshalToString(&RespWallet{Code: int64(models.OK), Message: "success", Data: &wallet})
}

// @Summary 扣除钻石，金币
// @Description 扣除钻石，金币
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqWalletSub true "请求参数"
// @Success 200 {object} RespWallet "返回结果"
// @Router /v2/rpc/userserver.wallet.sub [post]
func (c *Controller) WalletSub(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if c.server.common.GetUserID(ctx) != "" {
		logger.Error("session not allowed, sub wallet")
		return json.MarshalToString(&RespWallet{Code: int64(models.ACCESS_DENIED), Message: "session not allowed", Data: nil})
	}

	req := ReqWalletSub{}
	err := json.UnmarshalFromString(payload, &req)
	if err != nil {
		logger.Error("Failed to unmarshal payload for user_id %s: %v", req.UserID, err)
		return json.MarshalToString(&RespWallet{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	if req.UserID == "" || (req.Diamond <= 0 && req.DiamondGift <= 0 && req.Gold <= 0) {
		return json.MarshalToString(&RespWallet{Code: int64(models.PARAM_ERR), Message: "param error", Data: nil})
	}

	user := commbo.User{}
	obj := c.server.dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{user.GetKeyName(): req.UserID})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx, "wallet").As(&user)
	if err != nil {
		logger.Error("Failed to query wallet for user_id %s: %v", req.UserID, err)
		return json.MarshalToString(&RespWallet{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	wallet := PlayerWallet{}
	err = json.UnmarshalFromString(user.Wallet, &wallet)
	if err != nil {
		logger.Error("Failed to unmarshal wallet for user_id %s: %v", req.UserID, err)
		return json.MarshalToString(&RespWallet{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	// 前置检查
	if req.Diamond > 0 && req.DiamondGift > 0 {
		// 必须同时满足钻石和钻石赠送的扣除
		if wallet.Diamond < req.Diamond || wallet.DiamondGift < req.DiamondGift {
			return json.MarshalToString(&RespWallet{Code: int64(models.FAILED), Message: "diamond or diamond gift not enough", Data: nil})
		}
		wallet.Diamond -= req.Diamond
		wallet.DiamondGift -= req.DiamondGift
	} else if req.Diamond == 0 && req.DiamondGift > 0 {
		// 只扣除赠送
		if wallet.DiamondGift < req.DiamondGift {
			return json.MarshalToString(&RespWallet{Code: int64(models.FAILED), Message: "diamond gift not enough", Data: nil})
		}
		wallet.DiamondGift -= req.DiamondGift
	} else if req.Diamond > 0 {
		// 优先扣除购买获得的钻石
		if wallet.Diamond+wallet.DiamondGift < req.Diamond {
			return json.MarshalToString(&RespWallet{Code: int64(models.FAILED), Message: "diamond not enough", Data: nil})
		}
		if wallet.Diamond >= req.Diamond {
			wallet.Diamond -= req.Diamond
		} else {
			wallet.DiamondGift -= req.Diamond - wallet.Diamond
			wallet.Diamond = 0 // 购买获得的钻石已经扣完，剩余的钻石从赠送中扣除
		}
	}
	if req.Gold > 0 {
		if wallet.Gold < req.Gold {
			return json.MarshalToString(&RespWallet{Code: int64(models.FAILED), Message: "gold not enough", Data: nil})
		}
		wallet.Gold -= req.Gold
	}

	user.Wallet, _ = json.MarshalToString(wallet)
	_, err = obj.WithReqOption(models.WithValues(map[string]interface{}{"wallet": user.Wallet})).Update(ctx).Result()
	if err != nil {
		c.server.logger.Error("Failed to update wallet for user_id %s: %v", req.UserID, err)
		return json.MarshalToString(&RespWallet{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	return json.MarshalToString(&RespWallet{Code: int64(models.OK), Message: "success", Data: &wallet})
}
