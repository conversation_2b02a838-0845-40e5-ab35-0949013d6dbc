package main

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"sort"
	"strings"
)

// 举报回调请求
type ReportCallbackReq struct {
	ReportID        string `json:"report_id,omitempty"`        //举报请求的唯一ID，该id设置后，其他参数自动失效
	ViolationType   string `json:"violation_type,omitempty"`   //违规类型
	ReviewerID      string `json:"reviewer_id,omitempty"`      //审核人ID
	Status          int    `json:"status,omitempty"`           //处理状态,0：无需处理,1: 未处理,2: 已经处理
	PunishmentID    string `json:"punishment_id,omitempty"`    //惩罚类型
	PunishmentHours int    `json:"punishment_hours,omitempty"` //惩罚时长
	IsAppealable    bool   `json:"is_appealable,omitempty"`    //是否可申诉
}

// 举报回调响应
type ReportCallbackResp struct {
	Code    int    `json:"code,omitempty"`    //错误码
	Message string `json:"message,omitempty"` //错误信息
}

// 证据信息
type EvidenceInfo struct {
	EvidenceURL  string `json:"evidence_url,omitempty"`  //证据链接地址
	EvidenceType int    `json:"evidence_type,omitempty"` //证据类型
}

// 举报请求
type ReportReq struct {
	ReporterID     string          `json:"reporter_id,omitempty"`     //举报人玩家ID
	ReporterRegion string          `json:"reporter_region,omitempty"` //区服标识（可选）
	ReportedID     string          `json:"reported_id,omitempty"`     //被举报人玩家ID
	ReportType     int             `json:"report_type,omitempty"`     //举报类型
	Description    string          `json:"description,omitempty"`     //问题描述
	EvidenceInfos  []*EvidenceInfo `json:"evidence_infos,omitempty"`  //证据截图、视频链接（可选）
	ClientVersion  string          `json:"client_version,omitempty"`  //客户端版本（可选）
	CreatedAt      string          `json:"created_at,omitempty"`      //举报时间（可选，不填则以入库时间为准）
	ReportSource   string          `json:"report_source,omitempty"`   //举报来源(game, website等)
}

// 举报响应
type ReportResp struct {
	ErrorCode int    `json:"error_code,omitempty"` //错误码
	ErrorMsg  string `json:"error_msg,omitempty"`  //错误信息
	ReportID  string `json:"report_id,omitempty"`  //举报请求的唯一ID
}

// 查询举报请求
type QueryReportReq struct {
	ReportID       string `json:"report_id,omitempty"`       //举报请求的唯一ID，该id设置后，其他参数自动失效
	ReporterID     string `json:"reporter_id,omitempty"`     //举报人玩家ID
	ReporterRegion string `json:"reporter_region,omitempty"` //区服标识
	ReportedID     string `json:"reported_id,omitempty"`     //被举报人玩家ID
	ReportType     int    `json:"report_type,omitempty"`     //举报类型
	ClientVersion  string `json:"client_version,omitempty"`  //客户端版本
	ViolationType  string `json:"violation_type,omitempty"`  //违规类型
	Status         int    `json:"status,omitempty"`          //处理状态
	IsAppealable   int    `json:"is_appealable,omitempty"`   //是否可申诉
	BeginTime      string `json:"begin_time,omitempty"`      //举报时间（可选），不填则默认最近一周
	EndTime        string `json:"end_time,omitempty"`        //举报时间（可选），不填则默认最近一周
	Offset         int    `json:"offset,omitempty"`          //偏移量（offset）：表示从结果集的哪个位置开始返回数据，0 表示第一行。
	Limit          int    `json:"limit,omitempty"`           //返回的行数（limit）：表示最多返回多少行数据。
}

// 查询举报响应
type QueryReportResp struct {
	ErrorCode        int                 `json:"error_code,omitempty"`         //错误码
	ErrorMsg         string              `json:"error_msg,omitempty"`          //错误信息
	Total            int                 `json:"total,omitempty"`              //总行数
	ReportRecordItem []*ReportRecordItem `json:"report_record_item,omitempty"` //举报记录列表
}

// 举报记录项
type ReportRecordItem struct {
	ReportID        string          `json:"report_id,omitempty"`        //举报请求的唯一ID，该id设置后，其他参数自动失效
	ReporterID      string          `json:"reporter_id,omitempty"`      //举报人玩家ID
	ReporterRegion  string          `json:"reporter_region,omitempty"`  //区服标识
	ReportedID      string          `json:"reported_id,omitempty"`      //被举报人玩家ID
	ReportType      int             `json:"report_type,omitempty"`      //举报类型
	ClientVersion   string          `json:"client_version,omitempty"`   //客户端版本
	ViolationType   string          `json:"violation_type,omitempty"`   //违规类型
	Status          int             `json:"status,omitempty"`           //处理状态
	CreatedAt       string          `json:"created_at,omitempty"`       //创建时间
	HandledAt       string          `json:"handled_at,omitempty"`       //审核处理时间
	EvidenceInfos   []*EvidenceInfo `json:"evidence_infos,omitempty"`   //证据截图、视频链接（可选）
	Description     string          `json:"description,omitempty"`      //问题描述
	PunishmentID    string          `json:"punishment_id,omitempty"`    //惩罚类型
	PunishmentHours int             `json:"punishment_hours,omitempty"` //惩罚时长
	IsAppealable    bool            `json:"is_appealable,omitempty"`    //是否可申诉
	ReportSource    string          `json:"report_source,omitempty"`    //举报来源
}

// UploadFileInfo
type UploadFileInfo struct {
	FileName string `json:"file_name,omitempty"` // 文件名
	FileSize int    `json:"file_size,omitempty"` // 文件大小
	FileMD5  string `json:"file_md5,omitempty"`  // 文件MD5
}

// 获取上传URL请求
type GetUploadURLReq struct {
	UploadFileInfos []*UploadFileInfo `json:"files,omitempty"` // 上传文件信息
}

// 获取上传URL响应
type GetUploadURLResp struct {
	ErrorCode int      `json:"error_code,omitempty"` //错误码
	ErrorMsg  string   `json:"error_msg,omitempty"`  //错误信息
	URLs      []string `json:"urls,omitempty"`       //上传URL
}

// 签名方式
// 1. 对所有请求参数（包括公有参数和私有参数，但不包括signature参数），按照参数名ASCII码表升序顺序排序。如：foo=1， bar=2， foo_bar=3， baz=4 排序后的顺序是bar=2，baz=4，foo=1，foo_bar=3。
// 2. 将排序好的参数名和参数值构造成字符串，格式为：key1+value1+key2+value2…。根据上面的示例得到的构造结果为：bar2baz4foo1foo_bar3。
// 3. 选择与app_id配对的secretKey(步骤一注册APP时，从管理台上获取)，加到上一步构造好的参数字符串之后，如secretKey=6308afb129ea00301bd7c79621d07591 ，则最后的参数字符串为bar2baz4foo1foo_bar36308afb129ea00301bd7c79621d07591。
// 4. 把以上拼装好的字符串采用utf-8编码，使用MD5算法对字符串进行摘要，计算得到signature参数值，将其加入到接口请求参数中即可。签名后的字符串为32位长度的十六进制字符。
func ReportSign(secretKey string, header map[string]string, params map[string]interface{}) string {
	// 收集所有参数名
	keys := make([]string, 0, len(header)+len(params))
	for k := range header {
		if k != "signature" { // 排除signature参数
			keys = append(keys, k)
		}
	}
	for k := range params {
		if k != "signature" { // 排除signature参数
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 按ASCII码顺序拼接参数
	var signBuilder strings.Builder
	for _, k := range keys {
		if v, ok := header[k]; ok {
			signBuilder.WriteString(k)
			signBuilder.WriteString(v)
		} else if v, ok := params[k]; ok {
			signBuilder.WriteString(k)
			signBuilder.WriteString(fmt.Sprintf("%v", v))
		} else {
			fmt.Println("unknown key:", k)
		}
	}
	signBuilder.WriteString(secretKey)

	// 计算MD5
	md5Str := md5.Sum([]byte(signBuilder.String()))
	return hex.EncodeToString(md5Str[:])
}
