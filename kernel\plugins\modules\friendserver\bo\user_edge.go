// "source_id" uuid NOT NULL,
//   "position" int8 NOT NULL,
//   "update_time" timestamptz(6) NOT NULL DEFAULT now(),
//   "destination_id" uuid NOT NULL,
//   "state" int2 NOT NULL DEFAULT 0,

package bo

import (
	"bytes"
	"encoding/json"
	"fmt"
	"kernel/plugins/common"
	"kernel/plugins/models"
	"reflect"
	"time"
)

type UserEdge struct {
	SourceId      string    `json:"source_id"`
	Position      int64     `json:"position"`
	UpdateTime    time.Time `json:"update_time"`
	DestinationId string    `json:"destination_id"`
	State         int       `json:"state"`
}

func (f *UserEdge) GetTable() string {
	return "user_edge"
}

func (f *UserEdge) GetKeyName() string {
	return "source_id"
}

func (f *UserEdge) GetUniqueKeys() []string {
	return nil
}

func (f *UserEdge) GetSecondKeyName() string {
	return "destination_id"
}

func (f *UserEdge) GetRowResultType() models.RowResultType {
	return models.ROWRESULT_TYPE_MULTI
}

func (f *UserEdge) GetQueryArgs() string {
	return "*"
}

func (f *UserEdge) GetFlushType() models.FlushType {
	return models.FLUSH_TYPE_SYNC
}

func (f *UserEdge) GetCacheLevel() models.CacheLevel {
	return models.CACHE_LEVEL_IMPORT
}

func (f *UserEdge) GetVersionName() string {
	return ""
}

func (f *UserEdge) Marshal() ([]byte, error) {
	return json.Marshal(f)
}

func (f *UserEdge) MarshalToMap(filter map[string]struct{}) map[string]interface{} {
	dst := make(map[string]interface{})
	err := common.StrcutToMap(f, &dst)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	for k := range filter {
		delete(dst, k)
	}
	return dst
}

func (f *UserEdge) Unmarshal(buf []byte) error {
	d := json.NewDecoder(bytes.NewReader(buf))
	d.UseNumber()
	return d.Decode(f)
}

func (f *UserEdge) Clear() {
	p := reflect.ValueOf(f).Elem()
	p.Set(reflect.Zero(p.Type()))
}
