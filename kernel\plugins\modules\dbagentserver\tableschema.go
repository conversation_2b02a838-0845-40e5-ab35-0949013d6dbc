package main

import (
	"database/sql"
	"fmt"
	"kernel/kernel-common/runtime"
	"reflect"
	"strings"
	"sync"
	"time"

	"github.com/jmoiron/sqlx"
)

var tableSchema *TableSchema = nil
var once sync.Once

type TableSchema struct {
	tableStructMap sync.Map // key: table name, value: reflect.Type (struct)
	db             *sql.DB
	logger         runtime.Logger
	sqlxDB         *sqlx.DB
	// 添加表结构哈希缓存
	tableHashes sync.Map // key: table name, value: string (table structure hash)
}

func NewTableSchema(db *sql.DB, logger runtime.Logger) *TableSchema {
	once.Do(func() {
		tableSchema = &TableSchema{
			db:     db,
			logger: logger,
			sqlxDB: sqlx.NewDb(db, "postgres"),
		}
		//tableSchema.StartTableStructureMonitor(3 * time.Minute)
	})
	return tableSchema
}

func GetTableSchema() *TableSchema {
	return tableSchema
}

// 创建表结构体
func (t *TableSchema) CreateStructType(tableName string) (reflect.Type, error) {
	// 尝试从缓存中获取
	if structType, ok := t.tableStructMap.Load(tableName); ok {
		return structType.(reflect.Type), nil
	}

	columns, types, err := t.getTableStructure(tableName)
	if err != nil {
		return nil, err
	}

	// 创建结构体字段
	fields := make([]reflect.StructField, len(columns))
	for i, col := range columns {
		fields[i] = reflect.StructField{
			Name: strings.Title(col), // 首字母大写
			Type: t.getGoType(types[i]),
			Tag:  reflect.StructTag(fmt.Sprintf(`db:"%s"`, col)),
		}
	}

	// 创建结构体类型
	structType := reflect.StructOf(fields)
	t.tableStructMap.Store(tableName, structType)
	return structType, nil
}

// 添加获取已缓存表名的方法
func (t *TableSchema) GetCachedTableNames() []string {
	var tables []string
	t.tableStructMap.Range(func(key, value interface{}) bool {
		if tableName, ok := key.(string); ok {
			tables = append(tables, tableName)
		}
		return true
	})
	return tables
}

// 修改定期检查表结构变化的方法
func (t *TableSchema) StartTableStructureMonitor(interval time.Duration) {
	go func() {
		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		for range ticker.C {
			// 只获取已缓存的表名
			tables := t.GetCachedTableNames()

			for _, tableName := range tables {
				changed, err := t.checkTableStructureChanged(tableName)
				if err != nil {
					t.logger.Error("Error checking table structure for %s: %v", tableName, err)
					continue
				}

				if changed {
					t.logger.Warn("Table structure changed for %s, clearing cache", tableName)
				}
			}
		}
	}()
}

// 修改获取表结构的函数，添加是否可空的信息
func (t *TableSchema) getTableStructure(tableName string) ([]string, []string, error) {
	query := fmt.Sprintf(`
		SELECT column_name, data_type, is_nullable
		FROM information_schema.columns 
		WHERE table_name = $1 
		ORDER BY ordinal_position
	`)

	rows, err := t.sqlxDB.Query(query, tableName)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get table structure: %w", err)
	}
	defer rows.Close()

	var columns []string
	var types []string
	for rows.Next() {
		var colName, dataType, isNullable string
		if err := rows.Scan(&colName, &dataType, &isNullable); err != nil {
			return nil, nil, fmt.Errorf("failed to scan column info: %w", err)
		}
		columns = append(columns, colName)
		types = append(types, dataType)
	}
	if err := rows.Err(); err != nil {
		return nil, nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return columns, types, nil
}

// 添加类型转换函数
func (t *TableSchema) getGoType(pgType string) reflect.Type {
	// 创建可空类型
	nullableType := func(baseType reflect.Type) reflect.Type {
		return reflect.PointerTo(baseType)
	}

	switch pgType {
	case "integer", "int", "int4", "int8", "bigint":
		return nullableType(reflect.TypeOf(int64(0)))
	case "float", "float4", "float8", "double precision", "real":
		return nullableType(reflect.TypeOf(float64(0)))
	case "boolean", "bool":
		return nullableType(reflect.TypeOf(false))
	case "timestamp", "timestamptz", "date", "time":
		return nullableType(reflect.TypeOf(time.Time{}))
	default:
		return nullableType(reflect.TypeOf(""))
	}
}

// 添加计算表结构哈希的函数
func (t *TableSchema) calculateTableHash(columns []string, types []string) string {
	var builder strings.Builder
	for i := 0; i < len(columns); i++ {
		builder.WriteString(columns[i])
		builder.WriteString(":")
		builder.WriteString(types[i])
		builder.WriteString(";")
	}
	return builder.String()
}

// 添加检查表结构是否变化的函数
func (t *TableSchema) checkTableStructureChanged(tableName string) (bool, error) {
	columns, types, err := t.getTableStructure(tableName)
	if err != nil {
		return false, err
	}

	newHash := t.calculateTableHash(columns, types)

	// 获取旧的哈希值
	oldHash, exists := t.tableHashes.Load(tableName)
	if exists && oldHash.(string) == newHash {
		return false, nil
	}

	// 创建结构体字段
	fields := make([]reflect.StructField, len(columns))
	for i, col := range columns {
		fields[i] = reflect.StructField{
			Name: strings.Title(col), // 首字母大写
			Type: t.getGoType(types[i]),
			Tag:  reflect.StructTag(fmt.Sprintf(`db:"%s"`, col)),
		}
	}

	// 创建结构体类型
	structType := reflect.StructOf(fields)
	t.tableStructMap.Store(tableName, structType)
	t.tableHashes.Store(tableName, newHash)
	return true, nil
}
