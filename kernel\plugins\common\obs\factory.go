package obs

import (
	"fmt"
	"strings"
)

// Object Storage Factory
func NewOSClient(obsType string, osConfig *OSConfig) IOSClient {
	var result IOSClient
	switch obsType {
	case ObsTypeNameHuawei:
		result = NewOSHuawei(osConfig)
	case ObsTypeNameAliyun:
		result = NewOSAliyun(osConfig)
	case ObsTypeNameTencent:
		result = NewS3Client(ObsTypeNameTencent, osConfig)
	default:
		if strings.HasPrefix(obsType, "s3_") {
			result = NewS3Client(obsType, osConfig)
		} else {
			msg := fmt.Sprintf("No supported os type: %s. support type [ ucloud | huawei | aliyun | tencent ]", obsType)
			panic(msg)
		}
	}

	return result
}
