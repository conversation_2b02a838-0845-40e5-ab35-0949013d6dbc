package main

import (
	"database/sql"
	"kernel/plugins/models"
)

type CacheItem interface {
	Query() (bool, error)
	Insert(data map[string]interface{}) (sql.Result, error) // 同步插入
	Update(data map[string]interface{}) (sql.Result, error) // 同步更新
	Delete(secondval interface{}) (sql.Result, error)
	AsyncUpdate(kvs map[string]interface{}) // 异步插入和更新
	String(kvs map[string]interface{}) string
	IsDirty() bool
	Flush() error
	FlushType() models.FlushType
	GetRowResultType() models.RowResultType
	GetUniques() map[string]interface{}
	TableName() string
}
