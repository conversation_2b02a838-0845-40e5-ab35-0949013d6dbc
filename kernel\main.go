// Copyright 2018 The Nakama Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package main

import (
	"context"
	"database/sql"
	"flag"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"path/filepath"
	sysruntime "runtime"
	"strings"
	"syscall"
	"time"

	"kernel/console"
	"kernel/graceful"
	"kernel/kernel-common/accesslog"
	kernelruntime "kernel/kernel-common/runtime"
	"kernel/migrate"
	"kernel/se"
	"kernel/server"
	"kernel/social"

	"github.com/gofrs/uuid/v5"
	"github.com/jackc/pgx/v5/stdlib"
	_ "github.com/jackc/pgx/v5/stdlib" // Blank import to register SQL driver
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"google.golang.org/protobuf/encoding/protojson"

	_ "github.com/klauspost/compress"
	_ "github.com/prometheus/client_golang/prometheus"
	_ "github.com/prometheus/common/model"
)

const cookieFilename = ".cookie"

var (
	version  string = "3.0.0"
	commitID string = "dev"

	// Shared utility components.
	jsonpbMarshaler = &protojson.MarshalOptions{
		UseEnumNumbers:  true,
		EmitUnpopulated: false,
		Indent:          "",
		UseProtoNames:   true,
	}
	jsonpbUnmarshaler = &protojson.UnmarshalOptions{
		DiscardUnknown: false,
	}
)

func main() {
	defer os.Exit(0)

	semver := fmt.Sprintf("%s+%s", version, commitID)
	// Always set default timeout on HTTP client.
	http.DefaultClient.Timeout = 1500 * time.Millisecond

	tmpLogger := server.NewJSONLogger(os.Stdout, server.NewAtomicLevelAt(zapcore.InfoLevel), server.JSONFormat)

	ctx, ctxCancelFn = context.WithCancel(context.Background())

	if len(os.Args) > 1 {
		switch os.Args[1] {
		case "--version":
			fmt.Println(semver)
			return
		case "migrate":
			config := server.ParseArgs(tmpLogger, os.Args[2:])
			if config.GetDatabase().Disable {
				tmpLogger.Fatal("Database is disabled")
				return
			}
			server.ValidateConfigDatabase(tmpLogger, config)
			db := server.DbConnect(ctx, tmpLogger, config, true)
			defer db.Close()

			conn, err := db.Conn(ctx)
			if err != nil {
				tmpLogger.Fatal("Failed to acquire db conn for migration", zap.Error(err))
			}

			if err = conn.Raw(func(driverConn any) error {
				pgxConn := driverConn.(*stdlib.Conn).Conn()
				migrate.RunCmd(ctx, tmpLogger, pgxConn, os.Args[2], config.GetLimit(), config.GetLogger().Format)

				return nil
			}); err != nil {
				conn.Close()
				tmpLogger.Fatal("Failed to acquire pgx conn for migration", zap.Error(err))
			}
			conn.Close()
			return
		case "check":
			// Parse any command line args to look up runtime path.
			// Use full config structure even if not all of its options are available in this command.
			config := server.NewConfig(tmpLogger)
			var runtimePath string
			flags := flag.NewFlagSet("check", flag.ExitOnError)
			flags.StringVar(&runtimePath, "runtime.path", filepath.Join(config.GetDataDir(), "modules"), "Path for the server to scan for Lua and Go library files.")
			if err := flags.Parse(os.Args[2:]); err != nil {
				tmpLogger.Fatal("Could not parse check flags.")
			}
			config.GetRuntime().Path = runtimePath

			if err := server.CheckRuntime(tmpLogger, config, version); err != nil {
				// Errors are already logged in the function above.
				os.Exit(1)
			}
			return
		case "healthcheck":
			port := "7350"
			if len(os.Args) > 2 {
				port = os.Args[2]
			}

			resp, err := http.Get("http://localhost:" + port)
			if err != nil || resp.StatusCode != http.StatusOK {
				tmpLogger.Fatal("healthcheck failed")
			}
			tmpLogger.Info("healthcheck ok")
			return
		}
	}

	// 加载配置
	config = server.ParseArgs(tmpLogger, os.Args)

	isrungraceful := false
	if sysruntime.GOOS != "windows" {
		isrungraceful = true
	}
	if !isrungraceful {
		server_init(semver, tmpLogger, nil)
		server_start(semver, nil, nil, nil, nil)

		// Respect OS stop signals.
		c := make(chan os.Signal, 2)
		signal.Notify(c, os.Interrupt, syscall.SIGINT, syscall.SIGTERM)

		// Wait for a termination signal.
		<-c

		server.HandleShutdown(ctx, logger, matchRegistry, config.GetShutdownGraceSec(), runtime.Shutdown(), c, false)
		server_stop(false)

	} else {
		var (
			httpLn        net.Listener
			grpcLn        net.Listener
			consoleLn     net.Listener
			consoleGrpcLn net.Listener
			metricsLn     net.Listener
		)
		grace := graceful.New(graceful.WithStopTimeout(time.Duration(config.GetShutdownGraceSec()) * time.Second))
		addr := fmt.Sprintf("%v:%d", config.GetSocket().Address, config.GetSocket().Port)
		grpcAddr := fmt.Sprintf("%v:%d", config.GetSocket().Address, config.GetSocket().Port-1)
		consoleaddr := fmt.Sprintf("%v:%d", config.GetConsole().Address, config.GetConsole().Port)
		consoleGrpcAddr := fmt.Sprintf("%v:%d", config.GetConsole().Address, config.GetConsole().Port-3)
		if config.GetMetrics().PrometheusPort > 0 {
			metricsAddr := fmt.Sprintf(":%d", config.GetMetrics().PrometheusPort)
			grace.RegisterService(graceful.NewAddress(metricsAddr, "tcp"), func(ln net.Listener, c chan struct{}, isreload bool) error { metricsLn = ln; return nil }, func(isreload bool) error { return nil })
		}
		grace.RegisterService(graceful.NewAddress(consoleaddr, "tcp"), func(ln net.Listener, c chan struct{}, isreload bool) error { consoleLn = ln; return nil }, func(isreload bool) error { return nil })
		grace.RegisterService(graceful.NewAddress(consoleGrpcAddr, "tcp"), func(ln net.Listener, c chan struct{}, isreload bool) error { consoleGrpcLn = ln; return nil }, func(isreload bool) error { return nil })
		grace.RegisterService(graceful.NewAddress(grpcAddr, "tcp"), func(ln net.Listener, c chan struct{}, isreload bool) error { grpcLn = ln; return nil }, func(isreload bool) error { return nil })
		grace.RegisterService(graceful.NewAddress(addr, "tcp"), func(ln net.Listener, c chan struct{}, isreload bool) error {
			tmpLogger.Info("grace start", zap.Bool("isreload", isreload))
			httpLn = ln
			server_init(semver, tmpLogger, metricsLn)
			server_start(semver, httpLn, grpcLn, consoleLn, consoleGrpcLn)
			c <- struct{}{}
			return nil
		}, func(isreload bool) error {
			tmpLogger.Info("grace shutdown", zap.Bool("isreload", isreload))
			c := make(chan os.Signal, 1) // nothing do do
			server.HandleShutdown(ctx, logger, matchRegistry, config.GetShutdownGraceSec(), runtime.Shutdown(), c, isreload)
			server_stop(isreload)
			return nil
		})
		err := grace.Run()
		if err != nil {
			fmt.Println(err)
		}
	}
}

var (
	ctx                   context.Context
	ctxCancelFn           context.CancelFunc
	apiServer             *server.ApiServer
	consoleServer         *server.ConsoleServer
	matchmaker            server.Matchmaker
	partyRegistry         server.PartyRegistry
	leaderboardScheduler  server.LeaderboardScheduler
	googleRefundScheduler server.GoogleRefundScheduler
	tracker               server.Tracker
	statusRegistry        server.StatusRegistry
	sessionCache          server.SessionCache
	sessionRegistry       server.SessionRegistry
	metrics               server.Metrics
	loginAttemptCache     server.LoginAttemptCache
	consoleSessionCache   server.SessionCache
	leaderboardCache      server.LeaderboardCache
	pipeline              *server.Pipeline
	statusHandler         server.StatusHandler
	startupLogger         *zap.Logger
	logger                *zap.Logger
	runtimeInfo           *server.RuntimeInfo
	runtime               *server.Runtime
	router                server.MessageRouter
	matchRegistry         server.MatchRegistry
	streamManager         server.StreamManager
	leaderboardRankCache  server.LeaderboardRankCache
	fmCallbackHandler     kernelruntime.FmCallbackHandler
	storageIndex          server.StorageIndex
	db                    *sql.DB
	configWarnings        map[string]string
	config                server.Config
	socialClient          *social.Client
	accessLog             *accesslog.AccessLog
	cookie                string
	traceShutdownFn       func()
)

func server_init(semver string, tmpLogger *zap.Logger, metricsLn net.Listener) {
	logger, startupLogger = server.SetupLogging(tmpLogger, config)
	configWarnings = server.ValidateConfig(logger, config)

	startupLogger.Info("Nakama starting")
	startupLogger.Info("Node", zap.String("name", config.GetName()), zap.String("version", semver), zap.String("runtime", sysruntime.Version()), zap.Int("cpu", sysruntime.NumCPU()), zap.Int("proc", sysruntime.GOMAXPROCS(0)))
	startupLogger.Info("Data directory", zap.String("path", config.GetDataDir()))

	if !config.GetDatabase().Disable {
		redactedAddresses := make([]string, 0, 1)
		for _, address := range config.GetDatabase().Addresses {
			rawURL := fmt.Sprintf("postgres://%s", address)
			parsedURL, err := url.Parse(rawURL)
			if err != nil {
				logger.Fatal("Bad connection URL", zap.Error(err))
			}
			redactedAddresses = append(redactedAddresses, strings.TrimPrefix(parsedURL.Redacted(), "postgres://"))
		}
		startupLogger.Info("Database connections", zap.Strings("dsns", redactedAddresses))

		db = server.DbConnect(ctx, startupLogger, config, false)

		// Check migration status and fail fast if the schema has diverged.
		conn, err := db.Conn(context.Background())
		if err != nil {
			logger.Fatal("Failed to acquire db conn for migration check", zap.Error(err))
		}

		if err = conn.Raw(func(driverConn any) error {
			pgxConn := driverConn.(*stdlib.Conn).Conn()
			migrate.Check(ctx, startupLogger, pgxConn)
			return nil
		}); err != nil {
			conn.Close()
			logger.Fatal("Failed to acquire pgx conn for migration check", zap.Error(err))
		}
		conn.Close()
	} else {
		startupLogger.Warn("Database is disabled")
	}

	// Access to social provider integrations.
	socialClient = social.NewClient(logger, 5*time.Second, config.GetGoogleAuth().OAuthConfig)

	// Access log
	accessLog = server.InitAccessLog(config)

	// Start up server components.
	metrics = server.NewLocalMetrics(logger, startupLogger, db, config, metricsLn)
	sessionRegistry = server.NewLocalSessionRegistry(metrics)
	sessionCache = server.NewLocalSessionCache(config.GetSession().TokenExpirySec, config.GetSession().RefreshTokenExpirySec)
	consoleSessionCache = server.NewLocalSessionCache(config.GetConsole().TokenExpirySec, 0)
	loginAttemptCache = server.NewLocalLoginAttemptCache()
	statusRegistry = server.NewLocalStatusRegistry(logger, config, sessionRegistry, jsonpbMarshaler)
	tracker = server.StartLocalTracker(logger, config, sessionRegistry, statusRegistry, metrics, jsonpbMarshaler)
	router = server.NewLocalMessageRouter(sessionRegistry, tracker, jsonpbMarshaler)
	leaderboardCache = server.NewLocalLeaderboardCache(ctx, logger, startupLogger, db)
	leaderboardRankCache = server.NewLocalLeaderboardRankCache(ctx, startupLogger, db, config.GetLeaderboard(), leaderboardCache)
	leaderboardScheduler = server.NewLocalLeaderboardScheduler(logger, db, config, leaderboardCache, leaderboardRankCache)
	googleRefundScheduler = server.NewGoogleRefundScheduler(logger, db, config)
	matchRegistry = server.NewLocalMatchRegistry(logger, startupLogger, config, sessionRegistry, tracker, router, metrics, config.GetName())
	tracker.SetMatchJoinListener(matchRegistry.Join)
	tracker.SetMatchLeaveListener(matchRegistry.Leave)
	streamManager = server.NewLocalStreamManager(config, sessionRegistry, tracker)
	fmCallbackHandler = server.NewLocalFmCallbackHandler(config)

	var err error
	storageIndex, err = server.NewLocalStorageIndex(logger, db, config.GetStorage(), metrics)
	if err != nil {
		logger.Fatal("Failed to initialize storage index", zap.Error(err))
	}
	runtime, runtimeInfo, err = server.NewRuntime(ctx, logger, startupLogger, db, jsonpbMarshaler, jsonpbUnmarshaler, config, version, socialClient, leaderboardCache, leaderboardRankCache, leaderboardScheduler, sessionRegistry, sessionCache, statusRegistry, matchRegistry, tracker, metrics, streamManager, router, storageIndex, fmCallbackHandler, accessLog)
	if err != nil {
		startupLogger.Fatal("Failed initializing runtime modules", zap.Error(err))
	}
	matchmaker = server.NewLocalMatchmaker(logger, startupLogger, config, router, metrics, runtime)
	partyRegistry = server.NewLocalPartyRegistry(logger, config, matchmaker, tracker, streamManager, router, config.GetName())
	tracker.SetPartyJoinListener(partyRegistry.Join)
	tracker.SetPartyLeaveListener(partyRegistry.Leave)

	storageIndex.RegisterFilters(runtime)
	go func() {
		if err = storageIndex.Load(ctx); err != nil {
			logger.Error("Failed to load storage index entries from database", zap.Error(err))
		}
	}()

	leaderboardScheduler.Start(runtime)
	googleRefundScheduler.Start(runtime)

	pipeline = server.NewPipeline(logger, config, db, jsonpbMarshaler, jsonpbUnmarshaler, sessionRegistry, statusRegistry, matchRegistry, partyRegistry, matchmaker, tracker, router, runtime, accessLog, metrics)
	statusHandler = server.NewLocalStatusHandler(logger, sessionRegistry, matchRegistry, tracker, metrics, config.GetName())

	telemetryEnabled := len(os.Getenv("NAKAMA_TELEMETRY")) < 1
	console.UIFS.Nt = !telemetryEnabled
	cookie = newOrLoadCookie(telemetryEnabled, config)
	if telemetryEnabled {
		const telemetryKey = "YU1bIKUhjQA9WC0O6ouIRIWTaPlJ5kFs"
		_ = se.Start(telemetryKey, cookie, semver, "nakama")
		defer func() {
			_ = se.End(telemetryKey, cookie)
		}()
	}

	traceShutdownFn = server.InitTracer(config, startupLogger)
}

func server_start(semver string, httpLn net.Listener, grpcLn net.Listener, consoleLn net.Listener, consoleGrpcLn net.Listener) {
	apiServer = server.StartApiServer(config.GetDisableNakamaHandler(), httpLn, grpcLn, logger, startupLogger, db, jsonpbMarshaler, jsonpbUnmarshaler, config, version, socialClient, storageIndex, leaderboardCache, leaderboardRankCache, sessionRegistry, sessionCache, statusRegistry, matchRegistry, matchmaker, tracker, router, streamManager, metrics, pipeline, runtime, accessLog)
	consoleServer = server.StartConsoleServer(consoleLn, consoleGrpcLn, logger, startupLogger, db, config, tracker, router, streamManager, metrics, sessionRegistry, sessionCache, consoleSessionCache, loginAttemptCache, statusRegistry, statusHandler, runtimeInfo, matchRegistry, configWarnings, semver, leaderboardCache, leaderboardRankCache, leaderboardScheduler, storageIndex, apiServer, runtime, cookie)
	startupLogger.Info("Startup done")
}

func server_stop(isreload bool) {
	// Signal cancellation to the global runtime context.
	ctxCancelFn()

	// Gracefully stop remaining server components.
	apiServer.Stop()
	consoleServer.Stop()
	matchmaker.Stop()
	leaderboardScheduler.Stop()
	googleRefundScheduler.Stop()
	tracker.Stop()
	statusRegistry.Stop()
	sessionCache.Stop()
	sessionRegistry.Stop()
	metrics.Stop(logger)
	loginAttemptCache.Stop()
	if traceShutdownFn != nil {
		traceShutdownFn()
	}
	if isreload {
		startupLogger.Info("Reload complete")
	} else {
		startupLogger.Info("Stop complete")
	}
}

// Help improve Nakama by sending anonymous usage statistics.
//
// You can disable the telemetry completely before server start by setting the
// environment variable "NAKAMA_TELEMETRY" - i.e. NAKAMA_TELEMETRY=0 nakama
//
// These properties are collected:
// * A unique UUID v4 random identifier which is generated.
// * Version of Nakama being used which includes build metadata.
// * Amount of time the server ran for.
//
// This information is sent via Segment which allows the Nakama team to
// analyze usage patterns and errors in order to help improve the server.
func newOrLoadCookie(enabled bool, config server.Config) string {
	if !enabled {
		return ""
	}
	filePath := filepath.FromSlash(config.GetDataDir() + "/" + cookieFilename)
	b, err := os.ReadFile(filePath)
	cookie := uuid.FromBytesOrNil(b)
	if err != nil || cookie == uuid.Nil {
		cookie = uuid.Must(uuid.NewV4())
		_ = os.WriteFile(filePath, cookie.Bytes(), 0o644)
	}
	return cookie.String()
}
