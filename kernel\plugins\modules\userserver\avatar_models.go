package main

type Avatar struct {
	AvatarId   int64 `json:"avatar_id,omitempty"`
	ExpireTime int64 `json:"expire_time,omitempty"`
}

type RespAvatarList struct {
	Code    int64  `json:"code"`
	Message string `json:"message,omitempty"`
	Data    string `json:"data,omitempty"` // pb string
}

type ReqAvatarOpt struct {
	UserId string  `json:"user_id,omitempty"`
	Avatar *Avatar `json:"avatar,omitempty"`
}

type RespAvatarQuery struct {
	Code    int64   `json:"code"`
	Message string  `json:"message,omitempty"`
	Data    *Avatar `json:"data,omitempty"`
}

type RespAvatarUpdate struct {
	Code    int64   `json:"code"`
	Message string  `json:"message,omitempty"`
	Data    *Avatar `json:"data,omitempty"`
}
