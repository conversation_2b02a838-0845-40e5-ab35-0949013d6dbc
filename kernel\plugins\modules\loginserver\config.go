package main

import (
	"kernel/plugins/models"
)

type CustomConfig struct {
	Config    *LoginServerConfig      `json:"login_server_config,omitempty" yaml:"login_server_config,omitempty"`
	AccessLog *models.AccessLogConfig `json:"access_log,omitempty" yaml:"access_log,omitempty"`
	Logger    *models.LoggerConfig    `json:"logger,omitempty" yaml:"logger,omitempty"`
}

type LoginServerConfig struct {
	NodeConfig        *models.NodeConfig `json:"node_config,omitempty" yaml:"node_config,omitempty"`
	WatchSvcGroupList []string           `json:"watch_svc_group_list,omitempty" yaml:"watch_svc_group_list,omitempty"`
	EmailCfg          *EmailConfig       `json:"email_cfg,omitempty" yaml:"email_cfg,omitempty"`
	AggCfg            *AggConfig         `json:"agg_cfg,omitempty" yaml:"agg_cfg,omitempty"`
}

type EmailConfig struct {
	SmtpHost   string `json:"smtp_host,omitempty" yaml:"smtp_host,omitempty"`
	SmtpPort   string `json:"smtp_port,omitempty" yaml:"smtp_port,omitempty"`
	FromEmail  string `json:"from_email,omitempty" yaml:"from_email,omitempty"`
	FromPasswd string `json:"from_passwd,omitempty" yaml:"from_passwd,omitempty"`
}

// 聚合配置
type AggConfig struct {
	Url         string `json:"url,omitempty" yaml:"url,omitempty"`
	TenementId  string `json:"tenement_id,omitempty" yaml:"tenement_id,omitempty"`   // 租户id
	TenementKey string `json:"tenement_key,omitempty" yaml:"tenement_key,omitempty"` // 租户key
}
