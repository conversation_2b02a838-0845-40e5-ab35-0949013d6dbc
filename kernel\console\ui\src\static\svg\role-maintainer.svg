<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 24.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_extend "http://ns.adobe.com/Extensibility/1.0/">
	<!ENTITY ns_ai "http://ns.adobe.com/AdobeIllustrator/10.0/">
	<!ENTITY ns_graphs "http://ns.adobe.com/Graphs/1.0/">
	<!ENTITY ns_vars "http://ns.adobe.com/Variables/1.0/">
	<!ENTITY ns_imrep "http://ns.adobe.com/ImageReplacement/1.0/">
	<!ENTITY ns_sfw "http://ns.adobe.com/SaveForWeb/1.0/">
	<!ENTITY ns_custom "http://ns.adobe.com/GenericCustomNamespace/1.0/">
	<!ENTITY ns_adobe_xpath "http://ns.adobe.com/XPath/1.0/">
]>
<svg version="1.1" id="Layer_1" xmlns:x="&ns_extend;" xmlns:i="&ns_ai;" xmlns:graph="&ns_graphs;"
	 xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="30px" height="30px"
	 viewBox="0 0 30 30" style="enable-background:new 0 0 30 30;" xml:space="preserve">
<style type="text/css">
	.st0{fill-rule:evenodd;clip-rule:evenodd;fill:#7668ED;}
	.st1{fill:#FFFFFF;}
</style>
<metadata>
	<sfw  xmlns="&ns_sfw;">
		<slices></slices>
		<sliceSourceBounds  bottomLeftOrigin="true" height="30" width="30" x="0" y="-30"></sliceSourceBounds>
	</sfw>
</metadata>
<g>
	<circle class="st0" cx="15" cy="15" r="15"/>
	<g>
		<path class="st1" d="M22.5,7.7l-0.2-0.2c-0.8-0.8-2.1-0.8-2.9,0l-1.1,1.1l-2-1.6l-0.9-0.8L14.7,7l-4.2,5.1l0.7,0.7l4.2-3.4l1,1.2
			l-1.2,1.2l-5.7,5.7l3.1,3.1l5.7-5.7l1.4-1.4l2.9-2.9C23.3,9.8,23.3,8.5,22.5,7.7z"/>
		<polygon class="st1" points="6.9,23.1 11.2,21.8 8.2,18.8 		"/>
	</g>
</g>
</svg>
