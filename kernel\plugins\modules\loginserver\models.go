package main

import (
	"time"

	"kernel/plugins/models"
)

type AccountEmail struct {
	Email    string            `json:"email"`
	Password string            `json:"password"`
	Vars     map[string]string `json:"vars,omitempty"` // nick, clientip, uin
}

type AccountEmailResp struct {
	Code models.ErrorCode `json:"code"`
	Msg  string           `json:"msg,omitempty"`
}

// 查询用户登录记录请求
type ReqQueryLoginLogs struct {
	UserId    string `json:"user_id,omitempty"`
	Uin       int32  `json:"uin,omitempty"`
	PageSize  int32  `json:"page_size,omitempty"`
	PageNum   int32  `json:"page_num,omitempty"`
	StartTime int64  `json:"start_time,omitempty"` // 开始时间
	EndTime   int64  `json:"end_time,omitempty"`   // 结束时间
	Sort      string `json:"sort,omitempty"`       // 排序方式 asc, desc
}

type LoginLog struct {
	UserId    string    `json:"user_id,omitempty"`
	Uin       int32     `json:"uin,omitempty"`
	LoginTime time.Time `json:"login_time,omitempty"`
	IpAddress string    `json:"ip_address,omitempty"`
	Device    string    `json:"device,omitempty"`
	Metadata  string    `json:"metadata,omitempty"`
}

// 查询用户登录记录响应
type RespQueryLoginLogs struct {
	Code  models.ErrorCode `json:"code"`
	Msg   string           `json:"message,omitempty"`
	Total int32            `json:"total,omitempty"`
	Logs  []*LoginLog      `json:"logs,omitempty"`
}

type ReqAuthenticateEmail struct {
	Account  *AccountEmail `json:"account,omitempty"`
	Username string        `json:"username,omitempty"`
	Code     string        `json:"code,omitempty"`
}

type RespAuthenticateEmail struct {
	Code         models.ErrorCode `json:"code,omitempty"`
	Msg          string           `json:"message,omitempty"`
	Created      bool             `json:"created,omitempty"`
	Token        string           `json:"token,omitempty"`
	RefreshToken string           `json:"refresh_token,omitempty"`
}

// 聚合验证返回
type RespAggAuth struct {
	Ret  int          `json:"tars_ret"`
	Data *AggAuthData `json:"data,omitempty"`
}

type AggAuthData struct {
	UserId                 string `json:"userId"`                           // 聚合通行证账号ID(即是accountNum)
	ThirdUserId            string `json:"thirdUserId,omitempty"`            // 第三方用户ID
	MergeChannelId         string `json:"mergeChannelId,omitempty"`         // 聚合渠道ID
	AppId                  string `json:"appId,omitempty"`                  // 第三方appId
	ThirdUnionId           string `json:"thirdUnionId,omitempty"`           // 第三方联合ID
	Phone                  int    `json:"phone,omitempty"`                  // 手机号
	RealAuthenticationType int    `json:"realAuthenticationType,omitempty"` // 实名开关，1:关闭实名认证 2：开启实名认证
	IdentityStatus         int    `json:"identityStatus,omitempty"`         // 实名状态，-1：渠道未提供实名认证,1：未实名,2：成年人 ,3：未成年人
	Age                    int    `json:"age,omitempty"`                    // 年龄，-1：未获取到年龄
	Extension              string `json:"extension,omitempty"`              // 扩展字段
	SecurePhone            int    `json:"securePhone,omitempty"`            // 验证手机号
	CaptchaFlag            int    `json:"captchaFlag,omitempty"`            // 聚合侧是否人机:0-否;1-是(迷你世界才返回)
	LoginType              string `json:"loginType,omitempty"`              // 登录类型(channel-渠道登录;self-自定义账号登录;phone_code-手机号验证码登录;mini_pwd-迷你号密码登录;mini_question-迷你号问题密码登录;one_key-手机号一键登录)(迷你世界才返回)
	AntiAddictionResult    int    `json:"antiAddictionResult,omitempty"`    // 防沉迷结果：1-防沉迷通过;2-被防沉迷;
}
