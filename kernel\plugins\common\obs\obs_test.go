package obs

import (
	"fmt"
	"io/ioutil"
	"testing"
)

func TestUCloud(t *testing.T) {
	osfile := "os_ucloud.go"
	oskey := "sourcefile"

	osConfig := &OSConfig{
		AccessKey: "",
		Secretkey: "", // 不许入库
		Endpoint:  "hk.ufileos.com",
		Bucket:    "prod-env-cloud-maps-hk-ucb",
	}

	osUcloud := NewOSClient("ucloud", osConfig)
	err := osUcloud.PutFile(osfile, oskey)
	if err != nil {
		t.Error(err)
	}

	err = osUcloud.GetFile(oskey, "ucloud.dat")
	if err != nil {
		t.Error(err)
	}

	obsMd5 := osUcloud.GetObjectMD5(oskey)
	fileMd5, err := getFileMd5("ucloud.dat")
	if err != nil {
		t.Error(err)
	}

	if obsMd5 != fileMd5 {
		t.Error("obsMd5 != fileMd5")
	}

	meta := make(map[string]string)
	meta[MapMD5MetaKey] = obsMd5
	meta[MapIDMetaKey] = fmt.Sprintf("%v", "123456")
	err = osUcloud.PutObjectWithMetadata("hello", []byte("hello ucloud"), meta)
	if err != nil {
		t.Error(err)
	}

	ucloudMeata, err := osUcloud.GetObjectMetadata("hello")
	if err != nil {
		t.Error(err)
	}

	ucloudMetaMd5 := ucloudMeata[MapMD5MetaKey]
	if ucloudMetaMd5 != obsMd5 {
		t.Error(err)
	}

	ret, err := osUcloud.GetObject("hello")
	if err != nil {
		t.Error(err)
	}

	if string(ret) != "hello ucloud" {
		t.Error("ucloud get object fail")
	}
}

func TestHuawei(t *testing.T) {
	osfile := "os_huawei.go"
	oskey := "sourcefile"

	osConfig := &OSConfig{
		AccessKey: "",
		Secretkey: "", // 不许入库
		Endpoint:  "https://obs.cn-east-3.myhuaweicloud.com",
		Bucket:    "prod-env-cloud-maps-obs",
	}

	osHuawei := NewOSClient("huawei", osConfig)
	err := osHuawei.PutFile(osfile, oskey)
	if err != nil {
		t.Error(err)
	}

	objects, err := osHuawei.ListObjects("cloud_map/3402614168007/1000069575_1")
	if err != nil {
		t.Error(err)
	}
	fmt.Printf("list objects: %+v \n", objects)

	err = osHuawei.GetFile(oskey, "huawei.dat")
	if err != nil {
		t.Error(err)
	}

	obsMd5 := osHuawei.GetObjectMD5(oskey)
	fmt.Println("huawei: get object md5 = ", obsMd5)

	fileMd5, err := getFileMd5("huawei.dat")
	if err != nil {
		t.Error(err)
	}

	if obsMd5 != fileMd5 {
		t.Error("obsMd5 != fileMd5")
	}

	err = osHuawei.PutObject("hello", []byte("hello huawei"))
	if err != nil {
		t.Error(err)
	}

	ret, err := osHuawei.GetObject("hello")
	if err != nil {
		t.Error(err)
	}

	if string(ret) != "hello huawei" {
		t.Error("huawei get object fail")
	}

	downloadURL := osHuawei.GetObjectDownloadURL(oskey)
	t.Log(downloadURL)
}

func TestAliyun(t *testing.T) {
	osfile := "os_aliyun.go"
	oskey := "sourcefile"

	osConfig := &OSConfig{
		AccessKey: "",
		Secretkey: "", // 不许入库
		Endpoint:  "https://oss-eu-central-1.aliyuncs.com",
		Bucket:    "de-ali-cloud-maps",
	}

	osAliyun := NewOSClient("aliyun", osConfig)
	err := osAliyun.PutFile(osfile, oskey)
	if err != nil {
		t.Error(err)
	}

	err = osAliyun.GetFile(oskey, "aliyun.dat")
	if err != nil {
		t.Error(err)
	}

	obsMd5 := osAliyun.GetObjectMD5(oskey)
	fmt.Println("aliyun: get object md5 = ", obsMd5)

	fileMd5, err := getFileMd5("aliyun.dat")
	if err != nil {
		t.Error(err)
	}

	if obsMd5 != fileMd5 {
		t.Error("obsMd5 != fileMd5")
	}

	downloadURL := osAliyun.GetObjectDownloadURL(oskey)
	t.Log(downloadURL)

	err = osAliyun.PutObject("hello", []byte("hello aliyun"))
	if err != nil {
		t.Error(err)
	}

	ret, err := osAliyun.GetObject("hello")
	if err != nil {
		t.Error(err)
	}

	objects, err := osAliyun.ListObjects("/")
	if err != nil {
		t.Error(err)
	}
	fmt.Printf("list objects: %+v \n", objects)

	err = osAliyun.DelObject("hello")
	if err != nil {
		t.Error(err)
	}

	if string(ret) != "hello aliyun" {
		t.Error("aliyun get object fail")
	}

	metadata, err := osAliyun.GetObjectMetadata(oskey)
	if err != nil {
		t.Error(err)
	}
	t.Log(metadata)

}

func TestUCloudSetMeta(t *testing.T) {
	osConfig := &OSConfig{
		AccessKey: "",
		Secretkey: "", // 不许入库
		Endpoint:  "cn-wlcb.ufileos.com",
		Bucket:    "prod-env-cloud-maps-us3-ucb",
	}
	osUcloud := NewOSClient("ucloud", osConfig)
	err := SetMeta(osUcloud, t)
	if err != nil {
		t.Error(err)
	}
}

func TestAliSetMeta(t *testing.T) {
	osConfig := &OSConfig{
		AccessKey: "",
		Secretkey: "", // 不许入库
		Endpoint:  "https://oss-eu-central-1.aliyuncs.com",
		Bucket:    "de-ali-cloud-maps",
	}

	osAliyun := NewOSClient("aliyun", osConfig)
	err := SetMeta(osAliyun, t)
	if err != nil {
		t.Error(err)
	}
}

func TestHuaweiSetMeta(t *testing.T) {
	osConfig := &OSConfig{
		AccessKey: "",
		Secretkey: "", // 不许入库
		Endpoint:  "https://obs.cn-east-3.myhuaweicloud.com",
		Bucket:    "prod-env-cloud-maps-obs",
	}

	osHuawei := NewOSClient("huawei", osConfig)
	err := SetMeta(osHuawei, t)
	if err != nil {
		t.Error(err)
	}
}

// 修改元数据测试是否正确
func SetMeta(osClient IOSClient, t *testing.T) error {
	osfile := "os_aliyun.go"
	oskey := "sourcefile"

	fileData, err := ioutil.ReadFile(osfile)
	if err != nil {
		return err
	}

	//添加测试对象
	meta := map[string]string{"key1": "value1", "key2": "value2"}
	err = osClient.PutObjectWithMetadata(oskey, fileData, meta)
	if err != nil {
		return err
	}

	//获取当前meta
	meta2, err := osClient.GetObjectMetadata(oskey)
	if err != nil {
		return err
	}

	//修改meta
	meta2["key3"] = "value3"

	//设置meta
	err = osClient.SetObjectMetadata(oskey, meta2)
	if err != nil {
		return err
	}

	//再次获取当前meta
	meta3, err := osClient.GetObjectMetadata(oskey)
	if err != nil {
		return err
	}

	//检查自定义的元数据是否存在
	want := map[string]string{"key1": "value1", "key2": "value2", "key3": "value3"}
	for k, v := range want {
		v3, ok := meta3[k]
		if ok {
			if v3 != v {
				t.Errorf("k:%v, want:%v, got:%v", k, v, v3)
			}
		} else {
			t.Errorf("k:%v, want:%v, got:nil", k, v)
		}
	}

	md5 := osClient.GetObjectMD5(oskey)
	t.Log(md5)

	//删除测试对象
	osClient.DelObject(oskey)

	return nil
}

func TestTencent(t *testing.T) {
	osfile := "os_ucloud.go"
	oskey := "test345"

	osConfig := &OSConfig{
		// AccessKey: "AKID74nyxhDUYODwfGeokSyIqAG9oFSREpGV",
		// Secretkey: "A9wWtytb6RYU8f2GKDvPfjJhsGWjiU3O", // 不许入库
		AccessKey: "AKIDQrK0e6Zu58yqbKskdXPF6FYp2DZvDcK5",
		Secretkey: "0qbTJzXxJbjtzwmgjlm6UqCRVZVHpIdv", // 不许入库
		Endpoint:  "http://cos.sa-saopaulo.myqcloud.com",
		Bucket:    "bx-tx-cloud-maps-1252226338",
	}

	// osConfig := &OSConfig{
	// 	AccessKey: "AKID1xnpq3QjzPvWyGSpJmFxxw1CTvIcuDb0",
	// 	Secretkey: "fgdXN9yvtgKFF5xkLsSGKYwY9kJnMuQt", // 不许入库
	// 	Endpoint:  "https://cos.ap-nanjing.myqcloud.com",
	// 	Bucket:    "prod-cn-tx-cloud-maps-1252226338",
	// }

	// osConfig := &OSConfig{
	// 	AccessKey: "AKID74nyxhDUYODwfGeokSyIqAG9oFSREpGV",
	// 	Secretkey: "A9wWtytb6RYU8f2GKDvPfjJhsGWjiU3O", // 不许入库
	// 	Endpoint:  "http://cos.eu-frankfurt.myqcloud.com",
	// 	Bucket:    "de-tx-cloud-maps-1252226338",
	// }

	osTencent := NewOSClient("tencent", osConfig)
	err := osTencent.PutFile(osfile, oskey)
	if err != nil {
		t.Error(err)
	}

	err = osTencent.GetFile(oskey, "ucloud.dat")
	if err != nil {
		t.Error(err)
	}

	obsMd5 := osTencent.GetObjectMD5(oskey)
	fileMd5, err := getFileMd5("ucloud.dat")
	if err != nil {
		t.Error(err)
	}

	if obsMd5 != fileMd5 {
		t.Error("obsMd5 != fileMd5")
	}

	meta := make(map[string]string)
	meta[MapMD5MetaKey] = fileMd5
	meta[MapIDMetaKey] = fmt.Sprintf("%v", "abc123")
	err = osTencent.PutObjectWithMetadata("hello", []byte("hello ucloud"), meta)
	if err != nil {
		t.Error(err)
	}

	err = osTencent.SetObjectMetadata("hello", meta)
	if err != nil {
		t.Error(err)
	}

	tencentMeata, err := osTencent.GetObjectMetadata("hello")
	if err != nil {
		t.Error(err)
	}

	svcFileMd5, _ := tencentMeata[MapMD5MetaKey]
	if svcFileMd5 != fileMd5 {
		t.Error(err)
	}

	_, err = osTencent.GetObject(oskey)
	if err != nil {
		t.Error(err)
	}

}

func TestS3(t *testing.T) {
	osfile := "os_ucloud.go"
	oskey := "test345"

	osConfig := &OSConfig{
		// AccessKey: "AKID74nyxhDUYODwfGeokSyIqAG9oFSREpGV",
		// Secretkey: "A9wWtytb6RYU8f2GKDvPfjJhsGWjiU3O", // 不许入库
		AccessKey: "AKLTYzBjNTFiN2JkZTNlNGJjMmFhYWY3NmJkNDkyN2Q3MGI",
		Secretkey: "WkRNd1kyWmtNRFptTTJVd05ERm1ORGcyTXpoak1HRmtaV016TlRabE16TQ==", // 不许入库
		Endpoint:  "https://tos-s3-cn-beijing.volces.com",
		Bucket:    "prod-cn-hs-cloud-maps-bj",
	}

	// osConfig := &OSConfig{
	// 	AccessKey: "AKID1xnpq3QjzPvWyGSpJmFxxw1CTvIcuDb0",
	// 	Secretkey: "fgdXN9yvtgKFF5xkLsSGKYwY9kJnMuQt", // 不许入库
	// 	Endpoint:  "https://cos.ap-nanjing.myqcloud.com",
	// 	Bucket:    "prod-cn-tx-cloud-maps-1252226338",
	// }

	// osConfig := &OSConfig{
	// 	AccessKey: "AKID74nyxhDUYODwfGeokSyIqAG9oFSREpGV",
	// 	Secretkey: "A9wWtytb6RYU8f2GKDvPfjJhsGWjiU3O", // 不许入库
	// 	Endpoint:  "http://cos.eu-frankfurt.myqcloud.com",
	// 	Bucket:    "de-tx-cloud-maps-1252226338",
	// }

	osHuosan := NewOSClient("s3_huosan", osConfig)
	err := osHuosan.PutFile(osfile, oskey)
	if err != nil {
		t.Error(err)
	}

	err = osHuosan.GetFile(oskey, "ucloud.dat")
	if err != nil {
		t.Error(err)
	}

	obsMd5 := osHuosan.GetObjectMD5(oskey)
	fileMd5, err := getFileMd5("ucloud.dat")
	if err != nil {
		t.Error(err)
	}

	if obsMd5 != fileMd5 {
		t.Error("obsMd5 != fileMd5")
	}

	meta := make(map[string]string)
	meta[MapMD5MetaKey] = fileMd5
	meta[MapIDMetaKey] = fmt.Sprintf("%v", "abc123")
	err = osHuosan.PutObjectWithMetadata("hello", []byte("hello ucloud"), meta)
	if err != nil {
		t.Error(err)
	}

	err = osHuosan.SetObjectMetadata("hello", meta)
	if err != nil {
		t.Error(err)
	}

	tencentMeata, err := osHuosan.GetObjectMetadata("hello")
	if err != nil {
		t.Error(err)
	}

	svcFileMd5, _ := tencentMeata[MapMD5MetaKey]
	if svcFileMd5 != fileMd5 {
		t.Error(err)
	}

	_, err = osHuosan.GetObject(oskey)
	if err != nil {
		t.Error(err)
	}

}
