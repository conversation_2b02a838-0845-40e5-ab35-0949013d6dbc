package main

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/gorilla/websocket"
	"io"
	"log"
	"net/http"
	"net/url"
)

const (
	nakamaURL     = "http://127.0.0.1:7350/v2"
	serverKey     = "defaultkey:defaultkey"
	email         = "<EMAIL>"
	password      = "********"
	wsURLTemplate = "ws://127.0.0.1:7350/ws?token=%s"
)

func loginWithEmail() (string, error) {
	requestBody, err := json.Marshal(map[string]interface{}{
		"email":    email,
		"password": password,
		"create":   true,
	})
	if err != nil {
		return "", fmt.Errorf("failed to marshal request body: %v", err)
	}

	req, err := http.NewRequest("POST", nakamaURL+"/account/authenticate/email", bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Basic "+base64.StdEncoding.EncodeToString([]byte(serverKey)))

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("login failed with status code %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Token string `json:"token"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to parse response: %v", err)
	}

	return response.Token, nil
}

func httpRpc(rpcid string, payload []byte) (string, error) {
	body, err := json.Marshal(string(payload))
	if err != nil {
		return "", fmt.Errorf("failed to marshal request body: %v", err)
	}
	tmpurl := fmt.Sprintf("http://192.168.192.120:7350/v2/rpc/%s", rpcid)
	req, err := http.NewRequest("POST", tmpurl, bytes.NewBuffer(body))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Basic "+base64.StdEncoding.EncodeToString([]byte("defaulthttpkey:defaulthttpkey")))

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("rpc failed with status code %d: %s", resp.StatusCode, string(body))
	}

	body, err = io.ReadAll(resp.Body)
	return string(body), nil
}

func connectWebSocket(token string, recvfunc func(messageType int, p []byte)) (*websocket.Conn, error) {
	wsURL := fmt.Sprintf(wsURLTemplate, token)
	u, err := url.Parse(wsURL)
	if err != nil {
		return nil, fmt.Errorf("invalid WebSocket URL: %v", err)
	}

	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to WebSocket: %v", err)
	}

	fmt.Println("Connected to WebSocket")

	conn.SetPingHandler(func(appData string) error {
		conn.WriteMessage(websocket.PongMessage, []byte{})
		return nil
	})

	go func() {
		for {
			// 接收响应
			readtype, resp, err := conn.ReadMessage()
			if err != nil {
				log.Fatalf("Failed to read response: %v", err)
				break
			}
			//fmt.Printf("Received response: %d %s\n", readtype, string(resp))
			recvfunc(readtype, resp)
		}
	}()
	return conn, nil
}

// 发送 RPC 消息
func sendRpcMessage(conn *websocket.Conn, rpcID string, payload map[string]interface{}) ([]byte, error) {
	// 将 payload 转换为 JSON 字符串
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %v", err)
	}

	// 构造消息
	message := map[string]interface{}{
		"rpc": map[string]interface{}{
			"id":      rpcID,
			"payload": string(payloadBytes),
		},
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal message: %v", err)
	}

	// 发送消息
	err = conn.WriteMessage(websocket.TextMessage, messageBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to send message: %v", err)
	}

	fmt.Println("RPC message sent!")

	return nil, err
}

func main() {
	str, err := json.Marshal(map[string]int64{"a": 3, "b": 5})
	str1, err1 := httpRpc("add_numbers", str)
	log.Printf("RPC result: %s %v", str1, err1)
	return
	token, err := loginWithEmail()
	if err != nil {
		log.Fatalf("Error during login: %v", err)
	}
	fmt.Println("Login successful. Token:", token)

	conn, err := connectWebSocket(token, func(messageType int, p []byte) {
		fmt.Printf("Received response: %d %s\n", messageType, string(p))
	})
	if err != nil {
		log.Fatalf("WebSocket connection failed: %v", err)
	}
	defer conn.Close()
	fmt.Println("WebSocket connection established!")

	sendRpcMessage(conn, "add_numbers", map[string]interface{}{"a": 3, "b": 5})

	select {}
}
