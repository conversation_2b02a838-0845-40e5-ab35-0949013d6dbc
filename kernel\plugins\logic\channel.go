package logic

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"kernel/kernel-common/api"
	"kernel/kernel-common/rtapi"
	"kernel/kernel-common/runtime"
	"kernel/plugins/models"
	"regexp"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/gofrs/uuid/v5"
	"github.com/jackc/pgx/v5/pgtype"
	json "github.com/json-iterator/go"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

type ChannelGlobalDataStruct struct {
	i ServerModule
}

var (
	errChannelMessageIdInvalid = errors.New("Invalid message identifier")
	errChannelMessageNotFound  = errors.New("channel message not found")
	errChannelMessagePersist   = errors.New("error persisting channel message")
	controlCharsRegex          = regexp.MustCompilePOSIX("[[:cntrl:]]+")
	WorldChannelId             = "2...world" // 聊天世界频道
)

func NewChannelGlobalDataStruct(i ServerModule) *ChannelGlobalDataStruct {
	return &ChannelGlobalDataStruct{
		i: i,
	}
}

func (c *ChannelGlobalDataStruct) ChannelIdBuild(ctx context.Context, sender string, target string, chanType runtime.ChannelType) (string, error) {
	nk := c.i.GetNk()
	channelID, err := nk.ChannelIdBuild(ctx, sender, target, chanType)
	if err != nil {
		return "", err
	}
	return channelID, nil
}

func (c *ChannelGlobalDataStruct) BuildChannelId(ctx context.Context, userID uuid.UUID, target string, chanType rtapi.ChannelJoin_Type) (string, models.PresenceStream, error) {
	if target == "" {
		return "", models.PresenceStream{}, runtime.ErrInvalidChannelTarget
	}

	stream := models.PresenceStream{
		Mode: models.StreamModeChannel,
	}

	switch chanType {
	case rtapi.ChannelJoin_TYPE_UNSPECIFIED:
		// Defaults to room channel.
		fallthrough
	case rtapi.ChannelJoin_ROOM:
		if len(target) < 1 || len(target) > 64 {
			return "", models.PresenceStream{}, fmt.Errorf("Channel name is required and must be 1-64 chars: %w", runtime.ErrInvalidChannelTarget)
		}
		if controlCharsRegex.MatchString(target) {
			return "", models.PresenceStream{}, fmt.Errorf("Channel name must not contain control chars: %w", runtime.ErrInvalidChannelTarget)
		}
		if !utf8.ValidString(target) {
			return "", models.PresenceStream{}, fmt.Errorf("Channel name must only contain valid UTF-8 bytes: %w", runtime.ErrInvalidChannelTarget)
		}
		stream.Label = target
		// Channel mode is already set by default above.
	case rtapi.ChannelJoin_DIRECT_MESSAGE:
		// Check if user ID is valid.
		uid, err := uuid.FromString(target)
		if err != nil {
			return "", models.PresenceStream{}, fmt.Errorf("Invalid user ID in direct message join: %w", runtime.ErrInvalidChannelTarget)
		}
		// Not allowed to chat to the nil uuid.
		if uid == uuid.Nil {
			return "", models.PresenceStream{}, fmt.Errorf("Invalid user ID in direct message join: %w", runtime.ErrInvalidChannelTarget)
		}
		// If userID is the system user, skip these checks
		if userID != uuid.Nil {
			// // Check if the other user exists and has not blocked this user.
			// allowed, err := UserExistsAndDoesNotBlock(ctx, db, uid, userID)
			// if err != nil {
			// 	return "", models.PresenceStream{}, errors.New("Failed to look up user ID")
			// }
			// if !allowed {
			// 	return "", models.PresenceStream{}, fmt.Errorf("User ID not found: %w", runtime.ErrInvalidChannelTarget)
			// }
			// Assign the ID pair in a consistent order.
			if uid.String() > userID.String() {
				stream.Subject = userID.String()
				stream.Subcontext = uid.String()
			} else {
				stream.Subject = uid.String()
				stream.Subcontext = userID.String()
			}
			stream.Mode = models.StreamModeDM
		}
	case rtapi.ChannelJoin_GROUP:
		// Check if group ID is valid.
		gid, err := uuid.FromString(target)
		if err != nil {
			return "", models.PresenceStream{}, fmt.Errorf("Invalid group ID in group channel join: %w", runtime.ErrInvalidChannelTarget)
		}
		// if userID != uuid.Nil {
		// 	allowed, err := groupCheckUserPermission(ctx, logger, db, gid, userID, 2)
		// 	if err != nil {
		// 		return "", models.PresenceStream{}, errors.New("Failed to look up group membership")
		// 	}
		// 	if !allowed {
		// 		return "", models.PresenceStream{}, fmt.Errorf("Group not found: %w", runtime.ErrInvalidChannelTarget)
		// 	}
		// }

		stream.Subject = gid.String()
		stream.Mode = models.StreamModeGroup
	default:
		return "", models.PresenceStream{}, runtime.ErrInvalidChannelType
	}

	channelID, err := StreamToChannelId(stream)
	if err != nil {
		// Should not happen after the input validation above, but guard just in case.
		c.i.GetLogger().Error("Error converting stream to channel identifier", zap.Error(err), zap.Any("stream", stream))
		return "", models.PresenceStream{}, err
	}

	return channelID, stream, nil
}

func (c *ChannelGlobalDataStruct) ChannelMessageSend(ctx context.Context, channelID string, content map[string]interface{}, senderId, senderUsername string, persist bool, presences []runtime.Presence) (*rtapi.ChannelMessageAck, error) {
	contentStr := "{}"
	if content != nil {
		contentBytes, err := json.Marshal(content)
		if err != nil {
			return nil, fmt.Errorf("error encoding content: %v", err.Error())
		}
		contentStr = string(contentBytes)
	}
	return c.ChannelMessageSendRaw(ctx, channelID, contentStr, senderId, senderUsername, persist, presences)
}

func (c *ChannelGlobalDataStruct) ChannelMessageSendRaw(ctx context.Context, channelID string, content string, senderId, senderUsername string, persist bool, presences []runtime.Presence) (*rtapi.ChannelMessageAck, error) {
	db := c.i.GetDb()
	stream := c.i.GetStream()
	logger := c.i.GetLogger()
	channelIdToStreamResult, err := ChannelIdToStream(channelID)
	if err != nil {
		return nil, err
	}

	if content == "" {
		content = "{}"
	}

	ts := time.Now().Unix()
	message := &api.ChannelMessage{
		ChannelId:  channelID,
		MessageId:  uuid.Must(uuid.NewV4()).String(),
		Code:       &wrapperspb.Int32Value{Value: models.ChannelMessageTypeChat},
		SenderId:   senderId,
		Username:   senderUsername,
		Content:    content,
		CreateTime: &timestamppb.Timestamp{Seconds: ts},
		UpdateTime: &timestamppb.Timestamp{Seconds: ts},
		Persistent: &wrapperspb.BoolValue{Value: persist},
	}

	ack := &rtapi.ChannelMessageAck{
		ChannelId:  message.ChannelId,
		MessageId:  message.MessageId,
		Code:       message.Code,
		Username:   message.Username,
		CreateTime: message.CreateTime,
		UpdateTime: message.UpdateTime,
		Persistent: message.Persistent,
	}
	channelStream := channelIdToStreamResult.Stream
	switch channelStream.Mode {
	case models.StreamModeChannel:
		message.RoomName, ack.RoomName = channelStream.Label, channelStream.Label
	case models.StreamModeGroup:
		message.GroupId, ack.GroupId = channelStream.Subject, channelStream.Subject
	case models.StreamModeDM:
		message.UserIdOne, ack.UserIdOne = channelStream.Subject, channelStream.Subject
		message.UserIdTwo, ack.UserIdTwo = channelStream.Subcontext, channelStream.Subcontext
	}

	c.i.GetCommon().GroutinePool.Submit(func() {
		if persist {
			subject := uuid.FromStringOrNil(channelStream.Subject)
			subcontext := uuid.FromStringOrNil(channelStream.Subcontext)
			query := `INSERT INTO message (id, code, sender_id, username, stream_mode, stream_subject, stream_descriptor, stream_label, content, create_time, update_time)
VALUES ($1, $2, $3, $4, $5, $6::UUID, $7::UUID, $8, $9, $10, $10)`
			_, err := db.ExecContext(context.Background(), query, message.MessageId, message.Code.Value, message.SenderId, message.Username, channelStream.Mode, subject, subcontext, channelStream.Label, message.Content, time.Unix(message.CreateTime.Seconds, 0).UTC())
			if err != nil {
				logger.Error("Error persisting channel message %s", err.Error())
				//return nil, errChannelMessagePersist
				return
			}
		}

		if WorldChannelId != channelID {
			stream.StreamSendRaw(context.Background(), models.StreamModeChannel, channelStream.Subject, channelStream.Subcontext, channelStream.Label, &rtapi.Envelope{Message: &rtapi.Envelope_ChannelMessage{ChannelMessage: message}}, presences, true)
		} else {
			stream.StreamSendRawToAll(context.Background(), models.StreamModeChannel, channelStream.Subject, channelStream.Subcontext, channelStream.Label, &rtapi.Envelope{Message: &rtapi.Envelope_ChannelMessage{ChannelMessage: message}}, true)
		}
	})

	return ack, nil
}

func (c *ChannelGlobalDataStruct) ChannelMessageUpdate(ctx context.Context, channelID, messageID string, content map[string]interface{}, senderId, senderUsername string, persist bool, presences []runtime.Presence) (*rtapi.ChannelMessageAck, error) {
	contentStr := "{}"
	if content != nil {
		contentBytes, err := json.Marshal(content)
		if err != nil {
			return nil, fmt.Errorf("error encoding content: %v", err.Error())
		}
		contentStr = string(contentBytes)
	}

	return c.ChannelMessageUpdateRaw(ctx, channelID, messageID, contentStr, senderId, senderUsername, persist, presences)
}

func (c *ChannelGlobalDataStruct) ChannelMessageUpdateRaw(ctx context.Context, channelID, messageID string, content string, senderId, senderUsername string, persist bool, presences []runtime.Presence) (*rtapi.ChannelMessageAck, error) {
	db := c.i.GetDb()
	stream := c.i.GetStream()
	logger := c.i.GetLogger()
	channelIdToStreamResult, err := ChannelIdToStream(channelID)
	if err != nil {
		return nil, err
	}

	if _, err := uuid.FromString(messageID); err != nil {
		return nil, errChannelMessageIdInvalid
	}

	if content == "" {
		content = "{}"
	}

	ts := time.Now().Unix()
	message := &api.ChannelMessage{
		ChannelId:  channelID,
		MessageId:  messageID,
		Code:       &wrapperspb.Int32Value{Value: models.ChannelMessageTypeChatUpdate},
		SenderId:   senderId,
		Username:   senderUsername,
		Content:    content,
		CreateTime: &timestamppb.Timestamp{Seconds: ts},
		UpdateTime: &timestamppb.Timestamp{Seconds: ts},
		Persistent: &wrapperspb.BoolValue{Value: persist},
	}

	ack := &rtapi.ChannelMessageAck{
		ChannelId:  message.ChannelId,
		MessageId:  message.MessageId,
		Code:       message.Code,
		Username:   message.Username,
		CreateTime: message.CreateTime,
		UpdateTime: message.UpdateTime,
		Persistent: message.Persistent,
	}

	channelStream := channelIdToStreamResult.Stream
	switch channelStream.Mode {
	case models.StreamModeChannel:
		message.RoomName, ack.RoomName = channelStream.Label, channelStream.Label
	case models.StreamModeGroup:
		message.GroupId, ack.GroupId = channelStream.Subject, channelStream.Subject
	case models.StreamModeDM:
		message.UserIdOne, ack.UserIdOne = channelStream.Subject, channelStream.Subject
		message.UserIdTwo, ack.UserIdTwo = channelStream.Subcontext, channelStream.Subcontext
	}

	if persist {
		// First find and update the referenced message.
		var dbCreateTime pgtype.Timestamptz
		query := "UPDATE message SET update_time = $5, username = $4, content = $3 WHERE id = $1 AND sender_id = $2 RETURNING create_time"
		err := db.QueryRowContext(ctx, query, messageID, message.SenderId, message.Content, message.Username, time.Unix(message.UpdateTime.Seconds, 0).UTC()).Scan(&dbCreateTime)
		if err != nil {
			if err == sql.ErrNoRows {
				return nil, errChannelMessageNotFound
			}
			logger.Error("Error persisting channel message update %s", err.Error())
			return nil, errChannelMessagePersist
		}
		// Replace the message create time with the real one from DB.
		message.CreateTime = &timestamppb.Timestamp{Seconds: dbCreateTime.Time.Unix()}
	}

	stream.StreamSendRaw(ctx, models.StreamModeChannel, channelStream.Subject, channelStream.Subcontext, channelStream.Label, &rtapi.Envelope{Message: &rtapi.Envelope_ChannelMessage{ChannelMessage: message}}, presences, true)

	return ack, nil
}

func (c *ChannelGlobalDataStruct) ChannelMessageRemove(ctx context.Context, channelID, messageID string, senderId, senderUsername string, persist bool, presences []runtime.Presence) (*rtapi.ChannelMessageAck, error) {
	db := c.i.GetDb()
	stream := c.i.GetStream()
	logger := c.i.GetLogger()
	channelIdToStreamResult, err := ChannelIdToStream(channelID)
	if err != nil {
		return nil, err
	}

	if _, err := uuid.FromString(messageID); err != nil {
		return nil, errChannelMessageIdInvalid
	}

	ts := time.Now().Unix()
	message := &api.ChannelMessage{
		ChannelId:  channelID,
		MessageId:  messageID,
		Code:       &wrapperspb.Int32Value{Value: models.ChannelMessageTypeChatRemove},
		SenderId:   senderId,
		Username:   senderUsername,
		Content:    "{}",
		CreateTime: &timestamppb.Timestamp{Seconds: ts},
		UpdateTime: &timestamppb.Timestamp{Seconds: ts},
		Persistent: &wrapperspb.BoolValue{Value: persist},
	}

	ack := &rtapi.ChannelMessageAck{
		ChannelId:  message.ChannelId,
		MessageId:  message.MessageId,
		Code:       message.Code,
		Username:   message.Username,
		CreateTime: message.CreateTime,
		UpdateTime: message.UpdateTime,
		Persistent: message.Persistent,
	}

	channelStream := channelIdToStreamResult.Stream
	switch channelStream.Mode {
	case models.StreamModeChannel:
		message.RoomName, ack.RoomName = channelStream.Label, channelStream.Label
	case models.StreamModeGroup:
		message.GroupId, ack.GroupId = channelStream.Subject, channelStream.Subject
	case models.StreamModeDM:
		message.UserIdOne, ack.UserIdOne = channelStream.Subject, channelStream.Subject
		message.UserIdTwo, ack.UserIdTwo = channelStream.Subcontext, channelStream.Subcontext
	}

	if persist {
		// First find and remove the referenced message.
		var dbCreateTime pgtype.Timestamptz
		query := "DELETE FROM message WHERE id = $1 AND sender_id = $2 RETURNING create_time"
		err := db.QueryRowContext(ctx, query, messageID, message.SenderId).Scan(&dbCreateTime)
		if err != nil {
			if err == sql.ErrNoRows {
				return nil, errChannelMessageNotFound
			}
			logger.Error("Error persisting channel message remove %s", err.Error())
			return nil, errChannelMessagePersist
		}
		// Replace the message create time with the real one from DB.
		message.CreateTime = &timestamppb.Timestamp{Seconds: dbCreateTime.Time.Unix()}
	}

	stream.StreamSendRaw(ctx, models.StreamModeChannel, channelStream.Subject, channelStream.Subcontext, channelStream.Label, &rtapi.Envelope{Message: &rtapi.Envelope_ChannelMessage{ChannelMessage: message}}, presences, true)

	return ack, nil
}

func (c *ChannelGlobalDataStruct) ChannelMessagesList(ctx context.Context, channelId string, limit int, forward bool, cursor string) (messages []*api.ChannelMessage, nextCursor string, prevCursor string, err error) {
	nk := c.i.GetNk()
	messages, nextCursor, prevCursor, err = nk.ChannelMessagesList(ctx, channelId, limit, forward, cursor)
	if err != nil {
		return nil, "", "", err
	}
	return messages, nextCursor, prevCursor, nil
}

func ChannelIdToStream(channelID string) (*models.ChannelIdToStreamResult, error) {
	if channelID == "" {
		return nil, runtime.ErrChannelIDInvalid
	}

	components := strings.SplitN(channelID, ".", 4)
	if len(components) != 4 {
		return nil, runtime.ErrChannelIDInvalid
	}

	stream := models.PresenceStream{
		Mode: models.StreamModeChannel,
	}

	// Parse and assign mode.
	switch components[0] {
	case "2":
		// StreamModeChannel.
		// Expect no subject or subcontext.
		if components[1] != "" || components[2] != "" {
			return nil, runtime.ErrChannelIDInvalid
		}
		// Label.
		if l := len(components[3]); l < 1 || l > 64 {
			return nil, runtime.ErrChannelIDInvalid
		}
		stream.Label = components[3]
	case "3":
		// Expect no subcontext or label.
		if components[2] != "" || components[3] != "" {
			return nil, runtime.ErrChannelIDInvalid
		}
		// Subject.
		if components[1] != "" {
			stream.Subject = components[1]
		}
		// Mode.
		stream.Mode = models.StreamModeGroup
	case "4":
		// Expect lo label.
		if components[3] != "" {
			return nil, runtime.ErrChannelIDInvalid
		}
		// Subject.
		if components[1] != "" {
			stream.Subject = components[1]
		}
		// Subcontext.
		if components[2] != "" {
			stream.Subcontext = components[2]
		}
		// Mode.
		stream.Mode = models.StreamModeDM
	default:
		return nil, runtime.ErrChannelIDInvalid
	}

	return &models.ChannelIdToStreamResult{Stream: stream}, nil
}

func StreamToChannelId(stream models.PresenceStream) (string, error) {
	if stream.Mode != models.StreamModeChannel && stream.Mode != models.StreamModeGroup && stream.Mode != models.StreamModeDM {
		return "", runtime.ErrChannelIDInvalid
	}

	subject := stream.Subject
	subcontext := stream.Subcontext

	return fmt.Sprintf("%v.%v.%v.%v", stream.Mode, subject, subcontext, stream.Label), nil
}
