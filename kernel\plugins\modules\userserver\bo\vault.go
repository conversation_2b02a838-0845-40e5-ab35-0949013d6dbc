package bo

// CREATE TABLE IF NOT EXISTS vault (
// 	FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,

// 	user_id UUID NOT NULL PRIMARY KEY,
// 	items JSONB NOT NULL DEFAULT '{}'::JSONB,
// 	capacity int4 NOT NULL,
// 	cooldown int4 NOT NULL,
// 	season_tag TEXT NOT NULL,
// 	extract_time TIMESTAMP NOT NULL,
// 	created_at TIMESTAMPTZ DEFAULT now(),
// 	updated_at TIMESTAMPTZ DEFAULT now()
//   );

import (
	"bytes"
	"encoding/json"
	"fmt"
	"kernel/plugins/common"
	"kernel/plugins/models"
	"reflect"
	"time"
)

type Vault struct {
	UserId      string    `json:"user_id,omitempty"`
	Items       string    `json:"items,omitempty"`        // 物品列表
	Capacity    int64     `json:"capacity,omitempty"`     // 容量
	Cooldown    int64     `json:"cooldown,omitempty"`     // 冷却时间(秒)
	SeasonTag   string    `json:"season_tag,omitempty"`   // 赛季标签
	ExtractTime time.Time `json:"extract_time,omitempty"` // 提取时间
	Version     int32     `json:"version,omitempty"`      // 版本号
	CreatedAt   time.Time `json:"created_at,omitempty"`
	UpdatedAt   time.Time `json:"updated_at,omitempty"`
}

func (v *Vault) GetTable() string {
	return "vault"
}

func (v *Vault) GetKeyName() string {
	return "user_id"
}

func (v *Vault) GetUniqueKeys() []string {
	return []string{}
}

func (v *Vault) GetSecondKeyName() string {
	return ""
}

func (v *Vault) GetRowResultType() models.RowResultType {
	return models.ROWRESULT_TYPE_ONLYONE
}

func (v *Vault) GetQueryArgs() string {
	return "*"
}

func (v *Vault) GetFlushType() models.FlushType {
	return models.FLUSH_TYPE_SYNC
}

func (v *Vault) GetCacheLevel() models.CacheLevel {
	return models.CACHE_LEVEL_IMPORT
}

func (v *Vault) GetVersionName() string {
	return "version"
}

func (v *Vault) Marshal() ([]byte, error) {
	return json.Marshal(v)
}

func (v *Vault) MarshalToMap(filter map[string]struct{}) map[string]interface{} {
	dst := make(map[string]interface{})
	err := common.StrcutToMap(v, &dst)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	for k := range filter {
		delete(dst, k)
	}
	return dst
}

func (v *Vault) Unmarshal(buf []byte) error {
	d := json.NewDecoder(bytes.NewReader(buf))
	d.UseNumber()
	return d.Decode(v)
}

func (v *Vault) Clear() {
	p := reflect.ValueOf(v).Elem()
	p.Set(reflect.Zero(p.Type()))
}
