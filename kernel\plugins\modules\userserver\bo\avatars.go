package bo

import (
	"bytes"
	"encoding/json"
	"fmt"
	"kernel/plugins/common"
	"kernel/plugins/models"
	"reflect"
	"time"
)

// CREATE TABLE IF NOT EXISTS avatars (
// 	FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,

// 	user_id UUID NOT NULL PRIMARY KEY,
// 	items JSONB NOT NULL DEFAULT '{}'::JSONB,
// 	version int4 NOT NULL DEFAULT 0,
// 	created_at TIMESTAMPTZ DEFAULT now(),
// 	updated_at TIMESTAMPTZ DEFAULT now()
//   );

type Avatars struct {
	UserId    string    `json:"user_id,omitempty"`
	Items     string    `json:"items,omitempty"`
	Version   int32     `json:"version,omitempty"`
	CreatedAt time.Time `json:"created_at,omitempty"`
	UpdatedAt time.Time `json:"updated_at,omitempty"`
}

func (v *Avatars) GetTable() string {
	return "avatars"
}

func (v *Avatars) GetKeyName() string {
	return "user_id"
}

func (v *Avatars) GetUniqueKeys() []string {
	return []string{}
}

func (v *Avatars) GetSecondKeyName() string {
	return ""
}

func (v *Avatars) GetRowResultType() models.RowResultType {
	return models.ROWRESULT_TYPE_ONLYONE
}

func (v *Avatars) GetQueryArgs() string {
	return "*"
}

func (v *Avatars) GetFlushType() models.FlushType {
	return models.FLUSH_TYPE_SYNC
}

func (v *Avatars) GetCacheLevel() models.CacheLevel {
	return models.CACHE_LEVEL_IMPORT
}

func (v *Avatars) GetVersionName() string {
	return "version"
}

func (v *Avatars) Marshal() ([]byte, error) {
	return json.Marshal(v)
}

func (v *Avatars) MarshalToMap(filter map[string]struct{}) map[string]interface{} {
	dst := make(map[string]interface{})
	err := common.StrcutToMap(v, &dst)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	for k := range filter {
		delete(dst, k)
	}
	return dst
}

func (v *Avatars) Unmarshal(buf []byte) error {
	d := json.NewDecoder(bytes.NewReader(buf))
	d.UseNumber()
	return d.Decode(v)
}

func (v *Avatars) Clear() {
	p := reflect.ValueOf(v).Elem()
	p.Set(reflect.Zero(p.Type()))
}
