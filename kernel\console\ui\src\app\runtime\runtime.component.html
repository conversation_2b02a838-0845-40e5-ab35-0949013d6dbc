<h2 class="pb-4">Runtime Modules</h2>

<ngb-alert [dismissible]="false" type="danger" *ngIf="error">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">Failed to retrieve runtime information: {{error}}</h6>
</ngb-alert>

<table class="runtime-table mb-4 table table-sm table-bordered  mb-5">
  <thead class="thead-light">
    <tr>
      <th>Go Modules</th>
      <th style="width: 180px">Last Modified</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let m of runtimeInfo.go_modules"><td>{{m.path}}</td><td style="width: 200px">{{m.mod_time}}</td></tr>
    <tr *ngIf="runtimeInfo.go_modules.length === 0"><td class="text-muted">No Go modules were found.</td><td></td></tr>
  </tbody>
  <thead class="thead-light">
    <tr><th colspan="2">Lua Modules</th></tr>
  </thead>
  <tbody>
    <tr *ngFor="let m of runtimeInfo.lua_modules"><td>{{m.path}}</td><td style="width: 200px">{{m.mod_time}}</td></tr>
    <tr *ngIf="runtimeInfo.lua_modules.length === 0"><td class="text-muted">No Lua modules were found.</td><td></td></tr>
  </tbody>
  <thead class="thead-light">
    <tr><th colspan="2">JavaScript Modules</th></tr>
  </thead>
  <tbody>
    <tr *ngFor="let m of runtimeInfo.js_modules"><td>{{m.path}}</td><td style="width: 200px">{{m.mod_time}}</td></tr>
    <tr *ngIf="runtimeInfo.js_modules.length === 0"><td class="text-muted">No JavaScript modules were found.</td><td></td></tr>
  </tbody>
</table>

<h4 class="mb-4 section-divider d-flex">Registered RPC Functions</h4>

<table class="runtime-table table table-sm table-bordered">
  <thead class="thead-light">
    <tr><th colspan="2">Go RPC Functions</th></tr>
  </thead>
  <tbody>
    <tr *ngFor="let m of runtimeInfo.go_rpc_functions">
      <td><code>{{m}}</code></td>
      <td style="width: 180px; text-align: center;"><a class="btn btn-sm btn-secondary" [routerLink]="['/apiexplorer']" [queryParams]="{'endpoint': m}">API Explorer</a></td>
    </tr>
    <tr *ngIf="runtimeInfo.go_rpc_functions.length === 0"><td colspan="2" class="text-muted">No Go RPC functions were found.</td></tr>
  </tbody>
  <thead class="thead-light">
    <tr><th colspan="2">Lua RPC Functions</th></tr>
  </thead>
  <tbody>
    <tr *ngFor="let m of runtimeInfo.lua_rpc_functions">
      <td><code>{{m}}</code></td>
      <td style="width: 180px; text-align: center;"><a class="btn btn-sm btn-secondary" [routerLink]="['/apiexplorer']" [queryParams]="{'endpoint': m}">API Explorer</a></td>
    </tr>
    <tr *ngIf="runtimeInfo.lua_rpc_functions.length === 0"><td colspan="2" class="text-muted">No Lua RPC functions were found.</td></tr>
  </tbody>
  <thead class="thead-light">
    <tr><th colspan="2">JavaScript RPC Functions</th></tr>
  </thead>
  <tbody>
    <tr *ngFor="let m of runtimeInfo.js_rpc_functions">
      <td><code>{{m}}</code></td>
      <td style="width: 180px; text-align: center;"><a class="btn btn-sm btn-secondary" [routerLink]="['/apiexplorer']" [queryParams]="{'endpoint': m}">API Explorer</a></td>
    </tr>
    <tr *ngIf="runtimeInfo.js_rpc_functions.length === 0"><td colspan="2" class="text-muted">No JavaScript RPC functions were found.</td></tr>
  </tbody>
</table>
