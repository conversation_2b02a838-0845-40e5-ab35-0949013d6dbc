package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"sync"

	"go.uber.org/zap/zapcore"
)

type QueueServerModule struct {
	name            string
	logger          runtime.Logger
	db              *sql.DB
	nk              runtime.NakamaModule
	config          runtime.Config // nakama 配置
	CustomConfig    *CustomConfig  // 自定义配置
	isRegisterSelf  bool
	ProxyPassConfig sync.Map
	controller      *Controller
	service         *Service

	common  *logic.CommonGlobalDataStruct
	online  *logic.OnlineGlobalDataStruct
	send    *logic.SendGlobalDataStruct
	dbagent *logic.DbAgentGlobalDataStruct
}

var QueueServerData *QueueServerModule

func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
	QueueServerData = new(QueueServerModule)
	QueueServerData.name = models.SERVER_NAME_QUEUE
	QueueServerData.logger = logger
	QueueServerData.db = db
	QueueServerData.nk = nk
	QueueServerData.common = logic.NewCommonGlobalDataStruct()
	QueueServerData.online = logic.NewOnlineGlobalDataStruct(QueueServerData)
	QueueServerData.send = logic.NewSendGlobalDataStruct(QueueServerData)
	QueueServerData.dbagent = logic.NewDbAgentGlobalDataStruct(QueueServerData)
	config, err := initializer.GetConfig()
	if err != nil {
		return err
	}
	QueueServerData.config = config
	if err := QueueServerData.common.Init(QueueServerData, QueueServerData.CustomConfig, QueueServerData.customConfigEvent); err != nil {
		logger.Error("init common logic error %v", err)
		return err
	}

	// 创建服务
	QueueServerData.service = NewService(QueueServerData.common, QueueServerData.online, QueueServerData.send, QueueServerData.dbagent, QueueServerData.CustomConfig)

	// 自定义路由注册
	QueueServerData.controller = NewController(QueueServerData, QueueServerData.service)
	initializer.RegisterRpc(RPCID_QUEUESERVER_JOIN_QUEUE, QueueServerData.controller.JoinQueue)
	initializer.RegisterRpc(RPCID_QUEUESERVER_LEAVE_QUEUE, QueueServerData.controller.LeaveQueue)
	initializer.RegisterRpc(RPCID_QUEUESERVER_GET_QUEUE_STATUS, QueueServerData.controller.GetQueueStatus)

	// 初始化
	go QueueServerData.service.Init()
	go QueueServerData.controller.Init()

	return nil
}

func (s *QueueServerModule) GetCommon() *logic.CommonGlobalDataStruct {
	return s.common
}
func (s *QueueServerModule) GetOnline() *logic.OnlineGlobalDataStruct {
	return s.online
}
func (s *QueueServerModule) GetSend() *logic.SendGlobalDataStruct {
	return s.send
}
func (s *QueueServerModule) GetDbAgent() *logic.DbAgentGlobalDataStruct {
	return s.dbagent
}
func (s *QueueServerModule) GetNotify() *logic.NotifyGlobalDataStruct {
	return nil
}
func (s *QueueServerModule) GetStream() *logic.StreamGlobalDataStruct {
	return nil
}
func (s *QueueServerModule) GetChannel() *logic.ChannelGlobalDataStruct {
	return nil
}
func (s *QueueServerModule) GetLogger() runtime.Logger {
	return s.logger
}
func (s *QueueServerModule) GetConfig() runtime.Config {
	return s.config
}
func (s *QueueServerModule) GetName() string {
	return s.name
}
func (s *QueueServerModule) GetDb() *sql.DB {
	return s.db
}
func (s *QueueServerModule) GetNk() runtime.NakamaModule {
	return s.nk
}

// 配置读取回调
func (s *QueueServerModule) customConfigEvent(dataId string, newobj any) {
	tmpobj, ok := newobj.(*CustomConfig)
	if !ok {
		return
	}
	if tmpobj.Config == nil {
		tmpobj.Config = &QueueServerConfig{}
	}
	defaultNodeCfg := false
	if tmpobj.Config.NodeConfig == nil {
		// 使用默认节点配置
		scheme := "http"
		if s.config.GetSocket().GetUseTLS() {
			scheme = "https"
		}
		tmpobj.Config.NodeConfig = &models.NodeConfig{
			NodeId:   s.config.GetName(),
			SvcGroup: fmt.Sprintf("%s@@%s", s.name, models.NACOS_DEFAULT_GROUP),
			Host:     fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			NetHost:  fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			AuthKey:  s.config.GetRuntime().GetHTTPKey(),
			Weight:   10,
			Scheme:   scheme,
		}
		defaultNodeCfg = true
	}
	tmp, _ := json.Marshal(tmpobj)
	s.logger.Debug("customConfigEvent defaultNodeCfg: %v, customCfg: %s", defaultNodeCfg, string(tmp))
	s.common.SetNodeConfig(tmpobj.Config.NodeConfig)
	s.common.WatchNodeList.InitWatchSvr(tmpobj.Config.WatchSvcGroupList, tmpobj.Config.NodeConfig.SvcGroup)
	s.CustomConfig = tmpobj

	// 注册服务
	selfcfg := map[string]interface{}{}
	if err := common.StrcutToMap(tmpobj.Config.NodeConfig, &selfcfg); err == nil {
		if !s.isRegisterSelf {
			go s.common.RegisterAndUpdateSelfToNacos(false, selfcfg)
			s.isRegisterSelf = true
		} else {
			go s.common.RegisterAndUpdateSelfToNacos(true, selfcfg)
		}
	} else {
		s.logger.Error("register self, config error %v", err)
	}

	s.common.UpdateAccessLogConfig(tmpobj.AccessLog)
	loglevel, err := zapcore.ParseLevel(tmpobj.Logger.Level)
	if err != nil {
		s.logger.Error("parse log level error %v", err)
	} else {
		s.nk.SetLoggerLevel(loglevel)
	}
}
