package logic

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/gob"
	"errors"
	"fmt"
	"kernel/kernel-common/api"
	"kernel/kernel-common/rtapi"
	"kernel/kernel-common/runtime"
	"kernel/plugins/models"
	"time"

	"github.com/gofrs/uuid/v5"
	"github.com/jackc/pgx/v5/pgtype"
	json "github.com/json-iterator/go"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type NotifyGlobalDataStruct struct {
	i ServerModule
}

func NewNotifyGlobalDataStruct(i ServerModule) *NotifyGlobalDataStruct {
	return &NotifyGlobalDataStruct{
		i: i,
	}
}

func (n *NotifyGlobalDataStruct) NotificationSend(ctx context.Context, userID, subject string, content map[string]interface{}, code int, sender string, persistent bool) error {
	contentBytes, err := json.Marshal(content)
	if err != nil {
		return fmt.Errorf("failed to convert content: %s", err.Error())
	}
	contentString := string(contentBytes)

	if code <= 0 {
		return errors.New("expects code to number above 0")
	}

	notifications := make(map[uuid.UUID][]*api.Notification)
	uid := uuid.FromStringOrNil(userID)
	notifications[uid] = []*api.Notification{{
		Id:         uuid.Must(uuid.NewV4()).String(),
		Subject:    subject,
		Content:    contentString,
		SenderId:   sender,
		Code:       int32(code),
		Persistent: persistent,
		CreateTime: &timestamppb.Timestamp{Seconds: time.Now().UTC().Unix()},
	}}

	// 持久化通知
	persistentNotifications := make(map[uuid.UUID][]*api.Notification, len(notifications))
	for userID, ns := range notifications {
		for _, userNotification := range ns {
			// Select persistent notifications for storage.
			if userNotification.Persistent {
				if pun := persistentNotifications[userID]; pun == nil {
					persistentNotifications[userID] = []*api.Notification{userNotification}
				} else {
					persistentNotifications[userID] = append(pun, userNotification)
				}
			}
		}
	}

	// Store any persistent notifications.
	if len(persistentNotifications) > 0 {
		if err := n.NotificationSave(ctx, persistentNotifications); err != nil {
			return err
		}
	}

	push := &models.CommonPush{
		SessionIDs: make([]string, 0),
		Data: &rtapi.Notifications{
			Notifications: notifications[uid],
		},
	}

	// 查询所在网关节点
	list, err := n.i.GetOnline().QueryOnlineUsersInfo(ctx, []string{userID})
	if err != nil {
		return err
	}
	onlineusers, ok := list[userID]
	if !ok {
		return errors.New("user not online")
	}
	nodes := map[string]bool{}
	for i := 0; i < len(onlineusers); i++ {
		onlineuser := onlineusers[i]
		if onlineuser == nil || onlineuser.NodeId == "" || onlineuser.SessionId == "" {
			continue
		}
		push.SessionIDs = append(push.SessionIDs, onlineuser.SessionId)
		nodes[onlineuser.NodeId] = true
	}

	data, err := json.Marshal(push)
	if err != nil {
		return err
	}

	for nodeid := range nodes {
		resp, err := n.i.GetSend().RpcToNodeId(ctx, nodeid, models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, models.RPCID_GATESERVER_SEND, data, nil, models.RPC_TIMEOUT_DEFAULT)
		if err != nil {
			n.i.GetLogger().Error("send to %s %s failed %v %s", userID, nodeid, err, string(resp))
			continue
		}
	}

	return nil
}

// 获取通知列表
func (n *NotifyGlobalDataStruct) NotificationsList(ctx context.Context, userID string, limit int, cursor string) ([]*api.Notification, string, error) {
	if userID == "" {
		return nil, "", errors.New("expects a valid user id")
	}

	if limit < 0 || limit > 1000 {
		return nil, "", errors.New("expects limit to be 0-100")
	}

	uid, err := uuid.FromString(userID)
	if err != nil {
		return nil, "", errors.New("expects a valid user id")
	}

	cacheable := false
	var nc *notificationCacheableCursor
	if cursor != "" {
		nc = &notificationCacheableCursor{}
		cb, err := base64.RawURLEncoding.DecodeString(cursor)
		if err != nil {
			n.i.GetLogger().Warn("Could not base64 decode notification cursor. %s", cursor)
			return nil, "", errors.New("malformed cursor was used.")
		}
		if err = gob.NewDecoder(bytes.NewReader(cb)).Decode(nc); err != nil {
			n.i.GetLogger().Warn("Could not decode notification cursor. %s", cursor)
			return nil, "", errors.New("malformed cursor was used.")
		}
	}

	params := []interface{}{uid}

	limitQuery := ""
	if limit > 0 {
		params = append(params, limit+1)
		limitQuery = " LIMIT $2"
	}

	cursorQuery := ""
	if nc != nil && nc.NotificationID != nil {
		cursorQuery = " AND (user_id, create_time, id) > ($1::UUID, $3::TIMESTAMPTZ, $4::UUID)"
		params = append(params, &pgtype.Timestamptz{Time: time.Unix(0, nc.CreateTime).UTC(), Valid: true}, uuid.FromBytesOrNil(nc.NotificationID))
	}

	query := `
SELECT id, subject, content, code, sender_id, create_time
FROM notification
WHERE user_id = $1` + cursorQuery + `
ORDER BY create_time ASC, id ASC` + limitQuery

	rows, err := n.i.GetDb().QueryContext(ctx, query, params...)
	if err != nil {
		n.i.GetLogger().Error("Could not retrieve notifications. %s", err.Error())
		return nil, "", err
	}

	notifications := make([]*api.Notification, 0, limit)
	var lastCreateTime int64
	var resultCount int
	var hasNextPage bool
	for rows.Next() {
		resultCount++
		if limit > 0 && resultCount > limit {
			hasNextPage = true
			break
		}
		no := &api.Notification{Persistent: true, CreateTime: &timestamppb.Timestamp{}}
		var createTime pgtype.Timestamptz
		if err := rows.Scan(&no.Id, &no.Subject, &no.Content, &no.Code, &no.SenderId, &createTime); err != nil {
			_ = rows.Close()
			n.i.GetLogger().Error("Could not scan notification from database. %s", err.Error())
			return nil, "", err
		}

		lastCreateTime = createTime.Time.UnixNano()
		no.CreateTime.Seconds = createTime.Time.Unix()
		if no.SenderId == uuid.Nil.String() {
			no.SenderId = ""
		}
		notifications = append(notifications, no)
	}
	_ = rows.Close()

	notificationList := &api.NotificationList{}
	cursorBuf := new(bytes.Buffer)
	if len(notifications) == 0 {
		if cacheable {
			if len(cursor) > 0 {
				notificationList.CacheableCursor = cursor
			} else {
				newCursor := &notificationCacheableCursor{NotificationID: nil, CreateTime: 0}
				if err := gob.NewEncoder(cursorBuf).Encode(newCursor); err != nil {
					n.i.GetLogger().Error("Could not create new cursor. %s", err.Error())
					return nil, "", err
				}
				notificationList.CacheableCursor = base64.RawURLEncoding.EncodeToString(cursorBuf.Bytes())
			}
		}
	} else {
		notificationList.Notifications = notifications
		if cacheable || hasNextPage {
			lastNotification := notifications[len(notifications)-1]
			newCursor := &notificationCacheableCursor{
				NotificationID: uuid.FromStringOrNil(lastNotification.Id).Bytes(),
				CreateTime:     lastCreateTime,
			}
			if err := gob.NewEncoder(cursorBuf).Encode(newCursor); err != nil {
				n.i.GetLogger().Error("Could not create new cursor. %s", err.Error())
				return nil, "", err
			}

			notificationList.CacheableCursor = base64.RawURLEncoding.EncodeToString(cursorBuf.Bytes())
		}
	}

	return notificationList.Notifications, notificationList.CacheableCursor, nil
}

func (n *NotifyGlobalDataStruct) NotificationsSend(ctx context.Context, notifications []*runtime.NotificationSend) error {
	user_ids := make([]string, 0, len(notifications))
	notificationsMap := make(map[uuid.UUID][]*api.Notification)
	for _, notification := range notifications {
		uid := uuid.FromStringOrNil(notification.UserID)
		user_ids = append(user_ids, notification.UserID)
		contentBytes, err := json.Marshal(notification.Content)
		if err != nil {
			return fmt.Errorf("failed to convert content: %s", err.Error())
		}
		contentString := string(contentBytes)
		if notificationsMap[uid] == nil {
			notificationsMap[uid] = []*api.Notification{{
				Id:         uuid.Must(uuid.NewV4()).String(),
				Subject:    notification.Subject,
				Content:    contentString,
				SenderId:   notification.Sender,
				Code:       int32(notification.Code),
				Persistent: notification.Persistent,
				CreateTime: &timestamppb.Timestamp{Seconds: time.Now().UTC().Unix()},
			}}
		} else {
			notificationsMap[uid] = append(notificationsMap[uid], &api.Notification{
				Id:         uuid.Must(uuid.NewV4()).String(),
				Subject:    notification.Subject,
				Content:    contentString,
				SenderId:   notification.Sender,
				Code:       int32(notification.Code),
				Persistent: notification.Persistent,
				CreateTime: &timestamppb.Timestamp{Seconds: time.Now().UTC().Unix()},
			})
		}
	}

	// 持久化通知
	persistentNotifications := make(map[uuid.UUID][]*api.Notification, len(notificationsMap))
	for userID, ns := range notificationsMap {
		for _, userNotification := range ns {
			// Select persistent notifications for storage.
			if userNotification.Persistent {
				if pun := persistentNotifications[userID]; pun == nil {
					persistentNotifications[userID] = []*api.Notification{userNotification}
				} else {
					persistentNotifications[userID] = append(pun, userNotification)
				}
			}
		}
	}

	// Store any persistent notifications.
	if len(persistentNotifications) > 0 {
		if err := n.NotificationSave(ctx, persistentNotifications); err != nil {
			return err
		}
	}

	// 查询所在节点
	list, err := n.i.GetOnline().QueryOnlineUsersInfo(ctx, user_ids)
	if err != nil {
		return err
	}

	for struid, onlineusers := range list {
		for i := 0; i < len(onlineusers); i++ {
			onlineuser := onlineusers[i]
			if onlineuser == nil || onlineuser.NodeId == "" || onlineuser.SessionId == "" {
				continue
			}
			cfg, err := n.i.GetCommon().WatchNodeList.GetSvrCfgByID(models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, onlineuser.NodeId)
			if err != nil {
				n.i.GetLogger().Error("%s %s %s", onlineuser.NodeId, struid, err.Error())
				continue
			}
			if cfg == nil || cfg.Host == "" || cfg.Scheme == "" {
				continue
			}

			uid := uuid.FromStringOrNil(struid)
			list := notificationsMap[uid]
			if len(list) == 0 {
				continue
			}

			push := &models.CommonPush{
				SessionIDs: []string{onlineuser.SessionId},
				Data: &rtapi.Notifications{
					Notifications: list,
				},
			}
			data, err := json.Marshal(push)
			if err != nil {
				n.i.GetLogger().Error("marshal push failed %v", err)
				continue
			}

			_, err = n.i.GetSend().RpcToNodeId(ctx, cfg.NodeId, models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, models.RPCID_GATESERVER_MULTICAST, data, nil, models.RPC_TIMEOUT_DEFAULT)
			if err != nil {
				n.i.GetLogger().Error("multicast to %s failed %v", cfg.Host, err)
				continue
			}
		}
	}

	return nil
}

func (n *NotifyGlobalDataStruct) NotificationsSendMap(ctx context.Context, notificationsMap map[uuid.UUID][]*api.Notification) error {
	// 持久化通知
	user_ids := make([]string, 0, len(notificationsMap))
	persistentNotifications := make(map[uuid.UUID][]*api.Notification, len(notificationsMap))
	for userID, ns := range notificationsMap {
		user_ids = append(user_ids, userID.String())
		for _, userNotification := range ns {
			// Select persistent notifications for storage.
			if userNotification.Persistent {
				if pun := persistentNotifications[userID]; pun == nil {
					persistentNotifications[userID] = []*api.Notification{userNotification}
				} else {
					persistentNotifications[userID] = append(pun, userNotification)
				}
			}
		}
	}

	// Store any persistent notifications.
	if len(persistentNotifications) > 0 {
		if err := n.NotificationSave(ctx, persistentNotifications); err != nil {
			return err
		}
	}

	// 查询所在节点
	list, err := n.i.GetOnline().QueryOnlineUsersInfo(ctx, user_ids)
	if err != nil {
		return err
	}

	for struid, onlineusers := range list {
		for i := 0; i < len(onlineusers); i++ {
			onlineuser := onlineusers[i]
			if onlineuser == nil || onlineuser.NodeId == "" || onlineuser.SessionId == "" {
				continue
			}
			cfg, err := n.i.GetCommon().WatchNodeList.GetSvrCfgByID(models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, onlineuser.NodeId)
			if err != nil {
				n.i.GetLogger().Error("%s %s %s", onlineuser.NodeId, struid, err.Error())
				continue
			}
			if cfg == nil || cfg.Host == "" || cfg.Scheme == "" {
				continue
			}

			uid := uuid.FromStringOrNil(struid)
			list := notificationsMap[uid]
			if len(list) == 0 {
				continue
			}

			push := &models.CommonPush{
				SessionIDs: []string{onlineuser.SessionId},
				Data: &rtapi.Notifications{
					Notifications: list,
				},
			}
			data, err := json.Marshal(push)
			if err != nil {
				n.i.GetLogger().Error("marshal push failed %v", err)
				continue
			}

			_, err = n.i.GetSend().RpcToNodeId(ctx, cfg.NodeId, models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, models.RPCID_GATESERVER_MULTICAST, data, nil, models.RPC_TIMEOUT_DEFAULT)
			if err != nil {
				n.i.GetLogger().Error("multicast to %s failed %v", cfg.Host, err)
				continue
			}
		}
	}

	return nil
}

func (n *NotifyGlobalDataStruct) NotificationSendAll(ctx context.Context, subject string, content map[string]interface{}, code int, persistent bool) error {
	if subject == "" {
		return errors.New("expects subject to be a non-empty string")
	}

	contentBytes, err := json.Marshal(content)
	if err != nil {
		return fmt.Errorf("failed to convert content: %s", err.Error())
	}
	contentString := string(contentBytes)

	if code <= 0 {
		return errors.New("expects code to number above 0")
	}

	senderID := uuid.Nil.String()
	createTime := &timestamppb.Timestamp{Seconds: time.Now().UTC().Unix()}

	notification := &api.Notification{
		Id:         uuid.Must(uuid.NewV4()).String(),
		Subject:    subject,
		Content:    contentString,
		Code:       int32(code),
		SenderId:   senderID,
		Persistent: persistent,
		CreateTime: createTime,
	}

	// Non-persistent notifications don't need to work through all database users, just use currently connected notification streams.
	if !notification.Persistent {
		push := &models.CommonPush{
			Data: &rtapi.Notifications{
				Notifications: []*api.Notification{notification},
			},
		}
		data, err := json.Marshal(push)
		if err != nil {
			n.i.GetLogger().Error("boardcast to gate failed %v", err)
			return err
		}
		n.i.GetSend().RpcAll(ctx, models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, models.RPCID_GATESERVER_BOARDCAST, data, nil, models.RPC_TIMEOUT_DEFAULT)
		return nil
	}

	const limit = 10_000

	// Start dispatch in paginated batches.
	go func() {
		// Switch to a background context, the caller should not wait for the full operation to complete.
		ctx := context.Background()
		notificationLogger := n.i.GetLogger().WithField("notification_subject", notification.Subject)

		var userIDStr string
		for {
			sends := make(map[uuid.UUID][]*api.Notification, limit)

			params := make([]interface{}, 0, 1)
			query := "SELECT id FROM users"
			if userIDStr != "" {
				query += " WHERE id > $1"
				params = append(params, userIDStr)
			}
			query += fmt.Sprintf(" ORDER BY id ASC LIMIT %d", limit)

			rows, err := n.i.GetDb().QueryContext(ctx, query, params...)
			if err != nil {
				notificationLogger.Error("Failed to retrieve user data to send notification %s", err.Error())
				return
			}

			for rows.Next() {
				if err = rows.Scan(&userIDStr); err != nil {
					_ = rows.Close()
					notificationLogger.Error("Failed to scan user data to send notification id: %s %v", userIDStr, err)
					return
				}
				userID, err := uuid.FromString(userIDStr)
				if err != nil {
					_ = rows.Close()
					notificationLogger.Error("Failed to parse scanned user id data to send notification id: %s %v", userIDStr, err)
					return
				}
				sends[userID] = []*api.Notification{{
					Id:         uuid.Must(uuid.NewV4()).String(),
					Subject:    notification.Subject,
					Content:    notification.Content,
					Code:       notification.Code,
					SenderId:   notification.SenderId,
					CreateTime: notification.CreateTime,
					Persistent: notification.Persistent,
				}}
			}
			_ = rows.Close()

			if len(sends) == 0 {
				// Pagination finished.
				return
			}

			if err := n.NotificationSave(ctx, sends); err != nil {
				notificationLogger.Error("Failed to save persistent notifications %v", err)
				return
			}

			// Deliver live notifications to connected users.
			for userID, notifications := range sends {
				// 查询所在节点
				user_id := userID.String()
				list, err := n.i.GetOnline().QueryOnlineUsersInfo(ctx, []string{user_id})
				if err != nil {
					continue
				}
				onlineusers, ok := list[user_id]
				if !ok {
					continue
				}

				push := &models.CommonPush{
					SessionIDs: make([]string, 0),
					Data: &rtapi.Notifications{
						Notifications: notifications,
					},
				}
				nodes := map[string]bool{}
				for i := 0; i < len(onlineusers); i++ {
					onlineuser := onlineusers[i]
					if onlineuser == nil || onlineuser.NodeId == "" || onlineuser.SessionId == "" {
						continue
					}
					push.SessionIDs = append(push.SessionIDs, onlineuser.SessionId)
					nodes[onlineuser.NodeId] = true
				}

				data, err := json.Marshal(push)
				if err != nil {
					n.i.GetLogger().Error("marshal push failed %v", err)
					continue
				}

				for nodeid := range nodes {
					resp, err := n.i.GetSend().RpcToNodeId(ctx, nodeid, models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, models.RPCID_GATESERVER_SEND, data, nil, models.RPC_TIMEOUT_DEFAULT)
					if err != nil {
						n.i.GetLogger().Error("send to %s %s failed %v %s", user_id, nodeid, err, string(resp))
						continue
					}
				}

			}

			// Stop pagination when reaching the last (incomplete) page.
			if len(sends) < limit {
				return
			}
		}
	}()

	return nil
}

func (n *NotifyGlobalDataStruct) NotificationsUpdate(ctx context.Context, updates ...runtime.NotificationUpdate) error {
	return n.i.GetNk().NotificationsUpdate(ctx, updates...)
}

func (n *NotifyGlobalDataStruct) NotificationsDelete(ctx context.Context, notifications []*runtime.NotificationDelete) error {
	return n.i.GetNk().NotificationsDelete(ctx, notifications)
}

func (n *NotifyGlobalDataStruct) NotificationsGetId(ctx context.Context, userID string, ids []string) ([]*runtime.Notification, error) {
	return n.i.GetNk().NotificationsGetId(ctx, userID, ids)
}

func (n *NotifyGlobalDataStruct) NotificationsDeleteId(ctx context.Context, userID string, ids []string) error {
	return n.i.GetNk().NotificationsDeleteId(ctx, userID, ids)
}

func (n *NotifyGlobalDataStruct) NotificationSave(ctx context.Context, notifications map[uuid.UUID][]*api.Notification) error {
	ids := make([]string, 0, len(notifications))
	userIds := make([]uuid.UUID, 0, len(notifications))
	subjects := make([]string, 0, len(notifications))
	contents := make([]string, 0, len(notifications))
	codes := make([]int32, 0, len(notifications))
	senderIds := make([]string, 0, len(notifications))
	query := `
INSERT INTO
	notification (id, user_id, subject, content, code, sender_id)
SELECT
	unnest($1::uuid[]),
	unnest($2::uuid[]),
	unnest($3::text[]),
	unnest($4::jsonb[]),
	unnest($5::smallint[]),
	unnest($6::uuid[]);
`
	for userID, no := range notifications {
		for _, un := range no {
			ids = append(ids, un.Id)
			userIds = append(userIds, userID)
			subjects = append(subjects, un.Subject)
			contents = append(contents, un.Content)
			codes = append(codes, un.Code)
			senderIds = append(senderIds, un.SenderId)
		}
	}

	if _, err := n.i.GetDb().ExecContext(ctx, query, ids, userIds, subjects, contents, codes, senderIds); err != nil {
		n.i.GetLogger().Error("Could not save notifications.", zap.Error(err))
		return err
	}

	return nil
}

type notificationCacheableCursor struct {
	NotificationID []byte
	CreateTime     int64
}

type notificationSession struct {
	SessionID string
	UserID    uuid.UUID
}
