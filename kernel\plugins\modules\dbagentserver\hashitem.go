package main

import (
	"database/sql"
	"errors"
	"fmt"
	"kernel/plugins/common"
	"kernel/plugins/models"
	"regexp"
	"strconv"
	"strings"
	"sync"

	"github.com/jmoiron/sqlx"
	json "github.com/json-iterator/go"
)

// 添加版本类型常量
const (
	VERSION_TYPE_INT64 = iota
	VERSION_TYPE_TIMESTAMP
)

type HashCacheItem struct {
	db               *sql.DB
	sqlxDB           *sqlx.DB
	keys             map[string]interface{}
	rowResultType    models.RowResultType
	tableName        string
	rowsSecondName   string
	queryargsql      string
	flushType        models.FlushType
	uniqueKeys       []string
	versionCheckName string
	Hash             map[string]interface{} `json:"hash"`
	dirtySet         map[string]struct{}    `json:"-"`
	lock             sync.RWMutex           `json:"-"`
}

func NewHashCacheItem(db *sql.DB, keys map[string]interface{}, tablename, queryargsql string, flushType models.FlushType, rowresult models.RowResultType, rowssecondname string, uniquekeys []string, versionCheckName string) *HashCacheItem {
	hashitem := &HashCacheItem{
		db:               db,
		sqlxDB:           sqlx.NewDb(db, "postgres"),
		keys:             make(map[string]interface{}),
		tableName:        tablename,
		rowsSecondName:   rowssecondname,
		rowResultType:    rowresult,
		queryargsql:      queryargsql,
		flushType:        flushType,
		uniqueKeys:       append([]string{}, uniquekeys...),
		versionCheckName: versionCheckName,
		Hash:             make(map[string]interface{}),
		dirtySet:         make(map[string]struct{}),
	}
	for k, v := range keys {
		hashitem.keys[k] = v
	}

	return hashitem
}

// 预编译正则表达式，避免重复编译
var validIdentifierRegexp = regexp.MustCompile(`^[a-zA-Z_][a-zA-Z0-9_]*$`)

// isValidIdentifier 校验标识符是否合法
func isValidIdentifier(s string) bool {
	return validIdentifierRegexp.MatchString(s)
}

// makePlaceholder 根据索引返回对应占位符（这里以 PostgreSQL 为例，生成 $1, $2, ...）
func makePlaceholder(i int) string {
	return fmt.Sprintf("$%d", i)
}

func (h *HashCacheItem) Get(k string) interface{} {
	h.lock.RLock()
	defer h.lock.RUnlock()
	v, ok := h.Hash[k]
	if ok {
		return v
	}
	return ""
}

func (h *HashCacheItem) Set(k string, v interface{}) {
	h.lock.Lock()
	defer h.lock.Unlock()
	h.Hash[k] = v
	h.dirtySet[k] = struct{}{}
}

func (h *HashCacheItem) Del(k string) {
	h.lock.Lock()
	defer h.lock.Unlock()
	delete(h.Hash, k)
	delete(h.dirtySet, k)
}

func (h *HashCacheItem) Size() int {
	h.lock.RLock()
	defer h.lock.RUnlock()
	return len(h.Hash)
}

func (h *HashCacheItem) Clear() {
	h.lock.Lock()
	defer h.lock.Unlock()
	h.Hash = make(map[string]interface{})
	h.dirtySet = make(map[string]struct{})
}

func (h *HashCacheItem) GetAll() map[string]interface{} {
	d := make(map[string]interface{})
	h.lock.RLock()
	defer h.lock.RUnlock()
	for k, v := range h.Hash {
		d[k] = v
	}
	return d
}

func (h *HashCacheItem) Exists(k string) bool {
	h.lock.RLock()
	defer h.lock.RUnlock()
	_, ok := h.Hash[k]
	return ok
}

func (h *HashCacheItem) MultiHset(kvs map[string]interface{}) {
	h.lock.Lock()
	defer h.lock.Unlock()
	for k, v := range kvs {
		h.Hash[k] = v
		h.dirtySet[k] = struct{}{}
	}
}

func (h *HashCacheItem) MultiHset2(kvs map[string]string) {
	h.lock.Lock()
	defer h.lock.Unlock()
	for k, v := range kvs {
		h.Hash[k] = v
		h.dirtySet[k] = struct{}{}
	}
}

func (h *HashCacheItem) MultiHget(keys ...string) map[string]interface{} {
	tmp := map[string]interface{}{}
	h.lock.RLock()
	defer h.lock.RUnlock()
	for i := 0; i < len(keys); i++ {
		k := keys[i]
		v, ok := h.Hash[k]
		if ok {
			tmp[k] = v
		}
	}
	return tmp
}

func (h *HashCacheItem) MultiHdel(keys ...string) {
	h.lock.Lock()
	defer h.lock.Unlock()
	for i := 0; i < len(keys); i++ {
		delete(h.Hash, keys[i])
		delete(h.dirtySet, keys[i])
	}
}

func (h *HashCacheItem) HIncrBy(k string, value int64) (int64, error) {
	h.lock.Lock()
	defer h.lock.Unlock()
	tmpv, ok := h.Hash[k]
	if ok {
		var retval int64 = 0
		switch tmpv.(type) {
		case int64:
			retval = tmpv.(int64) + value
			h.Hash[k] = retval
		case string:
			val, err := strconv.ParseInt(tmpv.(string), 10, 64)
			if err != nil {
				return 0, errors.New("data error")
			}
			retval = val + value
			h.Hash[k] = retval
		default:
			return 0, errors.New("data error")
		}
		h.dirtySet[k] = struct{}{}
		return retval, nil
	} else {
		h.dirtySet[k] = struct{}{}
		h.Hash[k] = value
		return value, nil
	}
}

func (h *HashCacheItem) HIncrByFloat(k string, value float64) (float64, error) {
	h.lock.Lock()
	defer h.lock.Unlock()
	tmpv, ok := h.Hash[k]
	if ok {
		var retval float64 = 0
		switch tmpv.(type) {
		case float64:
			retval = tmpv.(float64) + value
			h.Hash[k] = retval
		case string:
			val, err := strconv.ParseFloat(tmpv.(string), 64)
			if err != nil {
				return 0, errors.New("data error")
			}
			retval = val + value
			h.Hash[k] = retval
		default:
			return 0, errors.New("data error")
		}
		h.dirtySet[k] = struct{}{}
		return retval, nil
	} else {
		h.Hash[k] = value
		h.dirtySet[k] = struct{}{}
		return value, nil
	}
}

func (h *HashCacheItem) Copy() *HashCacheItem {
	newhash := make(map[string]interface{})
	h.lock.RLock()
	defer h.lock.RUnlock()
	for k, v := range h.Hash {
		newhash[k] = v
	}
	return &HashCacheItem{
		Hash: newhash,
	}
}

func (h *HashCacheItem) writeToDB() (sql.Result, error) {
	if h.rowResultType == models.ROWRESULT_TYPE_ONLYONE {
		// 准备需要更新的数据
		data := make(map[string]interface{})
		for k := range h.dirtySet {
			data[k] = h.Hash[k]
		}

		if len(data) == 0 {
			return nil, nil
		}

		// 附加版本号，一起更新
		if h.versionCheckName != "" {
			currentVersion, err := h.getVersionValue()
			if err == nil {
				data[h.versionCheckName] = currentVersion + 1
			} else {
				fmt.Printf("getVersionValue error: %v", err)
				return nil, err
			}
		}

		// 构建 SQL 语句
		query, values, err := h.buildInsertOrUpdate(data)
		if err != nil {
			fmt.Printf("buildInsertQuery error: %v", err)
			return nil, err
		}

		// 执行 SQL 语句
		result, err := h.db.Exec(query, values...)
		if err != nil {
			fmt.Printf("Flush Exec %s error: %v", query, err)
			return result, err
		}

		// 检查是否真的更新了数据
		if h.versionCheckName != "" {
			rowsAffected, err := result.RowsAffected()
			if err != nil {
				return nil, err
			}

			if rowsAffected == 0 {
				return nil, models.ErrDBResVersionConflict
			}

			h.Set(h.versionCheckName, data[h.versionCheckName]) // 更新cache
		}

		// 清空脏数据标记
		h.dirtySet = make(map[string]struct{})
		return result, err
	} else {
		// 准备需要更新的数据
		for k := range h.dirtySet {
			hashval, ok := h.Hash[k].(map[string]interface{})
			if !ok {
				fmt.Printf("hashval is not a map[string]interface{}: %v", h.Hash[k])
				continue
			}
			data := make(map[string]interface{})
			for kk, vv := range hashval {
				data[kk] = vv
			}
			if len(data) == 0 {
				continue
			}
			// data中添加二级键
			if data[h.rowsSecondName] == nil {
				data[h.rowsSecondName] = k
			}
			query, values, err := h.buildInsertOrUpdate(data)
			if err != nil {
				fmt.Printf("buildInsertQuery error: %v", err)
				return nil, err
			}
			result, err := h.db.Exec(query, values...)
			if err != nil {
				fmt.Printf("Flush Exec %s error: %v", query, err)
				return result, err
			}

			// 清空脏数据标记
			delete(h.dirtySet, k)
		}
		return nil, nil
	}
}

// 添加获取版本值的方法
func (h *HashCacheItem) getVersionValue() (int64, error) {
	if h.versionCheckName == "" || len(h.Hash) == 0 {
		return 0, nil
	}

	versionValue, ok := h.Hash[h.versionCheckName]
	if !ok {
		return 0, fmt.Errorf("version not found in cache")
	}

	version, ok := versionValue.(int64)
	if !ok {
		return 0, fmt.Errorf("version type error: expected int64")
	}

	return version, nil
}

// 修改 buildInsertOrUpdate 方法中的版本检查部分
func (h *HashCacheItem) buildInsertOrUpdate(data map[string]interface{}) (string, []interface{}, error) {
	// 检查数据是否为空
	if len(data) == 0 {
		return "", nil, fmt.Errorf("data map is empty")
	}

	// 校验表名
	if !isValidIdentifier(h.tableName) {
		return "", nil, fmt.Errorf("invalid table name: %s", h.tableName)
	}

	// 预分配容量以减少内存分配
	keyNames := make([]string, 0, len(h.keys))
	for k := range h.keys {
		if !isValidIdentifier(k) {
			return "", nil, fmt.Errorf("invalid key name: %s", k)
		}
		keyNames = append(keyNames, k)
	}

	// 检查键名列表是否为空
	if len(keyNames) == 0 {
		return "", nil, fmt.Errorf("no valid keys found")
	}

	// 确保数据中包含所有键
	if h.rowResultType == models.ROWRESULT_TYPE_ONLYONE {
		// 对于单行结果，批量处理键
		for _, keyName := range keyNames {
			if _, exists := data[keyName]; !exists {
				data[keyName] = h.keys[keyName]
			}
		}
	} else {
		// 对于多行结果，批量处理主键
		for _, keyName := range keyNames {
			if keyName != h.rowsSecondName && data[keyName] == nil {
				data[keyName] = h.keys[keyName]
			}
		}

		// 检查二级键
		if data[h.rowsSecondName] == nil {
			return "", nil, fmt.Errorf("secondary key %s not found in data for multi-row result", h.rowsSecondName)
		}
	}

	// 构建列名和占位符
	columns := make([]string, 0, len(data))
	placeholders := make([]string, 0, len(data))
	values := make([]interface{}, 0, len(data))
	updateParts := make([]string, 0, len(data))

	i := 1
	for col, val := range data {
		if !isValidIdentifier(col) {
			return "", nil, fmt.Errorf("invalid column name: %s", col)
		}
		columns = append(columns, fmt.Sprintf("\"%s\"", col))
		placeholders = append(placeholders, fmt.Sprintf("$%d", i))
		values = append(values, val)

		// 为UPDATE部分构建SET子句，但排除主键
		isPrimaryKey := false
		for _, keyName := range keyNames {
			if col == keyName {
				isPrimaryKey = true
				break
			}
		}

		// 对于多行结果，二级键也应该被排除在更新列表之外
		if h.rowResultType == models.ROWRESULT_TYPE_MULTI && col == h.rowsSecondName {
			isPrimaryKey = true
		}

		if !isPrimaryKey {
			updateParts = append(updateParts, fmt.Sprintf("\"%s\" = $%d", col, i))
		}

		i++
	}

	// 构建INSERT部分
	query := fmt.Sprintf("INSERT INTO \"%s\" (%s) VALUES (%s)",
		h.tableName,
		strings.Join(columns, ", "),
		strings.Join(placeholders, ", "))

	// 添加ON CONFLICT部分
	conflictKeys := make([]string, 0, len(keyNames))
	for _, keyName := range keyNames {
		conflictKeys = append(conflictKeys, fmt.Sprintf("\"%s\"", keyName))
	}

	// 对于多行结果，需要将二级键也添加到冲突检测中
	if h.rowResultType == models.ROWRESULT_TYPE_MULTI {
		// 检查二级键是否已经在冲突键列表中
		secondKeyExists := false
		for _, keyName := range conflictKeys {
			if fmt.Sprintf("\"%s\"", h.rowsSecondName) == keyName {
				secondKeyExists = true
				break
			}
		}

		if !secondKeyExists {
			conflictKeys = append(conflictKeys, fmt.Sprintf("\"%s\"", h.rowsSecondName))
		}
	}

	// 如果有需要更新的字段，添加DO UPDATE部分
	if len(updateParts) > 0 {
		// 添加版本检查条件
		if h.rowResultType == models.ROWRESULT_TYPE_ONLYONE && h.versionCheckName != "" {
			// 在WHERE子句中添加版本检查，使用缓存中的版本号
			currentVersion, err := h.getVersionValue()
			if err != nil {
				return "", nil, err
			}
			versionIndex := i
			values = append(values, currentVersion)
			i++

			query += fmt.Sprintf(" ON CONFLICT (%s) DO UPDATE SET %s WHERE \"%s\".\"%s\" = $%d",
				strings.Join(conflictKeys, ", "),
				strings.Join(updateParts, ", "),
				h.tableName,
				h.versionCheckName,
				versionIndex)
		} else {
			query += fmt.Sprintf(" ON CONFLICT (%s) DO UPDATE SET %s",
				strings.Join(conflictKeys, ", "),
				strings.Join(updateParts, ", "))
		}
	} else {
		// 如果没有需要更新的字段，则什么都不做
		query += fmt.Sprintf(" ON CONFLICT (%s) DO NOTHING",
			strings.Join(conflictKeys, ", "))
	}

	return query, values, nil
}

// buildInsertQuery 构建带 INSERT SQL 语句
func (h *HashCacheItem) buildInsertQuery(data map[string]interface{}) (string, []interface{}, error) {
	// 检查数据是否为空
	if len(data) == 0 {
		return "", nil, fmt.Errorf("data map is empty")
	}

	// 校验表名
	if !isValidIdentifier(h.tableName) {
		return "", nil, fmt.Errorf("invalid table name: %s", h.tableName)
	}

	// 预分配容量以减少内存分配
	keyNames := make([]string, 0, len(h.keys))
	for k := range h.keys {
		if !isValidIdentifier(k) {
			return "", nil, fmt.Errorf("invalid key name: %s", k)
		}
		keyNames = append(keyNames, k)
	}

	// 检查键名列表是否为空
	if len(keyNames) == 0 {
		return "", nil, fmt.Errorf("no valid keys found")
	}

	// 确保数据中包含所有键
	if h.rowResultType == models.ROWRESULT_TYPE_ONLYONE {
		// 对于单行结果，批量处理键
		for _, keyName := range keyNames {
			if _, exists := data[keyName]; !exists {
				data[keyName] = h.keys[keyName]
			}
		}
	} else {
		// 对于多行结果，批量处理主键
		for _, keyName := range keyNames {
			if keyName != h.rowsSecondName && data[keyName] == nil {
				data[keyName] = h.keys[keyName]
			}
		}

		// 检查二级键
		if data[h.rowsSecondName] == nil {
			return "", nil, fmt.Errorf("secondary key %s not found in data for multi-row result", h.rowsSecondName)
		}
	}

	// 一次性分配足够的容量，避免后续扩容
	columns := make([]string, 0, len(data))
	placeholders := make([]string, 0, len(data))
	values := make([]interface{}, 0, len(data))

	// 使用 strings.Builder 预分配足够的容量
	var queryBuilder strings.Builder
	queryBuilder.Grow(512) // 预分配足够的空间，避免多次扩容

	// 一次性处理所有列
	i := 1
	for col, val := range data {
		if !isValidIdentifier(col) {
			return "", nil, fmt.Errorf("invalid column name: %s", col)
		}

		quotedCol := fmt.Sprintf("\"%s\"", col)
		columns = append(columns, quotedCol)
		placeholders = append(placeholders, makePlaceholder(i))
		values = append(values, val)
		i++
	}

	// 检查是否有列需要更新
	if len(columns) == 0 {
		return "", nil, fmt.Errorf("no valid columns to update")
	}

	// 构造插入SQL语句
	queryBuilder.WriteString("INSERT INTO ")
	queryBuilder.WriteString(fmt.Sprintf("\"%s\"", h.tableName))
	queryBuilder.WriteString(" (")
	queryBuilder.WriteString(strings.Join(columns, ", "))
	queryBuilder.WriteString(") VALUES (")
	queryBuilder.WriteString(strings.Join(placeholders, ", "))
	queryBuilder.WriteString(")")

	return queryBuilder.String(), values, nil
}

// 修改 buildUpdateQuery 方法中的版本检查部分
func (h *HashCacheItem) buildUpdateQuery(data map[string]interface{}) (string, []interface{}, error) {
	// 检查数据是否为空
	if len(data) == 0 {
		return "", nil, fmt.Errorf("data map is empty")
	}

	// 校验表名
	if !isValidIdentifier(h.tableName) {
		return "", nil, fmt.Errorf("invalid table name: %s", h.tableName)
	}

	// 预分配容量以减少内存分配
	keyNames := make([]string, 0, len(h.keys))
	for k := range h.keys {
		if !isValidIdentifier(k) {
			return "", nil, fmt.Errorf("invalid key name: %s", k)
		}
		keyNames = append(keyNames, k)
	}

	// 检查键名列表是否为空
	if len(keyNames) == 0 {
		return "", nil, fmt.Errorf("no valid keys found")
	}

	// 一次性分配足够的容量，避免后续扩容
	updateParts := make([]string, 0, len(data))
	values := make([]interface{}, 0, len(data)+len(h.keys))

	// 使用 strings.Builder 预分配足够的容量
	var queryBuilder strings.Builder
	queryBuilder.Grow(512) // 预分配足够的空间，避免多次扩容

	// 一次性处理所有需要更新的列
	i := 1
	for col, val := range data {
		// 跳过主键列
		isKey := false
		for _, keyName := range keyNames {
			if col == keyName {
				isKey = true
				break
			}
		}

		// 如果是多行结果，还需要检查二级键
		if h.rowResultType != models.ROWRESULT_TYPE_ONLYONE && col == h.rowsSecondName {
			isKey = true
		}

		if isKey {
			continue
		}

		if !isValidIdentifier(col) {
			return "", nil, fmt.Errorf("invalid column name: %s", col)
		}

		updateParts = append(updateParts, fmt.Sprintf("\"%s\" = %s", col, makePlaceholder(i)))
		values = append(values, val)
		i++
	}

	// 检查是否有列需要更新
	if len(updateParts) == 0 {
		return "", nil, fmt.Errorf("no valid columns to update")
	}

	// 构造WHERE条件
	whereConditions := make([]string, 0, len(h.keys)+1)
	for _, keyName := range keyNames {
		whereConditions = append(whereConditions, fmt.Sprintf("\"%s\" = %s", keyName, makePlaceholder(i)))
		values = append(values, h.keys[keyName])
		i++
	}

	// 如果是多行结果，还需要添加二级键条件
	if h.rowResultType != models.ROWRESULT_TYPE_ONLYONE {
		if secondKeyVal, ok := data[h.rowsSecondName]; ok {
			whereConditions = append(whereConditions, fmt.Sprintf("\"%s\" = %s", h.rowsSecondName, makePlaceholder(i)))
			values = append(values, secondKeyVal)
			i++
		} else {
			return "", nil, fmt.Errorf("secondary key %s not found in data for multi-row result", h.rowsSecondName)
		}
	}

	// 添加版本
	if h.rowResultType == models.ROWRESULT_TYPE_ONLYONE && h.versionCheckName != "" {
		// 获取当前版本号
		currentVersion, err := h.getVersionValue()
		if err != nil {
			return "", nil, err
		}

		// 在WHERE子句中添加版本检查，使用缓存中的版本号
		whereConditions = append(whereConditions, fmt.Sprintf("\"%s\" = $%d", h.versionCheckName, i))
		values = append(values, currentVersion)
		i++
	}

	// 构造更新SQL语句
	queryBuilder.WriteString("UPDATE ")
	queryBuilder.WriteString(fmt.Sprintf("\"%s\"", h.tableName))
	queryBuilder.WriteString(" SET ")
	queryBuilder.WriteString(strings.Join(updateParts, ", "))
	queryBuilder.WriteString(" WHERE ")
	queryBuilder.WriteString(strings.Join(whereConditions, " AND "))

	return queryBuilder.String(), values, nil
}

func (h *HashCacheItem) String(kvs map[string]interface{}) string {
	h.lock.RLock()
	defer h.lock.RUnlock()
	if len(h.Hash) == 0 {
		return models.ErrDBResEmpty.Error()
	}
	if h.rowResultType == models.ROWRESULT_TYPE_ONLYONE {
		if len(kvs) == 0 {
			v, _ := json.MarshalToString(h.Hash)
			return v
		} else {
			tmp := make(map[string]interface{}, len(kvs))
			for k, _ := range kvs {
				tmp[k] = h.Hash[k]
			}
			v, _ := json.MarshalToString(tmp)
			return v
		}
	} else {
		if len(kvs) == 0 {
			tmplist := make([]map[string]interface{}, 0, len(h.Hash))
			for _, v := range h.Hash {
				tmplist = append(tmplist, v.(map[string]interface{}))
			}
			v, _ := json.MarshalToString(tmplist)
			return v
		} else {
			tmplist := make([]map[string]interface{}, 0, len(kvs))
			for k, _ := range kvs {
				if v, ok := h.Hash[k]; ok {
					tmplist = append(tmplist, v.(map[string]interface{}))
				}
			}
			if len(tmplist) == 0 {
				return models.ErrDBResEmpty.Error()
			}
			v, _ := json.MarshalToString(tmplist)
			return v
		}
	}
}

func ConvertRowMap(rawMap map[string]interface{}) map[string]interface{} {
	converted := make(map[string]interface{}, len(rawMap))
	for k, v := range rawMap {
		switch val := v.(type) {
		case nil:
			converted[k] = nil
		case []byte:
			// 尝试智能转换
			s := string(val)

			// 先尝试int64
			if i64, err := strconv.ParseInt(s, 10, 64); err == nil {
				converted[k] = i64
				continue
			}

			// 尝试float64
			if f64, err := strconv.ParseFloat(s, 64); err == nil {
				converted[k] = f64
				continue
			}

			// 尝试bool
			if b, err := strconv.ParseBool(s); err == nil {
				converted[k] = b
				continue
			}

			// 都不成功，保留字符串
			converted[k] = s

		default:
			// 其他类型直接赋值
			converted[k] = val
		}
	}

	return converted
}

func (h *HashCacheItem) Query() (bool, error) {
	h.lock.Lock()
	defer h.lock.Unlock()

	queryKeys := make([]string, 0, len(h.keys))
	queryParams := make([]interface{}, 0, len(h.keys))
	paramIndex := 1

	for k, v := range h.keys {
		if !isValidIdentifier(k) {
			return false, fmt.Errorf("invalid column name: %s", k)
		}
		queryKeys = append(queryKeys, fmt.Sprintf("\"%s\" = $%d", k, paramIndex))
		queryParams = append(queryParams, v)
		paramIndex++
	}

	queryStr := fmt.Sprintf("SELECT %s FROM \"%s\" WHERE %s", h.queryargsql, h.tableName, strings.Join(queryKeys, " AND "))

	// 如果结构体类型未创建，使用原来的MapScan方式
	rowMap := make(map[string]interface{}, 32)
	results := make([]map[string]interface{}, 0, 1)

	rows, err := h.sqlxDB.Queryx(queryStr, queryParams...)
	if err != nil {
		return false, fmt.Errorf("query error: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		for k := range rowMap {
			delete(rowMap, k)
		}

		if err := rows.MapScan(rowMap); err != nil {
			return false, fmt.Errorf("failed to scan row: %w", err)
		}

		convertedMap := ConvertRowMap(rowMap)
		results = append(results, convertedMap)
	}
	if err := rows.Err(); err != nil {
		return false, fmt.Errorf("rows iteration error: %w", err)
	}

	if h.rowResultType == models.ROWRESULT_TYPE_ONLYONE {
		if len(results) > 1 {
			return false, fmt.Errorf("multiple rows found for key: %v", h.keys)
		}
		if len(results) == 1 {
			for k, v := range results[0] {
				h.Hash[k] = v
			}
		}
	} else {
		for _, v := range results {
			secondval := v[h.rowsSecondName]
			if secondval == nil {
				return false, fmt.Errorf("rowsSecondName value is nil: %v", h.rowsSecondName)
			}
			h.Hash[common.Interface2String(secondval)] = v
		}
	}

	return true, nil
}

func (h *HashCacheItem) Delete(secondval interface{}) (sql.Result, error) {
	if secondval == nil && h.rowResultType == models.ROWRESULT_TYPE_MULTI {
		return nil, fmt.Errorf("secondval value is nil")
	}

	h.lock.Lock()
	defer h.lock.Unlock()

	// 构建删除条件
	whereConditions := make([]string, 0, len(h.keys))
	values := make([]interface{}, 0, len(h.keys))
	i := 1

	for k, v := range h.keys {
		if !isValidIdentifier(k) {
			return nil, fmt.Errorf("无效的列名: %s", k)
		}
		whereConditions = append(whereConditions, fmt.Sprintf("\"%s\" = $%d", k, i))
		values = append(values, v)
		i++
	}

	if len(whereConditions) == 0 {
		return nil, fmt.Errorf("没有有效的键用于删除操作")
	}

	// 处理二级键情况
	if h.rowResultType == models.ROWRESULT_TYPE_MULTI {
		if !isValidIdentifier(h.rowsSecondName) {
			return nil, fmt.Errorf("无效的二级键名: %s", h.rowsSecondName)
		}
		whereConditions = append(whereConditions, fmt.Sprintf("\"%s\" = $%d", h.rowsSecondName, i))
		values = append(values, secondval)
	}

	query := fmt.Sprintf("DELETE FROM \"%s\" WHERE %s", h.tableName, strings.Join(whereConditions, " AND "))
	result, err := h.db.Exec(query, values...)
	if err != nil {
		return nil, err
	}

	// 处理二级键情况
	if h.rowResultType == models.ROWRESULT_TYPE_MULTI {
		// 只删除内存中对应的二级键数据
		strSecondVal := common.Interface2String(secondval)
		delete(h.Hash, strSecondVal)
		delete(h.dirtySet, strSecondVal)
	} else {
		// 单行结果或未指定二级键时，清空所有数据
		h.Hash = make(map[string]interface{})
		h.dirtySet = make(map[string]struct{})
	}

	return result, nil
}

func (h *HashCacheItem) Insert(data map[string]interface{}) (sql.Result, error) {
	h.lock.Lock()
	defer h.lock.Unlock()

	if h.rowResultType == models.ROWRESULT_TYPE_ONLYONE {
		// 附加版本号
		if h.versionCheckName != "" {
			if _, ok := data[h.versionCheckName]; !ok {
				currentVersion, err := h.getVersionValue()
				if err == nil {
					data[h.versionCheckName] = currentVersion + 1
				} else {
					return nil, err
				}
			}
		}
		query, values, err := h.buildInsertQuery(data)
		if err != nil {
			return nil, err
		}
		result, err := h.db.Exec(query, values...)
		if err != nil {
			return nil, err
		}

		// 后更新缓存
		for k, v := range data {
			h.Hash[k] = v
		}
		return result, nil
	} else {
		// 检查rowsSecondName是否存在
		secondval := data[h.rowsSecondName]
		if secondval == nil {
			return nil, fmt.Errorf("rowsSecondName value is nil: %v", h.rowsSecondName)
		}
		strsecondval := common.Interface2String(secondval)
		hashval, ok := h.Hash[strsecondval].(map[string]interface{})
		if !ok {
			hashval = make(map[string]interface{})
			h.Hash[strsecondval] = hashval
		}
		query, values, err := h.buildInsertQuery(data)
		if err != nil {
			return nil, err
		}
		result, err := h.db.Exec(query, values...)
		if err != nil {
			return nil, err
		}

		// 后更新缓存
		for k, v := range data {
			hashval[k] = v
		}
		return result, nil
	}

}

func (h *HashCacheItem) Update(kvs map[string]interface{}) (sql.Result, error) {
	h.lock.Lock()
	defer h.lock.Unlock()

	if h.rowResultType == models.ROWRESULT_TYPE_ONLYONE {
		// 附加版本号，一起更新
		if h.versionCheckName != "" && kvs[h.versionCheckName] == nil {
			currentVersion, err := h.getVersionValue()
			if err == nil {
				kvs[h.versionCheckName] = currentVersion + 1
			} else {
				return nil, err
			}
		}

		query, values, err := h.buildUpdateQuery(kvs)
		if err != nil {
			return nil, err
		}

		result, err := h.db.Exec(query, values...)
		if err != nil {
			return nil, err
		}

		// 检查是否真的更新了数据
		if h.versionCheckName != "" {
			rowsAffected, err := result.RowsAffected()
			if err != nil {
				return nil, err
			}

			if rowsAffected == 0 {
				return nil, models.ErrDBResVersionConflict
			}
		}

		// 更新缓存
		for k, v := range kvs {
			h.Hash[k] = v
		}

		return result, nil
	} else {
		// 检查rowsSecondName是否存在
		secondval := kvs[h.rowsSecondName]
		if secondval == nil {
			return nil, fmt.Errorf("rowsSecondName value is nil: %v", h.rowsSecondName)
		}
		strsecondval := common.Interface2String(secondval)
		hashval, ok := h.Hash[strsecondval].(map[string]interface{})
		if !ok {
			hashval = make(map[string]interface{})
			h.Hash[strsecondval] = hashval
		}

		query, values, err := h.buildUpdateQuery(kvs)
		if err != nil {
			return nil, err
		}
		result, err := h.db.Exec(query, values...)
		if err != nil {
			return nil, err
		}

		// 后更新缓存
		for k, v := range kvs {
			hashval[k] = v
		}

		return result, nil
	}
}

func (h *HashCacheItem) AsyncUpdate(kvs map[string]interface{}) {
	h.lock.Lock()
	defer h.lock.Unlock()

	if h.rowResultType == models.ROWRESULT_TYPE_ONLYONE {
		// 异步更新，删除版本号
		if h.versionCheckName != "" {
			delete(kvs, h.versionCheckName)
		}
		for k, v := range kvs {
			h.Hash[k] = v
			h.dirtySet[k] = struct{}{}
		}
	} else {
		secondval := kvs[h.rowsSecondName]
		if secondval == nil {
			fmt.Printf("rowsSecondName value is nil: %v\n", h.rowsSecondName)
			return
		}
		strsecondval := common.Interface2String(secondval)
		hashval, ok := h.Hash[strsecondval].(map[string]interface{})
		if !ok {
			hashval = make(map[string]interface{})
			h.Hash[strsecondval] = hashval
		}
		for k, v := range kvs {
			hashval[k] = v
		}
		h.dirtySet[strsecondval] = struct{}{}
	}
}

func (h *HashCacheItem) IsDirty() bool {
	h.lock.RLock()
	defer h.lock.RUnlock()
	return len(h.dirtySet) > 0
}

func (h *HashCacheItem) Flush() error {
	h.lock.Lock()
	defer h.lock.Unlock()
	if _, err := h.writeToDB(); err != nil {
		return err
	}
	return nil
}

func (h *HashCacheItem) FlushType() models.FlushType {
	return h.flushType
}

func (h *HashCacheItem) GetRowResultType() models.RowResultType {
	return h.rowResultType
}

func (h *HashCacheItem) GetUniques() map[string]interface{} {
	h.lock.RLock()
	defer h.lock.RUnlock()
	if len(h.uniqueKeys) == 0 {
		return nil
	}
	if h.rowResultType == models.ROWRESULT_TYPE_ONLYONE {
		uniques := make(map[string]interface{}, len(h.uniqueKeys))
		for _, v := range h.uniqueKeys {
			uniques[v] = h.Hash[v]
		}
		return uniques
	} else {
		return nil
	}
}

func (h *HashCacheItem) TableName() string {
	return h.tableName
}
