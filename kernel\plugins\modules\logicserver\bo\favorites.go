package bo

import (
	"bytes"
	"fmt"
	"kernel/plugins/common"
	"kernel/plugins/models"
	"reflect"

	json "github.com/json-iterator/go"
)

type Favorites struct {
	UserId string `json:"user_id,omitempty"`
	List   string `json:"list,omitempty"`
}

func (o *Favorites) GetTable() string {
	return "favorites"
}

func (o *Favorites) GetKeyName() string {
	return "user_id"
}

func (o *Favorites) GetUniqueKeys() []string {
	return nil
}

func (o *Favorites) GetSecondKeyName() string {
	return ""
}

func (o *Favorites) GetRowResultType() models.RowResultType {
	return models.ROWRESULT_TYPE_ONLYONE
}

func (o *Favorites) GetQueryArgs() string {
	return "*"
}
func (o *Favorites) GetFlushType() models.FlushType {
	return models.FLUSH_TYPE_ASYNC
}

func (o *Favorites) GetCacheLevel() models.CacheLevel {
	return models.CACHE_LEVEL_NORMAL
}

func (o *Favorites) GetVersionName() string {
	return ""
}

func (o *Favorites) Marshal() ([]byte, error) {
	return json.Marshal(o)
}

func (o *Favorites) MarshalToMap(filter map[string]struct{}) map[string]interface{} {
	dst := make(map[string]interface{})
	err := common.StrcutToMap(o, &dst)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	for k := range filter {
		delete(dst, k)
	}
	return dst
}

func (o *Favorites) Unmarshal(buf []byte) error {
	d := json.NewDecoder(bytes.NewReader(buf))
	d.UseNumber()
	return d.Decode(o)
}

func (o *Favorites) Clear() {
	p := reflect.ValueOf(o).Elem()
	p.Set(reflect.Zero(p.Type()))
}
