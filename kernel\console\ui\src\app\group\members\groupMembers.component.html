<ngb-alert [dismissible]="false" type="danger" class="mb-3" *ngIf="error">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">An error occurred: {{error}}</h6>
</ngb-alert>

<div *ngIf="!group.open && editionAllowed()" class="btn-group mb-1" ngbDropdown>
  <button type="button" class="btn btn-outline-secondary" ngbDropdownToggle>
    <span *ngIf="!activeState || activeState === ''">Add by membership state</span>
    <span *ngIf="activeState && activeState !== ''">{{activeState}}</span>
  </button>
  <div class="dropdown-menu" ngbDropdownMenu>
    <button *ngFor="let f of states" type="button" ngbDropdownItem (click)="activeState = f;">{{f}}</button>
  </div>
</div>

<div *ngIf="editionAllowed()" class="row no-gutters mb-4">
  <div class="col d-flex justify-content-between no-gutters align-items-center">
    <div class="col-md-9">
      <form [formGroup]="addForm">
        <div class="input-group">
          <input id="ids" type="text" class="form-control border-right-0" formControlName="ids" placeholder="Add user IDs as members, comma-separated"/>
          <div class="input-group-append">
            <div class="btn-group">
              <button type="submit" class="btn btn-primary dropdown-radius-left" (click)="add()">Add</button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<div class="row no-gutters">
  <table class="table table-sm table-hover table-bordered">
    <thead class="thead-light">
      <tr>
        <th style="width: 320px">User ID</th>
        <th>Name</th>
        <th style="width: 300px">State</th>
        <th style="width: 180px">Update Time</th>
        <th style="width: 70px" *ngIf="editionAllowed()">Demote</th>
        <th style="width: 70px" *ngIf="editionAllowed()">Promote</th>
        <th style="width: 90px" *ngIf="editionAllowed()">Remove</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngIf="members.length === 0">
        <td colSpan="7" class="text-muted">No group membership found.</td>
      </tr>

      <tr *ngFor="let m of members; index as i">
        <td (click)="viewAccount(m)">{{m.user.id}}</td>
        <td (click)="viewAccount(m)">{{m.user.username}}</td>
        <td (click)="viewAccount(m)">
          <span *ngIf="m.state === 0">Superadmin (0)</span>
          <span *ngIf="m.state === 1">Admin (1)</span>
          <span *ngIf="m.state === 2">Member (2)</span>
          <span *ngIf="m.state === 3">Join Request (3)</span>
          <span *ngIf="m.state === 4">Banned (4)</span>
        </td>
        <td (click)="viewAccount(m)">{{m.user.update_time}}</td>
        <td *ngIf="editionAllowed()" class="text-center">
          <button type="button" class="btn btn-sm btn-outline-secondary" (click)="demoteGroupUser($event, i, m);">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-down" viewBox="0 0 16 16">
            <path fill-rule="evenodd" d="M8 1a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L7.5 13.293V1.5A.5.5 0 0 1 8 1z"/>
          </svg>
        </button></td>
        <td *ngIf="editionAllowed()" class="text-center">
          <button type="button" class="btn btn-sm btn-outline-secondary" (click)="promoteGroupUser($event, i, m);">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-up" viewBox="0 0 16 16">
            <path fill-rule="evenodd" d="M8 15a.5.5 0 0 0 .5-.5V2.707l3.146 3.147a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V14.5a.5.5 0 0 0 .5.5z"/>
          </svg>
        </button></td>
        <td *ngIf="editionAllowed()" class="text-center align-middle">
          <button type="button" class="btn btn-sm btn-danger" (click)="deleteGroupUser($event, i, m);">Delete</button>
        </td>
      </tr>
    </tbody>
  </table>
</div>
