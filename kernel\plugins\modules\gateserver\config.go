package main

import (
	"kernel/plugins/models"
)

type CustomConfig struct {
	Config    *GateServerConfig       `json:"gate_server_config,omitempty" yaml:"gate_server_config,omitempty"`
	AccessLog *models.AccessLogConfig `json:"access_log,omitempty" yaml:"access_log,omitempty"`
	Logger    *models.LoggerConfig    `json:"logger,omitempty" yaml:"logger,omitempty"`
}

type GateServerConfig struct {
	NodeConfig        *models.NodeConfig       `json:"node_config,omitempty" yaml:"node_config,omitempty"`
	WatchSvcGroupList []string                 `json:"watch_svc_group_list,omitempty" yaml:"watch_svc_group_list,omitempty"`
	Routes            map[string]*models.Route `json:"routes,omitempty" yaml:"routes,omitempty"`             // 代理转发路由，key: urlpath
	IPWhiteList       map[string][]string      `json:"ipwhite_list,omitempty" yaml:"ipwhite_list,omitempty"` //路由+ip级别白名单列表 ip:每秒访问次数 (0.0.0.0或者* 表示不限制ip), (支持ip段 ip/掩码:次数)
	IPBlackList       map[string][]string      `json:"ipblack_list,omitempty" yaml:"ipblack_list,omitempty"`
	Limiters          map[string]string        `json:"limiters,omitempty"` // 路由级限流器列表， (路由:每秒访问次数)(路由:访问次数-突发上限-n秒内)，如果路由为*，匹配所有
}
