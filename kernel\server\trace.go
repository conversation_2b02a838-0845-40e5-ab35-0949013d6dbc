package server

import (
	"context"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.21.0"
	"go.uber.org/zap"
)

func InitTracer(config Config, startupLogger *zap.Logger) func() {
	service := config.GetName()
	grpcEndpoint := config.GetTrace().GetGrpcEndpoint()
	var tp *sdktrace.TracerProvider
	if config.GetTrace().GetEnabled() && grpcEndpoint != "" {
		otelExporter, err := otlptracegrpc.New(context.Background(),
			otlptracegrpc.WithInsecure(),
			otlptracegrpc.WithEndpoint(grpcEndpoint),
		)
		if err != nil {
			startupLogger.Fatal("Failed to create otel exporter", zap.Error(err))
			return nil
		}
		tp = sdktrace.NewTracerProvider(
			sdktrace.WithBatcher(otelExporter),
			sdktrace.WithResource(resource.NewWithAttributes(
				semconv.SchemaURL,
				semconv.ServiceName(service),
			)),
		)
	} else {
		tp = sdktrace.NewTracerProvider(
			sdktrace.WithSampler(sdktrace.AlwaysSample()),
			sdktrace.WithResource(resource.NewWithAttributes(
				semconv.SchemaURL,
				semconv.ServiceName(service),
			)),
		)
	}

	otel.SetTracerProvider(tp)
	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(
		propagation.TraceContext{},
		propagation.Baggage{},
	))
	return func() { _ = tp.Shutdown(context.Background()) }
}
