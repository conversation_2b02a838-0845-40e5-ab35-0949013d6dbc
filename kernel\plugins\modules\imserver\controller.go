package main

import (
	"bytes"
	"context"
	"database/sql"
	"errors"
	"kernel/kernel-common/rtapi"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"

	json "github.com/json-iterator/go"

	"github.com/gofrs/uuid/v5"
	"google.golang.org/protobuf/encoding/protojson"
)

const byteBracket byte = '{'

var (
	errChannelMessageNotFound = errors.New("channel message not found")
	errChannelMessagePersist  = errors.New("error persisting channel message")
)

type Controller struct {
	server *ImServerModule
}

func NewController(s *ImServerModule) *Controller {
	return &Controller{
		server: s,
	}
}

func (c *Controller) ChannelRt(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	in := &rtapi.Envelope{}
	err := protojson.Unmarshal([]byte(payload), in)
	if err != nil {
		return "", err
	}

	var out *rtapi.Envelope
	switch in.Message.(type) {
	case *rtapi.Envelope_ChannelJoin:
		out, err = c.channelJoin(ctx, in)
	case *rtapi.Envelope_ChannelLeave:
		out, err = c.channelLeave(ctx, in)
	case *rtapi.Envelope_ChannelMessageSend:
		out, err = c.channelMessageSend(ctx, in)
	case *rtapi.Envelope_ChannelMessageUpdate:
		out, err = c.channelMessageUpdate(ctx, in)
	case *rtapi.Envelope_ChannelMessageRemove:
		out, err = c.channelMessageRemove(ctx, in)
	default:
		return "", errors.New("unknown message type")
	}
	if err != nil {
		return "", err
	}

	resp, err := protojson.Marshal(out)
	return string(resp), err
}

func (c *Controller) channelJoin(ctx context.Context, envelope *rtapi.Envelope) (*rtapi.Envelope, error) {
	logger := c.server.GetLogger()
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		logger.Error("user_id is empty")
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_RUNTIME_EXCEPTION),
			Message: "session error",
		}}}, nil
	}
	session_id := c.server.common.GetSessionID(ctx)
	if session_id == "" {
		logger.Error("session_id is empty")
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_RUNTIME_EXCEPTION),
			Message: "session error",
		}}}, nil
	}
	nick := c.server.common.GetNick(ctx)
	if nick == "" {
		username := c.server.common.GetUsername(ctx)
		nick = username
	}

	syschannel := c.server.GetChannel()
	incoming := envelope.GetChannelJoin()

	// 私聊消息，检查一下target是否为uin，转换为uid
	oldtarget := incoming.Target
	tmpuin := common.StrToInt64(incoming.Target)
	if incoming.Type == int32(rtapi.ChannelJoin_DIRECT_MESSAGE) && tmpuin > 0 {
		results, err := c.server.common.QueryUserFiledsByUin(ctx, tmpuin, "id")
		if err != nil {
			return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
				Code:    int32(rtapi.Error_RUNTIME_EXCEPTION),
				Message: err.Error(),
			}}}, nil
		}
		if uid, ok := results["id"]; ok {
			uid, _ := uuid.FromString(uid.(string))
			incoming.Target = common.Interface2String(uid)
		} else {
			return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
				Code:    int32(rtapi.Error_RUNTIME_EXCEPTION),
				Message: "target is not a valid uin",
			}}}, nil
		}
	}

	uid, _ := uuid.FromString(user_id)
	channelID, stream, err := syschannel.BuildChannelId(ctx, uid, incoming.Target, rtapi.ChannelJoin_Type(incoming.Type))
	if err != nil {
		if errors.Is(err, runtime.ErrInvalidChannelTarget) || errors.Is(err, runtime.ErrInvalidChannelType) {
			return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
				Code:    int32(rtapi.Error_BAD_INPUT),
				Message: err.Error(),
			}}}, nil
		} else {
			return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
				Code:    int32(rtapi.Error_RUNTIME_EXCEPTION),
				Message: err.Error(),
			}}}, nil
		}
	}

	meta := models.PresenceMeta{
		Format:      models.SessionFormatJson,
		Hidden:      incoming.Hidden != nil && incoming.Hidden.Value,
		Persistence: incoming.Persistence == nil || incoming.Persistence.Value,
		Username:    nick, // 这里用昵称
	}

	channel := &rtapi.Channel{
		Id: channelID,
		Self: &rtapi.UserPresence{
			UserId:      user_id,
			SessionId:   session_id,
			Username:    meta.Username,
			Persistence: meta.Persistence,
		},
	}
	switch stream.Mode {
	case models.StreamModeChannel:
		meta.Persistence = false
		channel.Self.Persistence = false
		channel.RoomName = stream.Label
		if channelID != logic.WorldChannelId {
			r := c.server.common.Redis
			jsonData, _ := json.MarshalToString(&models.Presence{
				ID: models.PresenceID{
					SessionID: session_id,
				},
				UserID: user_id,
				Meta:   meta,
			})
			p := r.Pipeline()
			p.SAdd(ctx, "userchat:"+user_id, channelID)
			p.HSet(ctx, "chat:"+channelID, user_id, jsonData)
			_, err := p.Exec(ctx)
			if err != nil {
				logger.Error("join chat channel failed, channelID: %s, user_id: %s, error: %v", channelID, user_id, err)
				return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
					Code:    int32(rtapi.Error_RUNTIME_EXCEPTION),
					Message: "Join channel failed",
				}}}, nil
			}
		}
	case models.StreamModeGroup:
		channel.GroupId = stream.Subject
	case models.StreamModeDM:
		channel.UserIdOne = stream.Subject
		channel.UserIdTwo = stream.Subcontext
		if tmpuin > 0 {
			channel.GroupId = oldtarget // 目标uin
		}
	}
	out := &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Channel{Channel: channel}}
	return out, nil
}

func (c *Controller) channelLeave(ctx context.Context, envelope *rtapi.Envelope) (*rtapi.Envelope, error) {
	logger := c.server.GetLogger()
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		logger.Error("user_id is empty")
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_RUNTIME_EXCEPTION),
			Message: "session error",
		}}}, nil
	}

	incoming := envelope.GetChannelLeave()
	streamConversionResult, err := logic.ChannelIdToStream(incoming.ChannelId)
	if err != nil {
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_BAD_INPUT),
			Message: "Invalid channel identifier",
		}}}, nil
	}
	if streamConversionResult.Stream.Mode == models.StreamModeChannel {
		if incoming.ChannelId != logic.WorldChannelId {
			r := c.server.common.Redis
			p := r.Pipeline()
			p.SRem(ctx, "userchat:"+user_id, incoming.ChannelId)
			p.HDel(ctx, "chat:"+incoming.ChannelId, user_id)
			_, err = p.Exec(ctx)
			if err != nil {
				logger.Error("leave chat channel failed, channelID: %s, user_id: %s, error: %v", incoming.ChannelId, user_id, err)
				return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
					Code:    int32(rtapi.Error_RUNTIME_EXCEPTION),
					Message: "Leave channel failed",
				}}}, nil
			}
		}
	}
	out := &rtapi.Envelope{Cid: envelope.Cid}
	return out, nil
}

func (c *Controller) channelMessageSend(ctx context.Context, envelope *rtapi.Envelope) (*rtapi.Envelope, error) {
	logger := c.server.GetLogger()
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		logger.Error("user_id is empty")
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_RUNTIME_EXCEPTION),
			Message: "session error",
		}}}, nil
	}
	nick := c.server.common.GetNick(ctx)
	if nick == "" {
		username := c.server.common.GetUsername(ctx)
		nick = username
	}
	syschannel := c.server.GetChannel()
	incoming := envelope.GetChannelMessageSend()

	streamConversionResult, err := logic.ChannelIdToStream(incoming.ChannelId)
	if err != nil {
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_BAD_INPUT),
			Message: "Invalid channel identifier",
		}}}, nil
	}

	if maybeJSON := []byte(incoming.Content); !json.Valid(maybeJSON) || bytes.TrimSpace(maybeJSON)[0] != byteBracket {
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_BAD_INPUT),
			Message: "Message content must be a valid JSON object",
		}}}, nil
	}
	presences := []runtime.Presence{}
	persist := false
	switch streamConversionResult.Stream.Mode {
	case models.StreamModeChannel, models.StreamModeGroup:
		if incoming.ChannelId != logic.WorldChannelId {
			r := c.server.common.Redis
			jsonData, err := r.HGetAll(ctx, "chat:"+incoming.ChannelId).Result()
			if err != nil {
				logger.Error("error getting chat channel presences: %v", err)
				return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
					Code:    int32(rtapi.Error_RUNTIME_EXCEPTION),
					Message: "Send message failed",
				}}}, nil
			}
			for _, v := range jsonData {
				presence := &models.Presence{}
				json.UnmarshalFromString(v, &presence)
				presences = append(presences, presence)
			}
		}
	case models.StreamModeDM:
		persist = true
		receiver_uid := ""
		if streamConversionResult.Stream.Subject == user_id {
			receiver_uid = streamConversionResult.Stream.Subcontext
		} else {
			receiver_uid = streamConversionResult.Stream.Subject
		}
		presences = append(presences, &models.Presence{UserID: user_id})
		presences = append(presences, &models.Presence{UserID: receiver_uid})
	}
	// 这里用昵称
	ack, err := syschannel.ChannelMessageSendRaw(ctx, incoming.ChannelId, incoming.Content, user_id, nick, persist, presences)
	switch err {
	case errChannelMessagePersist:
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_RUNTIME_EXCEPTION),
			Message: "Could not persist message to channel history",
		}}}, nil
	}
	out := &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_ChannelMessageAck{ChannelMessageAck: ack}}
	return out, nil
}

func (c *Controller) channelMessageUpdate(ctx context.Context, envelope *rtapi.Envelope) (*rtapi.Envelope, error) {
	incoming := envelope.GetChannelMessageUpdate()
	logger := c.server.GetLogger()
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		logger.Error("user_id is empty")
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_RUNTIME_EXCEPTION),
			Message: "session error",
		}}}, nil
	}
	nick := c.server.common.GetNick(ctx)
	if nick == "" {
		username := c.server.common.GetUsername(ctx)
		nick = username
	}

	if _, err := uuid.FromString(incoming.MessageId); err != nil {
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_BAD_INPUT),
			Message: "Invalid message identifier",
		}}}, nil
	}

	streamConversionResult, err := logic.ChannelIdToStream(incoming.ChannelId)
	if err != nil {
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_BAD_INPUT),
			Message: "Invalid channel identifier",
		}}}, nil
	}

	if maybeJSON := []byte(incoming.Content); !json.Valid(maybeJSON) || bytes.TrimSpace(maybeJSON)[0] != byteBracket {
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_BAD_INPUT),
			Message: "Message content must be a valid JSON object",
		}}}, nil
	}

	presences := []runtime.Presence{}
	persist := false
	if streamConversionResult.Stream.Mode == models.StreamModeDM {
		persist = true
	}

	syschannel := c.server.GetChannel()
	ack, err := syschannel.ChannelMessageUpdateRaw(ctx, incoming.ChannelId, incoming.MessageId, incoming.Content, user_id, nick, persist, presences)
	switch err {
	case errChannelMessageNotFound:
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_BAD_INPUT),
			Message: "Could not find message to update in channel history",
		}}}, nil
	case errChannelMessagePersist:
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_RUNTIME_EXCEPTION),
			Message: "Could not persist message update to channel history",
		}}}, nil
	}

	out := &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_ChannelMessageAck{ChannelMessageAck: ack}}
	return out, nil
}

func (c *Controller) channelMessageRemove(ctx context.Context, envelope *rtapi.Envelope) (*rtapi.Envelope, error) {
	incoming := envelope.GetChannelMessageRemove()
	logger := c.server.GetLogger()
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		logger.Error("user_id is empty")
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_RUNTIME_EXCEPTION),
			Message: "session error",
		}}}, nil
	}
	nick := c.server.common.GetNick(ctx)
	if nick == "" {
		username := c.server.common.GetUsername(ctx)
		nick = username
	}

	if _, err := uuid.FromString(incoming.MessageId); err != nil {
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_BAD_INPUT),
			Message: "Invalid message identifier",
		}}}, nil
	}

	streamConversionResult, err := logic.ChannelIdToStream(incoming.ChannelId)
	if err != nil {
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_BAD_INPUT),
			Message: "Invalid channel identifier",
		}}}, nil
	}

	presences := []runtime.Presence{}
	persist := false
	if streamConversionResult.Stream.Mode == models.StreamModeDM {
		persist = true
	}

	syschannel := c.server.GetChannel()
	ack, err := syschannel.ChannelMessageRemove(ctx, incoming.ChannelId, incoming.MessageId, user_id, nick, persist, presences)
	switch err {
	case errChannelMessageNotFound:
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_BAD_INPUT),
			Message: "Could not find message to remove in channel history",
		}}}, nil
	case errChannelMessagePersist:
		return &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_RUNTIME_EXCEPTION),
			Message: "Could not persist message remove to channel history",
		}}}, nil
	}

	out := &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_ChannelMessageAck{ChannelMessageAck: ack}}
	return out, nil
}
