package common

import (
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
	"golang.org/x/time/rate"
)

type IPBlackWhiteListManager struct {
	MyIPBlackWhiteList      sync.Map // key: router, *IPBlackWhiteList
	MyIPBlackWhiteListTotal int
	allIPBlackWhiteList     IPBlackWhiteList // 通配
	clearcron               *cron.Cron
}

func NewIPBlackWhiteListManager() *IPBlackWhiteListManager {
	mgr := &IPBlackWhiteListManager{
		MyIPBlackWhiteList:      sync.Map{},
		MyIPBlackWhiteListTotal: 0,
		allIPBlackWhiteList:     IPBlackWhiteList{},
	}
	mgr.clearcron = cron.New()
	mgr.clearcron.AddFunc("0 0 * * *", mgr.CleanIPLimiter) // 每天0点执行
	return mgr
}

type IPBlackWhiteList struct {
	syscfg      sync.Map // 白名单系统访问配置 key:ip, value: &LimitSet
	syscfgTotal int
	blacksyscfg sync.Map // 黑名单系统配置 key:ip, value:true
	blacksTotal int      // 黑名单系统配置总数

	list sync.Map // ip限流列表
}

// ip白名单
type IPWhiteConfig struct {
	IP       string `json:"ip,omitempty"`       // 白名单ip, 0.0.0.0或者* 表示不限制ip
	Total    int32  `json:"total,omitempty"`    // 每秒访问总次数，0不限制
	Num      int32  `json:"num,omitempty"`      // 当前访问次数，一秒钟之内的访问次数
	LastTime int64  `json:"lasttime,omitempty"` // 最后访问时间，秒
}

// 例如：{"urlpath": ["***********/24:0"]} LimitSet:平均访问n次-突发上限n次-n秒内
func (ipw *IPBlackWhiteListManager) InitParseSysBlackWhiteList(whitelist, blacklist map[string][]string) {
	if whitelist == nil && blacklist == nil {
		return
	}
	// clear
	ipw.MyIPBlackWhiteList.Range(func(route, iplist interface{}) bool {
		iplist.(*IPBlackWhiteList).syscfg.Range(func(key, value interface{}) bool {
			iplist.(*IPBlackWhiteList).syscfg.Delete(key)
			return true
		})
		iplist.(*IPBlackWhiteList).blacksyscfg.Range(func(key, value interface{}) bool {
			iplist.(*IPBlackWhiteList).blacksyscfg.Delete(key)
			return true
		})
		ipw.MyIPBlackWhiteList.Delete(route)
		return true
	})
	ipw.allIPBlackWhiteList.syscfg.Range(func(key, value interface{}) bool {
		ipw.allIPBlackWhiteList.syscfg.Delete(key)
		return true
	})
	ipw.allIPBlackWhiteList.blacksyscfg.Range(func(key, value interface{}) bool {
		ipw.allIPBlackWhiteList.syscfg.Delete(key)
		return true
	})

	for route, iplist := range whitelist {
		var bwlist *IPBlackWhiteList
		if route == "*" {
			bwlist = &ipw.allIPBlackWhiteList
		} else {
			tmplist, _ := ipw.MyIPBlackWhiteList.LoadOrStore(route, new(IPBlackWhiteList))
			bwlist = tmplist.(*IPBlackWhiteList)
		}
		for _, v := range iplist {
			tmp := strings.Split(v, ":")
			if len(tmp) >= 1 {
				limitset := &LimitSet{Value: 0, Burst: 0, Second: 0}
				if len(tmp) > 1 {
					limtersets := strings.Split(tmp[1], "-")
					if len(limtersets) == 3 {
						limitset.Value = int32(StrToInt(limtersets[0]))
						limitset.Burst = int32(StrToInt(limtersets[1]))
						limitset.Second = int32(StrToInt(limtersets[2]))
					} else {
						limitset.Value = int32(StrToInt(limtersets[0]))
						limitset.Burst = limitset.Value
						limitset.Second = 1
					}
				}
				if strings.Contains(tmp[0], "/") {
					// 掩码
					minIp, maxIp := GetCidrIpRange(tmp[0])
					minip, _ := IPString2Long(minIp)
					maxip, _ := IPString2Long(maxIp)
					if minip > 0 && maxip > 0 {
						for i := minip; i < maxip; i++ {
							strip, _ := Long2IPString(i)
							if len(strip) > 0 {
								bwlist.syscfg.Store(strip, limitset)
							}
						}
					}
				} else {
					bwlist.syscfg.Store(tmp[0], limitset)
				}
			}
		}
	}
	for route, iplist := range blacklist {
		var bwlist *IPBlackWhiteList
		if route == "*" {
			bwlist = &ipw.allIPBlackWhiteList
		} else {
			tmplist, _ := ipw.MyIPBlackWhiteList.LoadOrStore(route, new(IPBlackWhiteList))
			bwlist = tmplist.(*IPBlackWhiteList)
		}
		for _, v := range iplist {
			if strings.Contains(v, "/") {
				// 掩码
				minIp, maxIp := GetCidrIpRange(v)
				minips := strings.Split(minIp, ".")
				maxips := strings.Split(maxIp, ".")
				if len(minips) == 4 && len(maxips) == 4 {
					for i := StrToInt(minips[3]); i < StrToInt(maxips[3]); i++ {
						ip := fmt.Sprintf("%s.%s.%s.%d", maxips[0], maxips[1], maxips[2], i)
						bwlist.blacksyscfg.Store(ip, true)
					}
				}
			} else {
				bwlist.blacksyscfg.Store(v, true)
			}
		}
	}

	total := 0
	ipw.MyIPBlackWhiteList.Range(func(route, bwlist interface{}) bool {
		count := 0
		bwlist.(*IPBlackWhiteList).syscfg.Range(func(key, value interface{}) bool {
			count++
			return true
		})
		bwlist.(*IPBlackWhiteList).syscfgTotal = count

		count = 0
		bwlist.(*IPBlackWhiteList).blacksyscfg.Range(func(key, value interface{}) bool {
			count++
			return true
		})
		bwlist.(*IPBlackWhiteList).blacksTotal = count
		total++
		return true
	})
	ipw.MyIPBlackWhiteListTotal = total

	count := 0
	ipw.allIPBlackWhiteList.syscfg.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	ipw.allIPBlackWhiteList.syscfgTotal = count

	count = 0
	ipw.allIPBlackWhiteList.blacksyscfg.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	ipw.allIPBlackWhiteList.blacksTotal = count

	if ipw.allIPBlackWhiteList.syscfgTotal > 0 || ipw.allIPBlackWhiteList.blacksTotal > 0 {
		ipw.MyIPBlackWhiteListTotal++
	}
}

// 添加访问，返回是否过载
func (ipw *IPBlackWhiteListManager) AddIPRequest(path, ip string) int {
	var bwlist *IPBlackWhiteList
	wblist, ok := ipw.MyIPBlackWhiteList.Load(path)
	if !ok {
		bwlist = &ipw.allIPBlackWhiteList
	} else {
		bwlist = wblist.(*IPBlackWhiteList)
	}

	if (bwlist.syscfgTotal == 0 && bwlist.blacksTotal == 0) || ip == "" {
		return http.StatusOK
	}

	// 是否在黑名单
	if bwlist.blacksTotal > 0 {
		if _, ok := bwlist.blacksyscfg.Load(ip); ok {
			//log.Debug("Access denied ", ip)
			return http.StatusForbidden
		}
	}

	// 是否在白名单
	allow := false
	var limitset *LimitSet
	if bwlist.syscfgTotal > 0 {
		check := []string{ip, "0.0.0.0", "*"}
		for i := 0; i < len(check); i++ {
			if set, ok := bwlist.syscfg.Load(check[i]); ok {
				allow = true
				limitset = set.(*LimitSet)
				break
			}
		}
		if !allow {
			//log.Warn("Access denied ", ip, " ", tmppath)
			return http.StatusForbidden
		}
	} else {
		return http.StatusOK
	}
	if limitset == nil || limitset.Value == 0 || limitset.Second == 0 || limitset.Burst == 0 {
		return http.StatusOK // 无次数限制
	}

	// 获取或创建限流器
	secondvalue := rate.Every(time.Duration(limitset.Second) * time.Second / time.Duration(limitset.Value)) // 每秒平均速率
	actual, loaded := bwlist.list.LoadOrStore(ip, rate.NewLimiter(secondvalue, int(limitset.Burst)))
	if loaded {
		oldLimiter := actual.(*rate.Limiter)
		// 如果限流参数变化,更新限流器
		if oldLimiter.Burst() != int(limitset.Burst) || oldLimiter.Limit() != secondvalue {
			newLimiter := rate.NewLimiter(secondvalue, int(limitset.Burst))
			bwlist.list.Store(ip, newLimiter)
			actual = newLimiter
		}
	}

	// 检查是否允许访问
	cfg := actual.(*rate.Limiter)
	if cfg.Allow() {
		return http.StatusOK
	}
	return http.StatusTooManyRequests
}

// 定时清理IP限流器列表，防止内存无限增长
func (ipw *IPBlackWhiteListManager) CleanIPLimiter() {
	// 清理所有路由的IP限流器
	ipw.MyIPBlackWhiteList.Range(func(route, bwlist interface{}) bool {
		if bwlist != nil {
			bwlist.(*IPBlackWhiteList).list.Range(func(key, value interface{}) bool {
				bwlist.(*IPBlackWhiteList).list.Delete(key)
				return true
			})
		}
		return true
	})

	// 清理通配路由的IP限流器
	ipw.allIPBlackWhiteList.list.Range(func(key, value interface{}) bool {
		ipw.allIPBlackWhiteList.list.Delete(key)
		return true
	})
}
