package main

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"hash/fnv"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	json "github.com/json-iterator/go"
)

type Service struct {
	common       *logic.CommonGlobalDataStruct
	online       *logic.OnlineGlobalDataStruct
	send         *logic.SendGlobalDataStruct
	dbagent      *logic.DbAgentGlobalDataStruct
	customConfig *CustomConfig

	watchRoomList sync.Map // key: room_id, value: *WatchRoomInfo
	bucket_list   sync.Map // key: bucket_id
}

func NewService(common *logic.CommonGlobalDataStruct, online *logic.OnlineGlobalDataStruct, send *logic.SendGlobalDataStruct, dbagent *logic.DbAgentGlobalDataStruct, config *CustomConfig) *Service {
	svc := &Service{
		common:       common,
		online:       online,
		send:         send,
		dbagent:      dbagent,
		customConfig: config,
	}
	return svc
}

func (s *Service) Init() error {
	// 解析支持的桶id
	for _, bucket := range s.customConfig.Config.BucketList {
		tmplist := strings.Split(bucket, "-")
		if len(tmplist) != 2 {
			val, err := strconv.Atoi(tmplist[0])
			if err != nil {
				s.common.Logger.Error("Init bucket_id err %s", bucket)
				continue
			}
			s.bucket_list.Store(val, struct{}{})
		} else {
			min := common.StrToInt(tmplist[0])
			max := common.StrToInt(tmplist[1])
			for i := min; i <= max; i++ {
				s.bucket_list.Store(i, struct{}{})
			}
		}
	}

	// 从gsmgr查询所有房间
	room_list, err := s.QueryAllRoom()
	if err != nil {
		s.common.Logger.Error("QueryAllRoom err %s", err.Error())
		return err
	}
	for _, tmproom := range room_list {
		if tmproom.Roomid == "" {
			continue
		}
		room := &RoomInfo{}
		common.DeepCopy(tmproom, room)

		bucket_id := s.GetBucketID(room.Roomid)
		s.AddRoomToRedisBucket(bucket_id, room.Roomid)
		if _, ok := s.bucket_list.Load(bucket_id); !ok {
			s.common.Logger.Debug("room %s not in bucket_list", room.Roomid)
			continue
		} else {
			s.common.Logger.Debug("room %s in bucket_list", room.Roomid)
		}
		watch_room := &WatchRoomInfo{
			svc:        s,
			room_id:    room.Roomid,
			room_info:  room,
			waitQueue:  common.NewQueue[QueueUserInfo](),
			allocReady: make(map[int64]int64),
			closed:     true,
			queryTime:  time.Now().Unix(),
		}
		s.watchRoomList.Store(room.Roomid, watch_room)

		watch_room.lock.Lock()
		defer watch_room.lock.Unlock()

		// 从redis加载排队用户
		key_roomqueue := fmt.Sprintf("room:queue:%s", room.Roomid)
		user_list, err := s.common.Redis.ZRange(context.TODO(), key_roomqueue, 0, -1).Result()
		if err != nil {
			s.common.Logger.Error("QueryRoomInfo %s err %s", room.Roomid, err.Error())
			continue
		}

		for _, member := range user_list {
			uid_uin := strings.Split(member, ":")
			if len(uid_uin) != 2 {
				s.common.Logger.Error("QueryRoomInfo %s err %s", room.Roomid, "user_id format error")
				continue
			}

			user_id := uid_uin[0]
			uin := common.StrToInt64(uid_uin[1])

			watch_room.waitQueue.Enqueue(QueueUserInfo{user_id: user_id, uin: uin})
		}

		watch_room.UpdateRoomInfo(room)
		watch_room.closed = false
		go watch_room.LoadRoomTask()
		go watch_room.AllocRoomTask()
	}

	return nil
}

func (s *Service) GetBucketID(room_id string) int {
	hash := fnv.New32a()
	_, err := hash.Write([]byte(room_id))
	if err != nil {
		s.common.Logger.Error("GetBucketID %s err %s", room_id, err.Error())
		return 0
	}
	return int(hash.Sum32() % uint32(QUEUE_BUCKET_SIZE))
}

func (s *Service) IsExistBucket(bucket_id int) bool {
	_, ok := s.bucket_list.Load(bucket_id)
	return ok
}

func (s *Service) AddRoomToRedisBucket(bucket_id int, room_id string) error {
	cfg := &models.NodeConfig{
		NodeId:   s.customConfig.Config.NodeConfig.NodeId,
		Host:     s.customConfig.Config.NodeConfig.Host,
		SvcGroup: s.customConfig.Config.NodeConfig.SvcGroup,
	}
	cfg_json, _ := json.Marshal(cfg)
	key_queuebucket := fmt.Sprintf("queuebucket:%d", bucket_id)
	_, err := s.common.Redis.HSetNX(context.TODO(), key_queuebucket, room_id, cfg_json).Result()
	if err != nil {
		s.common.Logger.Error("AddRoomToRedisBucket %s %s err %s", key_queuebucket, room_id, err.Error())
		return err
	}
	return nil
}

func (s *Service) GetRoomFromRedisBucket(bucket_id int, room_id string) (*models.NodeConfig, error) {
	key_queuebucket := fmt.Sprintf("queuebucket:%d", bucket_id)
	cfg_json, err := s.common.Redis.HGet(context.TODO(), key_queuebucket, room_id).Result()
	if err != nil {
		s.common.Logger.Error("GetRoomFromRedisBucket %s %s err %s", key_queuebucket, room_id, err.Error())
		return nil, err
	}
	var cfg models.NodeConfig
	err = json.Unmarshal([]byte(cfg_json), &cfg)
	if err != nil {
		s.common.Logger.Error("GetRoomFromRedisBucket %s %s err %s", key_queuebucket, room_id, err.Error())
		return nil, err
	}
	return &cfg, nil
}

func (s *Service) AddUserToWatchRoom(roomid string, user_id string, uin int64) error {
	watch_room, loaded := s.watchRoomList.LoadOrStore(roomid, &WatchRoomInfo{
		svc:        s,
		room_id:    roomid,
		room_info:  nil,
		waitQueue:  common.NewQueue[QueueUserInfo](),
		allocReady: make(map[int64]int64),
		closed:     true,
		queryTime:  time.Now().Unix(),
	})
	room := watch_room.(*WatchRoomInfo)

	query_room_func := func() error {
		resp, err := s.QueryRoomInfo(roomid)
		if err != nil {
			s.common.Logger.Error("QueryRoomInfo %s err %s", roomid, err.Error())
			return err
		}
		if resp.Code != 0 {
			// resp.Code == 1 room not found
			s.common.Logger.Error("QueryRoomInfo %s err %s", roomid, resp.Msg)
			return errors.New(resp.Msg)
		}
		room.UpdateRoomInfo(&resp.RoomInfo)
		room.closed = false
		go room.LoadRoomTask()
		go room.AllocRoomTask()
		return nil
	}

	// 是否新建
	if !loaded {
		room.lock.Lock()
		defer room.lock.Unlock()
		if err := query_room_func(); err != nil {
			return err
		}
	} else {
		room.lock.Lock()
		defer room.lock.Unlock()
		if room.room_info == nil || room.closed {
			if time.Now().Unix()-room.queryTime > 5 { // 5秒后再重新查询房间
				room.queryTime = time.Now().Unix()
				if err := query_room_func(); err != nil {
					s.watchRoomList.Delete(roomid)
					return err
				}
			}
			if room.room_info == nil {
				return errors.New("room not found")
			}
			if room.closed {
				return errors.New("room closed")
			}
		}
	}

	room.waitQueue.Enqueue(QueueUserInfo{user_id: user_id, uin: uin})
	return nil
}

func (s *Service) RemoveUserFromWatchRoom(roomid string, user_id string, uin int64) error {
	watch_room, ok := s.watchRoomList.Load(roomid)
	if !ok {
		return nil
	}
	room := watch_room.(*WatchRoomInfo)
	room.waitQueue.Leave(QueueUserInfo{user_id: user_id, uin: uin})
	return nil
}

func (s *Service) signGsmgrReq(param []byte) (*url.Values, error) {
	getSign := func(curlParam map[string]string) string {
		var sslice []string
		for key := range curlParam {
			sslice = append(sslice, key)
		}
		sort.Strings(sslice)
		var signContents string
		for _, v := range sslice {
			signContents = signContents + "&" + v + "=" + curlParam[v]
		}
		signContents = strings.TrimPrefix(signContents, "&")
		h := md5.New()
		h.Write([]byte(signContents))
		return hex.EncodeToString(h.Sum(nil))
	}

	curlParam := map[string]string{
		"appid":    s.customConfig.Config.GsmgrConfig.Appid,
		"appkey":   s.customConfig.Config.GsmgrConfig.Appsecret,
		"ts":       strconv.FormatInt(time.Now().Unix(), 10),
		"signType": "md5",
	}

	curlParam["md5"] = common.GetByteMD5(param)
	curlParam["sign"] = getSign(curlParam)
	delete(curlParam, "appkey")

	urlval := url.Values{}
	for key, value := range curlParam {
		urlval.Add(key, value)
	}
	return &urlval, nil
}

func (s *Service) QueryRoomInfo(room_id string) (*RespQueryRoomInfo, error) {
	param := map[string]interface{}{
		"room_id": room_id,
	}
	param_json, _ := json.Marshal(param)
	urlval, err := s.signGsmgrReq(param_json)
	if err != nil {
		s.common.Logger.Error("signGsmgrReq err %s", err.Error())
		return nil, err
	}
	tmpurl := fmt.Sprintf("http://%s/v2/server/room/query_by_roomid?%s", s.customConfig.Config.GsmgrConfig.Addr, urlval.Encode())
	body, err := common.HttpPost(context.TODO(), tmpurl, param_json, map[string]string{"Content-Type": "application/json"}, 10*time.Second)
	if err != nil {
		s.common.Logger.Error("queryRoomInfo err %s", err.Error())
		return nil, err
	}
	resp := RespQueryRoomInfo{}
	err = json.Unmarshal(body, &resp)
	if err != nil {
		s.common.Logger.Error("queryRoomInfo err %s", err.Error())
		return nil, err
	}
	return &resp, nil
}

func (s *Service) QueryAllRoom() ([]*RoomInfo, error) {
	param := map[string]interface{}{
		"server_category": 15,
		"page_from":       0,
		"page_size":       99999,
	}
	param_json, _ := json.Marshal(param)
	urlval, err := s.signGsmgrReq(param_json)
	if err != nil {
		s.common.Logger.Error("signGsmgrReq err %s", err.Error())
		return nil, err
	}
	tmpurl := fmt.Sprintf("http://%s/v2/server/room/list?%s", s.customConfig.Config.GsmgrConfig.Addr, urlval.Encode())
	body, err := common.HttpPost(context.TODO(), tmpurl, param_json, map[string]string{"Content-Type": "application/json"}, 10*time.Second)
	if err != nil {
		s.common.Logger.Error("queryAllRoom err %s", err.Error())
		return nil, err
	}

	resp := RespQueryAllRoom{}
	err = json.Unmarshal(body, &resp)
	if err != nil {
		s.common.Logger.Error("queryAllRoom err %s", err.Error())
		return nil, err
	}
	if resp.Code == 1 { // room not found
		return resp.RoomList, nil
	}
	if resp.Code != 0 {
		s.common.Logger.Error("queryAllRoom err %s", resp.Msg)
		return nil, err
	}
	return resp.RoomList, nil
}

func (s *Service) CloseWatchRoom(room_id string) {
	watch_room, ok := s.watchRoomList.Load(room_id)
	if !ok {
		return
	}
	watch_room.(*WatchRoomInfo).Close()
	s.watchRoomList.Delete(room_id)
}

type WatchRoomInfo struct {
	svc       *Service
	room_id   string
	room_info *RoomInfo
	lock      sync.RWMutex

	token_free_num int32

	waitQueue  *common.Queue[QueueUserInfo]
	allocReady map[int64]int64 // 分配成功，等待进入房间, key: uin, value: 分配成功时间
	closed     bool
	queryTime  int64
}

type QueueUserInfo struct {
	user_id string
	uin     int64
}

func (w *WatchRoomInfo) Close() {
	w.lock.Lock()
	defer w.lock.Unlock()

	w.closed = true
	w.waitQueue.Close()

	// 1. 从redis加载排队用户, 并删除玩家排队信息
	key_roomqueue := fmt.Sprintf("room:queue:%s", w.room_id)
	user_list, err := w.svc.common.Redis.ZRange(context.TODO(), key_roomqueue, 0, -1).Result()
	if err != nil {
		w.svc.common.Logger.Error("CloseWatchRoom ZRange %s err %s", w.room_id, err.Error())
		return
	}

	user_ids := make([]string, 0)
	key_userqueue_list := make([]string, 0)
	for _, member := range user_list {
		uid_uin := strings.Split(member, ":")
		if len(uid_uin) != 2 {
			w.svc.common.Logger.Error("CloseWatchRoom ZRange %s err %s", w.room_id, "user_id format error")
			continue
		}

		user_id := uid_uin[0]
		user_ids = append(user_ids, user_id)
		//uin := common.StrToInt64(uid_uin[1])

		key_userqueue := fmt.Sprintf("user:queue:%s", user_id)
		key_userqueue_list = append(key_userqueue_list, key_userqueue)
	}
	if len(user_ids) > 0 {
		w.svc.common.Redis.Del(context.TODO(), key_userqueue_list...)
		w.svc.send.PushMulticast(context.TODO(), user_ids, SUBJECT_QUEUESERVER_PUSH_JOINRESULT, &PushJoinQueueResult{
			Code: 1,
			Msg:  "room closed",
		}, "", 0)
	}

	w.waitQueue.Clear()

	// 2. 删除redis房间队列
	_, err = w.svc.common.Redis.Del(context.TODO(), key_roomqueue).Result()
	if err != nil {
		w.svc.common.Logger.Error("CloseWatchRoom Del %s err %s", key_roomqueue, err.Error())
	}
}

func (w *WatchRoomInfo) UpdateRoomInfo(room_info *RoomInfo) {
	if room_info == nil {
		return
	}

	w.room_info = room_info
	for _, uin := range room_info.Teams {
		for _, uin2 := range uin.UinList {
			delete(w.allocReady, common.StrToInt64(uin2)) // 已经进入房间，从就绪队列中移除
		}
	}

	// 计算有多少个空闲的位置
	free_num := room_info.RoomCap - room_info.PlayerNum - int32(len(w.allocReady))
	atomic.StoreInt32(&w.token_free_num, free_num)

	//w.svc.common.Logger.Debug("QueryRoomInfo %s token_free_num %d room_cap %d player_num %d alloc_ready %d", w.room_id, free_num, room_info.RoomCap, room_info.PlayerNum, len(w.allocReady))
}

func (w *WatchRoomInfo) LoadRoomTask() {
	ticker := w.svc.common.TimerPool.Get().NewTicker(3 * time.Second)
	for {
		select {
		case <-ticker.C:
			resp, err := w.svc.QueryRoomInfo(w.room_id)
			if err != nil {
				w.svc.common.Logger.Error("QueryRoomInfo %s err %s", w.room_id, err.Error())
				break
			}
			if resp.Code != 0 {
				if resp.Code == 1 { // room not found
					tmp_room_id := w.room_id
					go w.svc.CloseWatchRoom(tmp_room_id)
					return
				} else {
					w.svc.common.Logger.Error("QueryRoomInfo %s err %s", w.room_id, resp.Msg)
				}
				break
			}
			//w.svc.common.Logger.Debug("QueryRoomInfo success %s", w.room_id)
			w.lock.Lock()
			w.UpdateRoomInfo(&resp.RoomInfo)

			// 检查等待进入房间的用户是否超过16秒
			for uin, t := range w.allocReady {
				if time.Now().Unix()-t > 16 {
					delete(w.allocReady, uin)
				}
			}
			w.lock.Unlock()
		}
	}
}

func (w *WatchRoomInfo) AllocRoomTask() {
	for {
		if w.closed {
			w.svc.common.Logger.Info("AllocRoomTask exit, closed, room_id: %s", w.room_id)
			break
		}
		free_num := atomic.LoadInt32(&w.token_free_num)
		if free_num <= 0 {
			time.Sleep(60 * time.Millisecond)
			continue
		}

		user, ok := w.waitQueue.Dequeue()
		if !ok {
			if w.waitQueue.Closed() {
				w.svc.common.Logger.Info("AllocRoomTask waitQueue closed")
				break
			}
			w.svc.common.Logger.Error("AllocRoomTask waitQueue dequeue err")
			continue
		}

		// 通知分配成功
		w.lock.Lock()
		w.allocReady[user.uin] = time.Now().Unix()
		w.lock.Unlock()
		w.svc.common.Logger.Info("AllocRoomTask success room_id: %s user_id: %s uin: %d", w.room_id, user.user_id, user.uin)

		w.svc.common.GroutinePool.Submit(func() {
			// 从redis移除排队用户
			member := fmt.Sprintf("%s:%d", user.user_id, user.uin)
			key_roomqueue := fmt.Sprintf("room:queue:%s", w.room_id)
			_, err := w.svc.common.Redis.ZRem(context.TODO(), key_roomqueue, member).Result()
			if err != nil {
				w.svc.common.Logger.Error("AllocRoomTask ZRem %s err %s", member, err.Error())
			}

			// 从redis移除玩家排队信息
			key_userqueue := fmt.Sprintf("user:queue:%s", user.user_id)
			_, err = w.svc.common.Redis.Del(context.TODO(), key_userqueue).Result()
			if err != nil {
				w.svc.common.Logger.Error("AllocRoomTask Del %s err %s", key_userqueue, err.Error())
			}

			data := map[string]interface{}{
				"act":     "queueserver.joinroom",
				"user_id": user.user_id,
				"uin":     user.uin,
			}
			data_json, _ := json.Marshal(data)

			gs_router_msg := &models.GsRouterMsg{
				Identity: w.room_id,
				Data:     string(data_json),
			}
			gs_router_msg_json, _ := json.Marshal(gs_router_msg)

			// 通知msgbus转发游戏服
			msgbus_cfg := w.svc.customConfig.Config.MsgBusConfig
			msgbus_msg := &models.MsgBusReqPub{
				Topic:     "gameserver",
				Event:     5,
				Payload:   string(gs_router_msg_json),
				AppId:     common.StrToInt(msgbus_cfg.Appid),
				Timestamp: time.Now().Unix(),
				Sign:      "",
			}
			w.svc.send.PushToMsgBus(context.TODO(), msgbus_cfg.Addr, msgbus_cfg.Appid, msgbus_cfg.Appsecret, msgbus_msg)

			// 通知玩家
			roominfo := &RoomInfo{}
			common.DeepCopy(w.room_info, roominfo)
			roominfo.Teams = make([]TeamInfo, 0)
			w.svc.send.Push(context.TODO(), user.user_id, SUBJECT_QUEUESERVER_PUSH_JOINRESULT, &PushJoinQueueResult{
				Code: models.OK,
				Msg:  "success",
				Data: roominfo,
			}, "", 0)
		})
	}
}
