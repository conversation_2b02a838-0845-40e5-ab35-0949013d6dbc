<h2 class="pb-1">Leaderboard</h2>
<div class="leaderboard-top-nav d-flex justify-content-between align-items-baseline mb-3">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a [routerLink]="['/leaderboards']">Leaderboards</a></li>
      <li class="breadcrumb-item active">{{leaderboard.id}}</li>
    </ol>
  </nav>

  <div>
    <button *ngIf="deleteAllowed()" (click)="deleteLeaderboard($event)" type="button" class="btn btn-danger btn-danger-icon"><img class="mr-2" src="/static/svg/bin-red.svg" alt="" width="13" height="13">Delete Leaderboard</button>
  </div>
</div>

<ngb-alert [dismissible]="false" type="danger" class="mb-3" *ngIf="error">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">An error occurred: {{error}}</h6>
</ngb-alert>

<nav ngbNav #leaderboardNav="ngbNav" class="navbar-expand-sm p-0 mb-3">
  <div *ngFor="let v of views">
    <ng-container [ngbNavItem]="v.path">
      <a ngbNavLink routerLinkActive="account-link-active" [routerLink]="['/leaderboards', leaderboard.id, v.path]">{{v.label}}</a>
    </ng-container>
  </div>
</nav>

<router-outlet></router-outlet>
