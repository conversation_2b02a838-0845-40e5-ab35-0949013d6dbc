package main

// 玩家钱包，钻石：通过法币获取，金币：其他途径
type PlayerWallet struct {
	Diamond     int64 `json:"diamond,omitempty"`      // 购买获得，钻石数量
	DiamondGift int64 `json:"diamond_gift,omitempty"` // 非购买获得钻石数量
	Gold        int64 `json:"gold,omitempty"`         // 金币数量
}

type ReqWalletQuery struct {
	UserID string `json:"user_id,omitempty"` // 用户ID
}

// 添加钻石，金币
type ReqWalletAdd struct {
	UserID      string `json:"user_id,omitempty"`      // 用户ID
	Diamond     int64  `json:"diamond,omitempty"`      // 添加购买获得钻石数量
	DiamondGift int64  `json:"diamond_gift,omitempty"` // 赠送获得钻石数量
	Gold        int64  `json:"gold,omitempty"`         // 添加金币数量
	Reason      string `json:"reason,omitempty"`       // 添加原因
}

// 扣除钻石，金币
type ReqWalletSub struct {
	UserID      string `json:"user_id,omitempty"`      // 用户ID
	Diamond     int64  `json:"diamond,omitempty"`      // 扣除购买获得钻石数量
	DiamondGift int64  `json:"diamond_gift,omitempty"` // 扣除赠送获得钻石数量
	Gold        int64  `json:"gold,omitempty"`         // 扣除金币数量
	Reason      string `json:"reason,omitempty"`       // 扣除原因
}

// 钱包操作响应结果
type RespWallet struct {
	Code    int64         `json:"code,omitempty"`
	Message string        `json:"message,omitempty"`
	Data    *PlayerWallet `json:"data,omitempty"`
}
