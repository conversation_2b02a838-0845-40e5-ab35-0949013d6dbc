package logic

import (
	"context"
	"errors"
	"fmt"
	"kernel/kernel-common/api"
	"kernel/kernel-common/runtime"
	"kernel/plugins/models"
	"strconv"
	"strings"
	"time"

	"github.com/gofrs/uuid/v5"
	json "github.com/json-iterator/go"
	"github.com/redis/go-redis/v9"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	PLAYERS_EVENT_STREAM                 = "players:event:stream" // 玩家上线下线事件流名称
	PLAYERS_UIN_STATUS                   = "players:uin:status"   // 玩家uin状态
	PLAYERS_EVENT_STREAM_RETENTION_HOURS = 72                     // 保留多少小时

	KEY_SET_PLAYERS_UID_STATUS = "players:uid:status" // 玩家uid在线状态
	KEY_SET_PLAYERS_UIN_STATUS = "players:uin:status" // 玩家uin在线状态
)

type OnlineGlobalDataStruct struct {
	i ServerModule
}

func NewOnlineGlobalDataStruct(i ServerModule) *OnlineGlobalDataStruct {
	return &OnlineGlobalDataStruct{
		i: i,
	}
}

func (o *OnlineGlobalDataStruct) UserOnlineEvent(ctx context.Context, onlineinfo *models.UserOnlineInfo) bool {
	if onlineinfo == nil || onlineinfo.UserId == "" || onlineinfo.SessionId == "" || onlineinfo.Uin == 0 {
		return false
	}
	if o.i.GetCommon().Redis == nil {
		o.i.GetLogger().Error("redis is nil")
		return false
	}
	str, err := json.MarshalToString(onlineinfo)
	if err != nil {
		return false
	}
	g := o.i.GetCommon()
	key := fmt.Sprintf("player:online:%s", onlineinfo.UserId)
	g.Logger.Debug("UserOnline: %s %s %s", key, onlineinfo.SessionId, str)

	// 先检查是否有其他会话，如果有则踢掉

	existingSessions, err := g.Redis.HGetAll(ctx, key).Result()
	if err != nil {
		g.Logger.Error("UserOnline hgetall: %s %s", onlineinfo.UserId, err.Error())
	} else if len(existingSessions) > 0 {
		// 删除所有现有会话
		oldSessionIds := make([]string, 0)
		for oldSessionId := range existingSessions {
			g.Logger.Info("kick old session: user_id: %s session_id: %s", onlineinfo.UserId, oldSessionId)
			oldSessionIds = append(oldSessionIds, oldSessionId)
		}
		g.Redis.HDel(ctx, key, oldSessionIds...)

		// 通知被挤下线
		g.GroutinePool.Submit(func() {
			for oldSessionId, str := range existingSessions {
				oldSessionInfo := &models.UserOnlineInfo{}
				err := json.Unmarshal([]byte(str), oldSessionInfo)
				if err != nil {
					continue
				}
				notify := &api.Notification{
					Id:         uuid.Must(uuid.NewV4()).String(),
					Subject:    "single_socket",
					Content:    "{}",
					Code:       models.NotificationCodeSingleSocket,
					SenderId:   "",
					CreateTime: &timestamppb.Timestamp{Seconds: time.Now().Unix()},
					Persistent: false,
				}
				o.i.GetSend().PushCloseToSession(context.Background(), oldSessionInfo.NodeId, oldSessionId, runtime.PresenceReasonDisconnect, notify)
			}
		})
	}

	pipe := g.Redis.Pipeline()
	pipe.HSet(ctx, key, onlineinfo.SessionId, str)
	pipe.SAdd(ctx, KEY_SET_PLAYERS_UID_STATUS, onlineinfo.UserId)
	pipe.SAdd(ctx, KEY_SET_PLAYERS_UIN_STATUS, onlineinfo.Uin)

	threeDaysAgo := time.Now().Add(-PLAYERS_EVENT_STREAM_RETENTION_HOURS * time.Hour).UnixMilli()
	pipe.XAdd(ctx, &redis.XAddArgs{
		Stream: PLAYERS_EVENT_STREAM,
		MinID:  fmt.Sprintf("%d-0", threeDaysAgo),
		Approx: true, // 使用近似裁剪提升性能
		ID:     "*",
		Values: map[string]interface{}{
			"event": "online",
			"data":  str,
		},
	})

	_, err = pipe.Exec(ctx)
	if err != nil {
		g.Logger.Error("UserOnline pipeline exec error: %s %s %s", onlineinfo.UserId, onlineinfo.SessionId, err.Error())
		return false
	}

	return true
}

func (o *OnlineGlobalDataStruct) UserOfflineEvent(ctx context.Context, user_id string, session_id string, uin int64) bool {
	if o.i.GetCommon().Redis == nil {
		o.i.GetLogger().Error("redis is nil")
		return false
	}
	g := o.i.GetCommon()
	key := fmt.Sprintf("player:online:%s", user_id)
	g.Logger.Debug("UserOffline: %s %s", key, session_id)
	n, err := g.Redis.HDel(ctx, key, session_id).Result()
	if err != nil {
		g.Logger.Error("UserOffline hdel: %s %s %s", user_id, session_id, err.Error())
		return false
	}
	if n == 0 {
		return true
	}

	threeDaysAgo := time.Now().Add(-PLAYERS_EVENT_STREAM_RETENTION_HOURS * time.Hour).UnixMilli()

	pipe := g.Redis.Pipeline()
	pipe.SRem(ctx, KEY_SET_PLAYERS_UID_STATUS, user_id)
	pipe.SRem(ctx, KEY_SET_PLAYERS_UIN_STATUS, uin)
	pipe.XAdd(ctx, &redis.XAddArgs{
		Stream: PLAYERS_EVENT_STREAM,
		MinID:  fmt.Sprintf("%d-0", threeDaysAgo),
		Approx: true, // 使用近似裁剪提升性能
		ID:     "*",
		Values: map[string]interface{}{
			"event":      "offline",
			"user_id":    user_id,
			"session_id": session_id,
			"uin":        uin,
		},
	})

	_, err = pipe.Exec(ctx)
	if err != nil {
		g.Logger.Error("UserOffline srem: %s %s %s", user_id, session_id, err.Error())
	}
	return true
}

func (o *OnlineGlobalDataStruct) UserOfflineEventBatch(userSessions []*runtime.SessionInfo) bool {
	if o.i.GetCommon().Redis == nil {
		o.i.GetLogger().Error("redis is nil")
		return false
	}
	if len(userSessions) == 0 {
		return true
	}

	g := o.i.GetCommon()
	pipe := g.Redis.Pipeline() // 开启 Redis Pipeline

	for _, session := range userSessions {
		if session == nil {
			continue
		}

		key := fmt.Sprintf("player:online:%s", session.UserId)
		pipe.HDel(context.Background(), key, session.SessionId)
		pipe.SRem(context.Background(), KEY_SET_PLAYERS_UID_STATUS, session.UserId)
		pipe.SRem(context.Background(), KEY_SET_PLAYERS_UIN_STATUS, session.Uin)

		// 发布用户下线事件
		//str_event, _ := json.MarshalToString(session)
		//pipe.Publish(context.Background(), "players:offline:event", str_event)
		threeDaysAgo := time.Now().Add(-PLAYERS_EVENT_STREAM_RETENTION_HOURS * time.Hour).UnixMilli()
		pipe.XAdd(context.Background(), &redis.XAddArgs{
			Stream: PLAYERS_EVENT_STREAM,
			MinID:  fmt.Sprintf("%d-0", threeDaysAgo),
			Approx: true, // 使用近似裁剪提升性能
			ID:     "*",
			Values: map[string]interface{}{
				"event":      "offline",
				"user_id":    session.UserId,
				"session_id": session.SessionId,
				"uin":        session.Uin,
			},
		})
	}

	// 执行 Pipeline
	_, err := pipe.Exec(context.Background())
	if err != nil {
		g.Logger.Error("UserOfflineEventBatch pipeline exec error: %s", err.Error())
		return false
	}

	return true
}

// 获取在线用户数量
func (o *OnlineGlobalDataStruct) GetOnlineCount() (int64, error) {
	if o.i.GetCommon().Redis == nil {
		o.i.GetLogger().Error("redis is nil")
		return 0, errors.New("redis is nil")
	}
	return o.i.GetCommon().Redis.SCard(context.Background(), KEY_SET_PLAYERS_UID_STATUS).Result()
}

func (o *OnlineGlobalDataStruct) IsOnline(user_id string) bool {
	resp := make(map[string]bool)
	bytes, err := o.i.GetSend().Rpc(context.Background(), models.SERVER_NAME_STATUS, models.NACOS_DEFAULT_GROUP, models.RPCID_STATUSSERVER_QUERYONLINEBYUID, []byte(user_id), map[string]string{}, 3*time.Second)
	if err != nil {
		o.i.GetLogger().Error("IsOnline rpc error: %s", err.Error())
		goto errloop
	}
	if err := json.Unmarshal(bytes, &resp); err != nil {
		o.i.GetLogger().Error("IsOnline unmarshal error: %s", err.Error())
		goto errloop
	}
	return resp[user_id]

errloop: // 报错时，退避到redis查询
	if o.i.GetCommon().Redis == nil {
		return false
	}
	return o.i.GetCommon().Redis.SIsMember(context.Background(), KEY_SET_PLAYERS_UID_STATUS, user_id).Val()
}

// 查询在线玩家
func (o *OnlineGlobalDataStruct) QueryOnlineUsers(ctx context.Context, user_ids []string) (map[string]bool, error) {
	payload := strings.Join(user_ids, ",")
	resp := make(map[string]bool)
	bytes, err := o.i.GetSend().Rpc(context.Background(), models.SERVER_NAME_STATUS, models.NACOS_DEFAULT_GROUP, models.RPCID_STATUSSERVER_QUERYONLINEBYUID, []byte(payload), map[string]string{}, 3*time.Second)
	if err != nil {
		o.i.GetLogger().Error("QueryOnlineUsers rpc error: %s", err.Error())
		goto errloop
	}
	if err := json.Unmarshal(bytes, &resp); err != nil {
		o.i.GetLogger().Error("QueryOnlineUsers unmarshal error: %s", err.Error())
		goto errloop
	}
	return resp, nil

errloop: // 报错时，退避到redis查询
	if o.i.GetCommon().Redis == nil {
		return nil, errors.New("redis is nil")
	}
	playerCount := len(user_ids)

	// 根据数量选择不同策略
	if playerCount < 10 {
		return o.queryUsingLoop(ctx, user_ids)
	} else if playerCount <= 100 {
		return o.queryUsingPipeline(ctx, user_ids)
	} else {
		return o.queryUsingLuaScript(ctx, user_ids)
	}
}

// 方法 1: 使用循环逐个查询
func (o *OnlineGlobalDataStruct) queryUsingLoop(ctx context.Context, user_ids []string) (map[string]bool, error) {
	g := o.i.GetCommon()
	onlinePlayers := make(map[string]bool)
	for _, playerID := range user_ids {
		isOnline, err := g.Redis.SIsMember(ctx, KEY_SET_PLAYERS_UID_STATUS, playerID).Result()
		if err != nil {
			return nil, err
		}
		onlinePlayers[playerID] = isOnline
	}

	return onlinePlayers, nil
}

// 方法 2: 使用 Pipeline 批量查询
func (o *OnlineGlobalDataStruct) queryUsingPipeline(ctx context.Context, user_ids []string) (map[string]bool, error) {
	g := o.i.GetCommon()
	pipe := g.Redis.Pipeline()
	cmds := make([]*redis.BoolCmd, len(user_ids))

	// 批量发送 SISMEMBER 命令
	for i, playerID := range user_ids {
		cmds[i] = pipe.SIsMember(ctx, KEY_SET_PLAYERS_UID_STATUS, playerID)
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		return nil, err
	}

	// 收集在线状态
	onlinePlayers := make(map[string]bool)
	for i, cmd := range cmds {
		onlinePlayers[user_ids[i]] = cmd.Val()
	}

	return onlinePlayers, nil
}

// 方法 3: 使用 Lua 脚本批量查询
func (o *OnlineGlobalDataStruct) queryUsingLuaScript(ctx context.Context, user_ids []string) (map[string]bool, error) {
	g := o.i.GetCommon()
	luaScript := `
		local result = {}
		for i, player_id in ipairs(ARGV) do
			result[player_id] = redis.call("SISMEMBER", KEYS[1], player_id)
		end
		return result
	`

	// 执行 Lua 脚本
	result, err := g.Redis.Eval(ctx, luaScript, []string{KEY_SET_PLAYERS_UID_STATUS}, user_ids).Result()
	if err != nil {
		return nil, err
	}

	// 转换结果为映射
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("unexpected result type: %T", result)
	}

	// 转换 interface{} 值为 bool
	onlinePlayers := make(map[string]bool)
	for playerID, isOnline := range resultMap {
		onlinePlayers[playerID] = isOnline.(int64) == 1
	}
	return onlinePlayers, nil
}

func (o *OnlineGlobalDataStruct) IsOnlineByUin(uin int64) bool {
	var resp map[int64]bool
	bytes, err := o.i.GetSend().Rpc(context.Background(), models.SERVER_NAME_STATUS, models.NACOS_DEFAULT_GROUP, models.RPCID_STATUSSERVER_QUERYONLINEBYUIN, []byte(strconv.FormatInt(uin, 10)), map[string]string{}, 3*time.Second)
	if err != nil {
		o.i.GetLogger().Error("IsOnlineByUin rpc error: %s", err.Error())
		goto errloop
	}
	if err := json.Unmarshal(bytes, &resp); err != nil {
		o.i.GetLogger().Error("IsOnlineByUin unmarshal error: %s", err.Error())
		goto errloop
	}
	return resp[uin]

errloop: // 报错时，退避到redis查询
	if o.i.GetCommon().Redis == nil {
		return false
	}
	return o.i.GetCommon().Redis.SIsMember(context.Background(), KEY_SET_PLAYERS_UIN_STATUS, uin).Val()
}

// 查询在线玩家
func (o *OnlineGlobalDataStruct) QueryOnlineUsersByUin(ctx context.Context, uins []int64) (map[int64]bool, error) {
	struins := make([]string, len(uins))
	for i, uin := range uins {
		struins[i] = strconv.FormatInt(uin, 10)
	}
	payload := strings.Join(struins, ",")
	resp := make(map[int64]bool)
	bytes, err := o.i.GetSend().Rpc(ctx, models.SERVER_NAME_STATUS, models.NACOS_DEFAULT_GROUP, models.RPCID_STATUSSERVER_QUERYONLINEBYUIN, []byte(payload), map[string]string{}, 3*time.Second)
	if err != nil {
		o.i.GetLogger().Error("QueryOnlineUsersByUin rpc error: %s", err.Error())
		goto errloop
	}
	if err := json.Unmarshal(bytes, &resp); err != nil {
		o.i.GetLogger().Error("QueryOnlineUsersByUin unmarshal error: %s", err.Error())
		goto errloop
	}
	return resp, nil

errloop: // 报错时，退避到redis查询
	if o.i.GetCommon().Redis == nil {
		return nil, errors.New("redis is nil")
	}
	playerCount := len(uins)

	// 根据数量选择不同策略
	if playerCount < 10 {
		return o.queryUsingLoopByUin(ctx, uins)
	} else if playerCount <= 100 {
		return o.queryUsingPipelineByUin(ctx, uins)
	} else {
		return o.queryUsingLuaScriptByUin(ctx, uins)
	}
}

// 方法 1: 使用循环逐个查询
func (o *OnlineGlobalDataStruct) queryUsingLoopByUin(ctx context.Context, uins []int64) (map[int64]bool, error) {
	g := o.i.GetCommon()
	onlinePlayers := make(map[int64]bool)
	for _, uin := range uins {
		isOnline, err := g.Redis.SIsMember(ctx, KEY_SET_PLAYERS_UIN_STATUS, uin).Result()
		if err != nil {
			return nil, err
		}
		onlinePlayers[uin] = isOnline
	}

	return onlinePlayers, nil
}

// 方法 2: 使用 Pipeline 批量查询
func (o *OnlineGlobalDataStruct) queryUsingPipelineByUin(ctx context.Context, uins []int64) (map[int64]bool, error) {
	g := o.i.GetCommon()
	pipe := g.Redis.Pipeline()
	cmds := make([]*redis.BoolCmd, len(uins))

	// 批量发送 SISMEMBER 命令
	for i, uin := range uins {
		cmds[i] = pipe.SIsMember(ctx, KEY_SET_PLAYERS_UIN_STATUS, uin)
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		return nil, err
	}

	// 收集在线状态
	onlinePlayers := make(map[int64]bool)
	for i, cmd := range cmds {
		onlinePlayers[uins[i]] = cmd.Val()
	}

	return onlinePlayers, nil
}

// 方法 3: 使用 Lua 脚本批量查询
func (o *OnlineGlobalDataStruct) queryUsingLuaScriptByUin(ctx context.Context, uins []int64) (map[int64]bool, error) {
	g := o.i.GetCommon()
	// 将int64转换为字符串，因为Redis Lua脚本不能直接处理数字键
	uinStrings := make([]string, len(uins))
	for i, uin := range uins {
		uinStrings[i] = strconv.FormatInt(uin, 10)
	}

	luaScript := `
		local result = {}
		for i, uin in ipairs(ARGV) do
			result[uin] = redis.call("SISMEMBER", KEYS[1], uin)
		end
		return result
	`

	// 执行 Lua 脚本
	result, err := g.Redis.Eval(ctx, luaScript, []string{KEY_SET_PLAYERS_UIN_STATUS}, uinStrings).Result()
	if err != nil {
		return nil, err
	}

	// 转换结果为映射
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("unexpected result type: %T", result)
	}

	// 转换 interface{} 值为 bool
	onlinePlayers := make(map[int64]bool)
	for uinStr, isOnline := range resultMap {
		uin, err := strconv.ParseInt(uinStr, 10, 64)
		if err != nil {
			g.Logger.Error("Failed to parse uin %v: %v", uinStr, err)
			continue
		}
		onlinePlayers[uin] = isOnline.(int64) == 1
	}
	return onlinePlayers, nil
}

// 查询指定玩家会话信息
func (o *OnlineGlobalDataStruct) QueryOnlineUsersSession(ctx context.Context, user_id, session_id string) (*models.UserOnlineInfo, error) {
	var resp models.UserOnlineInfo
	bytes, err := o.i.GetSend().Rpc(ctx, models.SERVER_NAME_STATUS, models.NACOS_DEFAULT_GROUP, models.RPCID_STATUSSERVER_QUERYBYUID, []byte(user_id), map[string]string{}, 3*time.Second)
	if err != nil {
		o.i.GetLogger().Error("QueryOnlineUsersSession rpc error: %s", err.Error())
		goto errloop
	}
	if err := json.Unmarshal(bytes, &resp); err != nil {
		o.i.GetLogger().Error("QueryOnlineUsersSession unmarshal error: %s", err.Error())
		goto errloop
	}
	if resp.SessionId == session_id {
		return &resp, nil
	}
	return nil, nil

errloop: // 报错时，退避到redis查询
	if o.i.GetCommon().Redis == nil {
		return nil, errors.New("redis is nil")
	}

	redisKey := fmt.Sprintf("player:online:%s", user_id)
	result, err := o.i.GetCommon().Redis.HGet(ctx, redisKey, session_id).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		o.i.GetLogger().Error("Failed to query user info for %s: %s", user_id, err.Error())
		return nil, err
	}

	info := &models.UserOnlineInfo{}
	if err := json.Unmarshal([]byte(result), info); err != nil {
		o.i.GetLogger().Error("Failed to unmarshal user info for %s: %s", user_id, err.Error())
		return nil, err
	}
	return info, nil
}

// 查询在线玩家信息
func (o *OnlineGlobalDataStruct) QueryOnlineUsersInfo(ctx context.Context, user_ids []string) (map[string][]*models.UserOnlineInfo, error) {
	payload := strings.Join(user_ids, ",")
	resp := make(map[string]*models.UserOnlineInfo)
	result := make(map[string][]*models.UserOnlineInfo)
	bytes, err := o.i.GetSend().Rpc(context.Background(), models.SERVER_NAME_STATUS, models.NACOS_DEFAULT_GROUP, models.RPCID_STATUSSERVER_QUERYBYUID, []byte(payload), map[string]string{}, 3*time.Second)
	if err != nil {
		o.i.GetLogger().Error("QueryOnlineUsersInfo rpc error: %s", err.Error())
		goto errloop
	}
	if err := json.Unmarshal(bytes, &resp); err != nil {
		o.i.GetLogger().Error("QueryOnlineUsersInfo unmarshal error: %s", err.Error())
		goto errloop
	}
	for user_id, info := range resp {
		result[user_id] = append(result[user_id], info)
	}
	return result, nil

errloop: // 报错时，退避到redis查询
	if o.i.GetCommon().Redis == nil {
		return nil, errors.New("redis is nil")
	}
	playerCount := len(user_ids)

	// 根据数量选择不同策略
	if playerCount < 10 {
		return o.queryUsersInfoLoop(ctx, user_ids)
	} else if playerCount <= 100 {
		return o.queryUsersInfoPipeline(ctx, user_ids)
	} else {
		return o.queryUsersInfoLuaScript(ctx, user_ids)
	}
}

func (o *OnlineGlobalDataStruct) queryUsersInfoLoop(ctx context.Context, userIDs []string) (map[string][]*models.UserOnlineInfo, error) {
	g := o.i.GetCommon()
	result := make(map[string][]*models.UserOnlineInfo)
	for _, userID := range userIDs {
		redisKey := fmt.Sprintf("player:online:%s", userID)
		userInfo, err := g.Redis.HGetAll(ctx, redisKey).Result()
		if err != nil {
			g.Logger.Error("Failed to query user info for %s: %s", userID, err.Error())
			continue
		}
		result[userID] = make([]*models.UserOnlineInfo, 0)
		for session_id, str := range userInfo {
			info := &models.UserOnlineInfo{}
			if err := json.Unmarshal([]byte(str), info); err == nil {
				info.SessionId = session_id
				result[userID] = append(result[userID], info)
			} else {
				g.Logger.Error("Failed to unmarshal user info for %s: %s", userID, err.Error())
			}
		}
	}

	return result, nil
}

func (o *OnlineGlobalDataStruct) queryUsersInfoPipeline(ctx context.Context, userIDs []string) (map[string][]*models.UserOnlineInfo, error) {
	g := o.i.GetCommon()
	pipe := g.Redis.Pipeline()

	cmds := make([]*redis.MapStringStringCmd, len(userIDs))
	for i, userID := range userIDs {
		redisKey := fmt.Sprintf("player:online:%s", userID)
		cmds[i] = pipe.HGetAll(ctx, redisKey)
	}

	_, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		g.Logger.Error("Failed to execute pipeline: %s", err.Error())
		return nil, err
	}

	result := make(map[string][]*models.UserOnlineInfo)
	for i, cmd := range cmds {
		userInfo, err := cmd.Result()
		if err != nil {
			g.Logger.Error("Failed to get result for user %s: %s", userIDs[i], err.Error())
			continue
		}
		userID := userIDs[i]
		result[userID] = make([]*models.UserOnlineInfo, 0)
		for session_id, str := range userInfo {
			info := &models.UserOnlineInfo{}
			if err := json.Unmarshal([]byte(str), info); err == nil {
				info.SessionId = session_id
				result[userID] = append(result[userID], info)
			} else {
				g.Logger.Error("Failed to unmarshal user info for %s: %s", userID, err.Error())
			}
		}
	}

	return result, nil
}

func (o *OnlineGlobalDataStruct) queryUsersInfoLuaScript(ctx context.Context, userIDs []string) (map[string][]*models.UserOnlineInfo, error) {
	g := o.i.GetCommon()
	luaScript := redis.NewScript(`
		local result = {}
		for i, key in ipairs(KEYS) do
			local userInfo = redis.call("HGETALL", key)
			if next(userInfo) ~= nil then
				result[key] = userInfo
			end
		end
		return result
	`)

	keys := make([]string, len(userIDs))
	for i, userID := range userIDs {
		keys[i] = fmt.Sprintf("player:online:%s", userID)
	}

	rawResult, err := luaScript.Run(ctx, g.Redis, keys).Result()
	if err != nil {
		g.Logger.Error("Failed to execute Lua script: %s", err.Error())
		return nil, err
	}

	// 解析 Lua 返回的结果
	result := make(map[string][]*models.UserOnlineInfo)
	if rawResultMap, ok := rawResult.(map[string]interface{}); ok {
		for key, value := range rawResultMap {
			if userInfoMap, ok := value.(map[string]string); ok {
				userID := key
				result[userID] = make([]*models.UserOnlineInfo, 0)
				for session_id, str := range userInfoMap {
					info := &models.UserOnlineInfo{}
					if err := json.Unmarshal([]byte(str), info); err == nil {
						info.SessionId = session_id
						result[userID] = append(result[userID], info)
					} else {
						g.Logger.Error("Failed to unmarshal user info for %s: %s", userID, err.Error())
					}
				}
			}
		}
	}

	return result, nil
}

func (o *OnlineGlobalDataStruct) QueryOnlineUsersInfoByUin(ctx context.Context, uins []int64) (map[int64]*models.UserOnlineInfo, error) {
	struins := make([]string, len(uins))
	for i, uin := range uins {
		struins[i] = strconv.FormatInt(uin, 10)
	}
	payload := strings.Join(struins, ",")
	resp := make(map[int64]*models.UserOnlineInfo)
	bytes, err := o.i.GetSend().Rpc(ctx, models.SERVER_NAME_STATUS, models.NACOS_DEFAULT_GROUP, models.RPCID_STATUSSERVER_QUERYBYUIN, []byte(payload), map[string]string{}, 3*time.Second)
	if err != nil {
		o.i.GetLogger().Error("QueryOnlineUsersInfoByUin rpc error: %s", err.Error())
		return nil, err
	}
	if err := json.Unmarshal(bytes, &resp); err != nil {
		o.i.GetLogger().Error("QueryOnlineUsersInfoByUin unmarshal error: %s", err.Error())
		return nil, err
	}
	return resp, nil
}

func (o *OnlineGlobalDataStruct) QueryOnlineUsersInfoByStrUin(ctx context.Context, uins []string) (map[string]*models.UserOnlineInfo, error) {
	payload := strings.Join(uins, ",")
	resp := make(map[string]*models.UserOnlineInfo)
	bytes, err := o.i.GetSend().Rpc(ctx, models.SERVER_NAME_STATUS, models.NACOS_DEFAULT_GROUP, models.RPCID_STATUSSERVER_QUERYBYUIN, []byte(payload), map[string]string{}, 3*time.Second)
	if err != nil {
		o.i.GetLogger().Error("QueryOnlineUsersInfoByStrUin rpc error: %s", err.Error())
		return nil, err
	}
	if err := json.Unmarshal(bytes, &resp); err != nil {
		o.i.GetLogger().Error("QueryOnlineUsersInfoByStrUin unmarshal error: %s", err.Error())
		return nil, err
	}
	return resp, nil
}
