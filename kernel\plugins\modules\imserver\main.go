package main

import (
	"context"
	"database/sql"
	"fmt"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"os"
	"strings"
	"sync"
	"time"

	json "github.com/json-iterator/go"
	"github.com/redis/go-redis/v9"

	"go.uber.org/zap/zapcore"
)

type ImServerModule struct {
	name                 string
	logger               runtime.Logger
	db                   *sql.DB
	nk                   runtime.NakamaModule
	config               runtime.Config // nakama 配置
	CustomConfig         *CustomConfig  // 自定义配置
	isRegisterSelf       bool
	ProxyPassConfig      sync.Map
	controller           *Controller
	partyController      *PartyController
	statusConsumerCancel context.CancelFunc

	common  *logic.CommonGlobalDataStruct
	online  *logic.OnlineGlobalDataStruct
	send    *logic.SendGlobalDataStruct
	dbagent *logic.DbAgentGlobalDataStruct
	notify  *logic.NotifyGlobalDataStruct
	stream  *logic.StreamGlobalDataStruct
	channel *logic.ChannelGlobalDataStruct
}

var ImServerData *ImServerModule

func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
	ImServerData = new(ImServerModule)
	ImServerData.name = models.SERVER_NAME_IM
	ImServerData.logger = logger
	ImServerData.db = db
	ImServerData.nk = nk
	ImServerData.common = logic.NewCommonGlobalDataStruct()
	ImServerData.send = logic.NewSendGlobalDataStruct(ImServerData)
	ImServerData.dbagent = logic.NewDbAgentGlobalDataStruct(ImServerData)
	ImServerData.online = logic.NewOnlineGlobalDataStruct(ImServerData)
	ImServerData.notify = logic.NewNotifyGlobalDataStruct(ImServerData)
	ImServerData.stream = logic.NewStreamGlobalDataStruct(ImServerData)
	ImServerData.channel = logic.NewChannelGlobalDataStruct(ImServerData)
	config, err := initializer.GetConfig()
	if err != nil {
		return err
	}
	ImServerData.config = config
	if err := ImServerData.common.Init(ImServerData, ImServerData.CustomConfig, ImServerData.customConfigEvent); err != nil {
		logger.Error("init common logic error %v", err)
		return err
	}

	initializer.RegisterShutdown(ImServerData.Shutdown)

	// 自定义路由注册
	ImServerData.controller = NewController(ImServerData)
	initializer.RegisterRpc(models.RPCID_IMSERVER_CHANNEL_RT, ImServerData.controller.ChannelRt)

	ImServerData.partyController = NewPartyController(ImServerData)
	initializer.RegisterRpc(models.RPCID_IMSERVER_PARTY_RT, ImServerData.partyController.PartyRt)
	initializer.RegisterRpc(RPCID_IMSERVER_PARTY_LIST, ImServerData.partyController.PartyList)
	initializer.RegisterRpc(RPCID_IMSERVER_PARTY_DETAIL, ImServerData.partyController.PartyDetail)
	initializer.RegisterRpc(RPCID_IMSERVER_PARTY_UPDATE, ImServerData.partyController.PartyUpdate)
	initializer.RegisterRpc(RPCID_IMSERVER_PARTY_INVITE, ImServerData.partyController.PartyInvite)
	initializer.RegisterRpc(RPCID_IMSERVER_PARTY_QUERY, ImServerData.partyController.PartyQuery)

	// 从redis队列消费数据
	go ImServerData.consumeStatusData()

	return nil
}

func (s *ImServerModule) GetCommon() *logic.CommonGlobalDataStruct {
	return s.common
}
func (s *ImServerModule) GetOnline() *logic.OnlineGlobalDataStruct {
	return s.online
}
func (s *ImServerModule) GetSend() *logic.SendGlobalDataStruct {
	return s.send
}
func (s *ImServerModule) GetDbAgent() *logic.DbAgentGlobalDataStruct {
	return s.dbagent
}
func (s *ImServerModule) GetNotify() *logic.NotifyGlobalDataStruct {
	return s.notify
}
func (s *ImServerModule) GetStream() *logic.StreamGlobalDataStruct {
	return s.stream
}
func (s *ImServerModule) GetChannel() *logic.ChannelGlobalDataStruct {
	return s.channel
}
func (s *ImServerModule) GetLogger() runtime.Logger {
	return s.logger
}
func (s *ImServerModule) GetConfig() runtime.Config {
	return s.config
}
func (s *ImServerModule) GetName() string {
	return s.name
}
func (s *ImServerModule) GetDb() *sql.DB {
	return s.db
}
func (s *ImServerModule) GetNk() runtime.NakamaModule {
	return s.nk
}

// 配置读取回调
func (s *ImServerModule) customConfigEvent(dataId string, newobj any) {
	tmpobj, ok := newobj.(*CustomConfig)
	if !ok {
		return
	}
	if tmpobj.Config == nil {
		tmpobj.Config = &ImServerConfig{}
	}
	defaultNodeCfg := false
	if tmpobj.Config.NodeConfig == nil {
		// 使用默认节点配置
		scheme := "http"
		if s.config.GetSocket().GetUseTLS() {
			scheme = "https"
		}
		tmpobj.Config.NodeConfig = &models.NodeConfig{
			NodeId:   s.config.GetName(),
			SvcGroup: fmt.Sprintf("%s@@%s", s.name, models.NACOS_DEFAULT_GROUP),
			Host:     fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			NetHost:  fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			AuthKey:  s.config.GetRuntime().GetHTTPKey(),
			Weight:   10,
			Scheme:   scheme,
		}
		defaultNodeCfg = true
	}
	tmp, _ := json.Marshal(tmpobj)
	s.logger.Debug("customConfigEvent defaultNodeCfg: %v, customCfg: %s", defaultNodeCfg, string(tmp))
	s.common.SetNodeConfig(tmpobj.Config.NodeConfig)
	s.common.WatchNodeList.InitWatchSvr(tmpobj.Config.WatchSvcGroupList, tmpobj.Config.NodeConfig.SvcGroup)
	s.CustomConfig = tmpobj

	// 注册服务
	selfcfg := map[string]interface{}{}
	if err := common.StrcutToMap(tmpobj.Config.NodeConfig, &selfcfg); err == nil {
		if !s.isRegisterSelf {
			go s.common.RegisterAndUpdateSelfToNacos(false, selfcfg)
			s.isRegisterSelf = true
		} else {
			go s.common.RegisterAndUpdateSelfToNacos(true, selfcfg)
		}
	} else {
		s.logger.Error("register self, config error %v", err)
	}

	s.common.UpdateAccessLogConfig(tmpobj.AccessLog)
	loglevel, err := zapcore.ParseLevel(tmpobj.Logger.Level)
	if err != nil {
		s.logger.Error("parse log level error %v", err)
	} else {
		s.nk.SetLoggerLevel(loglevel)
	}
}

func (s *ImServerModule) Shutdown(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, reload bool) {
	logger.Info("%s shutdown", s.name)
	if !reload {
		s.common.UnRegisterSelfToNacos()
	}
	if s.statusConsumerCancel != nil {
		s.statusConsumerCancel()
	}
}

// 订阅在线状态事件, 分片模式消费
func (s *ImServerModule) consumeStatusData() {
	for i := 0; i < 100; i++ {
		if s.CustomConfig != nil {
			break
		}
		time.Sleep(100 * time.Millisecond)
	}
	if s.CustomConfig == nil {
		s.logger.Error("cannot start friend server: config not loaded")
		os.Exit(1)
		return
	}

	rdb := s.common.Redis
	consumerGroup := s.name
	nodeId := s.CustomConfig.Config.NodeConfig.NodeId

	ctx, cancel := context.WithCancel(context.Background())
	s.statusConsumerCancel = cancel

	// 创建消费者组，如果已存在则忽略错误
	err := rdb.XGroupCreateMkStream(ctx, logic.PLAYERS_EVENT_STREAM, consumerGroup, "0").Err()
	if err != nil && !strings.Contains(err.Error(), "BUSYGROUP") {
		s.logger.Error("create consumer group failed: %v", err)
	}

	// 启动多个消费者协程处理新消息
	for i := 0; i < 8; i++ {
		go func(workerID int) {
			consumerName := fmt.Sprintf("%s-%d", nodeId, workerID)

			for {
				select {
				case <-ctx.Done():
					return // 优雅退出
				default:
				}

				streams, err := rdb.XReadGroup(ctx, &redis.XReadGroupArgs{
					Group:    consumerGroup,
					Consumer: consumerName,
					Streams:  []string{logic.PLAYERS_EVENT_STREAM, ">"},
					Count:    10, // 每个工作线程处理较少的消息
					Block:    100 * time.Millisecond,
				}).Result()

				if (err != nil && err == redis.Nil) || len(streams) == 0 {
					continue
				}

				if err != nil {
					s.logger.Error("worker %d XReadGroup error: %v", workerID, err)
					continue
				}

				var ackIDs []string
				for _, str := range streams {
					for _, msg := range str.Messages {
						s.processStatusMessage(msg.Values, workerID)
						ackIDs = append(ackIDs, msg.ID)
					}
				}

				if len(ackIDs) > 0 {
					err := rdb.XAck(ctx, logic.PLAYERS_EVENT_STREAM, consumerGroup, ackIDs...).Err()
					if err != nil {
						s.logger.Error("worker %d XAck error: %v", workerID, err)
					}
				}
			}
		}(i)
	}

	s.retryPendingMessages(ctx, consumerGroup)
}

func (s *ImServerModule) retryPendingMessages(ctx context.Context, consumerGroup string) {
	rdb := s.common.Redis
	nodeId := s.CustomConfig.Config.NodeConfig.NodeId
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	const (
		idleThreshold    = 30 * time.Second
		maxRetryAttempts = 5
		claimBatchSize   = 100
	)

	for {
		select {
		case <-ctx.Done():
			return // 优雅退出
		case <-ticker.C:
			// 获取 pending 消息列表
			pendingList, err := rdb.XPendingExt(ctx, &redis.XPendingExtArgs{
				Stream: logic.PLAYERS_EVENT_STREAM,
				Group:  consumerGroup,
				Idle:   idleThreshold,
				Start:  "-",
				End:    "+",
				Count:  claimBatchSize,
			}).Result()

			if err != nil && err != redis.Nil {
				s.logger.Error("retryPendingMessages XPendingExt error: %v", err)
				continue
			}
			if len(pendingList) == 0 {
				continue
			}

			s.logger.Debug("found %d pending messages need to retry", len(pendingList))

			// 过滤出需要重试的消息
			var retryIDs []string
			var forceAckIDs []string
			for _, entry := range pendingList {
				if entry.RetryCount >= maxRetryAttempts {
					forceAckIDs = append(forceAckIDs, entry.ID)
				} else {
					retryIDs = append(retryIDs, entry.ID)
				}
			}

			// 执行 XCLAIM 批量拉回
			if len(retryIDs) > 0 {
				msgs, err := rdb.XClaim(ctx, &redis.XClaimArgs{
					Stream:   logic.PLAYERS_EVENT_STREAM,
					Group:    consumerGroup,
					Consumer: fmt.Sprintf("retry-consumer-%s", nodeId),
					MinIdle:  idleThreshold,
					Messages: retryIDs,
				}).Result()

				if err != nil && err != redis.Nil {
					s.logger.Error("retryPendingMessages XClaim error: %v", err)
				}

				// 处理成功拉取的消息
				var ackIDs []string
				claimedSet := make(map[string]struct{})
				for _, msg := range msgs {
					s.processStatusMessage(msg.Values, -2) // -2 表示重试流程
					ackIDs = append(ackIDs, msg.ID)
					claimedSet[msg.ID] = struct{}{}
				}

				// 未 claim 到的消息，强制 ack 掉
				for _, id := range retryIDs {
					if _, ok := claimedSet[id]; !ok {
						forceAckIDs = append(forceAckIDs, id)
					}
				}

				if len(ackIDs) > 0 {
					err := rdb.XAck(ctx, logic.PLAYERS_EVENT_STREAM, consumerGroup, ackIDs...).Err()
					if err != nil {
						s.logger.Error("retryPendingMessages XAck error: %v", err)
					}
				}
			}

			// 强制 ack（比如死信、重复 claim 失败等）
			if len(forceAckIDs) > 0 {
				s.logger.Warn("force ACK %d messages (retry limit reached or not claimable)", len(forceAckIDs))
				err := rdb.XAck(ctx, logic.PLAYERS_EVENT_STREAM, consumerGroup, forceAckIDs...).Err()
				if err != nil {
					s.logger.Error("retryPendingMessages force ACK error: %v", err)
				}
			}
		}
	}
}

// 处理状态消息
func (s *ImServerModule) processStatusMessage(values map[string]interface{}, workerID int) {
	if values == nil {
		s.logger.Error("worker %d receive empty message", workerID)
		return
	}

	event, ok := values["event"].(string)
	if !ok {
		s.logger.Error("worker %d invalid event type", workerID)
		return
	}

	switch event {
	case "online":
		onlineInfo := &models.UserOnlineInfo{}
		if dataStr, ok := values["data"].(string); ok {
			if err := json.Unmarshal([]byte(dataStr), onlineInfo); err != nil {
				s.logger.Error("worker %d parse online data failed: %v", workerID, err)
				return
			}
			if onlineInfo.UserId == "" || onlineInfo.Uin == 0 {
				s.logger.Error("worker %d online data missing required fields: %+v", workerID, onlineInfo)
				return
			}
		} else {
			s.logger.Error("worker %d online event missing data field", workerID)
			return
		}

		rdb := s.common.Redis

		// 获取用户所在party
		partyID, err := rdb.Get(context.TODO(), "userparty:"+common.Interface2String(onlineInfo.Uin)).Result()
		if err != nil {
			if err != redis.Nil {
				s.logger.Error("worker %d Get error: %v", workerID, err)
			}
			return
		}
		if partyID != "" {
			// 通知状态变更
			s.partyController.partyUpdateStatus(partyID, onlineInfo.Uin, true)
		}

	case "offline":
		uid := common.Interface2String(values["user_id"])
		uin := common.Interface2Int64(values["uin"])

		if uid == "" || uin == 0 {
			s.logger.Error("worker %d offline event missing required fields: user_id=%s, uin=%d", workerID, uid, uin)
			return
		}

		rdb := s.common.Redis
		channels, err := rdb.SMembers(context.TODO(), "userchat:"+uid).Result()
		if err != nil {
			if err != redis.Nil {
				s.logger.Error("worker %d SMembers error: %v", workerID, err)
			}
		} else {
			if len(channels) > 0 {
				p := rdb.Pipeline()
				for _, channel := range channels {
					p.HDel(context.TODO(), "chat:"+channel, uid)
				}
				p.Del(context.TODO(), "userchat:"+uid)
				_, err := p.Exec(context.TODO())
				if err != nil {
					s.logger.Error("worker %d HDel error: %v", workerID, err)
				}
			}
		}

		// 获取用户所在party
		partyID, err := rdb.Get(context.TODO(), "userparty:"+common.Interface2String(uin)).Result()
		if err != nil {
			if err != redis.Nil {
				s.logger.Error("worker %d Get error: %v", workerID, err)
			}
		} else {
			if partyID != "" {
				// 通知状态变更
				s.partyController.partyUpdateStatus(partyID, uin, false)
			}
		}

	default:
		s.logger.Error("worker %d unknown event type: %v", workerID, event)
	}
}
