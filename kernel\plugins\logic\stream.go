package logic

import (
	"context"
	"fmt"
	"kernel/kernel-common/rtapi"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/models"
	"time"

	"github.com/gofrs/uuid/v5"
	json "github.com/json-iterator/go"
	"google.golang.org/protobuf/encoding/protojson"
)

type StreamGlobalDataStruct struct {
	i ServerModule
}

func NewStreamGlobalDataStruct(i ServerModule) *StreamGlobalDataStruct {
	return &StreamGlobalDataStruct{
		i: i,
	}
}

func (s *StreamGlobalDataStruct) rpc(ctx context.Context, node_id, rpc_id string, payload []byte, headers map[string]string, timeout time.Duration) ([]byte, error) {
	return s.i.GetSend().RpcToNodeId(ctx, node_id, models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, rpc_id, payload, headers, timeout)
}

func (s *StreamGlobalDataStruct) StreamSend(ctx context.Context, mode uint8, subject, subcontext, label, data string, presences []runtime.Presence, reliable bool) error {
	if data == "" {
		return fmt.Errorf("data is empty")
	}
	if len(presences) == 0 {
		return fmt.Errorf("presences is empty")
	}

	logger := s.i.GetLogger()
	req := &models.StreamReq{
		Mode:       mode,
		Subject:    subject,
		Subcontext: subcontext,
		Label:      label,
		Data:       data,
		Reliable:   reliable,
	}
	// 批量查询会话信息
	userIds := make([]string, 0)
	uins := make([]int64, 0)
	for _, presence := range presences {
		tmpid := presence.GetUserId()
		if uuid.FromStringOrNil(tmpid) != uuid.Nil {
			userIds = append(userIds, tmpid)
		} else {
			uins = append(uins, common.Interface2Int64(tmpid))
		}
	}

	nodePresences := make(map[string][]*models.Presence, 0) // 网关节点id作为key
	if len(userIds) > 0 {
		userInfos, err := s.i.GetOnline().QueryOnlineUsersInfo(ctx, userIds)
		if err != nil {
			logger.Error("StreamSend query online users info error: %v", err)
			return err
		}
		for _, presence := range presences {
			infos := userInfos[presence.GetUserId()]
			if len(infos) == 0 {
				continue
			}
			for _, info := range infos {
				// 如果会话id不为空，则只发送给指定会话id
				if presence.GetSessionId() != "" && presence.GetSessionId() != info.SessionId {
					continue
				}
				presence := &models.Presence{
					ID: models.PresenceID{
						SessionID: info.SessionId,
					},
					UserID: presence.GetUserId(),
					Meta: models.PresenceMeta{
						Hidden:      presence.GetHidden(),
						Persistence: presence.GetPersistence(),
						Username:    presence.GetUsername(),
						Status:      presence.GetStatus(),
						Reason:      uint32(presence.GetReason()),
					},
				}
				nodePresences[info.NodeId] = append(nodePresences[info.NodeId], presence)
			}
		}
	}
	if len(uins) > 0 {
		userInfos, err := s.i.GetOnline().QueryOnlineUsersInfoByUin(ctx, uins)
		if err != nil {
			logger.Error("StreamSend query online users info error: %v", err)
			return err
		}
		for _, presence := range presences {
			info := userInfos[common.Interface2Int64(presence.GetUserId())]
			if info == nil {
				continue
			}

			// 如果会话id不为空，则只发送给指定会话id
			if presence.GetSessionId() != "" && presence.GetSessionId() != info.SessionId {
				continue
			}
			presence := &models.Presence{
				ID: models.PresenceID{
					SessionID: info.SessionId,
				},
				UserID: presence.GetUserId(),
				Meta: models.PresenceMeta{
					Hidden:      presence.GetHidden(),
					Persistence: presence.GetPersistence(),
					Username:    presence.GetUsername(),
					Status:      presence.GetStatus(),
					Reason:      uint32(presence.GetReason()),
				},
			}
			nodePresences[info.NodeId] = append(nodePresences[info.NodeId], presence)
		}
	}

	for node_id, presences := range nodePresences {
		req.Presences = presences
		payload, err := json.Marshal(req)
		if err != nil {
			logger.Error("StreamSend marshal payload error: %v", err)
			continue
		}

		resp, err := s.i.GetSend().RpcToNodeId(ctx, node_id, models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, models.RPCID_GATESERVER_STREAM_DIRECTSEND, payload, nil, time.Second*3)
		if err != nil {
			logger.Error("StreamSend rpc error: %v", err)
			continue
		}
		result := &models.CommonResp{}
		if err := json.Unmarshal(resp, result); err != nil {
			logger.Error("StreamSend unmarshal result error: %v", err)
			continue
		}
		if result.Code != models.OK {
			logger.Error("StreamSend result error: %v", result.Msg)
			continue
		}
	}
	return nil
}

func (s *StreamGlobalDataStruct) StreamSendRaw(ctx context.Context, mode uint8, subject, subcontext, label string, msg *rtapi.Envelope, presences []runtime.Presence, reliable bool) error {
	if msg == nil {
		return fmt.Errorf("msg is nil")
	}
	if len(presences) == 0 {
		return fmt.Errorf("presences is empty")
	}

	data, err := protojson.Marshal(msg)
	if err != nil {
		return err
	}

	return s.StreamSend(ctx, mode, subject, subcontext, label, string(data), presences, reliable)
}

func (s *StreamGlobalDataStruct) StreamSendRawToUserIds(ctx context.Context, user_ids []string, msg *rtapi.Envelope) error {
	if msg == nil {
		return fmt.Errorf("msg is nil")
	}
	if len(user_ids) == 0 {
		return nil
	}

	data, err := protojson.Marshal(msg)
	if err != nil {
		return err
	}

	presences := make([]runtime.Presence, len(user_ids))
	for i, user_id := range user_ids {
		presences[i] = &models.Presence{
			UserID: user_id,
		}
	}

	return s.StreamSend(ctx, 0, "", "", "", string(data), presences, true)
}

func (s *StreamGlobalDataStruct) StreamSendRawToAll(ctx context.Context, mode uint8, subject, subcontext, label string, msg *rtapi.Envelope, reliable bool) error {
	if msg == nil {
		return fmt.Errorf("msg is nil")
	}

	data, err := protojson.Marshal(msg)
	if err != nil {
		return err
	}

	req := &models.StreamReq{
		Mode:       mode,
		Subject:    subject,
		Subcontext: subcontext,
		Label:      label,
		Data:       string(data),
		Reliable:   reliable,
	}
	payload, err := json.Marshal(req)
	if err != nil {
		s.i.GetLogger().Error("StreamSendRawToAll marshal payload error: %v", err)
		return err
	}
	s.i.GetSend().RpcAll(ctx, models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, models.RPCID_GATESERVER_STREAM_DIRECTSEND_BOARDCAST, payload, nil, time.Second*3)
	return nil
}
