package main

import (
	"context"
	"database/sql"
	"fmt"
	"kernel/kernel-common/api"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	json "github.com/json-iterator/go"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap/zapcore"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/gorilla/mux"
)

type FriendServerModule struct {
	name                 string
	logger               runtime.Logger
	db                   *sql.DB
	nk                   runtime.NakamaModule
	config               runtime.Config // nakama 配置
	CustomConfig         *CustomConfig  // 自定义配置
	isRegisterSelf       bool
	ProxyPassConfig      sync.Map
	controller           *Controller
	statusConsumerCancel context.CancelFunc

	common  *logic.CommonGlobalDataStruct
	online  *logic.OnlineGlobalDataStruct
	send    *logic.SendGlobalDataStruct
	dbagent *logic.DbAgentGlobalDataStruct
	notify  *logic.NotifyGlobalDataStruct
}

var FriendServerData *FriendServerModule

func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
	FriendServerData = new(FriendServerModule)
	FriendServerData.name = models.SERVER_NAME_FRIEND
	FriendServerData.logger = logger
	FriendServerData.db = db
	FriendServerData.nk = nk
	FriendServerData.common = logic.NewCommonGlobalDataStruct()
	FriendServerData.online = logic.NewOnlineGlobalDataStruct(FriendServerData)
	FriendServerData.send = logic.NewSendGlobalDataStruct(FriendServerData)
	FriendServerData.dbagent = logic.NewDbAgentGlobalDataStruct(FriendServerData)
	FriendServerData.notify = logic.NewNotifyGlobalDataStruct(FriendServerData)
	config, err := initializer.GetConfig()
	if err != nil {
		return err
	}
	FriendServerData.config = config
	if err := FriendServerData.common.Init(FriendServerData, FriendServerData.CustomConfig, FriendServerData.customConfigEvent); err != nil {
		logger.Error("init common logic error %v", err)
		return err
	}
	initializer.RegisterHttp(false, "/v2/friend", FriendServerData.FriendHttpHandler)
	initializer.RegisterHttp(false, "/v2/friend/{id:.*}", FriendServerData.FriendHttpHandler)

	initializer.RegisterShutdown(FriendServerData.Shutdown)

	// 自定义路由注册
	FriendServerData.controller = NewController(FriendServerData, FriendServerData.notify)
	initializer.RegisterRpc(RPCID_FRIENDSERVER_SEARCH, FriendServerData.controller.SearchFriend)

	// 从redis队列消费数据
	go FriendServerData.consumeStatusData()

	return nil
}

func (s *FriendServerModule) GetCommon() *logic.CommonGlobalDataStruct {
	return s.common
}
func (s *FriendServerModule) GetOnline() *logic.OnlineGlobalDataStruct {
	return s.online
}
func (s *FriendServerModule) GetSend() *logic.SendGlobalDataStruct {
	return s.send
}
func (s *FriendServerModule) GetDbAgent() *logic.DbAgentGlobalDataStruct {
	return s.dbagent
}
func (s *FriendServerModule) GetNotify() *logic.NotifyGlobalDataStruct {
	return s.notify
}
func (s *FriendServerModule) GetStream() *logic.StreamGlobalDataStruct {
	return nil
}
func (s *FriendServerModule) GetChannel() *logic.ChannelGlobalDataStruct {
	return nil
}
func (s *FriendServerModule) GetLogger() runtime.Logger {
	return s.logger
}
func (s *FriendServerModule) GetConfig() runtime.Config {
	return s.config
}
func (s *FriendServerModule) GetName() string {
	return s.name
}
func (s *FriendServerModule) GetDb() *sql.DB {
	return s.db
}
func (s *FriendServerModule) GetNk() runtime.NakamaModule {
	return s.nk
}

// 配置读取回调
func (s *FriendServerModule) customConfigEvent(dataId string, newobj any) {
	tmpobj, ok := newobj.(*CustomConfig)
	if !ok {
		return
	}
	if tmpobj.Config == nil {
		tmpobj.Config = &FriendServerConfig{}
	}
	defaultNodeCfg := false
	if tmpobj.Config.NodeConfig == nil {
		// 使用默认节点配置
		scheme := "http"
		if s.config.GetSocket().GetUseTLS() {
			scheme = "https"
		}
		tmpobj.Config.NodeConfig = &models.NodeConfig{
			NodeId:   s.config.GetName(),
			SvcGroup: fmt.Sprintf("%s@@%s", s.name, models.NACOS_DEFAULT_GROUP),
			Host:     fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			NetHost:  fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			AuthKey:  s.config.GetRuntime().GetHTTPKey(),
			Weight:   10,
			Scheme:   scheme,
		}
		defaultNodeCfg = true
	}
	tmp, _ := json.Marshal(tmpobj)
	s.logger.Debug("customConfigEvent defaultNodeCfg: %v, customCfg: %s", defaultNodeCfg, string(tmp))
	s.common.SetNodeConfig(tmpobj.Config.NodeConfig)
	s.common.WatchNodeList.InitWatchSvr(tmpobj.Config.WatchSvcGroupList, tmpobj.Config.NodeConfig.SvcGroup)
	s.CustomConfig = tmpobj

	// 注册服务
	selfcfg := map[string]interface{}{}
	if err := common.StrcutToMap(tmpobj.Config.NodeConfig, &selfcfg); err == nil {
		if !s.isRegisterSelf {
			go s.common.RegisterAndUpdateSelfToNacos(false, selfcfg)
			s.isRegisterSelf = true
		} else {
			go s.common.RegisterAndUpdateSelfToNacos(true, selfcfg)
		}
	} else {
		s.logger.Error("register self, config error %v", err)
	}

	s.common.UpdateAccessLogConfig(tmpobj.AccessLog)
	loglevel, err := zapcore.ParseLevel(tmpobj.Logger.Level)
	if err != nil {
		s.logger.Error("parse log level error %v", err)
	} else {
		s.nk.SetLoggerLevel(loglevel)
	}
}

func (s *FriendServerModule) Shutdown(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, reload bool) {
	logger.Info("%s shutdown", s.name)
	if !reload {
		s.common.UnRegisterSelfToNacos()
	}
	if s.statusConsumerCancel != nil {
		s.statusConsumerCancel()
	}
}

// 自定义好友http请求处理
func (s *FriendServerModule) FriendHttpHandler(w http.ResponseWriter, r *http.Request) {
	muxvars := mux.Vars(r)
	id := muxvars["id"]
	s.logger.Debug("friend http handler method: %s path: %s id: %s", r.Method, r.URL.Path, id)

	var user_id string
	if auth := r.Header.Get("Authorization"); auth != "" {
		if httpKey, _, ok := common.ParseBasicAuth2(auth); ok { // 服务器认证
			if httpKey != s.config.GetRuntime().GetHTTPKey() {
				s.logger.Error("http key invalid")
				w.WriteHeader(http.StatusUnauthorized)
				w.Write([]byte(AuthTokenInvalid))
				return
			}
			// 注意：这里user_id为空字符串
		} else if _userID, _, _, _, _, _, ok := common.ParseBearerAuth([]byte(s.config.GetSession().GetEncryptionKey()), auth); !ok { // 客户端认证
			s.logger.Error("auth token invalid")
			w.WriteHeader(http.StatusUnauthorized)
			w.Write([]byte(AuthTokenInvalid))
			return
		} else {
			user_id = _userID.String()
		}
	} else {
		s.logger.Error("auth required")
		w.WriteHeader(http.StatusUnauthorized)
		w.Write([]byte(AuthTokenInvalid))
		return
	}

	var (
		respBody []byte
		err      error
	)
	switch r.Method {
	case http.MethodGet:
		if id == "" {
			// 处理 /v2/friend GET 请求
			respBody, err = s.handleListFriends(r, user_id)
		} else {
			// 处理 /v2/friend/{id} GET 请求
			if id == "friends" {
				respBody, err = s.handleFriendsOfFriendsList(r, user_id)
			} else {
				http.Error(w, "Invalid operation", http.StatusBadRequest)
			}
		}
	case http.MethodPost:
		if id == "" {
			respBody, err = s.handleAddFriend(r, user_id) // 处理添加好友
		} else if id == "block" {
			respBody, err = s.handleBlockFriend(r, user_id) // 处理添加黑名单
		} else if id == "facebook" { // 导入facebook好友
			//respBody, err = s.handleImportFacebookFriends(r, user_id)
		} else if id == "steam" { // 导入steam好友
			//respBody, err = s.handleImportSteamFriends(r, user_id)
		} else {
			http.Error(w, "Invalid operation", http.StatusBadRequest)
		}
	case http.MethodDelete:
		if id == "" {
			// 处理删除好友
			respBody, err = s.handleDeleteFriend(r, user_id)
		} else {
			http.Error(w, "Invalid operation", http.StatusBadRequest)
		}
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
	if err != nil {
		s.logger.Error("friend http handler error %v", err)
		http.Error(w, err.Error(), http.StatusBadRequest)
	}
	if respBody != nil {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write(respBody)
	}
}

// 处理列出好友列表
func (s *FriendServerModule) handleListFriends(r *http.Request, user_id string) ([]byte, error) {
	queryParams := r.URL.Query()
	cursor := queryParams.Get("cursor")

	var state *int
	if queryParams.Get("state") != "" {
		tmpstate := common.StrToInt(queryParams.Get("state"))
		state = new(int)
		*state = tmpstate
	}

	limit := 0
	if queryParams.Get("limit") != "" {
		tmplimit := common.StrToInt(queryParams.Get("limit"))
		if tmplimit < 1 || tmplimit > 1000 {
			return nil, status.Error(codes.InvalidArgument, "Invalid limit - limit must be between 1 and 1000.")
		}
		limit = tmplimit
	}

	if state != nil {
		if *state < 0 || *state > 3 {
			return nil, status.Error(codes.InvalidArgument, "Invalid state - state must be between 0 and 3.")
		}
	}

	list, outgoingCursor, err := s.controller.FriendsListDetail(r.Context(), user_id, limit, state, cursor)
	if err != nil {
		return nil, err
	}

	apiList := &api.FriendList{
		Friends: list,
		Cursor:  outgoingCursor,
	}
	return json.Marshal(apiList)
}

// 处理列出好友的好友列表
func (s *FriendServerModule) handleFriendsOfFriendsList(r *http.Request, user_id string) ([]byte, error) {
	queryParams := r.URL.Query()
	cursor := queryParams.Get("cursor")
	limit := common.StrToInt(queryParams.Get("limit"))
	list, outgoingCursor, err := s.controller.FriendsOfFriendsList(r.Context(), user_id, limit, cursor)
	if err != nil {
		return nil, err
	}

	apiList := &api.FriendsOfFriendsList{
		FriendsOfFriends: list,
		Cursor:           outgoingCursor,
	}
	return json.Marshal(apiList)
}

// 处理添加好友
func (s *FriendServerModule) handleAddFriend(r *http.Request, user_id string) ([]byte, error) {
	queryParams := r.URL.Query()
	ids := queryParams.Get("ids")
	usernames := queryParams.Get("usernames")

	var idsList []string
	if ids != "" {
		idsList = strings.Split(ids, ",")
	}

	var usernamesList []string
	if usernames != "" {
		usernamesList = strings.Split(usernames, ",")
	}

	err := s.controller.FriendsAdd(r.Context(), user_id, "", idsList, usernamesList)
	if err != nil {
		return json.Marshal(models.CommonResp{
			Code: models.FAILED,
			Msg:  err.Error(),
		})
	}

	return json.Marshal(models.CommonResp{
		Code: models.OK,
		Msg:  "success",
	})
}

// 处理删除好友
func (s *FriendServerModule) handleDeleteFriend(r *http.Request, user_id string) ([]byte, error) {
	queryParams := r.URL.Query()
	ids := queryParams.Get("ids")
	usernames := queryParams.Get("usernames")

	var idsList []string
	if ids != "" {
		idsList = strings.Split(ids, ",")
	}

	var usernamesList []string
	if usernames != "" {
		usernamesList = strings.Split(usernames, ",")
	}

	err := s.controller.FriendsDelete(r.Context(), user_id, "", idsList, usernamesList)
	if err != nil {
		return json.Marshal(models.CommonResp{
			Code: models.FAILED,
			Msg:  err.Error(),
		})
	}

	return json.Marshal(models.CommonResp{
		Code: models.OK,
		Msg:  "success",
	})
}

// 处理添加黑名单
func (s *FriendServerModule) handleBlockFriend(r *http.Request, user_id string) ([]byte, error) {
	queryParams := r.URL.Query()
	ids := queryParams.Get("ids")
	usernames := queryParams.Get("usernames")

	var idsList []string
	if ids != "" {
		idsList = strings.Split(ids, ",")
	}

	var usernamesList []string
	if usernames != "" {
		usernamesList = strings.Split(usernames, ",")
	}

	err := s.controller.FriendsBlock(r.Context(), user_id, "", idsList, usernamesList)
	if err != nil {
		return json.Marshal(models.CommonResp{
			Code: models.FAILED,
			Msg:  err.Error(),
		})
	}

	return json.Marshal(models.CommonResp{
		Code: models.OK,
		Msg:  "success",
	})
}

// 订阅在线状态事件, 分片模式消费
func (s *FriendServerModule) consumeStatusData() {
	for i := 0; i < 100; i++ {
		if s.CustomConfig != nil {
			break
		}
		time.Sleep(100 * time.Millisecond)
	}
	if s.CustomConfig == nil {
		s.logger.Error("cannot start friend server: config not loaded")
		os.Exit(1)
		return
	}

	rdb := s.common.Redis
	consumerGroup := s.name
	nodeId := s.CustomConfig.Config.NodeConfig.NodeId

	ctx, cancel := context.WithCancel(context.Background())
	s.statusConsumerCancel = cancel

	// 创建消费者组，如果已存在则忽略错误
	err := rdb.XGroupCreateMkStream(ctx, logic.PLAYERS_EVENT_STREAM, consumerGroup, "0").Err()
	if err != nil && !strings.Contains(err.Error(), "BUSYGROUP") {
		s.logger.Error("create consumer group failed: %v", err)
	}

	// 启动多个消费者协程处理新消息
	for i := 0; i < 16; i++ {
		go func(workerID int) {
			consumerName := fmt.Sprintf("%s-%d", nodeId, workerID)

			for {
				select {
				case <-ctx.Done():
					return // 优雅退出
				default:
				}

				streams, err := rdb.XReadGroup(ctx, &redis.XReadGroupArgs{
					Group:    consumerGroup,
					Consumer: consumerName,
					Streams:  []string{logic.PLAYERS_EVENT_STREAM, ">"},
					Count:    10, // 每个工作线程处理较少的消息
					Block:    100 * time.Millisecond,
				}).Result()

				if (err != nil && err == redis.Nil) || len(streams) == 0 {
					continue
				}

				if err != nil {
					s.logger.Error("worker %d XReadGroup error: %v", workerID, err)
					continue
				}

				var ackIDs []string
				for _, str := range streams {
					for _, msg := range str.Messages {
						s.processStatusMessage(msg.Values, workerID)
						ackIDs = append(ackIDs, msg.ID)
					}
				}

				if len(ackIDs) > 0 {
					err := rdb.XAck(ctx, logic.PLAYERS_EVENT_STREAM, consumerGroup, ackIDs...).Err()
					if err != nil {
						s.logger.Error("worker %d XAck error: %v", workerID, err)
					}
				}
			}
		}(i)
	}

	s.retryPendingMessages(ctx, consumerGroup)
}

func (s *FriendServerModule) retryPendingMessages(ctx context.Context, consumerGroup string) {
	rdb := s.common.Redis
	nodeId := s.CustomConfig.Config.NodeConfig.NodeId
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	const (
		idleThreshold    = 30 * time.Second
		maxRetryAttempts = 5
		claimBatchSize   = 100
	)

	for {
		select {
		case <-ctx.Done():
			return // 优雅退出
		case <-ticker.C:
			// 获取 pending 消息列表
			pendingList, err := rdb.XPendingExt(ctx, &redis.XPendingExtArgs{
				Stream: logic.PLAYERS_EVENT_STREAM,
				Group:  consumerGroup,
				Idle:   idleThreshold,
				Start:  "-",
				End:    "+",
				Count:  claimBatchSize,
			}).Result()

			if err != nil && err != redis.Nil {
				s.logger.Error("retryPendingMessages XPendingExt error: %v", err)
				continue
			}
			if len(pendingList) == 0 {
				continue
			}

			s.logger.Debug("found %d pending messages need to retry", len(pendingList))

			// 过滤出需要重试的消息
			var retryIDs []string
			var forceAckIDs []string
			for _, entry := range pendingList {
				if entry.RetryCount >= maxRetryAttempts {
					forceAckIDs = append(forceAckIDs, entry.ID)
				} else {
					retryIDs = append(retryIDs, entry.ID)
				}
			}

			// 执行 XCLAIM 批量拉回
			if len(retryIDs) > 0 {
				msgs, err := rdb.XClaim(ctx, &redis.XClaimArgs{
					Stream:   logic.PLAYERS_EVENT_STREAM,
					Group:    consumerGroup,
					Consumer: fmt.Sprintf("retry-consumer-%s", nodeId),
					MinIdle:  idleThreshold,
					Messages: retryIDs,
				}).Result()

				if err != nil && err != redis.Nil {
					s.logger.Error("retryPendingMessages XClaim error: %v", err)
				}

				// 处理成功拉取的消息
				var ackIDs []string
				claimedSet := make(map[string]struct{})
				for _, msg := range msgs {
					s.processStatusMessage(msg.Values, -2) // -2 表示重试流程
					ackIDs = append(ackIDs, msg.ID)
					claimedSet[msg.ID] = struct{}{}
				}

				// 未 claim 到的消息，强制 ack 掉
				for _, id := range retryIDs {
					if _, ok := claimedSet[id]; !ok {
						forceAckIDs = append(forceAckIDs, id)
					}
				}

				if len(ackIDs) > 0 {
					err := rdb.XAck(ctx, logic.PLAYERS_EVENT_STREAM, consumerGroup, ackIDs...).Err()
					if err != nil {
						s.logger.Error("retryPendingMessages XAck error: %v", err)
					}
				}
			}

			// 强制 ack（比如死信、重复 claim 失败等）
			if len(forceAckIDs) > 0 {
				s.logger.Warn("force ACK %d messages (retry limit reached or not claimable)", len(forceAckIDs))
				err := rdb.XAck(ctx, logic.PLAYERS_EVENT_STREAM, consumerGroup, forceAckIDs...).Err()
				if err != nil {
					s.logger.Error("retryPendingMessages force ACK error: %v", err)
				}
			}
		}
	}
}

// 处理状态消息
func (s *FriendServerModule) processStatusMessage(values map[string]interface{}, workerID int) {
	if values == nil {
		s.logger.Error("worker %d receive empty message", workerID)
		return
	}

	event, ok := values["event"].(string)
	if !ok {
		s.logger.Error("worker %d invalid event type", workerID)
		return
	}

	switch event {
	case "online":
		if dataStr, ok := values["data"].(string); ok {
			onlineInfo := &models.UserOnlineInfo{}
			if err := json.Unmarshal([]byte(dataStr), onlineInfo); err != nil {
				s.logger.Error("worker %d parse online data failed: %v", workerID, err)
				return
			}

			// 验证必要字段
			if onlineInfo.UserId == "" || onlineInfo.Uin == 0 {
				s.logger.Error("worker %d online data missing required fields: %+v", workerID, onlineInfo)
				return
			}

			// 告诉我的好友, 我上线了
			state := 0
			friend_uids, err := s.controller.FriendsListOnlyId(context.Background(), onlineInfo.UserId, &state)
			if err != nil {
				s.logger.Error("get friends list failed: %s %v", onlineInfo.UserId, err)
				return
			}
			if len(friend_uids) == 0 {
				return
			}

			s.send.PushMulticast(context.Background(), friend_uids, "push.friend.online", map[string]interface{}{
				"user_id": onlineInfo.UserId,
				"uin":     onlineInfo.Uin,
			}, "", 0)
		} else {
			s.logger.Error("worker %d online event missing data field", workerID)
		}
	case "offline":
		uid := common.Interface2String(values["user_id"])
		uin := common.Interface2Int64(values["uin"])

		if uid == "" || uin == 0 {
			s.logger.Error("worker %d offline event missing required fields: user_id=%s, uin=%d", workerID, uid, uin)
			return
		}

		// 告诉我的好友, 我下线了
		state := 0
		friend_uids, err := s.controller.FriendsListOnlyId(context.Background(), uid, &state)
		if err != nil {
			s.logger.Error("get friends list failed: %s %v", uid, err)
			return
		}
		if len(friend_uids) == 0 {
			return
		}

		s.send.PushMulticast(context.Background(), friend_uids, "push.friend.offline", map[string]interface{}{
			"user_id": uid,
			"uin":     uin,
		}, "", 0)

	default:
		s.logger.Error("worker %d unknown event type: %v", workerID, event)
	}
}
