package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"sync"

	"go.uber.org/zap/zapcore"
)

type MallServerModule struct {
	name            string
	logger          runtime.Logger
	db              *sql.DB
	nk              runtime.NakamaModule
	config          runtime.Config // nakama 配置
	CustomConfig    *CustomConfig  // 自定义配置
	isRegisterSelf  bool
	ProxyPassConfig sync.Map
	controller      *Controller

	common  *logic.CommonGlobalDataStruct
	online  *logic.OnlineGlobalDataStruct
	send    *logic.SendGlobalDataStruct
	dbagent *logic.DbAgentGlobalDataStruct
	notify  *logic.NotifyGlobalDataStruct
	stream  *logic.StreamGlobalDataStruct
	channel *logic.ChannelGlobalDataStruct

	visualCfg *common.VisualCfg // 可视化配置
}

var MallServerData *MallServerModule

func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
	MallServerData = new(MallServerModule)
	MallServerData.name = models.SERVER_NAME_MALL
	MallServerData.logger = logger
	MallServerData.db = db
	MallServerData.nk = nk
	MallServerData.common = logic.NewCommonGlobalDataStruct()
	MallServerData.send = logic.NewSendGlobalDataStruct(MallServerData)
	MallServerData.dbagent = logic.NewDbAgentGlobalDataStruct(MallServerData)
	MallServerData.online = logic.NewOnlineGlobalDataStruct(MallServerData)
	MallServerData.notify = logic.NewNotifyGlobalDataStruct(MallServerData)
	MallServerData.stream = logic.NewStreamGlobalDataStruct(MallServerData)
	MallServerData.channel = logic.NewChannelGlobalDataStruct(MallServerData)
	config, err := initializer.GetConfig()
	if err != nil {
		return err
	}
	MallServerData.config = config
	MallServerData.controller = NewController(MallServerData)
	if err := MallServerData.common.Init(MallServerData, MallServerData.CustomConfig, MallServerData.customConfigEvent); err != nil {
		logger.Error("init common logic error %v", err)
		return err
	}

	initializer.RegisterShutdown(MallServerData.Shutdown)

	// 自定义路由注册
	initializer.RegisterRpc(RPCID_MALLSERVER_TEST, MallServerData.controller.Test)

	return nil
}

func (s *MallServerModule) GetCommon() *logic.CommonGlobalDataStruct {
	return s.common
}
func (s *MallServerModule) GetOnline() *logic.OnlineGlobalDataStruct {
	return s.online
}
func (s *MallServerModule) GetSend() *logic.SendGlobalDataStruct {
	return s.send
}
func (s *MallServerModule) GetDbAgent() *logic.DbAgentGlobalDataStruct {
	return s.dbagent
}
func (s *MallServerModule) GetNotify() *logic.NotifyGlobalDataStruct {
	return s.notify
}
func (s *MallServerModule) GetStream() *logic.StreamGlobalDataStruct {
	return s.stream
}
func (s *MallServerModule) GetChannel() *logic.ChannelGlobalDataStruct {
	return s.channel
}
func (s *MallServerModule) GetLogger() runtime.Logger {
	return s.logger
}
func (s *MallServerModule) GetConfig() runtime.Config {
	return s.config
}
func (s *MallServerModule) GetName() string {
	return s.name
}
func (s *MallServerModule) GetDb() *sql.DB {
	return s.db
}
func (s *MallServerModule) GetNk() runtime.NakamaModule {
	return s.nk
}

// 配置读取回调
func (s *MallServerModule) customConfigEvent(dataId string, newobj any) {
	tmpobj, ok := newobj.(*CustomConfig)
	if !ok {
		return
	}
	if tmpobj.Config == nil {
		tmpobj.Config = &MallServerConfig{}
	}
	defaultNodeCfg := false
	if tmpobj.Config.NodeConfig == nil {
		// 使用默认节点配置
		scheme := "http"
		if s.config.GetSocket().GetUseTLS() {
			scheme = "https"
		}
		tmpobj.Config.NodeConfig = &models.NodeConfig{
			NodeId:   s.config.GetName(),
			SvcGroup: fmt.Sprintf("%s@@%s", s.name, models.NACOS_DEFAULT_GROUP),
			Host:     fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			NetHost:  fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			AuthKey:  s.config.GetRuntime().GetHTTPKey(),
			Weight:   10,
			Scheme:   scheme,
		}
		defaultNodeCfg = true
	}
	tmp, _ := json.Marshal(tmpobj)
	s.logger.Debug("customConfigEvent defaultNodeCfg: %v, customCfg: %s", defaultNodeCfg, string(tmp))
	s.common.SetNodeConfig(tmpobj.Config.NodeConfig)
	s.common.WatchNodeList.InitWatchSvr(tmpobj.Config.WatchSvcGroupList, tmpobj.Config.NodeConfig.SvcGroup)
	s.CustomConfig = tmpobj

	// 注册服务
	selfcfg := map[string]interface{}{}
	if err := common.StrcutToMap(tmpobj.Config.NodeConfig, &selfcfg); err == nil {
		if !s.isRegisterSelf {
			go s.common.RegisterAndUpdateSelfToNacos(false, selfcfg)
			s.isRegisterSelf = true
		} else {
			go s.common.RegisterAndUpdateSelfToNacos(true, selfcfg)
		}
	} else {
		s.logger.Error("register self, config error %v", err)
	}

	s.common.UpdateAccessLogConfig(tmpobj.AccessLog)
	loglevel, err := zapcore.ParseLevel(tmpobj.Logger.Level)
	if err != nil {
		s.logger.Error("parse log level error %v", err)
	} else {
		s.nk.SetLoggerLevel(loglevel)
	}

	// 初始化可视化配置
	if s.visualCfg == nil {
		var visualNacosClient *common.NacosClient
		nacosconfig := tmpobj.Config.NacosConfig
		if nacosconfig != nil && len(nacosconfig.Urls) > 0 {
			if nacosconfig.AccessKey != "" && nacosconfig.SecretKey != "" {
				visualNacosClient, err = common.InitNacosByAkSk(nacosconfig.AccessKey, nacosconfig.SecretKey, nacosconfig.NamespaceId, nacosconfig.Urls, uint64(nacosconfig.TimeoutMs))
				if err != nil {
					s.logger.Error("init nacos, use ak sk error %v", err)
				}
			} else {
				visualNacosClient, err = common.InitNacos(nacosconfig.Username, nacosconfig.Password, nacosconfig.NamespaceId, nacosconfig.Urls, uint64(nacosconfig.TimeoutMs))
				if err != nil {
					s.logger.Error("init nacos, use username password error %v", err)
				}
			}
		} else {
			visualNacosClient = s.common.NacosClient
		}
		if visualNacosClient != nil {
			s.visualCfg = common.NewVisualCfg(s.logger, visualNacosClient)
			for _, cfgName := range s.CustomConfig.Config.VisualCfgs {
				s.logger.Info("listen visualconfig %s", cfgName)
				if err := s.visualCfg.ListenConfig(cfgName, s.controller.VisualCfgCallEvent); err != nil {
					s.logger.Error("listen config %s error %v", cfgName, err)
				}
			}
			if err := s.visualCfg.ListenMainConfig(); err != nil {
				s.logger.Error("listen configVersionControl.json config error %v", err)
			}
		} else {
			s.logger.Error("init visual nacos client error, nacos config is nil")
		}
	}
	if s.visualCfg != nil {
		// VisualCfgs中存在，但是没有监听的，需要添加监听。VisualCfgs中不存在的需要取消监听
		adds := map[string]struct{}{}
		for _, cfgName := range s.CustomConfig.Config.VisualCfgs {
			adds[cfgName] = struct{}{}
			if _, ok := s.visualCfg.SubKeyCallback.Load(cfgName); !ok {
				s.logger.Info("listen visualconfig %s", cfgName)
				if err := s.visualCfg.ListenConfig(cfgName, s.controller.VisualCfgCallEvent); err != nil {
					s.logger.Error("listen config %s error %v", cfgName, err)
				}
			}
		}
		s.visualCfg.SubKeyCallback.Range(func(key any, value any) bool {
			cfgName := key.(string)
			if _, ok := adds[cfgName]; !ok {
				s.logger.Info("unlisten visualconfig %s", cfgName)
				s.visualCfg.UnListenConfig(cfgName)
			}
			return true
		})
	}
}

func (s *MallServerModule) Shutdown(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, reload bool) {
	logger.Info("%s shutdown", s.name)
	if !reload {
		s.common.UnRegisterSelfToNacos()
	}
}
