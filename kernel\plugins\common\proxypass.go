package common

import (
	"kernel/plugins/models"
	"math/rand"
	"net/url"
	"strings"
	"time"
)

// 反向代理数据
type ProxyPassData struct {
	WRR         *WRRServer  // 加权轮训
	Hash        *Consistent // 一致性hash
	Hosts       []string    // host列表 ip:port
	CfgData     *models.Route
	HealthCheck bool // 健康检查
}

func NewProxyPassData() *ProxyPassData {
	return new(ProxyPassData)
}

func (p *ProxyPassData) Parse(cfg *models.Route) {
	if cfg == nil {
		return
	}
	p.CfgData = cfg
	if p.CfgData.ProxyType == models.PROXYHOSTS {
		p.Hosts = strings.Split(p.CfgData.ProxyData, ",")
		switch p.CfgData.LBType {
		case models.PROXYLBRR, models.PROXYLBWRR:
			wrr := NewWRRServer()
			for i := 0; i < len(p.Hosts); i++ {
				wrr.Set(&WRRNode{Host: p.Hosts[i], Weight: 10, CurrentWeight: 0})
			}
			p.WRR = wrr
		case models.PROXYLBHASH:
			hash := NewConsistent()
			hash.Set(p.Hosts)
			p.Hash = hash
		case models.PROXYLBRAND:
		case models.PROXYLBLEASSNUM:
		}
	}
}

// 代理类型为hosts时，获取一个url
func (p *ProxyPassData) GetUrl(key string) string {
	host := ""
	if p.CfgData.ProxyType == models.PROXYHOSTS {
		if p.CfgData.LBType == models.PROXYLBRR || p.CfgData.LBType == models.PROXYLBWRR {
			if p.WRR != nil {
				if n := p.WRR.Get(); n != nil {
					host = n.Host
				}
			}
		} else if p.CfgData.LBType == models.PROXYLBRAND || p.CfgData.LBType == "" {
			if len(p.Hosts) == 0 {
				host = ""
			} else if len(p.Hosts) == 1 {
				host = p.Hosts[0]
			} else {
				rand.Seed(time.Now().UnixNano())
				index := rand.Intn(1000)
				host = p.Hosts[index%len(p.Hosts)]
			}
		} else if p.CfgData.LBType == models.PROXYLBHASH {
			host, _ = p.Hash.Get(key)
		}
	}
	tmpurl := &url.URL{Scheme: p.CfgData.Scheme, Host: host, Path: p.CfgData.RoutePath}
	return tmpurl.String()
}

func (p *ProxyPassData) GetHost(key string) (host string) {
	if p.CfgData.ProxyType == models.PROXYHOSTS {
		if p.CfgData.LBType == models.PROXYLBRR || p.CfgData.LBType == models.PROXYLBWRR {
			if p.WRR != nil {
				if n := p.WRR.Get(); n != nil {
					host = n.Host
				}
			}
		} else if p.CfgData.LBType == models.PROXYLBRAND || p.CfgData.LBType == "" {
			if len(p.Hosts) == 0 {
				host = ""
			} else if len(p.Hosts) == 1 {
				host = p.Hosts[0]
			} else {
				rand.Seed(time.Now().UnixNano())
				index := rand.Intn(1000)
				host = p.Hosts[index%len(p.Hosts)]
			}
		} else if p.CfgData.LBType == models.PROXYLBHASH {
			host, _ = p.Hash.Get(key)
		}
	}
	return
}

func (p *ProxyPassData) GetHostDetail(key string) (host, method, scheme, routepath, authkey string, transport int) {
	if p.CfgData.ProxyType == models.PROXYHOSTS {
		if p.CfgData.LBType == models.PROXYLBRR || p.CfgData.LBType == models.PROXYLBWRR {
			if p.WRR != nil {
				if n := p.WRR.Get(); n != nil {
					host = n.Host
				}
			}
		} else if p.CfgData.LBType == models.PROXYLBRAND || p.CfgData.LBType == "" {
			if len(p.Hosts) == 0 {
				host = ""
			} else if len(p.Hosts) == 1 {
				host = p.Hosts[0]
			} else {
				rand.Seed(time.Now().UnixNano())
				index := rand.Intn(1000)
				host = p.Hosts[index%len(p.Hosts)]
			}
		} else if p.CfgData.LBType == models.PROXYLBHASH {
			host, _ = p.Hash.Get(key)
		}
	}
	return host, p.CfgData.Method, p.CfgData.Scheme, p.CfgData.RoutePath, p.CfgData.AuthKey, p.CfgData.Transport
}
