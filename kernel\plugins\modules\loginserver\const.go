package main

import "regexp"

const (
	RPCID_CREATE_ACCOUNT_EMAIL = "loginserver.createaccount.email" // 根据邮箱注册账号
	RPCID_QUERY_LOGIN_LOGS     = "loginserver.queryloginlogs"      // 查询用户登录记录
	RPCID_AUTH_EMAIL           = "loginserver.auth.email"          // 验证邮箱登录
	RPCID_AUTH_EMAIL_CODE      = "loginserver.auth.emailcode"      // 验证邮箱验证码并登录
)

// 无效的特殊字符
var InvalidCharsRegex = regexp.MustCompilePOSIX(`([[:cntrl:]]|[[:space:]]|[\t\n\r\f\v])+`)
