package main

import (
	"kernel/plugins/models"
	"sync"

	"github.com/spaolacci/murmur3"
)

type StatusBucketList struct {
	bucketNum int
	list      []*StatusBucket
}

func NewStatusBucketList(bucketNum int) *StatusBucketList {
	userIndexBuckets := make([]*StatusBucket, bucketNum)
	for i := 0; i < bucketNum; i++ {
		userIndexBuckets[i] = NewStatusBucket(i)
	}
	return &StatusBucketList{
		bucketNum: bucketNum,
		list:      userIndexBuckets,
	}
}

func (s *StatusBucketList) Add(onlineinfo *models.UserOnlineInfo) {
	if onlineinfo == nil || onlineinfo.UserId == "" || onlineinfo.Uin == 0 {
		return
	}
	bucketid := int(murmur3.Sum64([]byte(onlineinfo.UserId)) % uint64(s.bucketNum))
	s.list[bucketid].AddUid(onlineinfo)
	bucketid = int(onlineinfo.Uin % int64(s.bucketNum))
	s.list[bucketid].AddUin(onlineinfo)
}

func (s *StatusBucketList) Remove(uid string, uin int64) {
	if uid == "" || uin == 0 {
		return
	}
	bucketid := int(murmur3.Sum64([]byte(uid)) % uint64(s.bucketNum))
	s.list[bucketid].RemoveUid(uid)
	bucketid = int(uin % int64(s.bucketNum))
	s.list[bucketid].RemoveUin(uin)
}

func (s *StatusBucketList) GetByUid(uid string) *models.UserOnlineInfo {
	if uid == "" {
		return nil
	}
	bucketid := int(murmur3.Sum64([]byte(uid)) % uint64(s.bucketNum))
	return s.list[bucketid].GetUid(uid)
}

func (s *StatusBucketList) GetByUin(uin int64) *models.UserOnlineInfo {
	if uin < 0 {
		return nil
	}
	bucketid := int(uin % int64(s.bucketNum))
	return s.list[bucketid].GetUin(uin)
}

type StatusBucket struct {
	bucketid int
	uins     map[int64]*models.UserOnlineInfo
	uids     map[string]*models.UserOnlineInfo
	sync.RWMutex
}

func NewStatusBucket(bucketid int) *StatusBucket {
	return &StatusBucket{
		bucketid: bucketid,
		uins:     make(map[int64]*models.UserOnlineInfo),
		uids:     make(map[string]*models.UserOnlineInfo),
	}
}

func (s *StatusBucket) AddUin(onlineinfo *models.UserOnlineInfo) {
	s.Lock()
	defer s.Unlock()
	s.uins[onlineinfo.Uin] = onlineinfo
}

func (s *StatusBucket) AddUid(onlineinfo *models.UserOnlineInfo) {
	s.Lock()
	defer s.Unlock()
	s.uids[onlineinfo.UserId] = onlineinfo
}

func (s *StatusBucket) RemoveUin(uin int64) {
	s.Lock()
	defer s.Unlock()
	delete(s.uins, uin)
}

func (s *StatusBucket) RemoveUid(uid string) {
	s.Lock()
	defer s.Unlock()
	delete(s.uids, uid)
}

func (s *StatusBucket) GetUin(uin int64) *models.UserOnlineInfo {
	s.RLock()
	defer s.RUnlock()
	return s.uins[uin]
}

func (s *StatusBucket) GetUid(uid string) *models.UserOnlineInfo {
	s.RLock()
	defer s.RUnlock()
	return s.uids[uid]
}
