package main

import (
	"kernel/plugins/models"
)

// 加入排队请求
type ReqJoinQueue struct {
	RoomID string `json:"room_id"`
}

// 加入排队结果
type RespJoinQueue struct {
	Code models.ErrorCode `json:"code"`
	Msg  string           `json:"message,omitempty"`
	Data *QueueStatus     `json:"data,omitempty"`
}

// 离开排队请求
type ReqLeaveQueue struct {
	RoomID string `json:"room_id"`
}

// 获取排队状态请求
type ReqGetQueueStatus struct {
	RoomID string `json:"room_id"`
}

// 获取排队状态结果
type RespGetQueueStatus struct {
	Code models.ErrorCode `json:"code"`
	Msg  string           `json:"message,omitempty"`
	Data *QueueStatus     `json:"data,omitempty"`
}

// 排队状态
type QueueStatus struct {
	Rank  int64 `json:"rank"`
	Total int64 `json:"total"`
}

// 查询所有房间请求
type RespQueryAllRoom struct {
	Code     int         `json:"code"`
	Msg      string      `json:"message,omitempty"`
	RoomList []*RoomInfo `json:"roomlist,omitempty"`
}

type RespQueryRoomInfo struct {
	Code int    `json:"code"`
	Msg  string `json:"message,omitempty"`
	RoomInfo
}

type RoomInfo struct {
	Aid             string           `json:"aid,omitempty"`
	Roomid          string           `json:"roomid,omitempty"`
	Ip              string           `json:"ip,omitempty"`
	Port            int              `json:"port,omitempty"`
	RoomCap         int32            `json:"room_cap,omitempty"`
	PlayerNum       int32            `json:"player_num,omitempty"`
	ModUrl          string           `json:"mod_url,omitempty"`
	RoomMods        string           `json:"room_mods,omitempty"`
	RoomUiLibs      string           `json:"room_ui_libs,omitempty"`
	RoomVer         string           `json:"room_ver,omitempty"`
	RoomName        string           `json:"room_name,omitempty"`
	RoomAudioConfig string           `json:"room_audio_config,omitempty"`
	RoomTranslate   string           `json:"room_translate,omitempty"` // 翻译下载url
	CzbUuid         string           `json:"czb_uuid,omitempty"`
	Uin             int              `json:"uin,omitempty"`
	NickName        string           `json:"nick_name,omitempty"`
	IsCloud         bool             `json:"is_cloud,omitempty"`
	PasswdMD5       string           `json:"passwd_md5,omitempty"`
	MapShareVersion string           `json:"share_version,omitempty"` // 地图版本号
	Passwd          string           `json:"passwd,omitempty"`        // 为房主的时候才会有这个字段
	TeamId          int              `json:"team_id,omitempty"`       // 队伍id
	PublicType      int32            `json:"public_type,omitempty"`   // 房间公开类型
	CanTrace        int32            `json:"can_trace,omitempty"`     // 房间追踪类型
	Personal        int              `json:"personal,omitempty"`      // personal=1 私人云服
	Teams           []TeamInfo       `json:"teams,omitempty"`         // 队伍信息
	RoomFrom        string           `json:"room_from,omitempty"`     // 房间来源
	RoomLabels      map[string]int32 `json:"room_labels,omitempty"`   // 房间标签
	RoomDesc        string           `json:"room_desc,omitempty"`     // 房间描述
	// extra_1  标识是否成功下发云服房间标识
	// extra_2  标识创建房间还是进入房间
	Extra1   string `json:"-"`
	Extra2   string `json:"-"`
	CostTime int64  `json:"-"`
}

type TeamInfo struct {
	TeamId  int      `json:"team_id,omitempty"`
	Cap     int      `json:"cap,omitempty"`
	UinList []string `json:"uin_list,omitempty"`
}

// 通知玩家加入排队结果
type PushJoinQueueResult struct {
	Code models.ErrorCode `json:"code"`
	Msg  string           `json:"message,omitempty"`
	Data *RoomInfo        `json:"data,omitempty"`
}
