package common

import (
	"encoding/json"
	"errors"
	"fmt"
	"kernel/kernel-common/runtime"
	"kernel/plugins/models"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/nacos-group/nacos-sdk-go/v2/model"
)

type WatchNodeList struct {
	WatchSvrList   sync.Map        // map[string]*SvrCfgList = make(map[string]*SvrCfgList) key: svcname@@nacosgroup
	watchSvrTypes  map[string]bool // key: svcname@@nacosgroup
	watchEventLock sync.Mutex
	logger         runtime.Logger
	serverName     string
	nacosClient    *NacosClient
}

type SvrCfgList struct {
	svcgroup       string // 服务类型名@@组名
	svrs           map[string]*models.NodeConfig
	sortSvrs       tsortsvrs
	svrMutex       sync.RWMutex
	ConsistentHash *Consistent
	WRR            *WRRServer
	FailedNum      int64 // 失败次数
}

func NewWatchNodeList(logger runtime.Logger, serverName string, nacosClient *NacosClient) *WatchNodeList {
	return &WatchNodeList{watchSvrTypes: make(map[string]bool), logger: logger, serverName: serverName, nacosClient: nacosClient}
}

//var WatchSvrList sync.Map // map[string]*SvrCfgList = make(map[string]*SvrCfgList)
//var WatchSvrTypes map[string]bool = make(map[string]bool)
//var WatchEventLock sync.Mutex

type tsortsvrs []*models.NodeConfig

// Len returns the length of the uints array.
func (x tsortsvrs) Len() int { return len(x) }

// Less returns true if element i is less than element j.
func (x tsortsvrs) Less(i, j int) bool { return x[i].ConnNum < x[j].ConnNum }

// Swap exchanges elements i and j.
func (x tsortsvrs) Swap(i, j int) { x[i], x[j] = x[j], x[i] }

func GenWatchSvrKey(svcname, nacosgroup string) string {
	return fmt.Sprintf("%s@@%s", svcname, nacosgroup)
}

func ParseWatchSvrKey(key string) (svcname, nacosgroup string) {
	arr := strings.Split(key, "@@")
	if len(arr) != 2 {
		return "", ""
	}
	return arr[0], arr[1]
}

func CheckWatchSvrKey(key string) bool {
	svcname, nacosgroup := ParseWatchSvrKey(key)
	if svcname == "" || nacosgroup == "" {
		return false
	}
	return true
}

func (w *WatchNodeList) InitWatchSvr(watchs []string, selfNodeGroup string) {
	for _, svcgroup := range watchs {
		if !CheckWatchSvrKey(svcgroup) {
			w.logger.Error("watchsvr key error %s, must be svcname@@nacosgroup", svcgroup)
			continue
		}
		w.WatchSvrList.LoadOrStore(svcgroup, w.NewSvrCfgList(svcgroup))
	}

	// 注册我关注的其他服务事件
	w.UpdateWatchSvr(watchs, selfNodeGroup)

	// 检查WatchSvrList服务是否有效
	//go checkInvaildSvrCfg()
}

func (w *WatchNodeList) NewSvrCfgList(svcgroup string) *SvrCfgList {
	return &SvrCfgList{svcgroup: svcgroup, svrs: make(map[string]*models.NodeConfig), ConsistentHash: NewConsistent(), WRR: NewWRRServer()}
}

func (w *WatchNodeList) UpdateWatchSvr(watchs []string, selfNodeGroup string) {
	tmpwatch := make(map[string]bool)
	for _, v := range watchs {
		tmpwatch[v] = true
	}
	// 添加关注自身服务类型
	if !tmpwatch[selfNodeGroup] {
		tmpwatch[selfNodeGroup] = true
	}
	// 注册我关注的其他服务事件
	for k, _ := range tmpwatch {
		if !w.watchSvrTypes[k] {
			w.logger.Info("add watcher other svr %s", k)
			w.watchSvrTypes[k] = true
			svcname, nacosgroup := ParseWatchSvrKey(k)
			if err := w.nacosClient.SubscribeWatch(svcname, nacosgroup, w.NacosWatchEvent(k)); err != nil {
				w.logger.Error("%s", err.Error())
			}
		} else {
			w.logger.Info("watcher other svr %s already exists", k)
		}
		//EtcdWatchContinue(models.GetRegEtcdSvrTypeKey(v), EtcdWatchEvent, EtcdWatchGetResp, clientv3.WithPrefix())
	}
	// 取消未关注的服务
	for k, _ := range w.watchSvrTypes {
		if !tmpwatch[k] {
			w.logger.Info("unwatcher other svr %s", k)
			svcname, nacosgroup := ParseWatchSvrKey(k)
			if err := w.nacosClient.UnSubscribeWatch(svcname, nacosgroup, func(services []model.Instance, err error) {}); err == nil {
				w.watchSvrTypes[k] = false
			}
		}
	}
}

func (w *WatchNodeList) NacosWatchEvent(svcgroup string) func(services []model.Instance, err error) {
	return func(services []model.Instance, err error) {
		w.watchEventLock.Lock()
		defer w.watchEventLock.Unlock()
		if err != nil {
			w.logger.Error("NacosWatchEvent: %s", err.Error())
			return
		}

		//log.Debug(jsoniter.MarshalToString(services))
		enables := map[string]bool{}
		for i := 0; i < len(services); i++ {
			newcfg := new(models.NodeConfig)
			w.NacosInstanceToServiceConfig(newcfg, &services[i])

			if newcfg.SvcGroup != svcgroup {
				w.logger.Error("node config error %s, svcgroup not match, %s != %s", newcfg.NodeId, newcfg.SvcGroup, svcgroup)
				continue
			}

			enables[newcfg.NodeId] = true
			ret, loaded := w.WatchSvrList.LoadOrStore(svcgroup, w.NewSvrCfgList(svcgroup))
			if ret != nil {
				cfglist := ret.(*SvrCfgList)
				oldcfg := cfglist.Get(newcfg.NodeId)
				if oldcfg != nil {
					cfglist.Update(newcfg, oldcfg)
					w.logger.Info("%s update %s server %s %v", w.serverName, newcfg.NodeId, newcfg.Host, services[i].Enable)
				} else {
					cfglist.Set(newcfg)
					w.logger.Info("%s add %s server %s %v", w.serverName, newcfg.NodeId, newcfg.Host, services[i].Enable)
				}
				sort.Sort(cfglist.sortSvrs)
			}
			if !loaded {
				w.logger.Error("watcher svr key not found, add %s", svcgroup)
			}
		}

		ret, ok := w.WatchSvrList.Load(svcgroup)
		if ok {
			list := ret.(*SvrCfgList)
			for k, v := range list.GetAll() {
				if !enables[k] {
					tmphost := v.Host
					w.logger.Info("delete %s server %s", k, tmphost)
					list.Del(k)
				}
			}
		}
	}
}

func (w *WatchNodeList) NacosInstanceToServiceConfig(cfg *models.NodeConfig, instance *model.Instance) {
	if cfg == nil || instance == nil {
		return
	}
	cfg.NodeId = instance.Metadata["node_id"]
	cfg.SvcGroup = instance.Metadata["svc_group"]
	cfg.Host = instance.Metadata["host"]
	cfg.NetHost = instance.Metadata["nethost"]
	cfg.Region = instance.Metadata["region"]
	cfg.AuthKey = instance.Metadata["auth_key"]
	cfg.Weight = StrToInt(instance.Metadata["weight"])
	cfg.HostName = instance.Metadata["hostname"]
	cfg.Scheme = instance.Metadata["scheme"]
	json.Unmarshal([]byte(instance.Metadata["extend"]), &cfg.Extend)
	cfg.MaxConns = StrToInt(instance.Metadata["maxconns"])
	cfg.ConnNum = StrToInt(instance.Metadata["connnum"])
}

// alloc server, ConsistentHash
func (w *WatchNodeList) AllocService(svrtype, group, key string) (string, error) {
	svcgroup := GenWatchSvrKey(svrtype, group)
	res, ok := w.WatchSvrList.Load(svcgroup)
	if ok {
		svrcfg := res.(*SvrCfgList)
		host, err := svrcfg.ConsistentHash.Get(key)
		if host == "" || err != nil {
		} else {
			svrcfg.FailedNum = 0
		}
		return host, err
	}
	return "", errors.New(svcgroup + " not found")
}

// alloc server, ConsistentHash
func (w *WatchNodeList) AllocServiceConfig(svrtype, group, key string) (*models.NodeConfig, error) {
	svcgroup := GenWatchSvrKey(svrtype, group)
	res, ok := w.WatchSvrList.Load(svcgroup)
	if ok {
		svrcfg := res.(*SvrCfgList)
		host, err := svrcfg.ConsistentHash.Get(key)
		if err != nil {
			return nil, err
		}
		cfg, err := w.GetSvrCfgByHost(svrtype, group, host)
		svrcfg.FailedNum = 0
		return cfg, err
	}
	return nil, errors.New(svcgroup + " not found")
}

// alloc server
func (w *WatchNodeList) RandAllocService(svrtype, group string) (string, error) {
	svcgroup := GenWatchSvrKey(svrtype, group)
	res, ok := w.WatchSvrList.Load(svcgroup)
	if ok {
		svrcfg := res.(*SvrCfgList)
		rand.Seed(time.Now().UnixNano())
		host, err := svrcfg.ConsistentHash.Get(strconv.Itoa(rand.Intn(1000)))
		if host == "" || err != nil {
		} else {
			svrcfg.FailedNum = 0
		}
		return host, err
	}
	return "", errors.New(svcgroup + " not found")
}

// alloc server，加权轮训
func (w *WatchNodeList) WRRAllocService(svrtype, group string) (string, error) {
	svcgroup := GenWatchSvrKey(svrtype, group)
	res, ok := w.WatchSvrList.Load(svcgroup)
	if ok {
		svrcfg := res.(*SvrCfgList)
		n := svrcfg.WRR.Get()
		if n == nil {
			return "", errors.New(svcgroup + " not found")
		}
		svrcfg.FailedNum = 0
		return n.Host, nil
	}
	return "", errors.New(svcgroup + " not found")
}

// alloc server，加权轮训
func (w *WatchNodeList) WRRAllocServiceCfg(svrtype, group string) (*models.NodeConfig, error) {
	svcgroup := GenWatchSvrKey(svrtype, group)
	res, ok := w.WatchSvrList.Load(svcgroup)
	if ok {
		svrcfg := res.(*SvrCfgList)
		n := svrcfg.WRR.Get()
		if n == nil {
			return nil, errors.New(svcgroup + " not found")
		}
		svrcfg.FailedNum = 0
		cfg, err := w.GetSvrCfgByHost(svrtype, group, n.Host)
		return cfg, err
	}
	return nil, errors.New(svcgroup + " not found")
}

// alloc server
func (w *WatchNodeList) RandAllocServiceCfg(svrtype, group string) (*models.NodeConfig, error) {
	svcgroup := GenWatchSvrKey(svrtype, group)
	res, ok := w.WatchSvrList.Load(svcgroup)
	if ok {
		svrcfg := res.(*SvrCfgList)
		rand.Seed(time.Now().UnixNano())
		host, err := svrcfg.ConsistentHash.Get(strconv.Itoa(rand.Intn(1000)))
		if err != nil {
			return nil, err
		}
		svrcfg.FailedNum = 0
		cfg, err := w.GetSvrCfgByHost(svrtype, group, host)
		return cfg, err
	}
	return nil, errors.New(svcgroup + " not found")
}

func (w *WatchNodeList) GetSvrCfgByID(svrtype, group, service_id string) (*models.NodeConfig, error) {
	svcgroup := GenWatchSvrKey(svrtype, group)
	res, ok := w.WatchSvrList.Load(svcgroup)
	if ok {
		svrcfgs := res.(*SvrCfgList)
		cfg := svrcfgs.Get(service_id)
		if cfg != nil {
			svrcfgs.FailedNum = 0
		}
		return cfg, nil
	}
	return nil, errors.New(svcgroup + " not found")
}

func (w *WatchNodeList) GetSvrCfgByHost(svrtype, group, host string) (*models.NodeConfig, error) {
	svcgroup := GenWatchSvrKey(svrtype, group)
	res, ok := w.WatchSvrList.Load(svcgroup)
	if ok {
		svrcfgs := res.(*SvrCfgList)
		cfg := svrcfgs.GetByHost(host)
		if cfg != nil {
			svrcfgs.FailedNum = 0
		}
		return cfg, nil
	}
	return nil, errors.New(svcgroup + " not found")
}

func (w *WatchNodeList) GetLeastConnNumSvr(svrtype, group string) (*models.NodeConfig, error) {
	svcgroup := GenWatchSvrKey(svrtype, group)
	res, ok := w.WatchSvrList.Load(svcgroup)
	if ok {
		svrcfgs := res.(*SvrCfgList)
		cfg := svrcfgs.GetLeastConnNum()
		if cfg != nil {
			svrcfgs.FailedNum = 0
		}
		return cfg, nil
	}
	return nil, errors.New(svcgroup + " not found")
}

func (w *WatchNodeList) GetSvrNodeNum(svrtype, group string) (int, error) {
	svcgroup := GenWatchSvrKey(svrtype, group)
	res, ok := w.WatchSvrList.Load(svcgroup)
	if ok {
		svrcfgs := res.(*SvrCfgList)
		return len(svrcfgs.svrs), nil
	}
	return 0, errors.New(svcgroup + " not found")
}

func (s *SvrCfgList) Get(svrid string) *models.NodeConfig {
	s.svrMutex.RLock()
	defer s.svrMutex.RUnlock()
	cfg, ok := s.svrs[svrid]
	if ok {
		return cfg
	}
	return nil
}
func (s *SvrCfgList) GetAll() map[string]*models.NodeConfig {
	tmp := map[string]*models.NodeConfig{}
	s.svrMutex.RLock()
	defer s.svrMutex.RUnlock()
	for k, v := range s.svrs {
		tmp[k] = v
	}
	return tmp
}

func (s *SvrCfgList) GetByHost(host string) *models.NodeConfig {
	s.svrMutex.RLock()
	defer s.svrMutex.RUnlock()
	for _, v := range s.svrs {
		if v.Host == host {
			return v
		}
	}
	return nil
}

func (s *SvrCfgList) Set(cfg *models.NodeConfig) {
	if cfg == nil {
		return
	}
	s.svrMutex.Lock()
	defer s.svrMutex.Unlock()

	if cfg.Scheme == "" {
		cfg.Scheme = "http"
	}

	s.sortSvrs = append(s.sortSvrs, cfg)
	s.ConsistentHash.Add(cfg.Host)
	if cfg.Weight == 0 {
		cfg.Weight = 10 // 启用的情况下调整权重
	}
	s.WRR.Set(&WRRNode{Host: cfg.Host, Weight: cfg.Weight})

	s.svrs[cfg.NodeId] = cfg
}

func (s *SvrCfgList) Del(svrid string) {
	s.svrMutex.Lock()
	defer s.svrMutex.Unlock()

	val, ok := s.svrs[svrid]
	if !ok || val == nil {
		return
	}
	s.ConsistentHash.Remove(val.Host)
	//s.WRR.Set(&WRRNode{Host: val.Host, Weight: 0})
	s.WRR.Del(val.Host)
	for i, v := range s.sortSvrs {
		if v.NodeId == svrid {
			s.sortSvrs = append(s.sortSvrs[:i], s.sortSvrs[i+1:]...)
			break
		}
	}
	val.Host = ""
	val.NetHost = ""

	delete(s.svrs, svrid)
}

//func (s *SvrCfgList) UpdateIsEnable(svrid string, isenable bool) {
//	s.svrMutex.Lock()
//	defer s.svrMutex.Unlock()
//	val, ok := s.svrs[svrid]
//	if ok {
//		if !val.IsEnable && isenable {
//			s.sortSvrs = append(s.sortSvrs, val)
//			s.ConsistentHash.Add(val.Host)
//			if val.Weight == 0 {
//				val.Weight = 10 // 启用的情况下调整权重
//			}
//			s.WRR.Set(&WRRNode{Host: val.Host, Weight: val.Weight})
//		}
//		val.IsEnable = isenable
//		if !isenable {
//			s.ConsistentHash.Remove(val.Host)
//			//s.WRR.Set(&WRRNode{Host: val.Host, Weight: 0})
//			s.WRR.Del(val.Host)
//			for i, v := range s.sortSvrs {
//				if v.ServiceID == svrid {
//					s.sortSvrs = append(s.sortSvrs[:i], s.sortSvrs[i+1:]...)
//					break
//				}
//			}
//			val.Host = ""
//			val.NetHost = ""
//		}
//	}
//}

func (s *SvrCfgList) Update(newcfg *models.NodeConfig, oldcfg *models.NodeConfig) {
	if newcfg == nil || oldcfg == nil {
		return
	}

	s.svrMutex.Lock()
	defer s.svrMutex.Unlock()

	if newcfg.Scheme == "" {
		newcfg.Scheme = "http"
	}
	if oldcfg.Host != newcfg.Host {
		if newcfg.Weight == 0 {
			newcfg.Weight = 10 // 启用的情况下调整权重
		}
		s.ConsistentHash.Add(newcfg.Host)
		s.WRR.Set(&WRRNode{Host: newcfg.Host, Weight: newcfg.Weight})
		s.ConsistentHash.Remove(oldcfg.Host)
		s.WRR.Del(oldcfg.Host)
	}
	oldcfg.Host = newcfg.Host
	oldcfg.NetHost = newcfg.NetHost
	oldcfg.AuthKey = newcfg.AuthKey
	oldcfg.Weight = newcfg.Weight
	oldcfg.ConnNum = newcfg.ConnNum
	oldcfg.MaxConns = newcfg.MaxConns
	oldcfg.HostName = newcfg.HostName
	oldcfg.Scheme = newcfg.Scheme
}

func (s *SvrCfgList) UpdateConnNum(svrid string, connNum int) {
	s.svrMutex.Lock()
	defer s.svrMutex.Unlock()
	val, ok := s.svrs[svrid]
	if ok {
		val.ConnNum = connNum
	}
}

func (s *SvrCfgList) GetLeastConnNum() *models.NodeConfig {
	s.svrMutex.Lock()
	defer s.svrMutex.Unlock()
	tmpnum := 9999999
	//var tmpsvr *models.ServiceConfig
	for _, v := range s.svrs {
		if v.MaxConns > 0 && v.ConnNum >= v.MaxConns {
			continue // 超过最大连接数，不再分配
		}
		if v.ConnNum <= tmpnum {
			tmpnum = v.ConnNum
			//tmpsvr = v
		}
	}
	if tmpnum == 9999999 {
		return nil
	}
	list := []*models.NodeConfig{}
	for _, v := range s.svrs {
		if v.MaxConns > 0 && v.ConnNum >= v.MaxConns {
			continue // 超过最大连接数，不再分配
		}
		if v.ConnNum <= (tmpnum+1500) || v.ConnNum >= (tmpnum-1500) {
			list = append(list, v)
		}
	}
	if len(list) == 0 {
		return nil
	}
	return list[rand.Intn(1000)%len(list)]
}

func (s *SvrCfgList) GetConnNumTotal() int {
	s.svrMutex.RLock()
	defer s.svrMutex.RUnlock()
	total := 0
	for _, v := range s.svrs {
		total += v.ConnNum
	}
	return total
}

func (s *SvrCfgList) Sort() {
	s.svrMutex.Lock()
	defer s.svrMutex.Unlock()
	sort.Sort(s.sortSvrs)
}

func (s *SvrCfgList) GetSort() []*models.NodeConfig {
	list := []*models.NodeConfig{}
	s.svrMutex.RLock()
	defer s.svrMutex.RUnlock()
	for i := 0; i < len(s.sortSvrs); i++ {
		list = append(list, s.sortSvrs[i])
	}
	return list
}
