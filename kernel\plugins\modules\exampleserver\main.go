package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"sync"

	"go.uber.org/zap/zapcore"
)

type ExampleServerModule struct {
	name            string
	logger          runtime.Logger
	db              *sql.DB
	nk              runtime.NakamaModule
	config          runtime.Config // nakama 配置
	CustomConfig    *CustomConfig  // 自定义配置
	isRegisterSelf  bool
	ProxyPassConfig sync.Map
	controller      *Controller

	common  *logic.CommonGlobalDataStruct
	online  *logic.OnlineGlobalDataStruct
	send    *logic.SendGlobalDataStruct
	dbagent *logic.DbAgentGlobalDataStruct
	notify  *logic.NotifyGlobalDataStruct
	stream  *logic.StreamGlobalDataStruct
	channel *logic.ChannelGlobalDataStruct
}

var ExampleServerData *ExampleServerModule

func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
	ExampleServerData = new(ExampleServerModule)
	ExampleServerData.name = models.SERVER_NAME_EXAMPLE
	ExampleServerData.logger = logger
	ExampleServerData.db = db
	ExampleServerData.nk = nk
	ExampleServerData.common = logic.NewCommonGlobalDataStruct()
	ExampleServerData.send = logic.NewSendGlobalDataStruct(ExampleServerData)
	ExampleServerData.dbagent = logic.NewDbAgentGlobalDataStruct(ExampleServerData)
	ExampleServerData.online = logic.NewOnlineGlobalDataStruct(ExampleServerData)
	ExampleServerData.notify = logic.NewNotifyGlobalDataStruct(ExampleServerData)
	ExampleServerData.stream = logic.NewStreamGlobalDataStruct(ExampleServerData)
	ExampleServerData.channel = logic.NewChannelGlobalDataStruct(ExampleServerData)
	config, err := initializer.GetConfig()
	if err != nil {
		return err
	}
	ExampleServerData.config = config
	if err := ExampleServerData.common.Init(ExampleServerData, ExampleServerData.CustomConfig, ExampleServerData.customConfigEvent); err != nil {
		logger.Error("init common logic error %v", err)
		return err
	}

	initializer.RegisterShutdown(ExampleServerData.Shutdown)

	// 自定义路由注册
	ExampleServerData.controller = NewController(ExampleServerData)
	initializer.RegisterRpc(RPCID_EXAMPLESERVER_TEST, ExampleServerData.controller.Test)

	return nil
}

func (s *ExampleServerModule) GetCommon() *logic.CommonGlobalDataStruct {
	return s.common
}
func (s *ExampleServerModule) GetOnline() *logic.OnlineGlobalDataStruct {
	return s.online
}
func (s *ExampleServerModule) GetSend() *logic.SendGlobalDataStruct {
	return s.send
}
func (s *ExampleServerModule) GetDbAgent() *logic.DbAgentGlobalDataStruct {
	return s.dbagent
}
func (s *ExampleServerModule) GetNotify() *logic.NotifyGlobalDataStruct {
	return s.notify
}
func (s *ExampleServerModule) GetStream() *logic.StreamGlobalDataStruct {
	return s.stream
}
func (s *ExampleServerModule) GetChannel() *logic.ChannelGlobalDataStruct {
	return s.channel
}
func (s *ExampleServerModule) GetLogger() runtime.Logger {
	return s.logger
}
func (s *ExampleServerModule) GetConfig() runtime.Config {
	return s.config
}
func (s *ExampleServerModule) GetName() string {
	return s.name
}
func (s *ExampleServerModule) GetDb() *sql.DB {
	return s.db
}
func (s *ExampleServerModule) GetNk() runtime.NakamaModule {
	return s.nk
}

// 配置读取回调
func (s *ExampleServerModule) customConfigEvent(dataId string, newobj any) {
	tmpobj, ok := newobj.(*CustomConfig)
	if !ok {
		return
	}
	if tmpobj.Config == nil {
		tmpobj.Config = &ExampleServerConfig{}
	}
	defaultNodeCfg := false
	if tmpobj.Config.NodeConfig == nil {
		// 使用默认节点配置
		scheme := "http"
		if s.config.GetSocket().GetUseTLS() {
			scheme = "https"
		}
		tmpobj.Config.NodeConfig = &models.NodeConfig{
			NodeId:   s.config.GetName(),
			SvcGroup: fmt.Sprintf("%s@@%s", s.name, models.NACOS_DEFAULT_GROUP),
			Host:     fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			NetHost:  fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			AuthKey:  s.config.GetRuntime().GetHTTPKey(),
			Weight:   10,
			Scheme:   scheme,
		}
		defaultNodeCfg = true
	}
	tmp, _ := json.Marshal(tmpobj)
	s.logger.Debug("customConfigEvent defaultNodeCfg: %v, customCfg: %s", defaultNodeCfg, string(tmp))
	s.common.SetNodeConfig(tmpobj.Config.NodeConfig)
	s.common.WatchNodeList.InitWatchSvr(tmpobj.Config.WatchSvcGroupList, tmpobj.Config.NodeConfig.SvcGroup)
	s.CustomConfig = tmpobj

	// 注册服务
	selfcfg := map[string]interface{}{}
	if err := common.StrcutToMap(tmpobj.Config.NodeConfig, &selfcfg); err == nil {
		if !s.isRegisterSelf {
			go s.common.RegisterAndUpdateSelfToNacos(false, selfcfg)
			s.isRegisterSelf = true
		} else {
			go s.common.RegisterAndUpdateSelfToNacos(true, selfcfg)
		}
	} else {
		s.logger.Error("register self, config error %v", err)
	}

	s.common.UpdateAccessLogConfig(tmpobj.AccessLog)
	loglevel, err := zapcore.ParseLevel(tmpobj.Logger.Level)
	if err != nil {
		s.logger.Error("parse log level error %v", err)
	} else {
		s.nk.SetLoggerLevel(loglevel)
	}
}

func (s *ExampleServerModule) Shutdown(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, reload bool) {
	logger.Info("%s shutdown", s.name)
	if !reload {
		s.common.UnRegisterSelfToNacos()
	}
}
