package main

import (
	"context"

	"github.com/redis/go-redis/v9"
)

type PartyList struct {
	c *PartyController
}

func NewPartyList(c *PartyController) *PartyList {
	return &PartyList{
		c: c,
	}
}

// 更新party成员数量
func (p *PartyList) UpdatePartyMembers(partyId string, num int) {
	// 基于zset实现，按人数排序，人数多的在前面
	rds := p.c.server.GetCommon().Redis
	logger := p.c.server.logger
	err := rds.ZAdd(context.Background(), "partylist", redis.Z{
		Score:  float64(num),
		Member: partyId,
	}).Err()
	if err != nil {
		logger.Error("update party members failed, %s", err.Error())
	}
}

// 删除party
func (p *PartyList) DeleteParty(partyId string) {
	rds := p.c.server.GetCommon().Redis
	rds.ZRem(context.Background(), "partylist", partyId)
}

// 获取party列表
func (p *PartyList) GetPartyList(pageSize, pageNum int) ([]string, error) {
	rds := p.c.server.GetCommon().Redis
	start := int64((pageNum - 1) * pageSize)
	end := start + int64(pageSize) - 1
	list, err := rds.ZRangeWithScores(context.Background(), "partylist", start, end).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, err
	}
	partyIds := make([]string, 0, len(list))
	for _, item := range list {
		partyIds = append(partyIds, item.Member.(string))
	}
	return partyIds, nil
}

// 获取party列表详情(包括队伍信息)，使用完了需要调用partyHandler.Release()释放
// pageNum 从0开始
func (p *PartyList) GetPartyListDetail(pageSize, pageNum int) ([]*PartyHandler, error) {
	rds := p.c.server.GetCommon().Redis
	start := int64(pageNum * pageSize)
	end := start + int64(pageSize) - 1
	// 按score从大到小排序：ZREVRANGEBYSCORE partylist 20 0
	list, err := rds.ZRevRangeWithScores(context.Background(), "partylist", start, end).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, err
	}
	if len(list) == 0 {
		return []*PartyHandler{}, nil
	}
	partyIds := make([]string, 0, len(list))
	for _, item := range list {
		partyIds = append(partyIds, item.Member.(string))
	}

	partyHandlers, err := LoadPartyHandlerBatch(p.c, partyIds)
	if err != nil {
		return nil, err
	}
	return partyHandlers, nil
}
