package logic

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	timewheel "kernel/plugins/common/timerwheel"
	"kernel/plugins/models"
	"net"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/fsnotify/fsnotify"
	json "github.com/json-iterator/go"
	"github.com/panjf2000/ants/v2"
	"github.com/redis/go-redis/v9"
	"gopkg.in/yaml.v3"

	commbo "kernel/plugins/bo"
)

// 配置改变事件回调函数
type CustomConfigEventFunc func(dataId string, newobj any)

type CommonGlobalDataStruct struct {
	i             ServerModule
	Name          string
	Logger        runtime.Logger
	NakamaConfig  runtime.Config
	RuntimeEnvs   map[string]string
	WatchNodeList *common.WatchNodeList
	Redis         redis.Cmdable
	TimerPool     *timewheel.TimeWheelPool
	GroutinePool  *ants.Pool
	NacosClient   *common.NacosClient

	configChangeEventFunc CustomConfigEventFunc
	customConfigObj       any
	nodeConfig            *models.NodeConfig
}

func NewCommonGlobalDataStruct() *CommonGlobalDataStruct {
	return &CommonGlobalDataStruct{
		RuntimeEnvs: make(map[string]string),
	}
}

func (g *CommonGlobalDataStruct) Init(i ServerModule, customConfigObj any, event_func CustomConfigEventFunc) error {
	var err error
	g.i = i
	g.Name = i.GetName()
	g.Logger = i.GetLogger()
	g.NakamaConfig = i.GetConfig()
	g.configChangeEventFunc = event_func
	g.customConfigObj, err = common.CreatePointerFromStruct(customConfigObj)
	if err != nil {
		return err
	}

	// 转换运行时环境变量
	common.ConvertRuntimeEnv(g.Logger, g.RuntimeEnvs, g.NakamaConfig.GetRuntime().GetEnv())

	nacosconfig := g.NakamaConfig.GetNacos()
	if nacosconfig != nil && len(nacosconfig.GetUrls()) > 0 {
		if nacosconfig.GetAccessKey() != "" && nacosconfig.GetSecretKey() != "" {
			g.NacosClient, err = common.InitNacosByAkSk(nacosconfig.GetAccessKey(), nacosconfig.GetSecretKey(), nacosconfig.GetNamespace(), nacosconfig.GetUrls(), uint64(nacosconfig.GetTimeoutMs()))
			if err != nil {
				g.Logger.Error("init nacos, use ak sk error %v", err)
				return err
			}
		} else {
			g.NacosClient, err = common.InitNacos(nacosconfig.GetUsername(), nacosconfig.GetPassword(), nacosconfig.GetNamespace(), nacosconfig.GetUrls(), uint64(nacosconfig.GetTimeoutMs()))
			if err != nil {
				g.Logger.Error("init nacos, use username password error %v", err)
				return err
			}
		}
	}

	redisconfig := g.NakamaConfig.GetRedis()
	if redisconfig != nil && len(redisconfig.GetAddrs()) > 0 {
		if len(redisconfig.GetAddrs()) == 1 {
			cli := redis.NewClient(&redis.Options{
				Addr:         redisconfig.GetAddrs()[0],
				Username:     redisconfig.GetUsername(),
				Password:     redisconfig.GetPassword(),
				MaxRetries:   -1, // 不重试
				ReadTimeout:  time.Millisecond * time.Duration(redisconfig.GetReadTimeoutMs()),
				WriteTimeout: time.Millisecond * time.Duration(redisconfig.GetWriteTimeoutMs()),
				MinIdleConns: redisconfig.GetMinIdleConns(),
				PoolSize:     redisconfig.GetPoolSize(),
				PoolTimeout:  time.Millisecond * time.Duration(redisconfig.GetPoolTimeoutMs()),
				DB:           redisconfig.GetDB(),
			})
			_, err = cli.Ping(context.TODO()).Result()
			if err != nil {
				g.Logger.Error("init redis single error: %v", err)
				return err
			}
			g.Redis = cli
		} else {
			cli := redis.NewClusterClient(&redis.ClusterOptions{
				Addrs:        redisconfig.GetAddrs(),
				Username:     redisconfig.GetUsername(),
				Password:     redisconfig.GetPassword(),
				MaxRetries:   -1, // 不重试
				ReadTimeout:  time.Millisecond * time.Duration(redisconfig.GetReadTimeoutMs()),
				WriteTimeout: time.Millisecond * time.Duration(redisconfig.GetWriteTimeoutMs()),
				MinIdleConns: redisconfig.GetMinIdleConns(),
				PoolSize:     redisconfig.GetPoolSize(),
				PoolTimeout:  time.Millisecond * time.Duration(redisconfig.GetPoolTimeoutMs()),
			})
			_, err = cli.Ping(context.TODO()).Result()
			if err != nil {
				g.Logger.Error("init redis cluster error: %v", err)
				return err
			}
			g.Redis = cli
		}
	}

	// 初始化服务发现
	g.WatchNodeList = common.NewWatchNodeList(g.Logger, g.Name, g.NacosClient)

	// 拉取本地自定义配置 example: LOCAL_CUSTOM_CONFIG_PATH=/root/soc-server/kernel/config.yaml
	use_local_config := false
	if g.RuntimeEnvs[models.LOCAL_CUSTOM_CONFIG_PATH_KEY] != "" {
		newCustomServerConfig, _ := common.CreatePointerFromStruct(g.customConfigObj)
		local_config_path := g.RuntimeEnvs[models.LOCAL_CUSTOM_CONFIG_PATH_KEY]
		if _, err := os.Stat(local_config_path); err == nil {
			datafile, err := os.ReadFile(local_config_path)
			if err != nil {
				g.Logger.Error("read local config file %s error %v", local_config_path, err)
				return err
			}

			if strings.HasSuffix(local_config_path, ".json") {
				if err := json.Unmarshal(datafile, newCustomServerConfig); err != nil {
					g.Logger.Error("unmarshal local config file %s error %v", local_config_path, err)
					return err
				}
			} else if strings.HasSuffix(local_config_path, ".yaml") || strings.HasSuffix(local_config_path, ".yml") {
				decoder := yaml.NewDecoder(bytes.NewBuffer(datafile))
				if err := decoder.Decode(newCustomServerConfig); err != nil {
					g.Logger.Error("unmarshal local config file %s error %v", local_config_path, err)
					return err
				}
			} else {
				g.Logger.Error("local config file %s format error, please use .json or .yaml", local_config_path)
				return err
			}
		}
		use_local_config = true
		event_func(local_config_path, newCustomServerConfig)
		go g.watherLocalConfig(local_config_path)
	}

	// 拉取远程自定义配置，如果使用本地自定义配置那么不读取远程nacos了
	if g.RuntimeEnvs[models.NACOS_CUSTOM_DATA_ID_GROUP_KEY] != "" && !use_local_config {
		dataid_group := g.RuntimeEnvs[models.NACOS_CUSTOM_DATA_ID_GROUP_KEY]
		dataid, group := common.ParseWatchSvrKey(dataid_group)
		g.NacosClient.ListenConfig(dataid, group, func(namespace, group, dataId, data string) {
			g.Logger.Debug("get nacos config %s:%s", dataid_group, data)
			newCustomServerConfig, _ := common.CreatePointerFromStruct(g.customConfigObj)
			if err := json.Unmarshal([]byte(data), newCustomServerConfig); err != nil {
				g.Logger.Error("get nacos config %s:%s error %v", dataid_group, data, err)
			} else {
				event_func(dataid, newCustomServerConfig)
			}
		})
		// 等10秒，检查nacos拉取配置
		for i := 0; i < 200; i++ {
			if g.nodeConfig != nil {
				break
			}
			time.Sleep(time.Millisecond * 50)
		}
	}

	g.TimerPool, err = timewheel.NewTimeWheelPool(10, 50*time.Millisecond, 120)
	if err != nil {
		g.Logger.Error("init timer pool error %v", err)
		return err
	}
	g.TimerPool.Start()

	g.GroutinePool, err = ants.NewPool(100000, ants.WithPreAlloc(true))
	if err != nil {
		g.Logger.Error("init groutine pool error %v", err)
		return err
	}

	return nil
}

// 注册自身到nacos
func (g *CommonGlobalDataStruct) RegisterAndUpdateSelfToNacos(isUpdate bool, metacfg map[string]interface{}) {
	if g.nodeConfig == nil {
		g.Logger.Error("node config is nil, register failed")
		return
	}
	for i := 0; i < 60; i++ {
		// 检查是否已经监听端口成功
		conn, err := net.DialTimeout("tcp", fmt.Sprint("127.0.0.1:", g.NakamaConfig.GetSocket().GetPort()), time.Second*1)
		if err != nil {
			time.Sleep(time.Millisecond * 500)
			continue
		}
		conn.Close()

		addrs := strings.Split(g.nodeConfig.Host, ":")
		if len(addrs) != 2 {
			g.Logger.Error("Host config error %s", g.nodeConfig.Host)
			break
		}
		svcname, group := common.ParseWatchSvrKey(g.nodeConfig.SvcGroup)
		var result bool
		if isUpdate {
			result, err = g.NacosClient.UpdateRegisterSelf(true, addrs[0], common.Interface2UInt64(addrs[1]), uint64(g.nodeConfig.Weight), svcname, group, metacfg)
		} else {
			result, err = g.NacosClient.RegisterSelf(true, addrs[0], common.Interface2UInt64(addrs[1]), uint64(g.nodeConfig.Weight), svcname, group, metacfg)
		}
		if err != nil {
			g.Logger.Error("%s nacos register error host: %s err: %v result: %v, isUpdate: %v", g.Name, g.nodeConfig.Host, err, result, isUpdate)
			time.Sleep(time.Millisecond * 500)
		} else {
			g.Logger.Info("%s nacos register success host: %s result: %v, isUpdate: %v", g.Name, g.nodeConfig.Host, result, isUpdate)
		}
		break
	}
}

func (g *CommonGlobalDataStruct) UnRegisterSelfToNacos() {
	if g.nodeConfig == nil {
		g.Logger.Error("node config is nil, unregister failed")
		return
	}

	addrs := strings.Split(g.nodeConfig.Host, ":")
	if len(addrs) != 2 {
		g.Logger.Error("Host config error %s", g.nodeConfig.Host)
		return
	}
	svcname, group := common.ParseWatchSvrKey(g.nodeConfig.SvcGroup)
	result, err := g.NacosClient.UnRegisterSelf(addrs[0], common.Interface2UInt64(addrs[1]), svcname, group)
	if err != nil {
		g.Logger.Error("%s nacos unregister error host: %s err: %v result: %v", g.Name, g.nodeConfig.Host, err, result)
	} else {
		g.Logger.Info("%s nacos unregister success host: %s result: %v", g.Name, g.nodeConfig.Host, result)
	}

	// 等待反注册完成
	waitnum := 0
	for {
		svrcfg, err := g.WatchNodeList.GetSvrCfgByID(svcname, group, g.nodeConfig.NodeId)
		if err != nil {
			g.Logger.Error("get svrcfg error %v", err)
			time.Sleep(time.Second * 1)
			break
		}
		time.Sleep(time.Millisecond * 100)
		if svrcfg != nil {
			waitnum++
		} else {
			break
		}
		if waitnum > 20 {
			g.Logger.Warn("wait 2s NacosUnRegisterSelf, force stop")
			break
		}
	}
}

// 关注本地配置变更
func (g *CommonGlobalDataStruct) watherLocalConfig(path string) {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		fmt.Println("new watcher failed: ", err)
		return
	}
	defer watcher.Close()

	g.Logger.Warn("watch file %s", path)
	err = watcher.Add(path)
	if err != nil {
		fmt.Println("add watch file failed:", err)
		return
	}

	for {
		select {
		case event, ok := <-watcher.Events:
			g.Logger.Info("event: %s", event.String())
			if !ok {
				return
			}

			if (event.Op&fsnotify.Write) == fsnotify.Write || (event.Op&fsnotify.Rename) == fsnotify.Rename {
				if (event.Op & fsnotify.Rename) == fsnotify.Rename {
					time.Sleep(time.Millisecond * 100)
					watcher.Remove(path)
					err := watcher.Add(path)
					if err != nil {
						g.Logger.Error("re-add watch file failed: %v", err)
						return
					}
				}

				datafile, err := os.ReadFile(path)
				if err != nil {
					g.Logger.Error("read local config file %s error %v", path, err)
					break
				}
				local_config_path := path
				newCustomServerConfig, err := common.CreatePointerFromStruct(g.customConfigObj)
				if err != nil {
					g.Logger.Error("create pointer from struct error %v", err)
					break
				}

				if strings.HasSuffix(local_config_path, ".json") {
					if err := json.Unmarshal(datafile, newCustomServerConfig); err != nil {
						g.Logger.Error("unmarshal local config file %s error %v", local_config_path, err)
						break
					}
				} else if strings.HasSuffix(local_config_path, ".yaml") || strings.HasSuffix(local_config_path, ".yml") {
					decoder := yaml.NewDecoder(bytes.NewBuffer(datafile))
					if err := decoder.Decode(newCustomServerConfig); err != nil {
						g.Logger.Error("unmarshal local config file %s error %v", local_config_path, err)
						break
					}
				} else {
					g.Logger.Error("unsupported config file format: %s", local_config_path)
					break
				}

				if newCustomServerConfig != nil {
					g.configChangeEventFunc(local_config_path, newCustomServerConfig)
				} else {
					g.Logger.Error("new custom server config is nil")
				}
			}
		case err, ok := <-watcher.Errors:
			g.Logger.Error("error: %v", err)
			if !ok {
				return
			}
		}
	}
}

// 设置节点配置
func (g *CommonGlobalDataStruct) SetNodeConfig(cfg *models.NodeConfig) {
	if cfg == nil {
		return
	}
	g.nodeConfig = cfg
}

func (g *CommonGlobalDataStruct) GetProxyPassByReq(req *http.Request, clientip string, p *common.ProxyPassData) *url.URL {
	//g.Logger.Debug("GetProxyPassByReq: %s", p.CfgData.ProxyType)
	_cfg := p
	if _cfg.CfgData.ProxyType == models.PROXYSVC {
		var err error
		var svrcfg *models.NodeConfig
		var path = p.CfgData.RoutePath
		svcname, group := common.ParseWatchSvrKey(_cfg.CfgData.ProxyData)
		if req != nil {
			if serviceid := req.Header.Get(models.X_Custom_Direct_ServiceID); serviceid != "" { // 直接选择具体的服务id
				svrcfg, err = g.WatchNodeList.GetSvrCfgByID(svcname, group, serviceid)
			}
		}

		if svrcfg == nil || err != nil {
			if _cfg.CfgData.LBType == models.PROXYLBRAND || _cfg.CfgData.LBType == "" {
				svrcfg, err = g.WatchNodeList.RandAllocServiceCfg(svcname, group)
			} else if _cfg.CfgData.LBType == models.PROXYLBRR || _cfg.CfgData.LBType == models.PROXYLBWRR {
				svrcfg, err = g.WatchNodeList.WRRAllocServiceCfg(svcname, group)
			} else if _cfg.CfgData.LBType == models.PROXYLBHASH {
				// 是否有自定义一致性hash
				if req != nil {
					if hashid := req.Header.Get(models.X_Custom_Hash_ID); hashid != "" {
						svrcfg, err = g.WatchNodeList.AllocServiceConfig(svcname, group, hashid)
					}
				}
				if svrcfg == nil {
					svrcfg, err = g.WatchNodeList.AllocServiceConfig(svcname, group, clientip)
				}
			}
		}
		if err != nil {
			g.Logger.Error(err.Error())
			return nil
		}
		if svrcfg.Host == "" {
			return nil
		}
		scheme := svrcfg.Scheme
		if scheme == "" {
			scheme = _cfg.CfgData.Scheme
		}
		if path == "" {
			path = req.URL.Path
		}
		tmpurl := &url.URL{Scheme: scheme, Host: svrcfg.Host, Path: path}
		return tmpurl
	} else if _cfg.CfgData.ProxyType == models.PROXYHOSTS {
		var allochost, scheme, path = "", "", ""
		if req != nil {
			if directhost := req.Header.Get(models.X_Custom_Direct_HOST); directhost != "" { // 直接选择具体的服务
				allochost = directhost
			}
		}
		if allochost == "" {
			if req != nil {
				if hashid := req.Header.Get(models.X_Custom_Hash_ID); hashid != "" {
					allochost, _, scheme, path, _, _ = _cfg.GetHostDetail(hashid)
				}
			}
			if allochost == "" {
				allochost, _, scheme, path, _, _ = _cfg.GetHostDetail(clientip)
			}
		}
		if allochost == "" {
			return nil
		}
		if scheme == "" {
			scheme = _cfg.CfgData.Scheme
		}
		if scheme == "" {
			scheme = "http"
		}
		if path == "" {
			path = req.URL.Path
		}
		tmpurl := &url.URL{Scheme: scheme, Host: allochost, Path: path}
		return tmpurl
	}
	return nil
}

// 从上下文获取用户id
func (g *CommonGlobalDataStruct) GetUserID(ctx context.Context) string {
	// 优先从上下文直接获取
	if userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID); userID != nil {
		return userID.(string)
	}

	// 从headers中获取
	if headers := ctx.Value(runtime.RUNTIME_CTX_HEADERS); headers != nil {
		header := http.Header(headers.(map[string][]string))
		return header.Get(runtime.RUNTIME_CTX_HEADER_USER_ID)
	}
	return ""
}

// 从上下文获取会话id
func (g *CommonGlobalDataStruct) GetSessionID(ctx context.Context) string {
	// 优先从上下文直接获取
	if sessionID := ctx.Value(runtime.RUNTIME_CTX_SESSION_ID); sessionID != nil {
		return sessionID.(string)
	}

	// 从headers中获取
	if headers := ctx.Value(runtime.RUNTIME_CTX_HEADERS); headers != nil {
		header := http.Header(headers.(map[string][]string))
		return header.Get(runtime.RUNTIME_CTX_HEADER_SESSION_ID)
	}
	return ""
}

// 从上下文获取uin
func (g *CommonGlobalDataStruct) GetUin(ctx context.Context) int64 {
	if vars := ctx.Value(runtime.RUNTIME_CTX_VARS); vars != nil {
		if uin, ok := vars.(map[string]string)["uin"]; ok {
			return common.Interface2Int64(uin)
		}
	}
	if headers := ctx.Value(runtime.RUNTIME_CTX_HEADERS); headers != nil {
		header := http.Header(headers.(map[string][]string))
		return common.Interface2Int64(header.Get(runtime.RUNTIME_CTX_HEADER_UIN))
	}
	return 0
}

// 从上下文获取uin
func (g *CommonGlobalDataStruct) GetStrUin(ctx context.Context) string {
	if vars := ctx.Value(runtime.RUNTIME_CTX_VARS); vars != nil {
		if uin, ok := vars.(map[string]string)["uin"]; ok {
			return uin
		}
	}
	if headers := ctx.Value(runtime.RUNTIME_CTX_HEADERS); headers != nil {
		header := http.Header(headers.(map[string][]string))
		return header.Get(runtime.RUNTIME_CTX_HEADER_UIN)
	}
	return ""
}

// 从上下文获取客户端ip
func (g *CommonGlobalDataStruct) GetClientIP(ctx context.Context) string {
	if clientIP := ctx.Value(runtime.RUNTIME_CTX_CLIENT_IP); clientIP != nil {
		return clientIP.(string)
	}
	if headers := ctx.Value(runtime.RUNTIME_CTX_HEADERS); headers != nil {
		header := http.Header(headers.(map[string][]string))
		return header.Get("X-Forwarded-For")
	}
	return ""
}

// 从上下文获取用户名
func (g *CommonGlobalDataStruct) GetUsername(ctx context.Context) string {
	if username := ctx.Value(runtime.RUNTIME_CTX_USERNAME); username != nil {
		name, _ := url.QueryUnescape(username.(string))
		return name
	}
	if headers := ctx.Value(runtime.RUNTIME_CTX_HEADERS); headers != nil {
		header := http.Header(headers.(map[string][]string))
		name, _ := url.QueryUnescape(header.Get(runtime.RUNTIME_CTX_HEADER_USERNAME))
		return name
	}
	return ""
}

// 从上下文获取查询参数
func (g *CommonGlobalDataStruct) GetQueryParams(ctx context.Context) url.Values {
	if queryParams := ctx.Value(runtime.RUNTIME_CTX_QUERY_PARAMS); queryParams != nil {
		return url.Values(queryParams.(map[string][]string))
	}
	return nil
}

// 从上下文获取昵称
func (g *CommonGlobalDataStruct) GetNick(ctx context.Context) string {
	if vars := ctx.Value(runtime.RUNTIME_CTX_VARS); vars != nil {
		if nick, ok := vars.(map[string]string)["nick"]; ok {
			name, _ := url.QueryUnescape(nick)
			return name
		}
	}
	if headers := ctx.Value(runtime.RUNTIME_CTX_HEADERS); headers != nil {
		header := http.Header(headers.(map[string][]string))
		name, _ := url.QueryUnescape(header.Get(runtime.RUNTIME_CTX_HEADER_NICK))
		return name
	}
	return ""
}

// 当前节点id
func (g *CommonGlobalDataStruct) GetNodeID() string {
	if g.nodeConfig != nil {
		return g.nodeConfig.NodeId
	}
	return ""
}

// 当前节点host
func (g *CommonGlobalDataStruct) GetNodeHost() string {
	if g.nodeConfig != nil {
		return g.nodeConfig.Host
	}
	return ""
}

func (g *CommonGlobalDataStruct) SecurityBearer(r *http.Request) (userID string, username string, vars map[string]string, exp int64, tokenId string, issuedAt int64, err error) {
	// Unless explicitly defined above, handlers require full user authentication.
	auth := r.Header.Get("Authorization")
	if auth == "" {
		auth = r.Header.Get("grpcgateway-authorization")
	}
	if auth == "" {
		// Neither "authorization" nor "grpc-authorization" were supplied.
		return "", "", nil, 0, "", 0, errors.New("Auth token required")
	}
	_userID, _username, _vars, _exp, _tokenId, _issuedAt, ok := common.ParseBearerAuth([]byte(g.NakamaConfig.GetSession().GetEncryptionKey()), auth)
	if !ok {
		// Value of "authorization" or "grpc-authorization" was malformed or expired.
		return "", "", nil, 0, "", 0, errors.New("Auth token invalid")
	}
	return _userID.String(), _username, _vars, _exp, _tokenId, _issuedAt, nil
}

func (g *CommonGlobalDataStruct) SecurityBearerContext(r *http.Request) (context.Context, error) {
	// Unless explicitly defined above, handlers require full user authentication.
	auth := r.Header.Get("Authorization")
	if auth == "" {
		auth = r.Header.Get("grpcgateway-authorization")
	}
	if auth == "" {
		// Neither "authorization" nor "grpc-authorization" were supplied.
		return nil, errors.New("Auth token required")
	}
	userID, username, vars, exp, tokenId, _, ok := common.ParseBearerAuth([]byte(g.NakamaConfig.GetSession().GetEncryptionKey()), auth)
	if !ok {
		// Value of "authorization" or "grpc-authorization" was malformed or expired.
		return nil, errors.New("Auth token invalid")
	}

	// Extract http headers
	headers := make(map[string][]string)
	for k, v := range r.Header {
		if k == "Grpc-Timeout" {
			continue
		}
		headers[k] = make([]string, 0, len(v))
		headers[k] = append(headers[k], v...)
	}

	clientIP, clientPort := common.ExtractClientAddressFromRequest(r)
	queryParams := r.URL.Query()
	queryParams.Del("http_key")
	ctx := g.NewRuntimeGoContext(r.Context(), g.nodeConfig.NodeId, "", g.RuntimeEnvs, "rpc", headers, queryParams, exp, userID.String(), username, vars, tokenId, clientIP, clientPort, "")
	return ctx, nil
}

// 服务间rpc调用认证
func (g *CommonGlobalDataStruct) SecurityBasic(r *http.Request) (bool, error) {
	// Session refresh and authentication functions only require server key.
	auth := r.Header.Get("Authorization")
	if auth == "" {
		auth = r.Header.Get("grpcgateway-authorization")
	}
	if auth == "" {
		// Neither "authorization" nor "grpc-authorization" were supplied.
		return false, errors.New("Server key required")
	}
	username, _, ok := common.ParseBasicAuth2(auth)
	if !ok {
		// Neither "authorization" nor "grpc-authorization" were supplied.
		return false, errors.New("Server key required")
	}
	if username != g.NakamaConfig.GetRuntime().GetHTTPKey() {
		// Value of "authorization" or "grpc-authorization" username component did not match server key.
		return false, errors.New("Server key invalid")
	}

	return true, nil
}

func (g *CommonGlobalDataStruct) NewRuntimeGoContext(ctx context.Context, node, version string, env map[string]string, mode string, headers, queryParams map[string][]string, sessionExpiry int64, userID, username string, vars map[string]string, sessionID, clientIP, clientPort, lang string) context.Context {
	ctx = context.WithValue(ctx, runtime.RUNTIME_CTX_ENV, env)
	ctx = context.WithValue(ctx, runtime.RUNTIME_CTX_MODE, mode)
	ctx = context.WithValue(ctx, runtime.RUNTIME_CTX_NODE, node)
	ctx = context.WithValue(ctx, runtime.RUNTIME_CTX_VERSION, version)

	if headers != nil {
		ctx = context.WithValue(ctx, runtime.RUNTIME_CTX_HEADERS, headers)
	}
	if queryParams != nil {
		ctx = context.WithValue(ctx, runtime.RUNTIME_CTX_QUERY_PARAMS, queryParams)
	}

	if userID != "" {
		ctx = context.WithValue(ctx, runtime.RUNTIME_CTX_USER_ID, userID)
		ctx = context.WithValue(ctx, runtime.RUNTIME_CTX_USERNAME, username)
		if vars != nil {
			ctx = context.WithValue(ctx, runtime.RUNTIME_CTX_VARS, vars)
		}
		ctx = context.WithValue(ctx, runtime.RUNTIME_CTX_USER_SESSION_EXP, sessionExpiry)
		if sessionID != "" {
			ctx = context.WithValue(ctx, runtime.RUNTIME_CTX_SESSION_ID, sessionID)
			// Lang is never reported without session ID.
			ctx = context.WithValue(ctx, runtime.RUNTIME_CTX_LANG, lang)
		}
	}

	if clientIP != "" {
		ctx = context.WithValue(ctx, runtime.RUNTIME_CTX_CLIENT_IP, clientIP)
	}
	if clientPort != "" {
		ctx = context.WithValue(ctx, runtime.RUNTIME_CTX_CLIENT_PORT, clientPort)
	}

	return ctx
}

func (g *CommonGlobalDataStruct) UpdateAccessLogConfig(accessLogCfg *models.AccessLogConfig) {
	if accessLogCfg == nil || g.NakamaConfig == nil || g.NakamaConfig.GetAccessLog() == nil {
		return
	}
	g.NakamaConfig.GetAccessLog().SetDir(accessLogCfg.Dir)
	g.NakamaConfig.GetAccessLog().SetIsDisable(accessLogCfg.IsDisable)
	g.NakamaConfig.GetAccessLog().SetWriteToLogFile(accessLogCfg.WriteToLogFile)
	g.NakamaConfig.GetAccessLog().SetRecordRespBody(accessLogCfg.RecordRespBody)
	g.NakamaConfig.GetAccessLog().SetDisableRecordReqBody(accessLogCfg.DisableRecordReqBody)
	g.NakamaConfig.GetAccessLog().SetRpcIds(accessLogCfg.RpcIds)
	g.NakamaConfig.GetAccessLog().SetMaxBackups(accessLogCfg.MaxBackups)
}

// 根据user_id查询用户基本信息
func (g *CommonGlobalDataStruct) QueryUser(ctx context.Context, user_id string) (*commbo.User, error) {
	dbagent := g.i.GetDbAgent()
	if dbagent == nil {
		return nil, errors.New("dbagent is nil")
	}
	user := commbo.User{}
	obj := dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{user.GetKeyName(): user_id})).WithOption(WithAutoRLock())
	defer func() {
		dbagent.Release(obj)
	}()
	err := obj.Query(ctx).As(&user)
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// 根据user_id查询用户指定字段信息
func (g *CommonGlobalDataStruct) QueryUserFileds(ctx context.Context, user_id string, fields ...string) (map[string]interface{}, error) {
	dbagent := g.i.GetDbAgent()
	if dbagent == nil {
		return nil, errors.New("dbagent is nil")
	}
	user := commbo.User{}
	obj := dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{user.GetKeyName(): user_id})).WithOption(WithAutoRLock())
	defer func() {
		dbagent.Release(obj)
	}()
	result := make(map[string]interface{})
	err := obj.Query(ctx, fields...).As(&result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// 根据uin查询用户基本信息
func (g *CommonGlobalDataStruct) QueryUserByUin(ctx context.Context, uin int64) (*commbo.User, error) {
	dbagent := g.i.GetDbAgent()
	if dbagent == nil {
		return nil, errors.New("dbagent is nil")
	}
	user := commbo.User{}
	obj := dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{"uin": uin})).WithOption(WithAutoRLock())
	defer func() {
		dbagent.Release(obj)
	}()
	err := obj.Query(ctx).As(&user)
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// 根据uin查询用户指定字段信息
func (g *CommonGlobalDataStruct) QueryUserFiledsByUin(ctx context.Context, uin int64, fields ...string) (map[string]interface{}, error) {
	dbagent := g.i.GetDbAgent()
	if dbagent == nil {
		return nil, errors.New("dbagent is nil")
	}
	user := commbo.User{}
	obj := dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{"uin": uin})).WithOption(WithAutoRLock())
	defer func() {
		dbagent.Release(obj)
	}()
	result := make(map[string]interface{})
	err := obj.Query(ctx, fields...).As(&result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
