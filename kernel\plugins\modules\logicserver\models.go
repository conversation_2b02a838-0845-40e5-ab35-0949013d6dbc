package main

import "kernel/plugins/models"

type RoleInfo struct {
	RoleID        int32  `json:"role_id"`
	RoleData      string `json:"role_data"`
	LastLoginTime int64  `json:"last_login_time"`
}

type Favorite struct {
	Time int64 `json:"time,omitempty"` //收藏时间
}

type History struct {
	Time int64 `json:"time,omitempty"` //时间
}

type RespCreateRole struct {
	Code models.ErrorCode `json:"code"`
	Msg  string           `json:"message,omitempty"`
	Data *RoleInfo        `json:"data,omitempty"`
}

type RespGetFavoriteRooms struct {
	Code models.ErrorCode     `json:"code"`
	Msg  string               `json:"message,omitempty"`
	Data map[string]*Favorite `json:"data,omitempty"`
}

type RespGetHistoryRooms struct {
	Code models.ErrorCode    `json:"code"`
	Msg  string              `json:"message,omitempty"`
	Data map[string]*History `json:"data,omitempty"`
}

// 请求查询用户在线状态
type ReqQueryStatus struct {
	UserIDs []string `json:"uids,omitempty"` // 多个用户ID
	Uins    []int64  `json:"uins,omitempty"` // 多个Uins
}

type QueryStatusData struct {
	UserIDs map[string]bool `json:"uids,omitempty"`
	Uins    map[int64]bool  `json:"uins,omitempty"`
}

// 响应查询用户在线状态
type RespQueryStatus struct {
	Code models.ErrorCode `json:"code"`
	Msg  string           `json:"message,omitempty"`
	Data *QueryStatusData `json:"data,omitempty"`
}
