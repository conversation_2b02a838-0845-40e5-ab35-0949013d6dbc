package common

import (
	"crypto/tls"
	"net"
	"net/http"
	"net/http/httputil"
	"net/url"
	"path"
	"strings"
	"time"
)

// 目标负载回调服务
type ProxyTargetLBFunc func(req *http.Request) *url.URL
type CustomResponseFunc func(response *http.Response) error
type ProxyHandlerFunc func(proxy *httputil.ReverseProxy) func(http.ResponseWriter, *http.Request)

var DefaultProxyConnectPool http.RoundTripper = &http.Transport{
	DialContext: (&net.Dialer{
		Timeout:   3 * time.Second,
		KeepAlive: 30 * time.Second,
	}).DialContext,
	MaxIdleConns:          1000,
	IdleConnTimeout:       60 * time.Second,
	ExpectContinueTimeout: 1 * time.Second,
	TLSClientConfig: &tls.Config{
		InsecureSkipVerify: true,
	},
}

//var DefaultHttp3ProxyConnectPool = NewHttp3RoundTripper()
//
//func NewHttp3RoundTripper() *http3.Transport {
//	return &http3.Transport{
//		TLSClientConfig: &tls.Config{
//			InsecureSkipVerify: true,
//		},
//		QUICConfig: &quic.Config{
//			MaxIdleTimeout:  60 * time.Second,
//			KeepAlivePeriod: 20 * time.Second,
//		},
//	}
//}

//func NewQuicProxy(hostAddr string, targetLBFunc ProxyTargetLBFunc, respFunc CustomResponseFunc, proxyHandelFunc ProxyHandlerFunc, transport http.RoundTripper) *http3.Server {
//	proxyHandler := NewReverseProxy(targetLBFunc)
//	if transport != nil {
//		proxyHandler.Transport = transport
//	}
//	if respFunc != nil {
//		proxyHandler.ModifyResponse = respFunc
//	}
//
//	if proxyHandelFunc != nil {
//		return &http3.Server{
//			Addr:    hostAddr,
//			Handler: http.HandlerFunc(proxyHandelFunc(proxyHandler)),
//		}
//	} else {
//		return &http3.Server{
//			Addr:    hostAddr,
//			Handler: proxyHandler,
//		}
//	}
//}

func NewReverseProxy(targetLBFunc ProxyTargetLBFunc) *httputil.ReverseProxy {
	director := func(req *http.Request) {
		target := targetLBFunc(req)
		if target == nil {
			return
		}
		modifyProxiedRequest(req, target)
	}

	p := &httputil.ReverseProxy{Director: director}
	return p
}

func mergeQuery(targetQuery, reqQuery string) string {
	var paramSlice []string
	if targetQuery != "" {
		paramSlice = strings.Split(targetQuery, "&")
	}

	if reqQuery != "" {
		paramSlice = append(paramSlice, strings.Split(reqQuery, "&")...)
	}

	var mergedSlice []string
	queryMap := make(map[string]bool)
	for _, param := range paramSlice {
		size := len(queryMap)
		queryMap[param] = true
		if size != len(queryMap) {
			mergedSlice = append(mergedSlice, param)
		}
	}
	return strings.Join(mergedSlice, "&")
}

func modifyProxiedRequest(req *http.Request, target *url.URL) {
	req.URL.Scheme = target.Scheme
	req.URL.Host = target.Host
	req.URL.RawQuery = mergeQuery(target.RawQuery, req.URL.RawQuery)
	req.Host = target.Host
	if target.Path != "" {
		req.URL.Path = path.Join(target.Path)
	} else {
		req.URL.Path = path.Join(target.Path, req.URL.Path)
	}

	if _, ok := req.Header["User-Agent"]; !ok {
		// explicitly disable User-Agent so it's not set to default value
		req.Header.Set("User-Agent", "")
	}
}
