package models

import (
	"bytes"
	"errors"
	"fmt"
	"reflect"
	"strconv"

	json "github.com/json-iterator/go"
)

var (
	ErrDBResEmpty           = errors.New("__empty__")
	ErrDBResVersionConflict = errors.New("__version_conflict__")
)

type DBModel interface {
	GetTable() string
	GetKeyName() string
	GetUniqueKeys() []string // 除了keyname之外的其他唯一键
	GetSecondKeyName() string
	GetRowResultType() RowResultType
	GetQueryArgs() string
	GetFlushType() FlushType
	GetCacheLevel() CacheLevel
	GetVersionName() string
	Marshal() ([]byte, error)
	MarshalToMap(filter map[string]struct{}) map[string]interface{}
	Unmarshal([]byte) error
	Clear()
}

type DBAgentReq struct {
	Keys   map[string]interface{} `json:"keys,omitempty"`   // 键
	Values map[string]interface{} `json:"values,omitempty"` // 值
	TTL    int64                  `json:"ttl,omitempty"`    // 过期时间，单位秒

	Table          string        `json:"table,omitempty"`          // 数据库表名
	RowsSecondName string        `json:"rowssecondname,omitempty"` // 多行结果集二级key
	RowResultType  RowResultType `json:"rowresulttype,omitempty"`  // 结果集类型
	QueryArgSql    string        `json:"queryargsql,omitempty"`    // 查询参数sql
	FlushType      FlushType     `json:"flushtype,omitempty"`      // 刷db类型
	CacheLevel     CacheLevel    `json:"cachelevel,omitempty"`     // 缓存级别
	UniqueKeys     []string      `json:"uniquekeys,omitempty"`     // 唯一键字段名
	VersionName    string        `json:"versionname,omitempty"`    // 版本检查字段名，如果为空，则不进行版本检查，只支持单行结果集数据，版本字段类型为int64

	NotOptCache bool    `json:"notoptcache,omitempty"` // 不操作缓存(false:操作缓存，true:不操作缓存)
	ErrDiscard  bool    `json:"errdiscard,omitempty"`  // 写db失败是否丢弃写
	LockId      uint64  `json:"lockid,omitempty"`      // 远程锁上下文id
	LockKey     string  `json:"lockkey,omitempty"`     // 远程锁的键
	LockOpt     LockOpt `json:"lockopt,omitempty"`     // 远程锁操作类型
	LockTimeout int64   `json:"locktimeout,omitempty"` // 远程锁，等待加锁超时时间，毫秒为单位
}

func NewDBAgentReq() *DBAgentReq {
	return &DBAgentReq{
		Keys:   make(map[string]interface{}),
		Values: make(map[string]interface{}),
	}
}

func (r *DBAgentReq) Reset() {
	// 清空map而不是重新分配
	for k := range r.Keys {
		delete(r.Keys, k)
	}
	for k := range r.Values {
		delete(r.Values, k)
	}

	// 重置其他字段
	r.TTL = 0
	r.Table = ""
	r.RowsSecondName = ""
	r.RowResultType = ""
	r.QueryArgSql = ""
	r.FlushType = 0
	r.CacheLevel = 0
	r.UniqueKeys = nil
	r.ErrDiscard = false
	r.NotOptCache = false
	r.LockId = 0
	r.LockKey = ""
	r.LockOpt = 0
	r.LockTimeout = 0
}

func (r *DBAgentReq) Unmarshal(buf []byte) error {
	d := json.NewDecoder(bytes.NewReader(buf))
	d.UseNumber()
	return d.Decode(r)
}

// 数据库代理请求选项
type DBReqOption func(opts *DBAgentReq)

func LoadDBReqOption(req *DBAgentReq, options ...DBReqOption) *DBAgentReq {
	for _, option := range options {
		option(req)
	}
	return req
}
func NewDBReqOption(options ...DBReqOption) *DBAgentReq {
	opts := new(DBAgentReq)
	for _, option := range options {
		option(opts)
	}
	return opts
}

func WithKeys(param map[string]interface{}) DBReqOption {
	return func(opts *DBAgentReq) {
		for k, v := range param {
			opts.Keys[k] = v
		}
	}
}
func WithTTL(param int64) DBReqOption {
	return func(opts *DBAgentReq) {
		opts.TTL = param
	}
}
func WithValues(param map[string]interface{}) DBReqOption {
	return func(opts *DBAgentReq) {
		for k := range opts.Values {
			delete(opts.Values, k)
		}
		for k, v := range param {
			opts.Values[k] = v
		}
	}
}
func WithValuesFromDBModel(param DBModel, filter map[string]struct{}) DBReqOption {
	return func(opts *DBAgentReq) {
		for k := range opts.Values {
			delete(opts.Values, k)
		}
		tmpmap := param.MarshalToMap(filter)
		for k, v := range tmpmap {
			opts.Values[k] = v
		}
	}
}
func WithTable(param string) DBReqOption {
	return func(opts *DBAgentReq) {
		opts.Table = param
	}
}
func WithRowResultType(param RowResultType) DBReqOption {
	return func(opts *DBAgentReq) {
		opts.RowResultType = param
	}
}
func WithQueryArgSql(param string) DBReqOption {
	return func(opts *DBAgentReq) {
		opts.QueryArgSql = param
	}
}
func WithFlushType(param FlushType) DBReqOption {
	return func(opts *DBAgentReq) {
		opts.FlushType = param
	}
}
func WithCacheLevel(param CacheLevel) DBReqOption {
	return func(opts *DBAgentReq) {
		opts.CacheLevel = param
	}
}
func WithUniqueKeys(param []string) DBReqOption {
	return func(opts *DBAgentReq) {
		opts.UniqueKeys = param
	}
}
func WithErrDiscard(param bool) DBReqOption {
	return func(opts *DBAgentReq) {
		opts.ErrDiscard = param
	}
}
func WithNotOptCache(param bool) DBReqOption {
	return func(opts *DBAgentReq) {
		opts.NotOptCache = param
	}
}
func WithLockId(param uint64) DBReqOption {
	return func(opts *DBAgentReq) {
		opts.LockId = param
	}
}
func WithLockKey(param string) DBReqOption {
	return func(opts *DBAgentReq) {
		opts.LockKey = param
	}
}
func WithLockOpt(param LockOpt) DBReqOption {
	return func(opts *DBAgentReq) {
		opts.LockOpt = param
	}
}
func WithLockTimeout(param int64) DBReqOption {
	return func(opts *DBAgentReq) {
		opts.LockTimeout = param
	}
}

type DBAgentResp struct {
	Code ErrorCode   `json:"code"`
	Msg  string      `json:"message,omitempty"`
	Data interface{} `json:"data,omitempty"`

	LastInsertId int64 `json:"lastinsertid,omitempty"`
	RowsAffected int64 `json:"rowsaffected,omitempty"`
}

func (r *DBAgentResp) Unmarshal(buf []byte) error {
	d := json.NewDecoder(bytes.NewReader(buf))
	d.UseNumber()
	return d.Decode(r)
}

func (r *DBAgentResp) Err() error {
	if r.Code != OK {
		return errors.New(r.Msg)
	}
	return nil
}

func (r *DBAgentResp) DataIsEmpty() bool {
	if r.Data != nil {
		if val, ok := r.Data.(string); ok && val == ErrDBResEmpty.Error() {
			return true
		}
	}
	return false
}

func (r *DBAgentResp) DataInt64() int64 {
	switch r.Data.(type) {
	case int64:
		return r.Data.(int64)
	case int32:
		return int64(r.Data.(int32))
	default:
		if f, e := strconv.ParseInt(fmt.Sprint(r.Data), 10, 64); e == nil {
			return f
		}
	}
	return 0
}

//	value 传入的指针
//
// 示例
//
//	var abc time.Time
//	v.As(&abc)
func (r *DBAgentResp) DataAs(value interface{}) (err error) {
	if r.Data == nil {
		return nil
	}
	if r.DataIsEmpty() {
		return ErrDBResEmpty
	}
	switch r.Data.(type) {
	case string:
		if err := json.Unmarshal([]byte(r.Data.(string)), value); err != nil {
			return err
		}
		return nil
	case []byte:
		if err := json.Unmarshal(r.Data.([]byte), value); err != nil {
			return err
		}
		return nil
	default:
		return errors.New("data type not support " + reflect.TypeOf(r.Data).String())
	}
}

func (r *DBAgentResp) DataAsMap() (map[string]interface{}, error) {
	if r.DataIsEmpty() {
		return nil, ErrDBResEmpty
	}
	switch r.Data.(type) {
	case map[string]interface{}:
		return r.Data.(map[string]interface{}), nil
	case string:
		m := make(map[string]interface{})
		if err := json.Unmarshal([]byte(r.Data.(string)), &m); err != nil {
			return nil, err
		}
		return m, nil
	case []byte:
		m := make(map[string]interface{})
		if err := json.Unmarshal(r.Data.([]byte), &m); err != nil {
			return nil, err
		}
		return m, nil
	default:
		return nil, errors.New("data type not support " + reflect.TypeOf(r.Data).String())
	}
}

func (r *DBAgentResp) DataAsString() (string, error) {
	if r.Data == nil {
		return "", nil
	}
	if r.DataIsEmpty() {
		return "", ErrDBResEmpty
	}
	switch r.Data.(type) {
	case string:
		return r.Data.(string), nil
	case []byte:
		return string(r.Data.([]byte)), nil
	default:
		return "", errors.New("data type not support " + reflect.TypeOf(r.Data).String())
	}
}
