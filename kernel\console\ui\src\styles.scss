/* You can add global styles to this file, and also import other style files */

@import "@ng-select/ng-select/themes/default.theme.css";
@import "vanilla-jsoneditor/themes/jse-theme-default.css";

/* Colors */
$brand-purple: #7668ED;
$brand-light-purple: #A69BFF;
$brand-grey: #FAFAFC;
$brand-dark-blue: #333564;
$brand-pink: #F853F7;
$brand-blue: #68B8EA;
$brand-red: #FE756A;
$brand-yellow: #EDD83E;
$brand-green: #63C179;

$font-color: #333564;

.bg-secondary {
  background-color: lighten($brand-purple, 8%)!important;
}

.bg-success {
  background-color: $brand-green;
}

/* Type */

body {
  /*//background-color: #f5f5f5;*/ /* TODO remove */
  font-family: 'Source Sans Pro', sans-serif;
}

h1, h2, h3, h4, h5 {
  /*font-family: 'Montserrat', sans-serif!important;*/
}


/* Button and link stuff */
a {
  color: $brand-purple;

  &:hover {
    color: lighten($brand-purple, 8%);
  }
}

.custom-control-input:checked~.custom-control-label::before {
  background-color: $brand-purple;
  border-color: $brand-purple;
}

.btn {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-size: 12px;

  /* Remove annoying blue border? */

  outline:none !important;
  outline-width: 0 !important;
  box-shadow: none;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  -webkit-appearance: none;
}

.btn-sm {
  font-size: 11px;
}

.btn-primary.dropdown-toggle {
  background-color: lighten($brand-purple, 5%);

  &[aria-expanded="true"] {
    background-color: $brand-purple;
    border-color: $brand-purple;
  }

  &:active {
    background-color: $brand-purple !important;
    border-color: $brand-purple;
  }

  &:focus {
    background-color: lighten($brand-pink, 5%)!important;
    border-color: $brand-pink;
  }
}

.btn-primary {
  background-color: $brand-purple;
  border-color: $brand-purple;

  &:hover {
    background-color: lighten($brand-purple, 5%);
    border-color: $brand-purple;
  }

  &:active {
    background-color: $brand-purple !important;
    border-color: $brand-purple;
  }

  &:focus {
    background-color: lighten($brand-pink, 5%)!important;
    border-color: $brand-pink;
  }

  &:disabled {
    border-color: $brand-purple;
  }
}

.btn-outline-primary {
  border: solid 2px $brand-purple;
  color: $brand-purple;
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-size: 13px;

  &:hover {
    background-color: lighten($brand-purple, 5%);
    border-color: $brand-purple;
  }

  &:active {
    background-color: lighten($brand-pink, 5%)!important;
    border-color: $brand-pink!important;
  }

  &:disabled {
    border-color: $brand-purple;
  }
}

.btn-outline-secondary {
  border: solid 1px darken($brand-grey, 25%);
  font-size: 10px;
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-size: 13px;

/*  img {
    margin-left: -0.5em;
  }*/

  &:hover {
    background-color: darken($brand-grey, 5%);
    color: $brand-purple;
  }

  &:active {
    background-color: darken($brand-purple, 2%)!important;
  }
}

.btn-outline-dark {
  border-color: darken($brand-grey, 18%);
  font-size: 10px;


  img {
    margin-left: -0.5em;
  }

  &:hover:not(.disabled) {
    background-color: $brand-purple;
    border-color: darken($brand-grey, 8%);
  }

  &:active {
    background-color: darken($brand-purple, 2%);
  }
}

.btn-danger {
  background-color: lighten($brand-red, 20%);
  color: $brand-red;
  border-color: $brand-red;

  &:hover {
    background-color: $brand-red!important;
    color: #fff;
  }
}

.btn-danger-icon {
  background-color: #fff;

  &:hover {
    color: $brand-red;
    background-color: lighten($brand-red, 20%)!important;
  }
}

.btn-group-toggle {
  label {
    font-size: 10px;
    }
}

.btn-warning {
  background-color: $brand-yellow;
  background-color: darken($brand-yellow, 2%)!important;

  &:hover {
    background-color: darken($brand-yellow, 10%)!important;
  }
}

/* Pagination btns */
.page-btns {
  .btn:hover {
    background: #A69BFF!important;
  }

  .btn {
    padding: 0.3em 0.9em;
  }
}

/* Breadcrumbs */
.breadcrumb {
  padding: 0;
  margin: 0;
  background: #fff;
}


/* Badges */

.badge {
  border-radius: 20px;
  padding: 0.3rem 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-size: 11px;
  color: $font-color;
  display: inline-flex;
  align-items: center;
}

.badge-secondary {
  background-color: darken($brand-grey, 5%);

  img {
    margin-right: 0.5rem;
    margin-left: -0.5rem;
  }
}


.badge-light {
  background-color: lighten($brand-purple, 28%);

  img {
    margin-right: 0.5rem;
    margin-left: -0.5rem;
  }
}

.badge-primary {
  background-color: lighten($brand-blue, 25%);

  img {
    margin-right: 0.5rem;
    margin-left: -0.5rem;
  }
}

.badge-success {
  background-color: lighten($brand-green, 30%);

  img {
    margin-right: 0.5rem;
    margin-left: -0.5rem;
  }
}

.badge-danger {
  background-color: lighten($brand-red, 20%);
  color: $brand-red;

  img {
    margin-right: 0.5rem;
    margin-left: -0.2rem;
  }
}

.badge-warning {
  background-color: lighten($brand-yellow, 30%);

  img {
    margin-right: 0.5rem;
    margin-left: -0.2rem;
  }
}

.badge-info {
  background-color: lighten($brand-pink, 30%);

  img {
    margin-right: 0.5rem;
    margin-left: -0.5rem;
  }
}

.badge-dark {
  background-color: darken($brand-grey, 26%);

  img {
    margin-right: 0.5rem;
    margin-left: -0.5rem;
  }
}

/* Loading... */
.loading {
  width: 50px;
}

/* Add borders to forms and sections */
.add-border {

  .row {
    border-top: solid 1px darken($brand-grey, 10%);
    border-left: solid 1px darken($brand-grey, 10%);
    border-right: solid 1px darken($brand-grey, 10%);
    padding: 0.6rem;
    margin: 0;
  }

  .form-row {
    border-top: solid 1px darken($brand-grey, 10%);
    margin: 0;
    padding-top: 2rem;
  }
}

.add-border-single-row {
  .row {
    border: solid 1px darken($brand-grey, 10%);
    padding: 0.75rem;
    margin: 0;
  }
}

.add-border-single-row-bottom {
   border-bottom: solid 1px darken($brand-grey, 10%);
}

.remove-sides {
  border-left: none!important;
  border-right: none!important;
}

.card-box {
  border: solid 1px darken($brand-grey, 10%);
  padding: 1.75rem 0.75rem;
}

/* Alerts */
.alert {
  h6 {
    margin-bottom: 8px;
  }

  small {
    font-size: 15px;
  }
}

.alert-primary {
  background-color: lighten($brand-purple, 30%);
  border-color: lighten($brand-purple, 10%);
  color: $brand-purple;
}

.alert-success {
  background-color: lighten($brand-green, 33%);
  border-color: lighten($brand-green, 10%);
  color: darken($brand-green, 35%);
}

.alert-secondary {
  background-color: $brand-grey;
  border-color: darken($brand-grey, 10%);
  color: darken($brand-grey, 60%);
}

.alert-danger {
  background-color: lighten($brand-red, 25%);
  border-color: lighten($brand-red, 10%);
  color: $brand-red;
}

.alert-warning {
  background-color: lighten($brand-yellow, 30%);
  border-color: lighten($brand-yellow, 10%);
  color: $font-color;
}

.alert-info {
  background-color: lighten($brand-blue, 30%);
  border-color: lighten($brand-blue, 10%);
  color: $font-color;
}

/* Section divider */
.section-divider::after {
  height: 1px;
  margin-left: 1rem;
  flex-grow: 1;
  align-self: center;
  background-color: darken($brand-grey, 10%);
  content: '';
}

/* Section divider button right*/
.section-divider-btn {
  display: flex;
  align-items: center;
}

.section-divider-btn__text {
  display: flex;
  flex-grow: 1;
  align-items: center;
}

.section-divider-btn__text::after {
  height: 1px;
  flex-grow: 1;
  margin-left: 15px;
  background-color: darken($brand-grey, 10%);
  content: '';
}

/* Tables */
.table .thead-light th {
  background-color: $brand-grey;
  border-color: darken($brand-grey, 10%);
  color: darken($brand-grey, 60%);
  border-bottom: 0;
}


.table-hover tbody tr:hover td {
  background-color: lighten($brand-purple, 30%);
}

/* Snazzy background gradient */
.bg-grad {
  background-color: #6a57ee;
  background-image:
    linear-gradient(to bottom,  #6a57ee 0%, #3a3f77 100%);
  background-position:
    3% 3%,
    top right,
    97% bottom,
    bottom left;
  background-repeat: no-repeat;
}

.spinner {
  height: 100%;
}

.spinner > p {
  display: table-cell;
  text-align: center;
  width: 100%;
  vertical-align: middle;
}

.spinner > span {
  display: block;
  text-align: left;
  width: 100%;
  min-width: 24px;
  height: 100%;
  min-height: 24px;
  background-image: url("/static/white-progress-spinner.gif");
  background-repeat: no-repeat;
  background-position-x: center;
  background-position-y: center;
}

.tooltip-bg .tooltip-inner {
  background-color: #7668ED;
  font-size: 11px;
  text-align: left;
}
.bs-tooltip-right .arrow::before {
  border-right-color: #7668ED;
}
.bs-tooltip-top .arrow::before {
  border-top-color: #7668ED;
}
.bs-tooltip-bottom .arrow::before {
  border-bottom-color: #7668ED;
}
.bs-tooltip-left .arrow::before {
  border-left-color: #7668ED;
}

// Used to animate arrow expansion in tables
.arrow {
  position: relative;
  overflow: hidden;
  margin-right: 5px;
  display: inline-block;
}

.arrow-right {
  background-image: url("/static/svg/arrow-right.svg");
  background-repeat: no-repeat;
  background-position: center;
  height: 12px;
  width: 12px;
  top: 0;
  left: 0;
}

.arrow-down {
  background-image: url("/static/svg/arrow-down.svg");
  background-repeat: no-repeat;
  background-position: center;
  height: 12px;
  width: 12px;
  top: 0;
  left: 0;
}

// Config drop-zone styling, must be in this file to be picked up by the component
.drop-zone {
  border: 2px dashed $brand-purple;
  border-radius:5px;
  height: 100px;
  margin: auto;
  background-color: #e5e2ff;
  margin-bottom: 48px;
}

.drop-zone-content {
  margin-top: 25px;
  align-items: center;
  color: #0782d0;
  display: flex;
  height: 50px;
  justify-content: center;
}

.jsoneditor {
  --jse-theme-color: #7668ED;
  --jes-theme-light-color: #A69BFF;
  --jse-button-primary-background: #7668ED;
  --jse-message-success-background: #A69BFF;
}
