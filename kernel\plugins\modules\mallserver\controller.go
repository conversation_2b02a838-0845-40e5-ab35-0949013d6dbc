package main

import (
	"context"
	"database/sql"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"kernel/plugins/modules/mallserver/bo"
	"sync"

	"github.com/gofrs/uuid/v5"
	json "github.com/json-iterator/go"
)

type Controller struct {
	server *MallServerModule
	cfgs   sync.Map // 可视化配置列表，key: 模块名，value: 模块配置
}

func NewController(s *MallServerModule) *Controller {
	return &Controller{
		server: s,
	}
}

// 可视化配置回调函数，dataId: 模块名，data: 模块配置
func (c *Controller) VisualCfgCallEvent(dataId, data string) {
	c.server.logger.Info("visualCfgEvent: %s, %s", dataId, data)
	switch dataId {
	case "mall_test_cfg":
		cfg := &MallTestCfg{}
		if err := json.Unmarshal([]byte(data), cfg); err != nil {
			c.server.logger.Error("parse mall_test_cfg error: %v", err)
		}
		c.cfgs.Store(dataId, cfg)
	}
}

// 获取可视化配置数据
func (c *Controller) GetVisualCfg(dataId string) (interface{}, bool) {
	return c.cfgs.Load(dataId)
}

func (c *Controller) Test(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	return payload, nil
}

// @Summary 充值
// @Description 充值
// @Tags mallserver
// @Accept json
// @Produce json
// @Param payload body ReqRecharge true "充值订单"
// @Success 200 {object} models.CommonResp
// @Router /v2/rpc/mallserver.recharge [post]
func (c *Controller) Recharge(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.SESSION_ERR, Msg: "session error", Data: nil})
	}

	var req ReqRecharge
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&models.CommonResp{Code: models.PARAM_ERR, Msg: "param error", Data: nil})
	}

	if req.ProductId == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.PARAM_ERR, Msg: "product_id is empty", Data: nil})
	}

	mall_test_cfg, ok := c.GetVisualCfg("mall_test_cfg")
	if !ok {
		return json.MarshalToString(&models.CommonResp{Code: models.DATA_NOT_EXIST, Msg: "mall_test_cfg not found", Data: nil})
	}
	cfg := mall_test_cfg.(*MallTestCfg)

	amount := 0.0
	found := false
	for _, item := range cfg.MallTestItemList.List {
		if item.ShopId == req.ProductId {
			found = true
			amount = common.Interface2Float64(item.Price)
			break
		}
	}
	if !found {
		return json.MarshalToString(&models.CommonResp{Code: models.PARAM_ERR, Msg: "product_id not found", Data: nil})
	}

	order_id := uuid.Must(uuid.NewV4()).String()
	order := bo.RechargeOrder{
		OrderId:   order_id,
		UserId:    user_id,
		ProductId: req.ProductId,
		Amount:    amount,
		Status:    0,
	}
	obj := c.server.dbagent.Create(ctx, &order, models.WithKeys(map[string]interface{}{order.GetKeyName(): order_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	resp, err := obj.WithReqOption(models.WithValuesFromDBModel(&order, nil)).Insert(ctx).Result()
	if err != nil {
		logger.Error("Failed to insert recharge order for user %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}
	if resp.RowsAffected <= 0 {
		logger.Error("Failed to insert recharge order for user %s: %v", user_id, resp)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "insert failed", Data: nil})
	}

	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "success", Data: &order})
}
