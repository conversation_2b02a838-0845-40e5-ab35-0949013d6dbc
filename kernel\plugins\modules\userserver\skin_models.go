package main

type Skin struct {
	SkinId     int64 `json:"skin_id,omitempty"`
	ExpireTime int64 `json:"expire_time,omitempty"`
}

type RespSkinList struct {
	Code    int64  `json:"code"`
	Message string `json:"message,omitempty"`
	Data    string `json:"data,omitempty"` // pb string
}

type ReqSkinOpt struct {
	UserId string `json:"user_id,omitempty"`
	Skin   *Skin  `json:"skin,omitempty"`
}

type RespSkinQuery struct {
	Code    int64  `json:"code"`
	Message string `json:"message,omitempty"`
	Data    *Skin  `json:"data,omitempty"`
}

type RespSkinUpdate struct {
	Code    int64  `json:"code"`
	Message string `json:"message,omitempty"`
	Data    *Skin  `json:"data,omitempty"`
}
