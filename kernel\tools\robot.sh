#!/bin/bash

# 环境，test、prod，支持传入，默认test
ENV=$1
if [ -z "$ENV" ]; then
    ENV="test"
fi

# 清空文件
echo > robots_${ENV}.csv

# 生成总数
TOTAL=1000
# 定义并发数
CONCURRENCY=10

# 创建账号的函数
create_account() {
    local start=$1
    local end=$2
    
    for i in $(seq $start $end); do
        # 根据ENV做不同的处理
        if [ "$ENV" == "test" ]; then
            # 生成随机账号
            email="robot$<EMAIL>"
            # 生成随机密码
            password="********"
            # 输出账号和密码
            echo "$email,$password" >> robots_${ENV}.csv
            echo $email,$password
            curl -X POST 'http://soc-dev.mini1.cn:7350/v1/account/create/email' --header 'Content-Type: application/json' -H "Authorization: Basic ZGVmYXVsdGh0dHBrZXk6ZGVmYXVsdGh0dHBrZXk=" --data-raw "{\"email\": \"$email\", \"password\": \"$password\"}" > /dev/null
        
        elif [ "$ENV" == "prod" ]; then
            # 生成随机账号
            email="robot$<EMAIL>"
            # 生成随机密码
            password="Ashcraft@123."
            # 输出账号和密码
            echo "$email,$password" >> robots_${ENV}.csv
            echo $email,$password
            curl -s -X POST 'https://api.ashcraft.world:7250/v2/account/authenticate/email?create=true' --header 'Content-Type: application/json' -H "Authorization: Basic MmY3MmQ1MjQzNzg0MDNiYjZmMTZmNzMxMGFjNTMyMzg6MmY3MmQ1MjQzNzg0MDNiYjZmMTZmNzMxMGFjNTMyMzg=" --data-raw "{\"email\": \"$email\", \"password\": \"$password\"}" > /dev/null
        fi
        
    done
}

# 计算每个进程处理的账号数量
ACCOUNTS_PER_PROCESS=$((TOTAL / CONCURRENCY))

# 并发创建账号
for ((i=0; i<CONCURRENCY; i++)); do
    start=$((i * ACCOUNTS_PER_PROCESS + 1))
    end=$((start + ACCOUNTS_PER_PROCESS - 1))
    # 最后一个进程处理剩余的账号
    if [ $i -eq $((CONCURRENCY - 1)) ]; then
        end=$TOTAL
    fi
    
    # 后台运行创建账号的函数
    create_account $start $end &
done

# 等待所有后台进程完成
wait
