package main

import "kernel/plugins/models"

// 组队操作码，客户端自定义得必须>100，100以内得系统保留
type PartyOpCode int64

const (
	PartyOpCodeUpdate PartyOpCode = 1 // 更新组队信息
	PartyOpCodeOnline PartyOpCode = 2 // 更新队伍成员在线状态
)

// 申请入队最大人数
const (
	PartyJoinRequestMaxSize = 100
	PartyInviteMaxSize      = 100
)

type PartyListReq struct {
	PageSize int `json:"page_size,omitempty"`
	PageNum  int `json:"page_num,omitempty"`
}

type PartyDetailData struct {
	PartyId          string             `json:"id,omitempty"`
	PartyLeader      string             `json:"leader,omitempty"`
	PartyMemberCount int                `json:"count,omitempty"`
	PartyMemberList  []*models.Presence `json:"members,omitempty"`
	PartyDesc        string             `json:"desc,omitempty"`   // 招募描述
	PartyLabels      string             `json:"labels,omitempty"` // 招募标签
}

type PartyListResp struct {
	Code    int                `json:"code"`
	Message string             `json:"message,omitempty"`
	Data    []*PartyDetailData `json:"data,omitempty"`
}

type PartyDetailReq struct {
	PartyId string `json:"id,omitempty"`
}

type PartyDetailResp struct {
	Code    int              `json:"code"`
	Message string           `json:"message,omitempty"`
	Data    *PartyDetailData `json:"data,omitempty"`
}

type PartyUpdateReq struct {
	PartyDesc   string `json:"desc,omitempty"`
	PartyLabels string `json:"labels,omitempty"`
	Open        bool   `json:"open,omitempty"` // 是否公开
}

type PartyUpdateResp struct {
	Code    int    `json:"code"`
	Message string `json:"message,omitempty"`
}

// 邀请加入队伍
type PartyInviteReq struct {
	Uin string `json:"uin"`
}

// 查询指定玩家所在队伍
type PartyQueryReq struct {
	Uin string `json:"uin"`
}

// 查询指定玩家所在队伍
type PartyQueryResp struct {
	Code    int              `json:"code"`
	Message string           `json:"message,omitempty"`
	Data    *PartyDetailData `json:"data,omitempty"`
}
