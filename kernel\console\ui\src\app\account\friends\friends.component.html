<script src="friends.component.ts"></script>
<ngb-alert [dismissible]="false" type="danger" class="mb-3" *ngIf="error">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">An error occurred: {{error}}</h6>
</ngb-alert>

<div class="row no-gutters">
  <table class="table table-sm table-hover table-bordered">
    <thead class="thead-light">
      <tr>
        <th style="width: 320px">User ID</th>
        <th>Username</th>
        <th style="width: 300px">State</th>
        <th style="width: 180px">Update Time</th>
        <th style="width: 90px" *ngIf="deleteAllowed()">Remove</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngIf="friends.length === 0">
        <td colSpan="5" class="text-muted">No friends found.</td>
      </tr>

      <tr *ngFor="let f of friends; index as i">
        <td (click)="viewAccount(f.user);">{{f.user.id}}</td>
        <td (click)="viewAccount(f.user);">{{f.user.username}}</td>
        <td (click)="viewAccount(f.user);">
          <span *ngIf="f.state === 0">Friend (0)</span>
          <span *ngIf="f.state === 1">Invite Sent (1)</span>
          <span *ngIf="f.state === 2">Invite Received (2)</span>
          <span *ngIf="f.state === 3">Blocked (3)</span>
        </td>
        <td>{{f.update_time}}</td>
        <td *ngIf="deleteAllowed()" class="text-center"><button type="button" class="btn btn-sm btn-danger" (click)="deleteFriend($event, i, f);">Delete</button></td>
      </tr>
    </tbody>
  </table>
</div>
