definitions:
  main.AccountEmail:
    properties:
      email:
        type: string
      password:
        type: string
      vars:
        additionalProperties:
          type: string
        description: nick, clientip
        type: object
    type: object
  main.AccountEmailResp:
    properties:
      code:
        $ref: '#/definitions/models.ErrorCode'
      msg:
        type: string
    type: object
  main.Avatar:
    properties:
      avatar_id:
        type: integer
      expire_time:
        type: integer
    type: object
  main.EvidenceInfo:
    properties:
      evidence_type:
        description: 证据类型
        type: integer
      evidence_url:
        description: 证据链接地址
        type: string
    type: object
  main.Favorite:
    properties:
      time:
        description: 收藏时间
        type: integer
    type: object
  main.GetUploadURLReq:
    properties:
      files:
        description: 上传文件信息
        items:
          $ref: '#/definitions/main.UploadFileInfo'
        type: array
    type: object
  main.GetUploadURLResp:
    properties:
      error_code:
        description: 错误码
        type: integer
      error_msg:
        description: 错误信息
        type: string
      urls:
        description: 上传URL
        items:
          type: string
        type: array
    type: object
  main.History:
    properties:
      time:
        description: 时间
        type: integer
    type: object
  main.LoginLog:
    properties:
      device:
        type: string
      ip_address:
        type: string
      login_time:
        type: string
      metadata:
        type: string
      uin:
        type: integer
      user_id:
        type: string
    type: object
  main.Mount:
    properties:
      expire_time:
        type: integer
      mount_id:
        type: integer
    type: object
  main.PartyDetailData:
    properties:
      count:
        type: integer
      desc:
        description: 招募描述
        type: string
      id:
        type: string
      labels:
        description: 招募标签
        type: string
      leader:
        type: string
      members:
        items:
          $ref: '#/definitions/models.Presence'
        type: array
    type: object
  main.PartyDetailReq:
    properties:
      id:
        type: string
    type: object
  main.PartyDetailResp:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/main.PartyDetailData'
      message:
        type: string
    type: object
  main.PartyInviteReq:
    properties:
      uin:
        type: string
    type: object
  main.PartyListReq:
    properties:
      page_num:
        type: integer
      page_size:
        type: integer
    type: object
  main.PartyListResp:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/main.PartyDetailData'
        type: array
      message:
        type: string
    type: object
  main.PartyUpdateReq:
    properties:
      desc:
        type: string
      labels:
        type: string
      open:
        description: 是否公开
        type: boolean
    type: object
  main.PartyUpdateResp:
    properties:
      code:
        type: integer
      message:
        type: string
    type: object
  main.PlayerWallet:
    properties:
      diamond:
        description: 购买获得，钻石数量
        type: integer
      diamond_gift:
        description: 非购买获得钻石数量
        type: integer
      gold:
        description: 金币数量
        type: integer
    type: object
  main.QueryReportReq:
    properties:
      begin_time:
        description: 举报时间（可选），不填则默认最近一周
        type: string
      client_version:
        description: 客户端版本
        type: string
      end_time:
        description: 举报时间（可选），不填则默认最近一周
        type: string
      is_appealable:
        description: 是否可申诉
        type: integer
      limit:
        description: 返回的行数（limit）：表示最多返回多少行数据。
        type: integer
      offset:
        description: 偏移量（offset）：表示从结果集的哪个位置开始返回数据，0 表示第一行。
        type: integer
      report_id:
        description: 举报请求的唯一ID，该id设置后，其他参数自动失效
        type: string
      report_type:
        description: 举报类型
        type: integer
      reported_id:
        description: 被举报人玩家ID
        type: string
      reporter_id:
        description: 举报人玩家ID
        type: string
      reporter_region:
        description: 区服标识
        type: string
      status:
        description: 处理状态
        type: integer
      violation_type:
        description: 违规类型
        type: string
    type: object
  main.QueryReportResp:
    properties:
      error_code:
        description: 错误码
        type: integer
      error_msg:
        description: 错误信息
        type: string
      report_record_item:
        description: 举报记录列表
        items:
          $ref: '#/definitions/main.ReportRecordItem'
        type: array
      total:
        description: 总行数
        type: integer
    type: object
  main.QueryStatusData:
    properties:
      uids:
        additionalProperties:
          type: boolean
        type: object
      uins:
        additionalProperties:
          type: boolean
        type: object
    type: object
  main.QueueStatus:
    properties:
      rank:
        type: integer
      total:
        type: integer
    type: object
  main.ReportCallbackReq:
    properties:
      is_appealable:
        description: 是否可申诉
        type: boolean
      punishment_hours:
        description: 惩罚时长
        type: integer
      punishment_id:
        description: 惩罚类型
        type: string
      report_id:
        description: 举报请求的唯一ID，该id设置后，其他参数自动失效
        type: string
      reviewer_id:
        description: 审核人ID
        type: string
      status:
        description: '处理状态,0：无需处理,1: 未处理,2: 已经处理'
        type: integer
      violation_type:
        description: 违规类型
        type: string
    type: object
  main.ReportCallbackResp:
    properties:
      code:
        description: 错误码
        type: integer
      message:
        description: 错误信息
        type: string
    type: object
  main.ReportRecordItem:
    properties:
      client_version:
        description: 客户端版本
        type: string
      created_at:
        description: 创建时间
        type: string
      description:
        description: 问题描述
        type: string
      evidence_infos:
        description: 证据截图、视频链接（可选）
        items:
          $ref: '#/definitions/main.EvidenceInfo'
        type: array
      handled_at:
        description: 审核处理时间
        type: string
      is_appealable:
        description: 是否可申诉
        type: boolean
      punishment_hours:
        description: 惩罚时长
        type: integer
      punishment_id:
        description: 惩罚类型
        type: string
      report_id:
        description: 举报请求的唯一ID，该id设置后，其他参数自动失效
        type: string
      report_source:
        description: 举报来源
        type: string
      report_type:
        description: 举报类型
        type: integer
      reported_id:
        description: 被举报人玩家ID
        type: string
      reporter_id:
        description: 举报人玩家ID
        type: string
      reporter_region:
        description: 区服标识
        type: string
      status:
        description: 处理状态
        type: integer
      violation_type:
        description: 违规类型
        type: string
    type: object
  main.ReportReq:
    properties:
      client_version:
        description: 客户端版本（可选）
        type: string
      created_at:
        description: 举报时间（可选，不填则以入库时间为准）
        type: string
      description:
        description: 问题描述
        type: string
      evidence_infos:
        description: 证据截图、视频链接（可选）
        items:
          $ref: '#/definitions/main.EvidenceInfo'
        type: array
      report_source:
        description: 举报来源(game, website等)
        type: string
      report_type:
        description: 举报类型
        type: integer
      reported_id:
        description: 被举报人玩家ID
        type: string
      reporter_id:
        description: 举报人玩家ID
        type: string
      reporter_region:
        description: 区服标识（可选）
        type: string
    type: object
  main.ReportResp:
    properties:
      error_code:
        description: 错误码
        type: integer
      error_msg:
        description: 错误信息
        type: string
      report_id:
        description: 举报请求的唯一ID
        type: string
    type: object
  main.ReqAvatarOpt:
    properties:
      avatar:
        $ref: '#/definitions/main.Avatar'
      user_id:
        type: string
    type: object
  main.ReqBanUser:
    properties:
      reason:
        description: 封禁原因
        type: string
      time:
        description: 封禁时长，单位秒
        type: integer
      uids:
        description: 多个用户ID
        items:
          type: string
        type: array
    type: object
  main.ReqGetQueueStatus:
    properties:
      room_id:
        type: string
    type: object
  main.ReqJoinQueue:
    properties:
      room_id:
        type: string
    type: object
  main.ReqLeaveQueue:
    properties:
      room_id:
        type: string
    type: object
  main.ReqModifyBase:
    properties:
      fields:
        additionalProperties: true
        description: 字段名看models.User内的json tag {"name":"张三","age":18}
        type: object
      user_id:
        description: 用户ID
        type: string
    type: object
  main.ReqModifyGender:
    properties:
      gender:
        description: 性别
        type: integer
      user_id:
        description: 用户ID
        type: string
    type: object
  main.ReqMountOpt:
    properties:
      mount:
        $ref: '#/definitions/main.Mount'
      user_id:
        type: string
    type: object
  main.ReqQueryBase:
    properties:
      fields:
        description: 查询时可选，如果*则查询所有字段, 字段名看models.User内的json tag
        items:
          type: string
        type: array
      page:
        description: 页码，查询所有用户时有效
        type: integer
      size:
        description: 每页数量，查询所有用户时有效
        type: integer
      uids:
        description: 多个用户ID, 如果*则查询所有用户
        items:
          type: string
        type: array
      uins:
        description: 多个Uins
        items:
          type: integer
        type: array
    type: object
  main.ReqQueryLoginLogs:
    properties:
      end_time:
        description: 结束时间
        type: integer
      page_num:
        type: integer
      page_size:
        type: integer
      sort:
        description: 排序方式 asc, desc
        type: string
      start_time:
        description: 开始时间
        type: integer
      uin:
        type: integer
      user_id:
        type: string
    type: object
  main.ReqQueryStatus:
    properties:
      uids:
        description: 多个用户ID
        items:
          type: string
        type: array
      uins:
        description: 多个Uins
        items:
          type: integer
        type: array
    type: object
  main.ReqSkinOpt:
    properties:
      skin:
        $ref: '#/definitions/main.Skin'
      user_id:
        type: string
    type: object
  main.ReqUnbanUser:
    properties:
      uids:
        description: 多个用户ID
        items:
          type: string
        type: array
    type: object
  main.ReqVaultsDelete:
    properties:
      items:
        items:
          $ref: '#/definitions/main.VaultItem'
        type: array
    type: object
  main.ReqVaultsDeposit:
    properties:
      items:
        items:
          $ref: '#/definitions/main.VaultItem'
        type: array
    type: object
  main.ReqVaultsExpand:
    properties:
      count:
        type: integer
    type: object
  main.ReqVaultsExtract:
    properties:
      items:
        items:
          $ref: '#/definitions/main.VaultItem'
        type: array
    type: object
  main.ReqWalletAdd:
    properties:
      diamond:
        description: 添加购买获得钻石数量
        type: integer
      diamond_gift:
        description: 赠送获得钻石数量
        type: integer
      gold:
        description: 添加金币数量
        type: integer
      reason:
        description: 添加原因
        type: string
      user_id:
        description: 用户ID
        type: string
    type: object
  main.ReqWalletSub:
    properties:
      diamond:
        description: 扣除购买获得钻石数量
        type: integer
      diamond_gift:
        description: 扣除赠送获得钻石数量
        type: integer
      gold:
        description: 扣除金币数量
        type: integer
      reason:
        description: 扣除原因
        type: string
      user_id:
        description: 用户ID
        type: string
    type: object
  main.RespAuthenticateEmail:
    properties:
      code:
        $ref: '#/definitions/models.ErrorCode'
      created:
        type: boolean
      message:
        type: string
      refresh_token:
        type: string
      token:
        type: string
    type: object
  main.RespAvatarList:
    properties:
      code:
        type: integer
      data:
        description: pb string
        type: string
      message:
        type: string
    type: object
  main.RespAvatarQuery:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/main.Avatar'
      message:
        type: string
    type: object
  main.RespAvatarUpdate:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/main.Avatar'
      message:
        type: string
    type: object
  main.RespCreateRole:
    properties:
      code:
        $ref: '#/definitions/models.ErrorCode'
      data:
        $ref: '#/definitions/main.RoleInfo'
      message:
        type: string
    type: object
  main.RespGetFavoriteRooms:
    properties:
      code:
        $ref: '#/definitions/models.ErrorCode'
      data:
        additionalProperties:
          $ref: '#/definitions/main.Favorite'
        type: object
      message:
        type: string
    type: object
  main.RespGetHistoryRooms:
    properties:
      code:
        $ref: '#/definitions/models.ErrorCode'
      data:
        additionalProperties:
          $ref: '#/definitions/main.History'
        type: object
      message:
        type: string
    type: object
  main.RespGetQueueStatus:
    properties:
      code:
        $ref: '#/definitions/models.ErrorCode'
      data:
        $ref: '#/definitions/main.QueueStatus'
      message:
        type: string
    type: object
  main.RespJoinQueue:
    properties:
      code:
        $ref: '#/definitions/models.ErrorCode'
      data:
        $ref: '#/definitions/main.QueueStatus'
      message:
        type: string
    type: object
  main.RespMountList:
    properties:
      code:
        type: integer
      data:
        description: pb string
        type: string
      message:
        type: string
    type: object
  main.RespMountQuery:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/main.Mount'
      message:
        type: string
    type: object
  main.RespMountUpdate:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/main.Mount'
      message:
        type: string
    type: object
  main.RespQueryBase:
    properties:
      total:
        description: 总数量
        type: integer
      users:
        additionalProperties:
          $ref: '#/definitions/models.User'
        description: key：用户id
        type: object
    type: object
  main.RespQueryLoginLogs:
    properties:
      code:
        $ref: '#/definitions/models.ErrorCode'
      logs:
        items:
          $ref: '#/definitions/main.LoginLog'
        type: array
      message:
        type: string
      total:
        type: integer
    type: object
  main.RespQueryStatus:
    properties:
      code:
        $ref: '#/definitions/models.ErrorCode'
      data:
        $ref: '#/definitions/main.QueryStatusData'
      message:
        type: string
    type: object
  main.RespSkinList:
    properties:
      code:
        type: integer
      data:
        description: pb string
        type: string
      message:
        type: string
    type: object
  main.RespSkinQuery:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/main.Skin'
      message:
        type: string
    type: object
  main.RespSkinUpdate:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/main.Skin'
      message:
        type: string
    type: object
  main.RespVaultsDelete:
    properties:
      code:
        $ref: '#/definitions/models.ErrorCode'
      message:
        type: string
    type: object
  main.RespVaultsDeposit:
    properties:
      code:
        $ref: '#/definitions/models.ErrorCode'
      items:
        items:
          $ref: '#/definitions/main.VaultItem'
        type: array
      message:
        type: string
    type: object
  main.RespVaultsExpand:
    properties:
      capacity:
        type: integer
      code:
        $ref: '#/definitions/models.ErrorCode'
      message:
        type: string
    type: object
  main.RespVaultsExtract:
    properties:
      code:
        $ref: '#/definitions/models.ErrorCode'
      items:
        items:
          $ref: '#/definitions/main.VaultItem'
        type: array
      message:
        type: string
    type: object
  main.RespVaultsList:
    properties:
      code:
        type: integer
      data:
        description: pb string
        type: string
      message:
        type: string
    type: object
  main.RespWallet:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/main.PlayerWallet'
      message:
        type: string
    type: object
  main.RoleInfo:
    properties:
      last_login_time:
        type: integer
      role_data:
        type: string
      role_id:
        type: integer
    type: object
  main.Skin:
    properties:
      expire_time:
        type: integer
      skin_id:
        type: integer
    type: object
  main.UploadFileInfo:
    properties:
      file_md5:
        description: 文件MD5
        type: string
      file_name:
        description: 文件名
        type: string
      file_size:
        description: 文件大小
        type: integer
    type: object
  main.VaultItem:
    properties:
      bind_status:
        type: integer
      count:
        type: integer
      expire_time:
        type: integer
      item_id:
        type: integer
      slot_id:
        type: integer
    type: object
  models.CacheLevel:
    enum:
    - 1
    - 2
    type: integer
    x-enum-comments:
      CACHE_LEVEL_IMPORT: 重要
      CACHE_LEVEL_NORMAL: 正常
    x-enum-varnames:
    - CACHE_LEVEL_NORMAL
    - CACHE_LEVEL_IMPORT
  models.ClosePush:
    properties:
      data:
        type: object
      reason:
        enum:
        - 0
        - 1
        - 2
        - 3
        - 4
        type: integer
      sessions:
        items:
          type: string
        type: array
    type: object
  models.CommonPush:
    properties:
      data:
        type: object
      sessions:
        items:
          type: string
        type: array
    type: object
  models.CommonResp:
    properties:
      code:
        $ref: '#/definitions/models.ErrorCode'
      data: {}
      message:
        type: string
    type: object
  models.DBAgentReq:
    properties:
      cachelevel:
        allOf:
        - $ref: '#/definitions/models.CacheLevel'
        description: 缓存级别
      errdiscard:
        description: 写db失败是否丢弃写
        type: boolean
      flushtype:
        allOf:
        - $ref: '#/definitions/models.FlushType'
        description: 刷db类型
      keys:
        additionalProperties: true
        description: 键
        type: object
      lockid:
        description: 远程锁上下文id
        type: integer
      lockkey:
        description: 远程锁的键
        type: string
      lockopt:
        allOf:
        - $ref: '#/definitions/models.LockOpt'
        description: 远程锁操作类型
      locktimeout:
        description: 远程锁，等待加锁超时时间，毫秒为单位
        type: integer
      notoptcache:
        description: 不操作缓存(false:操作缓存，true:不操作缓存)
        type: boolean
      queryargsql:
        description: 查询参数sql
        type: string
      rowresulttype:
        allOf:
        - $ref: '#/definitions/models.RowResultType'
        description: 结果集类型
      rowssecondname:
        description: 多行结果集二级key
        type: string
      table:
        description: 数据库表名
        type: string
      ttl:
        description: 过期时间，单位秒
        type: integer
      uniquekeys:
        description: 唯一键字段名
        items:
          type: string
        type: array
      values:
        additionalProperties: true
        description: 值
        type: object
      versionname:
        description: 版本检查字段名，如果为空，则不进行版本检查，只支持单行结果集数据，版本字段类型为int64
        type: string
    type: object
  models.DBAgentResp:
    properties:
      code:
        $ref: '#/definitions/models.ErrorCode'
      data: {}
      lastinsertid:
        type: integer
      message:
        type: string
      rowsaffected:
        type: integer
    type: object
  models.ErrorCode:
    enum:
    - 0
    - 21
    - 22
    - 23
    - 24
    - 25
    - 26
    - 27
    - 28
    - 29
    - 30
    - 31
    - 32
    - 33
    type: integer
    x-enum-comments:
      AUTH_CODE: 告诉客户端需要验证码
      AUTH_ERR: 验证错误
      DATA_EXIST: 数据已存在
      DATA_NOT_EXIST: 数据不存在
      REPEAT_REG: 重复注册
    x-enum-varnames:
    - OK
    - TIMEOUT
    - FAILED
    - BUSY
    - EXCEPTION
    - PARAM_ERR
    - LOCK_ERR
    - SESSION_ERR
    - ACCESS_DENIED
    - AUTH_CODE
    - AUTH_ERR
    - REPEAT_REG
    - DATA_EXIST
    - DATA_NOT_EXIST
  models.FlushType:
    enum:
    - 0
    - 1
    - 2
    - 3
    type: integer
    x-enum-comments:
      FLUSH_TYPE_ASYNC: 立即异步回写
      FLUSH_TYPE_DEL: 删除回写
      FLUSH_TYPE_SYNC: 立即同步回写，默认值
      FLUSH_TYPE_TIMER: 定时回写
    x-enum-varnames:
    - FLUSH_TYPE_SYNC
    - FLUSH_TYPE_ASYNC
    - FLUSH_TYPE_TIMER
    - FLUSH_TYPE_DEL
  models.LockOpt:
    enum:
    - 0
    - 1
    - 2
    - 11
    - 12
    type: integer
    x-enum-comments:
      LOCKOPT_LOCK_READ: 读锁
      LOCKOPT_LOCK_WRITE: 写锁
      LOCKOPT_NONE: 无锁
      LOCKOPT_UNLOCK_READ: 解读锁
      LOCKOPT_UNLOCK_WRITE: 解写锁
    x-enum-varnames:
    - LOCKOPT_NONE
    - LOCKOPT_LOCK_READ
    - LOCKOPT_LOCK_WRITE
    - LOCKOPT_UNLOCK_READ
    - LOCKOPT_UNLOCK_WRITE
  models.Presence:
    properties:
      id:
        $ref: '#/definitions/models.PresenceID'
      meta:
        $ref: '#/definitions/models.PresenceMeta'
      stream:
        $ref: '#/definitions/models.PresenceStream'
      user_id:
        type: string
    type: object
  models.PresenceID:
    properties:
      node:
        type: string
      session_id:
        type: string
    type: object
  models.PresenceMeta:
    properties:
      format:
        $ref: '#/definitions/models.SessionFormat'
      hidden:
        type: boolean
      persistence:
        type: boolean
      reason:
        type: integer
      status:
        type: string
      username:
        type: string
    type: object
  models.PresenceStream:
    properties:
      label:
        type: string
      mode:
        type: integer
      subcontext:
        type: string
      subject:
        type: string
    type: object
  models.RowResultType:
    enum:
    - onlyone
    - multi
    type: string
    x-enum-comments:
      ROWRESULT_TYPE_MULTI: 多行
      ROWRESULT_TYPE_ONLYONE: 单行
    x-enum-varnames:
    - ROWRESULT_TYPE_ONLYONE
    - ROWRESULT_TYPE_MULTI
  models.SessionFormat:
    enum:
    - 0
    - 1
    type: integer
    x-enum-varnames:
    - SessionFormatJson
    - SessionFormatProtobuf
  models.User:
    properties:
      apple_id:
        description: The Apple Sign In ID in the user's account.
        type: string
      avatar_url:
        description: A URL for an avatar image.
        type: string
      create_time:
        description: The UNIX time (for gRPC clients) or ISO string (for REST clients)
          when the user was created.
        format: date-time
        type: string
      disable_time:
        description: DisableTime
        format: date-time
        type: string
      display_name:
        description: The display name of the user.
        type: string
      edge_count:
        description: Number of related edges to this user.
        type: integer
      facebook_id:
        description: The Facebook id in the user's account.
        type: string
      facebook_instant_game_id:
        description: The Facebook Instant Game ID in the user's account.
        type: string
      gamecenter_id:
        description: The Apple Game Center in of the user's account.
        type: string
      google_id:
        description: The Google id in the user's account.
        type: string
      id:
        description: The id of the user's account.
        type: string
      lang_tag:
        description: The language expected to be a tag which follows the BCP-47 spec.
        type: string
      location:
        description: The location set by the user.
        type: string
      metadata:
        description: Additional information stored as a JSON object.
        type: string
      online:
        description: Indicates whether the user is currently online.
        type: boolean
      steam_id:
        description: The Steam id in the user's account.
        type: string
      timezone:
        description: The timezone set by the user.
        type: string
      update_time:
        description: The UNIX time (for gRPC clients) or ISO string (for REST clients)
          when the user was last updated.
        format: date-time
        type: string
      username:
        description: The username of the user's account.
        type: string
    type: object
  models.UserOnlineInfo:
    properties:
      apiid:
        description: api id
        type: integer
      client_ip:
        description: 客户端ip
        type: string
      cltversion:
        description: 客户端版本
        type: integer
      node_addr:
        description: 网关节点地址
        type: string
      node_id:
        description: 网关节点id
        type: string
      online_time:
        description: 在线时间
        type: integer
      session_id:
        description: 会话id
        type: string
      uin:
        description: 用户uin
        type: integer
      user_id:
        description: 用户id
        type: string
    type: object
info:
  contact: {}
paths:
  /v2/rpc/dbagentserver.delete:
    post:
      consumes:
      - application/json
      description: 数据库代理删除
      parameters:
      - description: 请求参数
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/models.DBAgentReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/models.DBAgentResp'
      summary: 数据库代理删除
      tags:
      - dbagentserver
  /v2/rpc/dbagentserver.insert:
    post:
      consumes:
      - application/json
      description: 数据库代理插入
      parameters:
      - description: 请求参数
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/models.DBAgentReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/models.DBAgentResp'
      summary: 数据库代理插入
      tags:
      - dbagentserver
  /v2/rpc/dbagentserver.query:
    post:
      consumes:
      - application/json
      description: 数据库代理查询
      parameters:
      - description: 请求参数
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/models.DBAgentReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/models.DBAgentResp'
      summary: 数据库代理查询
      tags:
      - dbagentserver
  /v2/rpc/dbagentserver.rawexec:
    post:
      consumes:
      - application/json
      description: 数据库代理执行原始sql
      parameters:
      - description: 请求参数
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/models.DBAgentReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/models.DBAgentResp'
      summary: 数据库代理执行原始sql
      tags:
      - dbagentserver
  /v2/rpc/dbagentserver.rawquery:
    post:
      consumes:
      - application/json
      description: 数据库代理执行原始sql，不支持联表查询(因为更新会失败)
      parameters:
      - description: 请求参数
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/models.DBAgentReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/models.DBAgentResp'
      summary: 数据库代理执行原始sql
      tags:
      - dbagentserver
  /v2/rpc/dbagentserver.update:
    post:
      consumes:
      - application/json
      description: 数据库代理更新
      parameters:
      - description: 请求参数
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/models.DBAgentReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/models.DBAgentResp'
      summary: 数据库代理更新
      tags:
      - dbagentserver
  /v2/rpc/gateserver.boardcast:
    post:
      consumes:
      - application/json
      description: 广播数据
      parameters:
      - description: 数据内容
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/models.CommonPush'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 广播数据
      tags:
      - gateserver
  /v2/rpc/gateserver.close:
    post:
      consumes:
      - application/json
      description: 关闭连接
      parameters:
      - description: 请求参数
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/models.ClosePush'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 关闭连接
      tags:
      - gateserver
  /v2/rpc/gateserver.multicast:
    post:
      consumes:
      - application/json
      description: 多播数据
      parameters:
      - description: 数据内容
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/models.CommonPush'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 多播数据
      tags:
      - gateserver
  /v2/rpc/gateserver.send:
    post:
      consumes:
      - application/json
      description: 发送数据
      parameters:
      - description: 数据内容
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/models.CommonPush'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 发送数据
      tags:
      - gateserver
  /v2/rpc/imserver.party.detail:
    post:
      consumes:
      - application/json
      description: 获取用户组队详情
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.PartyDetailReq'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.PartyDetailResp'
      summary: 获取用户组队详情
      tags:
      - Party
  /v2/rpc/imserver.party.invite:
    post:
      consumes:
      - application/json
      description: 邀请加入队伍
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.PartyInviteReq'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 邀请加入队伍
      tags:
      - Party
  /v2/rpc/imserver.party.list:
    post:
      consumes:
      - application/json
      description: 获取用户组队列表
      parameters:
      - description: 每页数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 页码
        in: query
        name: page_num
        required: true
        type: integer
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.PartyListReq'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.PartyListResp'
      summary: 获取用户组队列表
      tags:
      - Party
  /v2/rpc/imserver.party.update:
    post:
      consumes:
      - application/json
      description: 修改组队信息
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.PartyUpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.PartyUpdateResp'
      summary: 修改组队信息
      tags:
      - Party
  /v2/rpc/logicserver.addfavoriteroom:
    post:
      consumes:
      - application/json
      description: 添加一个收藏
      parameters:
      - description: 房间ID
        in: body
        name: payload
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功添加收藏
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 添加房间收藏
      tags:
      - logicserver
  /v2/rpc/logicserver.addhistoryroom:
    post:
      consumes:
      - application/json
      description: 添加一个历史房间
      parameters:
      - description: 房间ID
        in: body
        name: payload
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功添加历史房间
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 添加历史房间
      tags:
      - logicserver
  /v2/rpc/logicserver.createrole:
    post:
      consumes:
      - application/json
      description: 根据用户ID创建一个新角色，如果角色已存在则返回失败
      parameters:
      - description: 角色数据，JSON格式
        in: body
        name: payload
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功创建角色
          schema:
            $ref: '#/definitions/main.RespCreateRole'
      summary: 创建一个新角色
      tags:
      - logicserver
  /v2/rpc/logicserver.delfavoriterooms:
    post:
      consumes:
      - application/json
      description: 删除收藏房间
      parameters:
      - description: 房间ID列表 json
        in: body
        name: payload
        required: true
        schema:
          items:
            type: string
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: 成功删除收藏
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 删除收藏房间
      tags:
      - logicserver
  /v2/rpc/logicserver.delhistoryrooms:
    post:
      consumes:
      - application/json
      description: 删除历史房间
      parameters:
      - description: 房间ID列表
        in: body
        name: payload
        required: true
        schema:
          items:
            type: string
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: 成功删除历史房间
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 删除历史房间
      tags:
      - logicserver
  /v2/rpc/logicserver.getfavoriterooms:
    post:
      consumes:
      - application/json
      description: 获取用户的收藏列表
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/main.RespGetFavoriteRooms'
      summary: 获取收藏房间列表
      tags:
      - logicserver
  /v2/rpc/logicserver.gethistoryrooms:
    post:
      consumes:
      - application/json
      description: 获取用户的历史列表
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/main.RespGetHistoryRooms'
      summary: 获取历史房间列表
      tags:
      - logicserver
  /v2/rpc/logicserver.getuploadurl:
    post:
      consumes:
      - application/json
      description: 获取一个上传文件的url
      parameters:
      - description: 请求参数
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/main.GetUploadURLReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/main.GetUploadURLResp'
      summary: 获取一个上传文件的url
      tags:
      - logicserver
  /v2/rpc/logicserver.loginfinish:
    post:
      consumes:
      - application/json
      description: 登录完成
      produces:
      - application/json
      responses:
        "200":
          description: 响应结果
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 登录完成
      tags:
      - logicserver
  /v2/rpc/logicserver.queryreport:
    post:
      consumes:
      - application/json
      description: 查询举报
      parameters:
      - description: 请求参数
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/main.QueryReportReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/main.QueryReportResp'
      summary: 查询举报
      tags:
      - logicserver
  /v2/rpc/logicserver.querystatus:
    post:
      consumes:
      - application/json
      description: 查询用户状态 logicserver.querystatus
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqQueryStatus'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespQueryStatus'
      summary: 查询用户状态
      tags:
      - logicserver
  /v2/rpc/logicserver.report:
    post:
      consumes:
      - application/json
      description: 举报
      parameters:
      - description: 请求参数
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/main.ReportReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/main.ReportResp'
      summary: 举报
      tags:
      - logicserver
  /v2/rpc/logicserver.reportcallback:
    post:
      consumes:
      - application/json
      description: 举报回调
      parameters:
      - description: 请求参数
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/main.ReportCallbackReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/main.ReportCallbackResp'
      summary: 举报回调
      tags:
      - logicserver
  /v2/rpc/loginserver.auth.email:
    post:
      consumes:
      - application/json
      description: 自定义验证邮箱
      parameters:
      - description: 账号信息
        in: body
        name: account
        required: true
        schema:
          $ref: '#/definitions/main.AccountEmail'
      produces:
      - application/json
      responses:
        "200":
          description: 创建结果
          schema:
            $ref: '#/definitions/main.RespAuthenticateEmail'
      summary: 自定义验证邮箱
  /v2/rpc/loginserver.auth.emailcode:
    post:
      consumes:
      - application/json
      description: 验证邮箱验证码
      parameters:
      - description: 账号信息
        in: body
        name: account
        required: true
        schema:
          $ref: '#/definitions/main.AccountEmail'
      produces:
      - application/json
      responses:
        "200":
          description: 验证结果
          schema:
            $ref: '#/definitions/main.RespAuthenticateEmail'
      summary: 验证邮箱验证码
  /v2/rpc/loginserver.createaccount.email:
    post:
      consumes:
      - application/json
      description: 根据邮箱注册账号，不需要验证邮箱，一般给运营后台调用
      parameters:
      - description: 账号信息
        in: body
        name: account
        required: true
        schema:
          $ref: '#/definitions/main.AccountEmail'
      produces:
      - application/json
      responses:
        "200":
          description: 创建结果
          schema:
            $ref: '#/definitions/main.AccountEmailResp'
      summary: 根据邮箱注册账号
  /v2/rpc/loginserver.queryloginlogs:
    post:
      consumes:
      - application/json
      description: 查询用户登录记录
      parameters:
      - description: 查询条件
        in: body
        name: account
        required: true
        schema:
          $ref: '#/definitions/main.ReqQueryLoginLogs'
      produces:
      - application/json
      responses:
        "200":
          description: 查询结果
          schema:
            $ref: '#/definitions/main.RespQueryLoginLogs'
      summary: 查询用户登录记录
  /v2/rpc/queueserver.getqueuestatus:
    post:
      consumes:
      - application/json
      description: 获取排队状态
      parameters:
      - description: 获取排队状态请求
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/main.ReqGetQueueStatus'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/main.RespGetQueueStatus'
      summary: 获取排队状态
      tags:
      - queueserver
  /v2/rpc/queueserver.joinqueue:
    post:
      consumes:
      - application/json
      description: 加入排队
      parameters:
      - description: 加入排队请求
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/main.ReqJoinQueue'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/main.RespJoinQueue'
      summary: 加入排队
      tags:
      - queueserver
  /v2/rpc/queueserver.leavequeue:
    post:
      consumes:
      - application/json
      description: 离开排队
      parameters:
      - description: 离开排队请求
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/main.ReqLeaveQueue'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/main.ReqLeaveQueue'
      summary: 离开排队
      tags:
      - queueserver
  /v2/rpc/sessionserver.session.batchoffline:
    post:
      consumes:
      - application/json
      description: 批量下线,网关关闭的时候调用
      produces:
      - application/json
      responses:
        "200":
          description: 响应结果
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 批量下线
      tags:
      - sessionserver
  /v2/rpc/sessionserver.session.offline:
    post:
      consumes:
      - application/json
      description: 用户下线
      parameters:
      - description: 用户下线信息
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/models.UserOnlineInfo'
      produces:
      - application/json
      responses:
        "200":
          description: 响应结果
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 用户下线
      tags:
      - sessionserver
  /v2/rpc/sessionserver.session.online:
    post:
      consumes:
      - application/json
      description: 用户上线
      parameters:
      - description: 用户上线信息
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/models.UserOnlineInfo'
      produces:
      - application/json
      responses:
        "200":
          description: 响应结果
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 用户上线
      tags:
      - sessionserver
  /v2/rpc/statusserver.querybyuid:
    post:
      consumes:
      - application/json
      description: 根据uid查询在线信息
      parameters:
      - description: '查询用户状态请求,多个uid用逗号分隔，例如: 1,2,3'
        in: body
        name: payload
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: 查询用户状态响应
          schema:
            additionalProperties:
              $ref: '#/definitions/models.UserOnlineInfo'
            type: object
      summary: 根据uid查询用户在线信息
  /v2/rpc/statusserver.querybyuin:
    post:
      consumes:
      - application/json
      description: 根据uin查询用户在线信息
      parameters:
      - description: '查询用户状态请求,多个uin用逗号分隔，例如: 1,2,3'
        in: body
        name: payload
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: 查询用户状态响应
          schema:
            additionalProperties:
              $ref: '#/definitions/models.UserOnlineInfo'
            type: object
      summary: 根据uin查询用户在线信息
  /v2/rpc/statusserver.queryonlinebyuid:
    post:
      consumes:
      - application/json
      description: 根据uid查询是否在线
      parameters:
      - description: '查询用户状态请求,多个uid用逗号分隔，例如: 1,2,3'
        in: body
        name: payload
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: 查询用户状态响应
          schema:
            additionalProperties:
              type: boolean
            type: object
      summary: 根据uid查询用户是否在线
  /v2/rpc/statusserver.queryonlinebyuin:
    post:
      consumes:
      - application/json
      description: 根据uin查询是否在线
      parameters:
      - description: '多个uin用逗号分隔，例如: 1,2,3'
        in: body
        name: payload
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: 查询用户状态响应
          schema:
            additionalProperties:
              type: boolean
            type: object
      summary: 根据uin查询用户是否在线
  /v2/rpc/userserver.avatar.add:
    post:
      consumes:
      - application/json
      description: 添加装扮
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqAvatarOpt'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 添加装扮
      tags:
      - userserver
  /v2/rpc/userserver.avatar.delete:
    post:
      consumes:
      - application/json
      description: 删除装扮
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqAvatarOpt'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 删除装扮
      tags:
      - userserver
  /v2/rpc/userserver.avatar.list:
    post:
      consumes:
      - application/json
      description: 获取装扮列表
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespAvatarList'
      summary: 获取装扮列表
      tags:
      - userserver
  /v2/rpc/userserver.avatar.query:
    post:
      consumes:
      - application/json
      description: 查询装扮
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqAvatarOpt'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespAvatarQuery'
      summary: 查询装扮
      tags:
      - userserver
  /v2/rpc/userserver.avatar.update:
    post:
      consumes:
      - application/json
      description: 更新装扮
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqAvatarOpt'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespAvatarUpdate'
      summary: 更新装扮
      tags:
      - userserver
  /v2/rpc/userserver.ban:
    post:
      consumes:
      - application/json
      description: 封禁用户
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqBanUser'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 封禁用户
      tags:
      - userserver
  /v2/rpc/userserver.modify:
    post:
      consumes:
      - application/json
      description: 修改用户信息
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqModifyBase'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 修改用户信息
      tags:
      - userserver
  /v2/rpc/userserver.modifygender:
    post:
      consumes:
      - application/json
      description: 修改用户性别
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqModifyGender'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 修改用户性别
      tags:
      - userserver
  /v2/rpc/userserver.mount.add:
    post:
      consumes:
      - application/json
      description: 添加坐骑
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqMountOpt'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 添加坐骑
      tags:
      - userserver
  /v2/rpc/userserver.mount.delete:
    post:
      consumes:
      - application/json
      description: 删除坐骑
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqMountOpt'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 删除坐骑
      tags:
      - userserver
  /v2/rpc/userserver.mount.list:
    post:
      consumes:
      - application/json
      description: 获取坐骑列表
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespMountList'
      summary: 获取坐骑列表
      tags:
      - userserver
  /v2/rpc/userserver.mount.query:
    post:
      consumes:
      - application/json
      description: 查询坐骑
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqMountOpt'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespMountQuery'
      summary: 查询坐骑
      tags:
      - userserver
  /v2/rpc/userserver.mount.update:
    post:
      consumes:
      - application/json
      description: 更新坐骑
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqMountOpt'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespMountUpdate'
      summary: 更新坐骑
      tags:
      - userserver
  /v2/rpc/userserver.query:
    post:
      consumes:
      - application/json
      description: 查询用户信息 userserver.query
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqQueryBase'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespQueryBase'
      summary: 查询用户信息
      tags:
      - userserver
  /v2/rpc/userserver.skin.add:
    post:
      consumes:
      - application/json
      description: 添加皮肤
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqSkinOpt'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 添加皮肤
      tags:
      - userserver
  /v2/rpc/userserver.skin.delete:
    post:
      consumes:
      - application/json
      description: 删除皮肤
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqSkinOpt'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 删除皮肤
      tags:
      - userserver
  /v2/rpc/userserver.skin.list:
    post:
      consumes:
      - application/json
      description: 获取皮肤列表
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespSkinList'
      summary: 获取皮肤列表
      tags:
      - userserver
  /v2/rpc/userserver.skin.query:
    post:
      consumes:
      - application/json
      description: 查询皮肤
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqSkinOpt'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespSkinQuery'
      summary: 查询皮肤
      tags:
      - userserver
  /v2/rpc/userserver.skin.update:
    post:
      consumes:
      - application/json
      description: 更新皮肤
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqSkinOpt'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespSkinUpdate'
      summary: 更新皮肤
      tags:
      - userserver
  /v2/rpc/userserver.storage.delete:
    post:
      consumes:
      - application/json
      description: 删除仓库中指定物品
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqVaultsDelete'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespVaultsDelete'
      summary: 删除仓库中指定物品
      tags:
      - userserver
  /v2/rpc/userserver.storage.deposit:
    post:
      consumes:
      - application/json
      description: 存入指定物品列表
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqVaultsDeposit'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespVaultsDeposit'
      summary: 存入指定物品列表
      tags:
      - userserver
  /v2/rpc/userserver.storage.expand:
    post:
      consumes:
      - application/json
      description: 仓库扩容
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqVaultsExpand'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespVaultsExpand'
      summary: 仓库扩容
      tags:
      - userserver
  /v2/rpc/userserver.storage.extract:
    post:
      consumes:
      - application/json
      description: 提取指定物品列表
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqVaultsExtract'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespVaultsExtract'
      summary: 提取指定物品列表
      tags:
      - userserver
  /v2/rpc/userserver.storage.info:
    post:
      consumes:
      - application/json
      description: 获取仓库状态及物品详情
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespVaultsList'
      summary: 获取仓库状态及物品详情
      tags:
      - userserver
  /v2/rpc/userserver.unban:
    post:
      consumes:
      - application/json
      description: 解封用户
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqUnbanUser'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/models.CommonResp'
      summary: 解封用户
      tags:
      - userserver
  /v2/rpc/userserver.wallet.add:
    post:
      consumes:
      - application/json
      description: 添加钻石，金币
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqWalletAdd'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespWallet'
      summary: 添加钻石，金币
      tags:
      - userserver
  /v2/rpc/userserver.wallet.query:
    post:
      consumes:
      - application/json
      description: 获取钱包信息
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespWallet'
      summary: 获取钱包信息
      tags:
      - userserver
  /v2/rpc/userserver.wallet.sub:
    post:
      consumes:
      - application/json
      description: 扣除钻石，金币
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.ReqWalletSub'
      produces:
      - application/json
      responses:
        "200":
          description: 返回结果
          schema:
            $ref: '#/definitions/main.RespWallet'
      summary: 扣除钻石，金币
      tags:
      - userserver
swagger: "2.0"
