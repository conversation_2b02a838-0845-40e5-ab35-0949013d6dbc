-- +migrate Up
CREATE TABLE IF NOT EXISTS skins (
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,

    user_id UUID NOT NULL PRIMARY KEY,
    items JSONB NOT NULL DEFAULT '{}'::J<PERSON><PERSON><PERSON>,
    version int4 NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

CREATE TABLE IF NOT EXISTS avatars (
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,

    user_id UUID NOT NULL PRIMARY KEY,
    items JSONB NOT NULL DEFAULT '{}'::J<PERSON><PERSON><PERSON>,
    version int4 NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

CREATE TABLE IF NOT EXISTS mounts (
    <PERSON>OREI<PERSON>N KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,

    user_id UUID NOT NULL PRIMARY KEY,
    items JSONB NOT NULL DEFAULT '{}'::J<PERSON><PERSON><PERSON>,
    version int4 NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

CREATE TABLE IF NOT EXISTS recharge_order (
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,

    -- 内部订单号（先建订单时生成）
    order_id UUID NOT NULL PRIMARY KEY,               -- 订单号
    user_id UUID NOT NULL,                            -- 玩家ID

    -- 渠道订单号（渠道方返回）
    channel_order_id VARCHAR(128),                    -- 渠道订单号，支付成功后填
    channel VARCHAR(32) NOT NULL,                     -- 渠道标识，如 google, apple, huawei 等
    product_id VARCHAR(64) NOT NULL,                  -- 商品ID，例如 diamond_500
    amount NUMERIC(10, 2) NOT NULL,                   -- 订单金额
    currency VARCHAR(8) NOT NULL,                     -- 货币代码，ISO 4217，如 USD, CNY

    status SMALLINT NOT NULL DEFAULT 0,               -- 订单状态：0待支付,1已支付待发货,2已发货,3支付失败,4过期未支付

    extra JSONB DEFAULT NULL,                         -- 额外信息，比如购买IP、设备号、渠道原始返回数据等

    created_at TIMESTAMP NOT NULL DEFAULT NOW(),      -- 创建时间
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),      -- 更新时间（触发器更新）

    CONSTRAINT uniq_channel_order UNIQUE (channel, channel_order_id)
);

-- 索引
CREATE INDEX IF NOT EXISTS idx_user_id ON recharge_order(user_id);
CREATE INDEX IF NOT EXISTS idx_status ON recharge_order(status);

-- 注释（PostgreSQL 用 COMMENT ON）
COMMENT ON TABLE recharge_order IS '充值订单表';
COMMENT ON COLUMN recharge_order.order_id IS '订单号';
COMMENT ON COLUMN recharge_order.channel_order_id IS '渠道订单号，支付成功后填';
COMMENT ON COLUMN recharge_order.extra IS '额外信息，比如购买IP、设备号、渠道原始返回数据等';


-- +migrate Down
DROP TABLE IF EXISTS skins, avatars, mounts, recharge_order;