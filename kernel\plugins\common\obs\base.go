package obs

// Object storage interface
type IOSClient interface {
	// put raw data to os
	PutObject(key string, data []byte) error
	// put raw data to os with metadata
	PutObjectWithMetadata(objectKey string, data []byte, metaData map[string]string) error
	// get raw data from os
	GetObject(key string) ([]byte, error)
	// delete from os
	DelObject(key string) error
	// delete object batch
	DelObjects(keys ...string) error
	// put file to os
	PutFile(path string, key string) error
	// get file from os
	GetFile(key string, file string) error
	// get object metadata
	GetObjectMetadata(key string) (map[string]string, error)
	// set object metadata
	SetObjectMetadata(key string, metaData map[string]string) error
	// get object md5, return emtpy string if object not exist
	GetObjectMD5(key string) string
	// get object download url
	GetObjectDownloadURL(key string) string
	// release resoure
	Close()
	// list objects
	ListObjects(prefix string) ([]string, error)
	// get safe upload url
	GetSafeUploadURL(key string, expiresSeconds int, maxSize int64, expectedMD5 string) string
}

// Object storage client base
type OSClientBase struct {
	OSConfig
	Name string
}

type OSConfig struct {
	AccessKey string
	Secretkey string
	Endpoint  string
	Bucket    string
}
