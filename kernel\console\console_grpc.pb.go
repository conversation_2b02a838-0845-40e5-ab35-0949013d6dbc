// Copyright 2018 The Nakama Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

//*
// The RPC protocol for the developer console.

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.28.3
// source: console.proto

package console

import (
	context "context"
	api "kernel/kernel-common/api"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Console_Authenticate_FullMethodName              = "/nakama.console.Console/Authenticate"
	Console_AuthenticateLogout_FullMethodName        = "/nakama.console.Console/AuthenticateLogout"
	Console_AuthenticateMFASetup_FullMethodName      = "/nakama.console.Console/AuthenticateMFASetup"
	Console_AddUser_FullMethodName                   = "/nakama.console.Console/AddUser"
	Console_AddGroupUsers_FullMethodName             = "/nakama.console.Console/AddGroupUsers"
	Console_BanAccount_FullMethodName                = "/nakama.console.Console/BanAccount"
	Console_CallApiEndpoint_FullMethodName           = "/nakama.console.Console/CallApiEndpoint"
	Console_CallRpcEndpoint_FullMethodName           = "/nakama.console.Console/CallRpcEndpoint"
	Console_DeleteAllData_FullMethodName             = "/nakama.console.Console/DeleteAllData"
	Console_DeleteAccount_FullMethodName             = "/nakama.console.Console/DeleteAccount"
	Console_DeleteChannelMessages_FullMethodName     = "/nakama.console.Console/DeleteChannelMessages"
	Console_DeleteFriend_FullMethodName              = "/nakama.console.Console/DeleteFriend"
	Console_DeleteGroup_FullMethodName               = "/nakama.console.Console/DeleteGroup"
	Console_DeleteGroupUser_FullMethodName           = "/nakama.console.Console/DeleteGroupUser"
	Console_DeleteStorage_FullMethodName             = "/nakama.console.Console/DeleteStorage"
	Console_DeleteStorageObject_FullMethodName       = "/nakama.console.Console/DeleteStorageObject"
	Console_DeleteAccounts_FullMethodName            = "/nakama.console.Console/DeleteAccounts"
	Console_DeleteLeaderboard_FullMethodName         = "/nakama.console.Console/DeleteLeaderboard"
	Console_DeleteLeaderboardRecord_FullMethodName   = "/nakama.console.Console/DeleteLeaderboardRecord"
	Console_DeleteNotification_FullMethodName        = "/nakama.console.Console/DeleteNotification"
	Console_DeleteUser_FullMethodName                = "/nakama.console.Console/DeleteUser"
	Console_DeleteWalletLedger_FullMethodName        = "/nakama.console.Console/DeleteWalletLedger"
	Console_DemoteGroupMember_FullMethodName         = "/nakama.console.Console/DemoteGroupMember"
	Console_ExportAccount_FullMethodName             = "/nakama.console.Console/ExportAccount"
	Console_ExportGroup_FullMethodName               = "/nakama.console.Console/ExportGroup"
	Console_GetAccount_FullMethodName                = "/nakama.console.Console/GetAccount"
	Console_GetConfig_FullMethodName                 = "/nakama.console.Console/GetConfig"
	Console_GetFriends_FullMethodName                = "/nakama.console.Console/GetFriends"
	Console_GetGroup_FullMethodName                  = "/nakama.console.Console/GetGroup"
	Console_GetMembers_FullMethodName                = "/nakama.console.Console/GetMembers"
	Console_GetGroups_FullMethodName                 = "/nakama.console.Console/GetGroups"
	Console_GetLeaderboard_FullMethodName            = "/nakama.console.Console/GetLeaderboard"
	Console_GetMatchState_FullMethodName             = "/nakama.console.Console/GetMatchState"
	Console_GetRuntime_FullMethodName                = "/nakama.console.Console/GetRuntime"
	Console_GetStatus_FullMethodName                 = "/nakama.console.Console/GetStatus"
	Console_GetStorage_FullMethodName                = "/nakama.console.Console/GetStorage"
	Console_GetWalletLedger_FullMethodName           = "/nakama.console.Console/GetWalletLedger"
	Console_GetNotification_FullMethodName           = "/nakama.console.Console/GetNotification"
	Console_GetPurchase_FullMethodName               = "/nakama.console.Console/GetPurchase"
	Console_GetSubscription_FullMethodName           = "/nakama.console.Console/GetSubscription"
	Console_ListApiEndpoints_FullMethodName          = "/nakama.console.Console/ListApiEndpoints"
	Console_ListLeaderboardRecords_FullMethodName    = "/nakama.console.Console/ListLeaderboardRecords"
	Console_ListLeaderboards_FullMethodName          = "/nakama.console.Console/ListLeaderboards"
	Console_ListStorage_FullMethodName               = "/nakama.console.Console/ListStorage"
	Console_ListStorageCollections_FullMethodName    = "/nakama.console.Console/ListStorageCollections"
	Console_ListAccounts_FullMethodName              = "/nakama.console.Console/ListAccounts"
	Console_ListChannelMessages_FullMethodName       = "/nakama.console.Console/ListChannelMessages"
	Console_ListGroups_FullMethodName                = "/nakama.console.Console/ListGroups"
	Console_ListNotifications_FullMethodName         = "/nakama.console.Console/ListNotifications"
	Console_ListMatches_FullMethodName               = "/nakama.console.Console/ListMatches"
	Console_ListPurchases_FullMethodName             = "/nakama.console.Console/ListPurchases"
	Console_ListSubscriptions_FullMethodName         = "/nakama.console.Console/ListSubscriptions"
	Console_ListUsers_FullMethodName                 = "/nakama.console.Console/ListUsers"
	Console_PromoteGroupMember_FullMethodName        = "/nakama.console.Console/PromoteGroupMember"
	Console_RequireUserMfa_FullMethodName            = "/nakama.console.Console/RequireUserMfa"
	Console_ResetUserMfa_FullMethodName              = "/nakama.console.Console/ResetUserMfa"
	Console_UnbanAccount_FullMethodName              = "/nakama.console.Console/UnbanAccount"
	Console_UnlinkCustom_FullMethodName              = "/nakama.console.Console/UnlinkCustom"
	Console_UnlinkDevice_FullMethodName              = "/nakama.console.Console/UnlinkDevice"
	Console_UnlinkEmail_FullMethodName               = "/nakama.console.Console/UnlinkEmail"
	Console_UnlinkApple_FullMethodName               = "/nakama.console.Console/UnlinkApple"
	Console_UnlinkFacebook_FullMethodName            = "/nakama.console.Console/UnlinkFacebook"
	Console_UnlinkFacebookInstantGame_FullMethodName = "/nakama.console.Console/UnlinkFacebookInstantGame"
	Console_UnlinkGameCenter_FullMethodName          = "/nakama.console.Console/UnlinkGameCenter"
	Console_UnlinkGoogle_FullMethodName              = "/nakama.console.Console/UnlinkGoogle"
	Console_UnlinkSteam_FullMethodName               = "/nakama.console.Console/UnlinkSteam"
	Console_UpdateAccount_FullMethodName             = "/nakama.console.Console/UpdateAccount"
	Console_UpdateGroup_FullMethodName               = "/nakama.console.Console/UpdateGroup"
	Console_WriteStorageObject_FullMethodName        = "/nakama.console.Console/WriteStorageObject"
)

// ConsoleClient is the client API for Console service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConsoleClient interface {
	// Authenticate a console user with username and password.
	Authenticate(ctx context.Context, in *AuthenticateRequest, opts ...grpc.CallOption) (*ConsoleSession, error)
	// Log out a session and invalidate the session token.
	AuthenticateLogout(ctx context.Context, in *AuthenticateLogoutRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Change an account's MFA using a code, usually delivered over email.
	AuthenticateMFASetup(ctx context.Context, in *AuthenticateMFASetupRequest, opts ...grpc.CallOption) (*AuthenticateMFASetupResponse, error)
	// Add a new console user.
	AddUser(ctx context.Context, in *AddUserRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Add/join members to a group.
	AddGroupUsers(ctx context.Context, in *AddGroupUsersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Ban a user.
	BanAccount(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// API Explorer - call an endpoint
	CallApiEndpoint(ctx context.Context, in *CallApiEndpointRequest, opts ...grpc.CallOption) (*CallApiEndpointResponse, error)
	// API Explorer - call a custom RPC endpoint
	CallRpcEndpoint(ctx context.Context, in *CallApiEndpointRequest, opts ...grpc.CallOption) (*CallApiEndpointResponse, error)
	// Deletes all data
	DeleteAllData(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete all information stored for a user account.
	DeleteAccount(ctx context.Context, in *AccountDeleteRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete messages.
	DeleteChannelMessages(ctx context.Context, in *DeleteChannelMessagesRequest, opts ...grpc.CallOption) (*DeleteChannelMessagesResponse, error)
	// Delete the friend relationship between two users.
	DeleteFriend(ctx context.Context, in *DeleteFriendRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Remove a group.
	DeleteGroup(ctx context.Context, in *DeleteGroupRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Remove a user from a group.
	DeleteGroupUser(ctx context.Context, in *DeleteGroupUserRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete all storage data.
	DeleteStorage(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete a storage object.
	DeleteStorageObject(ctx context.Context, in *DeleteStorageObjectRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete (non-recorded) all user accounts.
	DeleteAccounts(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete leaderboard
	DeleteLeaderboard(ctx context.Context, in *LeaderboardRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete leaderboard record
	DeleteLeaderboardRecord(ctx context.Context, in *DeleteLeaderboardRecordRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete notification
	DeleteNotification(ctx context.Context, in *DeleteNotificationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete console user.
	DeleteUser(ctx context.Context, in *Username, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete a wallet ledger item.
	DeleteWalletLedger(ctx context.Context, in *DeleteWalletLedgerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Demote a user from a group.
	DemoteGroupMember(ctx context.Context, in *UpdateGroupUserStateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Export all information stored about a user account.
	ExportAccount(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*AccountExport, error)
	// Export all information stored about a group.
	ExportGroup(ctx context.Context, in *GroupId, opts ...grpc.CallOption) (*GroupExport, error)
	// Get detailed account information for a single user.
	GetAccount(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*Account, error)
	// Get server config and configuration warnings.
	GetConfig(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*Config, error)
	// Get a user's list of friend relationships.
	GetFriends(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*api.FriendList, error)
	// Get detailed group information.
	GetGroup(ctx context.Context, in *GroupId, opts ...grpc.CallOption) (*api.Group, error)
	// Get a list of members of the group.
	GetMembers(ctx context.Context, in *GroupId, opts ...grpc.CallOption) (*api.GroupUserList, error)
	// Get a list of groups the user is a member of.
	GetGroups(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*api.UserGroupList, error)
	// Get leaderboard.
	GetLeaderboard(ctx context.Context, in *LeaderboardRequest, opts ...grpc.CallOption) (*Leaderboard, error)
	// Get current state of a running match
	GetMatchState(ctx context.Context, in *MatchStateRequest, opts ...grpc.CallOption) (*MatchState, error)
	// Get runtime info
	GetRuntime(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*RuntimeInfo, error)
	// Get current status data for all nodes.
	GetStatus(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*StatusList, error)
	// Get a storage object.
	GetStorage(ctx context.Context, in *api.ReadStorageObjectId, opts ...grpc.CallOption) (*api.StorageObject, error)
	// Get a list of the user's wallet transactions.
	GetWalletLedger(ctx context.Context, in *GetWalletLedgerRequest, opts ...grpc.CallOption) (*WalletLedgerList, error)
	// Get a notification by id.
	GetNotification(ctx context.Context, in *GetNotificationRequest, opts ...grpc.CallOption) (*Notification, error)
	// Get purchase by transaction_id
	GetPurchase(ctx context.Context, in *GetPurchaseRequest, opts ...grpc.CallOption) (*api.ValidatedPurchase, error)
	// Get subscription by original_transaction_id
	GetSubscription(ctx context.Context, in *GetSubscriptionRequest, opts ...grpc.CallOption) (*api.ValidatedSubscription, error)
	// API Explorer - list all endpoints
	ListApiEndpoints(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ApiEndpointList, error)
	// List leaderboard records.
	ListLeaderboardRecords(ctx context.Context, in *api.ListLeaderboardRecordsRequest, opts ...grpc.CallOption) (*api.LeaderboardRecordList, error)
	// List leaderboards
	ListLeaderboards(ctx context.Context, in *LeaderboardListRequest, opts ...grpc.CallOption) (*LeaderboardList, error)
	// List (and optionally filter) storage data.
	ListStorage(ctx context.Context, in *ListStorageRequest, opts ...grpc.CallOption) (*StorageList, error)
	// List storage collections
	ListStorageCollections(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*StorageCollectionsList, error)
	// List (and optionally filter) accounts.
	ListAccounts(ctx context.Context, in *ListAccountsRequest, opts ...grpc.CallOption) (*AccountList, error)
	// List channel messages with the selected filter
	ListChannelMessages(ctx context.Context, in *ListChannelMessagesRequest, opts ...grpc.CallOption) (*api.ChannelMessageList, error)
	// List (and optionally filter) groups.
	ListGroups(ctx context.Context, in *ListGroupsRequest, opts ...grpc.CallOption) (*GroupList, error)
	// List notifications.
	ListNotifications(ctx context.Context, in *ListNotificationsRequest, opts ...grpc.CallOption) (*NotificationList, error)
	// List ongoing matches
	ListMatches(ctx context.Context, in *ListMatchesRequest, opts ...grpc.CallOption) (*MatchList, error)
	// List validated purchases
	ListPurchases(ctx context.Context, in *ListPurchasesRequest, opts ...grpc.CallOption) (*api.PurchaseList, error)
	// List validated subscriptions
	ListSubscriptions(ctx context.Context, in *ListSubscriptionsRequest, opts ...grpc.CallOption) (*api.SubscriptionList, error)
	// List (and optionally filter) users.
	ListUsers(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*UserList, error)
	// Promote a user from a group.
	PromoteGroupMember(ctx context.Context, in *UpdateGroupUserStateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Sets the user's MFA as required or not required.
	RequireUserMfa(ctx context.Context, in *RequireUserMfaRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Reset a user's multi-factor authentication credentials.
	ResetUserMfa(ctx context.Context, in *ResetUserMfaRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Unban a user.
	UnbanAccount(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Unlink the custom ID from a user account.
	UnlinkCustom(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Unlink the device ID from a user account.
	UnlinkDevice(ctx context.Context, in *UnlinkDeviceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Unlink the email from a user account.
	UnlinkEmail(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Unlink the Apple ID from a user account.
	UnlinkApple(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Unlink the Facebook ID from a user account.
	UnlinkFacebook(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Unlink the Facebook Instant Game ID from a user account.
	UnlinkFacebookInstantGame(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Unlink the Game Center ID from a user account.
	UnlinkGameCenter(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Unlink the Google ID from a user account.
	UnlinkGoogle(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Unlink the Steam ID from a user account.
	UnlinkSteam(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Update one or more fields on a user account.
	UpdateAccount(ctx context.Context, in *UpdateAccountRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Update one or more fields on a group.
	UpdateGroup(ctx context.Context, in *UpdateGroupRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Write a new storage object or replace an existing one.
	WriteStorageObject(ctx context.Context, in *WriteStorageObjectRequest, opts ...grpc.CallOption) (*api.StorageObjectAck, error)
}

type consoleClient struct {
	cc grpc.ClientConnInterface
}

func NewConsoleClient(cc grpc.ClientConnInterface) ConsoleClient {
	return &consoleClient{cc}
}

func (c *consoleClient) Authenticate(ctx context.Context, in *AuthenticateRequest, opts ...grpc.CallOption) (*ConsoleSession, error) {
	out := new(ConsoleSession)
	err := c.cc.Invoke(ctx, Console_Authenticate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) AuthenticateLogout(ctx context.Context, in *AuthenticateLogoutRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_AuthenticateLogout_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) AuthenticateMFASetup(ctx context.Context, in *AuthenticateMFASetupRequest, opts ...grpc.CallOption) (*AuthenticateMFASetupResponse, error) {
	out := new(AuthenticateMFASetupResponse)
	err := c.cc.Invoke(ctx, Console_AuthenticateMFASetup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) AddUser(ctx context.Context, in *AddUserRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_AddUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) AddGroupUsers(ctx context.Context, in *AddGroupUsersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_AddGroupUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) BanAccount(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_BanAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) CallApiEndpoint(ctx context.Context, in *CallApiEndpointRequest, opts ...grpc.CallOption) (*CallApiEndpointResponse, error) {
	out := new(CallApiEndpointResponse)
	err := c.cc.Invoke(ctx, Console_CallApiEndpoint_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) CallRpcEndpoint(ctx context.Context, in *CallApiEndpointRequest, opts ...grpc.CallOption) (*CallApiEndpointResponse, error) {
	out := new(CallApiEndpointResponse)
	err := c.cc.Invoke(ctx, Console_CallRpcEndpoint_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) DeleteAllData(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_DeleteAllData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) DeleteAccount(ctx context.Context, in *AccountDeleteRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_DeleteAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) DeleteChannelMessages(ctx context.Context, in *DeleteChannelMessagesRequest, opts ...grpc.CallOption) (*DeleteChannelMessagesResponse, error) {
	out := new(DeleteChannelMessagesResponse)
	err := c.cc.Invoke(ctx, Console_DeleteChannelMessages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) DeleteFriend(ctx context.Context, in *DeleteFriendRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_DeleteFriend_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) DeleteGroup(ctx context.Context, in *DeleteGroupRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_DeleteGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) DeleteGroupUser(ctx context.Context, in *DeleteGroupUserRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_DeleteGroupUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) DeleteStorage(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_DeleteStorage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) DeleteStorageObject(ctx context.Context, in *DeleteStorageObjectRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_DeleteStorageObject_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) DeleteAccounts(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_DeleteAccounts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) DeleteLeaderboard(ctx context.Context, in *LeaderboardRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_DeleteLeaderboard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) DeleteLeaderboardRecord(ctx context.Context, in *DeleteLeaderboardRecordRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_DeleteLeaderboardRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) DeleteNotification(ctx context.Context, in *DeleteNotificationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_DeleteNotification_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) DeleteUser(ctx context.Context, in *Username, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_DeleteUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) DeleteWalletLedger(ctx context.Context, in *DeleteWalletLedgerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_DeleteWalletLedger_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) DemoteGroupMember(ctx context.Context, in *UpdateGroupUserStateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_DemoteGroupMember_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) ExportAccount(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*AccountExport, error) {
	out := new(AccountExport)
	err := c.cc.Invoke(ctx, Console_ExportAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) ExportGroup(ctx context.Context, in *GroupId, opts ...grpc.CallOption) (*GroupExport, error) {
	out := new(GroupExport)
	err := c.cc.Invoke(ctx, Console_ExportGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) GetAccount(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*Account, error) {
	out := new(Account)
	err := c.cc.Invoke(ctx, Console_GetAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) GetConfig(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*Config, error) {
	out := new(Config)
	err := c.cc.Invoke(ctx, Console_GetConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) GetFriends(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*api.FriendList, error) {
	out := new(api.FriendList)
	err := c.cc.Invoke(ctx, Console_GetFriends_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) GetGroup(ctx context.Context, in *GroupId, opts ...grpc.CallOption) (*api.Group, error) {
	out := new(api.Group)
	err := c.cc.Invoke(ctx, Console_GetGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) GetMembers(ctx context.Context, in *GroupId, opts ...grpc.CallOption) (*api.GroupUserList, error) {
	out := new(api.GroupUserList)
	err := c.cc.Invoke(ctx, Console_GetMembers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) GetGroups(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*api.UserGroupList, error) {
	out := new(api.UserGroupList)
	err := c.cc.Invoke(ctx, Console_GetGroups_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) GetLeaderboard(ctx context.Context, in *LeaderboardRequest, opts ...grpc.CallOption) (*Leaderboard, error) {
	out := new(Leaderboard)
	err := c.cc.Invoke(ctx, Console_GetLeaderboard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) GetMatchState(ctx context.Context, in *MatchStateRequest, opts ...grpc.CallOption) (*MatchState, error) {
	out := new(MatchState)
	err := c.cc.Invoke(ctx, Console_GetMatchState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) GetRuntime(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*RuntimeInfo, error) {
	out := new(RuntimeInfo)
	err := c.cc.Invoke(ctx, Console_GetRuntime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) GetStatus(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*StatusList, error) {
	out := new(StatusList)
	err := c.cc.Invoke(ctx, Console_GetStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) GetStorage(ctx context.Context, in *api.ReadStorageObjectId, opts ...grpc.CallOption) (*api.StorageObject, error) {
	out := new(api.StorageObject)
	err := c.cc.Invoke(ctx, Console_GetStorage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) GetWalletLedger(ctx context.Context, in *GetWalletLedgerRequest, opts ...grpc.CallOption) (*WalletLedgerList, error) {
	out := new(WalletLedgerList)
	err := c.cc.Invoke(ctx, Console_GetWalletLedger_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) GetNotification(ctx context.Context, in *GetNotificationRequest, opts ...grpc.CallOption) (*Notification, error) {
	out := new(Notification)
	err := c.cc.Invoke(ctx, Console_GetNotification_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) GetPurchase(ctx context.Context, in *GetPurchaseRequest, opts ...grpc.CallOption) (*api.ValidatedPurchase, error) {
	out := new(api.ValidatedPurchase)
	err := c.cc.Invoke(ctx, Console_GetPurchase_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) GetSubscription(ctx context.Context, in *GetSubscriptionRequest, opts ...grpc.CallOption) (*api.ValidatedSubscription, error) {
	out := new(api.ValidatedSubscription)
	err := c.cc.Invoke(ctx, Console_GetSubscription_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) ListApiEndpoints(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ApiEndpointList, error) {
	out := new(ApiEndpointList)
	err := c.cc.Invoke(ctx, Console_ListApiEndpoints_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) ListLeaderboardRecords(ctx context.Context, in *api.ListLeaderboardRecordsRequest, opts ...grpc.CallOption) (*api.LeaderboardRecordList, error) {
	out := new(api.LeaderboardRecordList)
	err := c.cc.Invoke(ctx, Console_ListLeaderboardRecords_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) ListLeaderboards(ctx context.Context, in *LeaderboardListRequest, opts ...grpc.CallOption) (*LeaderboardList, error) {
	out := new(LeaderboardList)
	err := c.cc.Invoke(ctx, Console_ListLeaderboards_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) ListStorage(ctx context.Context, in *ListStorageRequest, opts ...grpc.CallOption) (*StorageList, error) {
	out := new(StorageList)
	err := c.cc.Invoke(ctx, Console_ListStorage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) ListStorageCollections(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*StorageCollectionsList, error) {
	out := new(StorageCollectionsList)
	err := c.cc.Invoke(ctx, Console_ListStorageCollections_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) ListAccounts(ctx context.Context, in *ListAccountsRequest, opts ...grpc.CallOption) (*AccountList, error) {
	out := new(AccountList)
	err := c.cc.Invoke(ctx, Console_ListAccounts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) ListChannelMessages(ctx context.Context, in *ListChannelMessagesRequest, opts ...grpc.CallOption) (*api.ChannelMessageList, error) {
	out := new(api.ChannelMessageList)
	err := c.cc.Invoke(ctx, Console_ListChannelMessages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) ListGroups(ctx context.Context, in *ListGroupsRequest, opts ...grpc.CallOption) (*GroupList, error) {
	out := new(GroupList)
	err := c.cc.Invoke(ctx, Console_ListGroups_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) ListNotifications(ctx context.Context, in *ListNotificationsRequest, opts ...grpc.CallOption) (*NotificationList, error) {
	out := new(NotificationList)
	err := c.cc.Invoke(ctx, Console_ListNotifications_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) ListMatches(ctx context.Context, in *ListMatchesRequest, opts ...grpc.CallOption) (*MatchList, error) {
	out := new(MatchList)
	err := c.cc.Invoke(ctx, Console_ListMatches_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) ListPurchases(ctx context.Context, in *ListPurchasesRequest, opts ...grpc.CallOption) (*api.PurchaseList, error) {
	out := new(api.PurchaseList)
	err := c.cc.Invoke(ctx, Console_ListPurchases_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) ListSubscriptions(ctx context.Context, in *ListSubscriptionsRequest, opts ...grpc.CallOption) (*api.SubscriptionList, error) {
	out := new(api.SubscriptionList)
	err := c.cc.Invoke(ctx, Console_ListSubscriptions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) ListUsers(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*UserList, error) {
	out := new(UserList)
	err := c.cc.Invoke(ctx, Console_ListUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) PromoteGroupMember(ctx context.Context, in *UpdateGroupUserStateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_PromoteGroupMember_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) RequireUserMfa(ctx context.Context, in *RequireUserMfaRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_RequireUserMfa_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) ResetUserMfa(ctx context.Context, in *ResetUserMfaRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_ResetUserMfa_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) UnbanAccount(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_UnbanAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) UnlinkCustom(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_UnlinkCustom_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) UnlinkDevice(ctx context.Context, in *UnlinkDeviceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_UnlinkDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) UnlinkEmail(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_UnlinkEmail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) UnlinkApple(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_UnlinkApple_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) UnlinkFacebook(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_UnlinkFacebook_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) UnlinkFacebookInstantGame(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_UnlinkFacebookInstantGame_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) UnlinkGameCenter(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_UnlinkGameCenter_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) UnlinkGoogle(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_UnlinkGoogle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) UnlinkSteam(ctx context.Context, in *AccountId, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_UnlinkSteam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) UpdateAccount(ctx context.Context, in *UpdateAccountRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_UpdateAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) UpdateGroup(ctx context.Context, in *UpdateGroupRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Console_UpdateGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consoleClient) WriteStorageObject(ctx context.Context, in *WriteStorageObjectRequest, opts ...grpc.CallOption) (*api.StorageObjectAck, error) {
	out := new(api.StorageObjectAck)
	err := c.cc.Invoke(ctx, Console_WriteStorageObject_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConsoleServer is the server API for Console service.
// All implementations must embed UnimplementedConsoleServer
// for forward compatibility
type ConsoleServer interface {
	// Authenticate a console user with username and password.
	Authenticate(context.Context, *AuthenticateRequest) (*ConsoleSession, error)
	// Log out a session and invalidate the session token.
	AuthenticateLogout(context.Context, *AuthenticateLogoutRequest) (*emptypb.Empty, error)
	// Change an account's MFA using a code, usually delivered over email.
	AuthenticateMFASetup(context.Context, *AuthenticateMFASetupRequest) (*AuthenticateMFASetupResponse, error)
	// Add a new console user.
	AddUser(context.Context, *AddUserRequest) (*emptypb.Empty, error)
	// Add/join members to a group.
	AddGroupUsers(context.Context, *AddGroupUsersRequest) (*emptypb.Empty, error)
	// Ban a user.
	BanAccount(context.Context, *AccountId) (*emptypb.Empty, error)
	// API Explorer - call an endpoint
	CallApiEndpoint(context.Context, *CallApiEndpointRequest) (*CallApiEndpointResponse, error)
	// API Explorer - call a custom RPC endpoint
	CallRpcEndpoint(context.Context, *CallApiEndpointRequest) (*CallApiEndpointResponse, error)
	// Deletes all data
	DeleteAllData(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// Delete all information stored for a user account.
	DeleteAccount(context.Context, *AccountDeleteRequest) (*emptypb.Empty, error)
	// Delete messages.
	DeleteChannelMessages(context.Context, *DeleteChannelMessagesRequest) (*DeleteChannelMessagesResponse, error)
	// Delete the friend relationship between two users.
	DeleteFriend(context.Context, *DeleteFriendRequest) (*emptypb.Empty, error)
	// Remove a group.
	DeleteGroup(context.Context, *DeleteGroupRequest) (*emptypb.Empty, error)
	// Remove a user from a group.
	DeleteGroupUser(context.Context, *DeleteGroupUserRequest) (*emptypb.Empty, error)
	// Delete all storage data.
	DeleteStorage(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// Delete a storage object.
	DeleteStorageObject(context.Context, *DeleteStorageObjectRequest) (*emptypb.Empty, error)
	// Delete (non-recorded) all user accounts.
	DeleteAccounts(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// Delete leaderboard
	DeleteLeaderboard(context.Context, *LeaderboardRequest) (*emptypb.Empty, error)
	// Delete leaderboard record
	DeleteLeaderboardRecord(context.Context, *DeleteLeaderboardRecordRequest) (*emptypb.Empty, error)
	// Delete notification
	DeleteNotification(context.Context, *DeleteNotificationRequest) (*emptypb.Empty, error)
	// Delete console user.
	DeleteUser(context.Context, *Username) (*emptypb.Empty, error)
	// Delete a wallet ledger item.
	DeleteWalletLedger(context.Context, *DeleteWalletLedgerRequest) (*emptypb.Empty, error)
	// Demote a user from a group.
	DemoteGroupMember(context.Context, *UpdateGroupUserStateRequest) (*emptypb.Empty, error)
	// Export all information stored about a user account.
	ExportAccount(context.Context, *AccountId) (*AccountExport, error)
	// Export all information stored about a group.
	ExportGroup(context.Context, *GroupId) (*GroupExport, error)
	// Get detailed account information for a single user.
	GetAccount(context.Context, *AccountId) (*Account, error)
	// Get server config and configuration warnings.
	GetConfig(context.Context, *emptypb.Empty) (*Config, error)
	// Get a user's list of friend relationships.
	GetFriends(context.Context, *AccountId) (*api.FriendList, error)
	// Get detailed group information.
	GetGroup(context.Context, *GroupId) (*api.Group, error)
	// Get a list of members of the group.
	GetMembers(context.Context, *GroupId) (*api.GroupUserList, error)
	// Get a list of groups the user is a member of.
	GetGroups(context.Context, *AccountId) (*api.UserGroupList, error)
	// Get leaderboard.
	GetLeaderboard(context.Context, *LeaderboardRequest) (*Leaderboard, error)
	// Get current state of a running match
	GetMatchState(context.Context, *MatchStateRequest) (*MatchState, error)
	// Get runtime info
	GetRuntime(context.Context, *emptypb.Empty) (*RuntimeInfo, error)
	// Get current status data for all nodes.
	GetStatus(context.Context, *emptypb.Empty) (*StatusList, error)
	// Get a storage object.
	GetStorage(context.Context, *api.ReadStorageObjectId) (*api.StorageObject, error)
	// Get a list of the user's wallet transactions.
	GetWalletLedger(context.Context, *GetWalletLedgerRequest) (*WalletLedgerList, error)
	// Get a notification by id.
	GetNotification(context.Context, *GetNotificationRequest) (*Notification, error)
	// Get purchase by transaction_id
	GetPurchase(context.Context, *GetPurchaseRequest) (*api.ValidatedPurchase, error)
	// Get subscription by original_transaction_id
	GetSubscription(context.Context, *GetSubscriptionRequest) (*api.ValidatedSubscription, error)
	// API Explorer - list all endpoints
	ListApiEndpoints(context.Context, *emptypb.Empty) (*ApiEndpointList, error)
	// List leaderboard records.
	ListLeaderboardRecords(context.Context, *api.ListLeaderboardRecordsRequest) (*api.LeaderboardRecordList, error)
	// List leaderboards
	ListLeaderboards(context.Context, *LeaderboardListRequest) (*LeaderboardList, error)
	// List (and optionally filter) storage data.
	ListStorage(context.Context, *ListStorageRequest) (*StorageList, error)
	// List storage collections
	ListStorageCollections(context.Context, *emptypb.Empty) (*StorageCollectionsList, error)
	// List (and optionally filter) accounts.
	ListAccounts(context.Context, *ListAccountsRequest) (*AccountList, error)
	// List channel messages with the selected filter
	ListChannelMessages(context.Context, *ListChannelMessagesRequest) (*api.ChannelMessageList, error)
	// List (and optionally filter) groups.
	ListGroups(context.Context, *ListGroupsRequest) (*GroupList, error)
	// List notifications.
	ListNotifications(context.Context, *ListNotificationsRequest) (*NotificationList, error)
	// List ongoing matches
	ListMatches(context.Context, *ListMatchesRequest) (*MatchList, error)
	// List validated purchases
	ListPurchases(context.Context, *ListPurchasesRequest) (*api.PurchaseList, error)
	// List validated subscriptions
	ListSubscriptions(context.Context, *ListSubscriptionsRequest) (*api.SubscriptionList, error)
	// List (and optionally filter) users.
	ListUsers(context.Context, *emptypb.Empty) (*UserList, error)
	// Promote a user from a group.
	PromoteGroupMember(context.Context, *UpdateGroupUserStateRequest) (*emptypb.Empty, error)
	// Sets the user's MFA as required or not required.
	RequireUserMfa(context.Context, *RequireUserMfaRequest) (*emptypb.Empty, error)
	// Reset a user's multi-factor authentication credentials.
	ResetUserMfa(context.Context, *ResetUserMfaRequest) (*emptypb.Empty, error)
	// Unban a user.
	UnbanAccount(context.Context, *AccountId) (*emptypb.Empty, error)
	// Unlink the custom ID from a user account.
	UnlinkCustom(context.Context, *AccountId) (*emptypb.Empty, error)
	// Unlink the device ID from a user account.
	UnlinkDevice(context.Context, *UnlinkDeviceRequest) (*emptypb.Empty, error)
	// Unlink the email from a user account.
	UnlinkEmail(context.Context, *AccountId) (*emptypb.Empty, error)
	// Unlink the Apple ID from a user account.
	UnlinkApple(context.Context, *AccountId) (*emptypb.Empty, error)
	// Unlink the Facebook ID from a user account.
	UnlinkFacebook(context.Context, *AccountId) (*emptypb.Empty, error)
	// Unlink the Facebook Instant Game ID from a user account.
	UnlinkFacebookInstantGame(context.Context, *AccountId) (*emptypb.Empty, error)
	// Unlink the Game Center ID from a user account.
	UnlinkGameCenter(context.Context, *AccountId) (*emptypb.Empty, error)
	// Unlink the Google ID from a user account.
	UnlinkGoogle(context.Context, *AccountId) (*emptypb.Empty, error)
	// Unlink the Steam ID from a user account.
	UnlinkSteam(context.Context, *AccountId) (*emptypb.Empty, error)
	// Update one or more fields on a user account.
	UpdateAccount(context.Context, *UpdateAccountRequest) (*emptypb.Empty, error)
	// Update one or more fields on a group.
	UpdateGroup(context.Context, *UpdateGroupRequest) (*emptypb.Empty, error)
	// Write a new storage object or replace an existing one.
	WriteStorageObject(context.Context, *WriteStorageObjectRequest) (*api.StorageObjectAck, error)
	mustEmbedUnimplementedConsoleServer()
}

// UnimplementedConsoleServer must be embedded to have forward compatible implementations.
type UnimplementedConsoleServer struct {
}

func (UnimplementedConsoleServer) Authenticate(context.Context, *AuthenticateRequest) (*ConsoleSession, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Authenticate not implemented")
}
func (UnimplementedConsoleServer) AuthenticateLogout(context.Context, *AuthenticateLogoutRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthenticateLogout not implemented")
}
func (UnimplementedConsoleServer) AuthenticateMFASetup(context.Context, *AuthenticateMFASetupRequest) (*AuthenticateMFASetupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthenticateMFASetup not implemented")
}
func (UnimplementedConsoleServer) AddUser(context.Context, *AddUserRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddUser not implemented")
}
func (UnimplementedConsoleServer) AddGroupUsers(context.Context, *AddGroupUsersRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddGroupUsers not implemented")
}
func (UnimplementedConsoleServer) BanAccount(context.Context, *AccountId) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BanAccount not implemented")
}
func (UnimplementedConsoleServer) CallApiEndpoint(context.Context, *CallApiEndpointRequest) (*CallApiEndpointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CallApiEndpoint not implemented")
}
func (UnimplementedConsoleServer) CallRpcEndpoint(context.Context, *CallApiEndpointRequest) (*CallApiEndpointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CallRpcEndpoint not implemented")
}
func (UnimplementedConsoleServer) DeleteAllData(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAllData not implemented")
}
func (UnimplementedConsoleServer) DeleteAccount(context.Context, *AccountDeleteRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAccount not implemented")
}
func (UnimplementedConsoleServer) DeleteChannelMessages(context.Context, *DeleteChannelMessagesRequest) (*DeleteChannelMessagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteChannelMessages not implemented")
}
func (UnimplementedConsoleServer) DeleteFriend(context.Context, *DeleteFriendRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteFriend not implemented")
}
func (UnimplementedConsoleServer) DeleteGroup(context.Context, *DeleteGroupRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGroup not implemented")
}
func (UnimplementedConsoleServer) DeleteGroupUser(context.Context, *DeleteGroupUserRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGroupUser not implemented")
}
func (UnimplementedConsoleServer) DeleteStorage(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteStorage not implemented")
}
func (UnimplementedConsoleServer) DeleteStorageObject(context.Context, *DeleteStorageObjectRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteStorageObject not implemented")
}
func (UnimplementedConsoleServer) DeleteAccounts(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAccounts not implemented")
}
func (UnimplementedConsoleServer) DeleteLeaderboard(context.Context, *LeaderboardRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLeaderboard not implemented")
}
func (UnimplementedConsoleServer) DeleteLeaderboardRecord(context.Context, *DeleteLeaderboardRecordRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLeaderboardRecord not implemented")
}
func (UnimplementedConsoleServer) DeleteNotification(context.Context, *DeleteNotificationRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNotification not implemented")
}
func (UnimplementedConsoleServer) DeleteUser(context.Context, *Username) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUser not implemented")
}
func (UnimplementedConsoleServer) DeleteWalletLedger(context.Context, *DeleteWalletLedgerRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWalletLedger not implemented")
}
func (UnimplementedConsoleServer) DemoteGroupMember(context.Context, *UpdateGroupUserStateRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DemoteGroupMember not implemented")
}
func (UnimplementedConsoleServer) ExportAccount(context.Context, *AccountId) (*AccountExport, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportAccount not implemented")
}
func (UnimplementedConsoleServer) ExportGroup(context.Context, *GroupId) (*GroupExport, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportGroup not implemented")
}
func (UnimplementedConsoleServer) GetAccount(context.Context, *AccountId) (*Account, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccount not implemented")
}
func (UnimplementedConsoleServer) GetConfig(context.Context, *emptypb.Empty) (*Config, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConfig not implemented")
}
func (UnimplementedConsoleServer) GetFriends(context.Context, *AccountId) (*api.FriendList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFriends not implemented")
}
func (UnimplementedConsoleServer) GetGroup(context.Context, *GroupId) (*api.Group, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroup not implemented")
}
func (UnimplementedConsoleServer) GetMembers(context.Context, *GroupId) (*api.GroupUserList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMembers not implemented")
}
func (UnimplementedConsoleServer) GetGroups(context.Context, *AccountId) (*api.UserGroupList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroups not implemented")
}
func (UnimplementedConsoleServer) GetLeaderboard(context.Context, *LeaderboardRequest) (*Leaderboard, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLeaderboard not implemented")
}
func (UnimplementedConsoleServer) GetMatchState(context.Context, *MatchStateRequest) (*MatchState, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMatchState not implemented")
}
func (UnimplementedConsoleServer) GetRuntime(context.Context, *emptypb.Empty) (*RuntimeInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRuntime not implemented")
}
func (UnimplementedConsoleServer) GetStatus(context.Context, *emptypb.Empty) (*StatusList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStatus not implemented")
}
func (UnimplementedConsoleServer) GetStorage(context.Context, *api.ReadStorageObjectId) (*api.StorageObject, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStorage not implemented")
}
func (UnimplementedConsoleServer) GetWalletLedger(context.Context, *GetWalletLedgerRequest) (*WalletLedgerList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWalletLedger not implemented")
}
func (UnimplementedConsoleServer) GetNotification(context.Context, *GetNotificationRequest) (*Notification, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNotification not implemented")
}
func (UnimplementedConsoleServer) GetPurchase(context.Context, *GetPurchaseRequest) (*api.ValidatedPurchase, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPurchase not implemented")
}
func (UnimplementedConsoleServer) GetSubscription(context.Context, *GetSubscriptionRequest) (*api.ValidatedSubscription, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSubscription not implemented")
}
func (UnimplementedConsoleServer) ListApiEndpoints(context.Context, *emptypb.Empty) (*ApiEndpointList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListApiEndpoints not implemented")
}
func (UnimplementedConsoleServer) ListLeaderboardRecords(context.Context, *api.ListLeaderboardRecordsRequest) (*api.LeaderboardRecordList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLeaderboardRecords not implemented")
}
func (UnimplementedConsoleServer) ListLeaderboards(context.Context, *LeaderboardListRequest) (*LeaderboardList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLeaderboards not implemented")
}
func (UnimplementedConsoleServer) ListStorage(context.Context, *ListStorageRequest) (*StorageList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStorage not implemented")
}
func (UnimplementedConsoleServer) ListStorageCollections(context.Context, *emptypb.Empty) (*StorageCollectionsList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStorageCollections not implemented")
}
func (UnimplementedConsoleServer) ListAccounts(context.Context, *ListAccountsRequest) (*AccountList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAccounts not implemented")
}
func (UnimplementedConsoleServer) ListChannelMessages(context.Context, *ListChannelMessagesRequest) (*api.ChannelMessageList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListChannelMessages not implemented")
}
func (UnimplementedConsoleServer) ListGroups(context.Context, *ListGroupsRequest) (*GroupList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGroups not implemented")
}
func (UnimplementedConsoleServer) ListNotifications(context.Context, *ListNotificationsRequest) (*NotificationList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNotifications not implemented")
}
func (UnimplementedConsoleServer) ListMatches(context.Context, *ListMatchesRequest) (*MatchList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMatches not implemented")
}
func (UnimplementedConsoleServer) ListPurchases(context.Context, *ListPurchasesRequest) (*api.PurchaseList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPurchases not implemented")
}
func (UnimplementedConsoleServer) ListSubscriptions(context.Context, *ListSubscriptionsRequest) (*api.SubscriptionList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSubscriptions not implemented")
}
func (UnimplementedConsoleServer) ListUsers(context.Context, *emptypb.Empty) (*UserList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUsers not implemented")
}
func (UnimplementedConsoleServer) PromoteGroupMember(context.Context, *UpdateGroupUserStateRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PromoteGroupMember not implemented")
}
func (UnimplementedConsoleServer) RequireUserMfa(context.Context, *RequireUserMfaRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RequireUserMfa not implemented")
}
func (UnimplementedConsoleServer) ResetUserMfa(context.Context, *ResetUserMfaRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetUserMfa not implemented")
}
func (UnimplementedConsoleServer) UnbanAccount(context.Context, *AccountId) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnbanAccount not implemented")
}
func (UnimplementedConsoleServer) UnlinkCustom(context.Context, *AccountId) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkCustom not implemented")
}
func (UnimplementedConsoleServer) UnlinkDevice(context.Context, *UnlinkDeviceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkDevice not implemented")
}
func (UnimplementedConsoleServer) UnlinkEmail(context.Context, *AccountId) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkEmail not implemented")
}
func (UnimplementedConsoleServer) UnlinkApple(context.Context, *AccountId) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkApple not implemented")
}
func (UnimplementedConsoleServer) UnlinkFacebook(context.Context, *AccountId) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkFacebook not implemented")
}
func (UnimplementedConsoleServer) UnlinkFacebookInstantGame(context.Context, *AccountId) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkFacebookInstantGame not implemented")
}
func (UnimplementedConsoleServer) UnlinkGameCenter(context.Context, *AccountId) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkGameCenter not implemented")
}
func (UnimplementedConsoleServer) UnlinkGoogle(context.Context, *AccountId) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkGoogle not implemented")
}
func (UnimplementedConsoleServer) UnlinkSteam(context.Context, *AccountId) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkSteam not implemented")
}
func (UnimplementedConsoleServer) UpdateAccount(context.Context, *UpdateAccountRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAccount not implemented")
}
func (UnimplementedConsoleServer) UpdateGroup(context.Context, *UpdateGroupRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGroup not implemented")
}
func (UnimplementedConsoleServer) WriteStorageObject(context.Context, *WriteStorageObjectRequest) (*api.StorageObjectAck, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WriteStorageObject not implemented")
}
func (UnimplementedConsoleServer) mustEmbedUnimplementedConsoleServer() {}

// UnsafeConsoleServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConsoleServer will
// result in compilation errors.
type UnsafeConsoleServer interface {
	mustEmbedUnimplementedConsoleServer()
}

func RegisterConsoleServer(s grpc.ServiceRegistrar, srv ConsoleServer) {
	s.RegisterService(&Console_ServiceDesc, srv)
}

func _Console_Authenticate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthenticateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).Authenticate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_Authenticate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).Authenticate(ctx, req.(*AuthenticateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_AuthenticateLogout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthenticateLogoutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).AuthenticateLogout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_AuthenticateLogout_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).AuthenticateLogout(ctx, req.(*AuthenticateLogoutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_AuthenticateMFASetup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthenticateMFASetupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).AuthenticateMFASetup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_AuthenticateMFASetup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).AuthenticateMFASetup(ctx, req.(*AuthenticateMFASetupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_AddUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).AddUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_AddUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).AddUser(ctx, req.(*AddUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_AddGroupUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddGroupUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).AddGroupUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_AddGroupUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).AddGroupUsers(ctx, req.(*AddGroupUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_BanAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).BanAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_BanAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).BanAccount(ctx, req.(*AccountId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_CallApiEndpoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CallApiEndpointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).CallApiEndpoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_CallApiEndpoint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).CallApiEndpoint(ctx, req.(*CallApiEndpointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_CallRpcEndpoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CallApiEndpointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).CallRpcEndpoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_CallRpcEndpoint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).CallRpcEndpoint(ctx, req.(*CallApiEndpointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_DeleteAllData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).DeleteAllData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_DeleteAllData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).DeleteAllData(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_DeleteAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).DeleteAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_DeleteAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).DeleteAccount(ctx, req.(*AccountDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_DeleteChannelMessages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteChannelMessagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).DeleteChannelMessages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_DeleteChannelMessages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).DeleteChannelMessages(ctx, req.(*DeleteChannelMessagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_DeleteFriend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteFriendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).DeleteFriend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_DeleteFriend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).DeleteFriend(ctx, req.(*DeleteFriendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_DeleteGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).DeleteGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_DeleteGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).DeleteGroup(ctx, req.(*DeleteGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_DeleteGroupUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGroupUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).DeleteGroupUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_DeleteGroupUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).DeleteGroupUser(ctx, req.(*DeleteGroupUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_DeleteStorage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).DeleteStorage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_DeleteStorage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).DeleteStorage(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_DeleteStorageObject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteStorageObjectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).DeleteStorageObject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_DeleteStorageObject_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).DeleteStorageObject(ctx, req.(*DeleteStorageObjectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_DeleteAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).DeleteAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_DeleteAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).DeleteAccounts(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_DeleteLeaderboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LeaderboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).DeleteLeaderboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_DeleteLeaderboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).DeleteLeaderboard(ctx, req.(*LeaderboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_DeleteLeaderboardRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLeaderboardRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).DeleteLeaderboardRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_DeleteLeaderboardRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).DeleteLeaderboardRecord(ctx, req.(*DeleteLeaderboardRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_DeleteNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).DeleteNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_DeleteNotification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).DeleteNotification(ctx, req.(*DeleteNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_DeleteUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Username)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).DeleteUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_DeleteUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).DeleteUser(ctx, req.(*Username))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_DeleteWalletLedger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteWalletLedgerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).DeleteWalletLedger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_DeleteWalletLedger_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).DeleteWalletLedger(ctx, req.(*DeleteWalletLedgerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_DemoteGroupMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroupUserStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).DemoteGroupMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_DemoteGroupMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).DemoteGroupMember(ctx, req.(*UpdateGroupUserStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_ExportAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).ExportAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_ExportAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).ExportAccount(ctx, req.(*AccountId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_ExportGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).ExportGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_ExportGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).ExportGroup(ctx, req.(*GroupId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_GetAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).GetAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_GetAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).GetAccount(ctx, req.(*AccountId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_GetConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).GetConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_GetConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).GetConfig(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_GetFriends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).GetFriends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_GetFriends_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).GetFriends(ctx, req.(*AccountId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_GetGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).GetGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_GetGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).GetGroup(ctx, req.(*GroupId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_GetMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).GetMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_GetMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).GetMembers(ctx, req.(*GroupId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_GetGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).GetGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_GetGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).GetGroups(ctx, req.(*AccountId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_GetLeaderboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LeaderboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).GetLeaderboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_GetLeaderboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).GetLeaderboard(ctx, req.(*LeaderboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_GetMatchState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MatchStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).GetMatchState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_GetMatchState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).GetMatchState(ctx, req.(*MatchStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_GetRuntime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).GetRuntime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_GetRuntime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).GetRuntime(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_GetStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).GetStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_GetStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).GetStatus(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_GetStorage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ReadStorageObjectId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).GetStorage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_GetStorage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).GetStorage(ctx, req.(*api.ReadStorageObjectId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_GetWalletLedger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWalletLedgerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).GetWalletLedger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_GetWalletLedger_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).GetWalletLedger(ctx, req.(*GetWalletLedgerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_GetNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).GetNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_GetNotification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).GetNotification(ctx, req.(*GetNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_GetPurchase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPurchaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).GetPurchase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_GetPurchase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).GetPurchase(ctx, req.(*GetPurchaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_GetSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).GetSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_GetSubscription_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).GetSubscription(ctx, req.(*GetSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_ListApiEndpoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).ListApiEndpoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_ListApiEndpoints_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).ListApiEndpoints(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_ListLeaderboardRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ListLeaderboardRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).ListLeaderboardRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_ListLeaderboardRecords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).ListLeaderboardRecords(ctx, req.(*api.ListLeaderboardRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_ListLeaderboards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LeaderboardListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).ListLeaderboards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_ListLeaderboards_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).ListLeaderboards(ctx, req.(*LeaderboardListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_ListStorage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListStorageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).ListStorage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_ListStorage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).ListStorage(ctx, req.(*ListStorageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_ListStorageCollections_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).ListStorageCollections(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_ListStorageCollections_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).ListStorageCollections(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_ListAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).ListAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_ListAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).ListAccounts(ctx, req.(*ListAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_ListChannelMessages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListChannelMessagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).ListChannelMessages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_ListChannelMessages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).ListChannelMessages(ctx, req.(*ListChannelMessagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_ListGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).ListGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_ListGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).ListGroups(ctx, req.(*ListGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_ListNotifications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNotificationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).ListNotifications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_ListNotifications_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).ListNotifications(ctx, req.(*ListNotificationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_ListMatches_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMatchesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).ListMatches(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_ListMatches_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).ListMatches(ctx, req.(*ListMatchesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_ListPurchases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPurchasesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).ListPurchases(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_ListPurchases_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).ListPurchases(ctx, req.(*ListPurchasesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_ListSubscriptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSubscriptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).ListSubscriptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_ListSubscriptions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).ListSubscriptions(ctx, req.(*ListSubscriptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_ListUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).ListUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_ListUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).ListUsers(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_PromoteGroupMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroupUserStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).PromoteGroupMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_PromoteGroupMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).PromoteGroupMember(ctx, req.(*UpdateGroupUserStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_RequireUserMfa_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequireUserMfaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).RequireUserMfa(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_RequireUserMfa_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).RequireUserMfa(ctx, req.(*RequireUserMfaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_ResetUserMfa_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetUserMfaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).ResetUserMfa(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_ResetUserMfa_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).ResetUserMfa(ctx, req.(*ResetUserMfaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_UnbanAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).UnbanAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_UnbanAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).UnbanAccount(ctx, req.(*AccountId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_UnlinkCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).UnlinkCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_UnlinkCustom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).UnlinkCustom(ctx, req.(*AccountId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_UnlinkDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnlinkDeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).UnlinkDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_UnlinkDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).UnlinkDevice(ctx, req.(*UnlinkDeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_UnlinkEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).UnlinkEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_UnlinkEmail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).UnlinkEmail(ctx, req.(*AccountId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_UnlinkApple_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).UnlinkApple(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_UnlinkApple_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).UnlinkApple(ctx, req.(*AccountId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_UnlinkFacebook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).UnlinkFacebook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_UnlinkFacebook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).UnlinkFacebook(ctx, req.(*AccountId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_UnlinkFacebookInstantGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).UnlinkFacebookInstantGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_UnlinkFacebookInstantGame_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).UnlinkFacebookInstantGame(ctx, req.(*AccountId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_UnlinkGameCenter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).UnlinkGameCenter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_UnlinkGameCenter_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).UnlinkGameCenter(ctx, req.(*AccountId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_UnlinkGoogle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).UnlinkGoogle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_UnlinkGoogle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).UnlinkGoogle(ctx, req.(*AccountId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_UnlinkSteam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).UnlinkSteam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_UnlinkSteam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).UnlinkSteam(ctx, req.(*AccountId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_UpdateAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).UpdateAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_UpdateAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).UpdateAccount(ctx, req.(*UpdateAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_UpdateGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).UpdateGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_UpdateGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).UpdateGroup(ctx, req.(*UpdateGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Console_WriteStorageObject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteStorageObjectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsoleServer).WriteStorageObject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Console_WriteStorageObject_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsoleServer).WriteStorageObject(ctx, req.(*WriteStorageObjectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Console_ServiceDesc is the grpc.ServiceDesc for Console service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Console_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "nakama.console.Console",
	HandlerType: (*ConsoleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Authenticate",
			Handler:    _Console_Authenticate_Handler,
		},
		{
			MethodName: "AuthenticateLogout",
			Handler:    _Console_AuthenticateLogout_Handler,
		},
		{
			MethodName: "AuthenticateMFASetup",
			Handler:    _Console_AuthenticateMFASetup_Handler,
		},
		{
			MethodName: "AddUser",
			Handler:    _Console_AddUser_Handler,
		},
		{
			MethodName: "AddGroupUsers",
			Handler:    _Console_AddGroupUsers_Handler,
		},
		{
			MethodName: "BanAccount",
			Handler:    _Console_BanAccount_Handler,
		},
		{
			MethodName: "CallApiEndpoint",
			Handler:    _Console_CallApiEndpoint_Handler,
		},
		{
			MethodName: "CallRpcEndpoint",
			Handler:    _Console_CallRpcEndpoint_Handler,
		},
		{
			MethodName: "DeleteAllData",
			Handler:    _Console_DeleteAllData_Handler,
		},
		{
			MethodName: "DeleteAccount",
			Handler:    _Console_DeleteAccount_Handler,
		},
		{
			MethodName: "DeleteChannelMessages",
			Handler:    _Console_DeleteChannelMessages_Handler,
		},
		{
			MethodName: "DeleteFriend",
			Handler:    _Console_DeleteFriend_Handler,
		},
		{
			MethodName: "DeleteGroup",
			Handler:    _Console_DeleteGroup_Handler,
		},
		{
			MethodName: "DeleteGroupUser",
			Handler:    _Console_DeleteGroupUser_Handler,
		},
		{
			MethodName: "DeleteStorage",
			Handler:    _Console_DeleteStorage_Handler,
		},
		{
			MethodName: "DeleteStorageObject",
			Handler:    _Console_DeleteStorageObject_Handler,
		},
		{
			MethodName: "DeleteAccounts",
			Handler:    _Console_DeleteAccounts_Handler,
		},
		{
			MethodName: "DeleteLeaderboard",
			Handler:    _Console_DeleteLeaderboard_Handler,
		},
		{
			MethodName: "DeleteLeaderboardRecord",
			Handler:    _Console_DeleteLeaderboardRecord_Handler,
		},
		{
			MethodName: "DeleteNotification",
			Handler:    _Console_DeleteNotification_Handler,
		},
		{
			MethodName: "DeleteUser",
			Handler:    _Console_DeleteUser_Handler,
		},
		{
			MethodName: "DeleteWalletLedger",
			Handler:    _Console_DeleteWalletLedger_Handler,
		},
		{
			MethodName: "DemoteGroupMember",
			Handler:    _Console_DemoteGroupMember_Handler,
		},
		{
			MethodName: "ExportAccount",
			Handler:    _Console_ExportAccount_Handler,
		},
		{
			MethodName: "ExportGroup",
			Handler:    _Console_ExportGroup_Handler,
		},
		{
			MethodName: "GetAccount",
			Handler:    _Console_GetAccount_Handler,
		},
		{
			MethodName: "GetConfig",
			Handler:    _Console_GetConfig_Handler,
		},
		{
			MethodName: "GetFriends",
			Handler:    _Console_GetFriends_Handler,
		},
		{
			MethodName: "GetGroup",
			Handler:    _Console_GetGroup_Handler,
		},
		{
			MethodName: "GetMembers",
			Handler:    _Console_GetMembers_Handler,
		},
		{
			MethodName: "GetGroups",
			Handler:    _Console_GetGroups_Handler,
		},
		{
			MethodName: "GetLeaderboard",
			Handler:    _Console_GetLeaderboard_Handler,
		},
		{
			MethodName: "GetMatchState",
			Handler:    _Console_GetMatchState_Handler,
		},
		{
			MethodName: "GetRuntime",
			Handler:    _Console_GetRuntime_Handler,
		},
		{
			MethodName: "GetStatus",
			Handler:    _Console_GetStatus_Handler,
		},
		{
			MethodName: "GetStorage",
			Handler:    _Console_GetStorage_Handler,
		},
		{
			MethodName: "GetWalletLedger",
			Handler:    _Console_GetWalletLedger_Handler,
		},
		{
			MethodName: "GetNotification",
			Handler:    _Console_GetNotification_Handler,
		},
		{
			MethodName: "GetPurchase",
			Handler:    _Console_GetPurchase_Handler,
		},
		{
			MethodName: "GetSubscription",
			Handler:    _Console_GetSubscription_Handler,
		},
		{
			MethodName: "ListApiEndpoints",
			Handler:    _Console_ListApiEndpoints_Handler,
		},
		{
			MethodName: "ListLeaderboardRecords",
			Handler:    _Console_ListLeaderboardRecords_Handler,
		},
		{
			MethodName: "ListLeaderboards",
			Handler:    _Console_ListLeaderboards_Handler,
		},
		{
			MethodName: "ListStorage",
			Handler:    _Console_ListStorage_Handler,
		},
		{
			MethodName: "ListStorageCollections",
			Handler:    _Console_ListStorageCollections_Handler,
		},
		{
			MethodName: "ListAccounts",
			Handler:    _Console_ListAccounts_Handler,
		},
		{
			MethodName: "ListChannelMessages",
			Handler:    _Console_ListChannelMessages_Handler,
		},
		{
			MethodName: "ListGroups",
			Handler:    _Console_ListGroups_Handler,
		},
		{
			MethodName: "ListNotifications",
			Handler:    _Console_ListNotifications_Handler,
		},
		{
			MethodName: "ListMatches",
			Handler:    _Console_ListMatches_Handler,
		},
		{
			MethodName: "ListPurchases",
			Handler:    _Console_ListPurchases_Handler,
		},
		{
			MethodName: "ListSubscriptions",
			Handler:    _Console_ListSubscriptions_Handler,
		},
		{
			MethodName: "ListUsers",
			Handler:    _Console_ListUsers_Handler,
		},
		{
			MethodName: "PromoteGroupMember",
			Handler:    _Console_PromoteGroupMember_Handler,
		},
		{
			MethodName: "RequireUserMfa",
			Handler:    _Console_RequireUserMfa_Handler,
		},
		{
			MethodName: "ResetUserMfa",
			Handler:    _Console_ResetUserMfa_Handler,
		},
		{
			MethodName: "UnbanAccount",
			Handler:    _Console_UnbanAccount_Handler,
		},
		{
			MethodName: "UnlinkCustom",
			Handler:    _Console_UnlinkCustom_Handler,
		},
		{
			MethodName: "UnlinkDevice",
			Handler:    _Console_UnlinkDevice_Handler,
		},
		{
			MethodName: "UnlinkEmail",
			Handler:    _Console_UnlinkEmail_Handler,
		},
		{
			MethodName: "UnlinkApple",
			Handler:    _Console_UnlinkApple_Handler,
		},
		{
			MethodName: "UnlinkFacebook",
			Handler:    _Console_UnlinkFacebook_Handler,
		},
		{
			MethodName: "UnlinkFacebookInstantGame",
			Handler:    _Console_UnlinkFacebookInstantGame_Handler,
		},
		{
			MethodName: "UnlinkGameCenter",
			Handler:    _Console_UnlinkGameCenter_Handler,
		},
		{
			MethodName: "UnlinkGoogle",
			Handler:    _Console_UnlinkGoogle_Handler,
		},
		{
			MethodName: "UnlinkSteam",
			Handler:    _Console_UnlinkSteam_Handler,
		},
		{
			MethodName: "UpdateAccount",
			Handler:    _Console_UpdateAccount_Handler,
		},
		{
			MethodName: "UpdateGroup",
			Handler:    _Console_UpdateGroup_Handler,
		},
		{
			MethodName: "WriteStorageObject",
			Handler:    _Console_WriteStorageObject_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "console.proto",
}
