package common

import (
	"bytes"
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
)

var HttpConnectPool http.RoundTripper = &http.Transport{
	//DialContext: (&net.Dialer{
	//	Timeout:   3 * time.Second,
	//	KeepAlive: 30 * time.Second,
	//}).DialContext,
	DialContext:           NewSelfDialer().DialContext,
	MaxIdleConns:          2000,
	MaxIdleConnsPerHost:   20,
	IdleConnTimeout:       60 * time.Second,
	ExpectContinueTimeout: 1 * time.Second,
	TLSClientConfig: &tls.Config{
		InsecureSkipVerify: true,
	},
}
var OtelHttpConnectPool = otelhttp.NewTransport(HttpConnectPool)

func HttpGet(ctx context.Context, url string, header map[string]string, timeout time.Duration) ([]byte, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}
	for k, v := range header {
		req.Header.Set(k, v)
	}
	if len(header) == 0 {
		req.Header.Set("Content-Type", "application/json")
	}
	client := &http.Client{Timeout: timeout, Transport: OtelHttpConnectPool}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("status code: %d, body: %s", resp.StatusCode, string(body))
	}
	return io.ReadAll(resp.Body)
}

func HttpPost(ctx context.Context, url string, data []byte, header map[string]string, timeout time.Duration) ([]byte, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewBuffer(data))
	if err != nil {
		return nil, err
	}
	for k, v := range header {
		req.Header.Set(k, v)
	}
	if len(header) == 0 {
		req.Header.Set("Content-Type", "application/json")
	}
	client := &http.Client{Timeout: timeout, Transport: OtelHttpConnectPool}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("status code: %d, body: %s", resp.StatusCode, string(body))
	}
	return io.ReadAll(resp.Body)
}

type ResponseWithRecorder struct {
	http.ResponseWriter
	StatusCode int
	Body       bytes.Buffer
}

func (rec *ResponseWithRecorder) WriteHeader(statusCode int) {
	rec.ResponseWriter.WriteHeader(statusCode)
	rec.StatusCode = statusCode
}

func (rec *ResponseWithRecorder) Write(d []byte) (n int, err error) {
	n, err = rec.ResponseWriter.Write(d)
	if err != nil {
		return
	}
	rec.Body.Write(d)
	return
}
