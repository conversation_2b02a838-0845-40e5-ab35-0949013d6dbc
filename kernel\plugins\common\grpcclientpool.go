package common

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"google.golang.org/grpc"
)

var (
	// ErrClosed is the error when the client pool is closed
	ErrClosed = errors.New("grpc pool: client pool is closed")
	// ErrTimeout is the error when the client pool timed out
	ErrTimeout = errors.New("grpc pool: client pool timed out")
	// ErrAlreadyClosed is the error when the client conn was already closed
	ErrAlreadyClosed = errors.New("grpc pool: the connection was already closed")
	// ErrFullPool is the error when the pool is already full
	ErrFullPool = errors.New("grpc pool: closing a ClientConn into a full pool")
)

const (
	// DefaultMaxConnsPerConn 单个连接默认最大并发数
	DefaultMaxConnsPerConn = 100
	// DefaultMaxIdleConns 默认最大空闲连接数
	DefaultMaxIdleConns = 10
	// DefaultMaxIdleTime 默认最大空闲时间
	DefaultMaxIdleTime = 120 * time.Second
	// DefaultMaxConnLifetime 默认连接最大生命周期
	DefaultMaxConnLifetime = 10 * time.Minute
	// DefaultConnUseTimeout 默认连接使用超时时间
	DefaultConnUseTimeout = 30 * time.Second
)

// Factory is a function type creating a grpc client
type Factory func() (*grpc.ClientConn, error)

// FactoryWithContext is a function type creating a grpc client
// that accepts the context parameter that could be passed from
// Get or NewWithContext method.
type FactoryWithContext func(context.Context) (*grpc.ClientConn, error)

// GrpcPool is the grpc client pool with connection sharing support
type GrpcPool struct {
	clients         sync.Map // map[string]*sharedConn
	factory         FactoryWithContext
	idleTimeout     time.Duration
	maxLifeDuration time.Duration
	minConns        int32
	maxConns        int32
	activeConns     int32
	maxConnsPerConn int32 // 单个连接最大并发数
	mu              sync.RWMutex
	closed          bool
	version         int64 // 用于确保删除正确的连接
	connID          int64 // 全局唯一连接ID
	waiters         sync.Cond
	connUseTimeout  time.Duration // 连接使用超时时间
}

// sharedConn represents a shared gRPC connection with reference counting
type sharedConn struct {
	*grpc.ClientConn
	pool          *GrpcPool
	timeUsed      int64 // 使用 atomic 操作
	timeInitiated int64 // 使用 atomic 操作
	unhealthy     int32 // 使用 atomic 操作
	refCount      int32
	mu            sync.Mutex
	closed        int32 // 使用 atomic 操作
	version       int64 // 用于确保删除正确的连接
	lastUsed      int64 // 最后使用时间
}

// ClientConn is the wrapper for a grpc client conn
type ClientConn struct {
	*sharedConn
	key     string
	version int64
	cancel  context.CancelFunc // 用于自动释放连接
}

// PoolOption 连接池配置选项
type PoolOption func(*GrpcPool)

// WithMaxConnsPerConn 设置单个连接最大并发数
func WithMaxConnsPerConn(n int) PoolOption {
	return func(p *GrpcPool) {
		if n > 0 {
			p.maxConnsPerConn = int32(n)
		}
	}
}

// WithIdleTimeout 设置空闲超时时间
func WithIdleTimeout(timeout time.Duration) PoolOption {
	return func(p *GrpcPool) {
		if timeout > 0 {
			p.idleTimeout = timeout
		}
	}
}

// WithMaxLifeDuration 设置连接最大生命周期
func WithMaxLifeDuration(duration time.Duration) PoolOption {
	return func(p *GrpcPool) {
		if duration > 0 {
			p.maxLifeDuration = duration
		}
	}
}

// WithConnUseTimeout 设置连接使用超时时间
func WithConnUseTimeout(timeout time.Duration) PoolOption {
	return func(p *GrpcPool) {
		if timeout > 0 {
			p.connUseTimeout = timeout
		}
	}
}

// New creates a new clients pool with the given initial and maximum capacity,
// and the timeout for the idle clients. Returns an error if the initial
// clients could not be created
func NewGrpcPool(factory Factory, minConns, maxConns int, opts ...PoolOption) (*GrpcPool, error) {
	return NewWithContext(context.Background(), func(ctx context.Context) (*grpc.ClientConn, error) { return factory() },
		minConns, maxConns, opts...)
}

// NewWithContext creates a new clients pool with the given initial and maximum
// capacity, and the timeout for the idle clients. The context parameter would
// be passed to the factory method during initialization. Returns an error if the
// initial clients could not be created.
func NewWithContext(ctx context.Context, factory FactoryWithContext, minConns, maxConns int, opts ...PoolOption) (*GrpcPool, error) {
	if maxConns <= 0 {
		maxConns = 1
	}
	if minConns < 0 {
		minConns = 0
	}
	if minConns > maxConns {
		minConns = maxConns
	}

	p := &GrpcPool{
		factory:         factory,
		minConns:        int32(minConns),
		maxConns:        int32(maxConns),
		idleTimeout:     DefaultMaxIdleTime,
		maxConnsPerConn: DefaultMaxConnsPerConn,
		maxLifeDuration: DefaultMaxConnLifetime,
		connUseTimeout:  DefaultConnUseTimeout,
	}
	p.waiters.L = &p.mu

	// 应用配置选项
	for _, opt := range opts {
		opt(p)
	}

	// Initialize initial connections
	for i := 0; i < minConns; i++ {
		c, err := factory(ctx)
		if err != nil {
			// 如果初始化失败，关闭已创建的连接
			p.Close()
			return nil, err
		}

		key := fmt.Sprintf("conn_%d", atomic.AddInt64(&p.connID, 1))
		now := time.Now().UnixNano()
		p.clients.Store(key, &sharedConn{
			ClientConn:    c,
			pool:          p,
			timeUsed:      now,
			timeInitiated: now,
			lastUsed:      now,
		})
		atomic.AddInt32(&p.activeConns, 1)
	}

	// 启动清理任务
	go p.cleanupLoop()

	return p, nil
}

// cleanupLoop 定期清理过期和无效连接
func (p *GrpcPool) cleanupLoop() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			p.cleanup()
			// 补充连接
			p.replenish()
		}
	}
}

// cleanup 清理过期和无效连接
func (p *GrpcPool) cleanup() {
	now := time.Now().UnixNano()
	p.clients.Range(func(key, value interface{}) bool {
		conn := value.(*sharedConn)
		conn.mu.Lock()
		defer conn.mu.Unlock()

		// 检查连接是否已关闭
		if atomic.LoadInt32(&conn.closed) == 1 {
			return true
		}

		// 检查连接是否超时未使用
		lastUsed := atomic.LoadInt64(&conn.lastUsed)
		if time.Duration(now-lastUsed) > p.connUseTimeout {
			atomic.StoreInt32(&conn.unhealthy, 1)
		}

		// 检查连接是否不健康且引用计数为0
		if atomic.LoadInt32(&conn.unhealthy) == 1 && atomic.LoadInt32(&conn.refCount) == 0 {
			conn.ClientConn.Close()
			conn.ClientConn = nil
			atomic.StoreInt32(&conn.closed, 1)
			p.clients.Delete(key)
			atomic.AddInt32(&p.activeConns, -1)
		}

		return true
	})
}

// replenish 补充连接池中的连接
func (p *GrpcPool) replenish() {
	current := atomic.LoadInt32(&p.activeConns)
	if current < p.minConns {
		// 需要补充的连接数
		need := p.minConns - current
		for i := int32(0); i < need; i++ {
			// 创建新连接
			conn, err := p.factory(context.Background())
			if err != nil {
				continue
			}

			key := fmt.Sprintf("conn_%d", atomic.AddInt64(&p.connID, 1))
			now := time.Now().UnixNano()
			p.clients.Store(key, &sharedConn{
				ClientConn:    conn,
				pool:          p,
				timeUsed:      now,
				timeInitiated: now,
				lastUsed:      now,
			})
			atomic.AddInt32(&p.activeConns, 1)
		}
	}
}

func (p *GrpcPool) IsClosed() bool {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.closed
}

func (p *GrpcPool) Close() {
	p.mu.Lock()
	if p.closed {
		p.mu.Unlock()
		return
	}
	p.closed = true
	p.mu.Unlock()

	p.clients.Range(func(key, value interface{}) bool {
		if conn, ok := value.(*sharedConn); ok {
			conn.mu.Lock()
			if conn.ClientConn != nil && atomic.CompareAndSwapInt32(&conn.closed, 0, 1) {
				conn.ClientConn.Close()
				conn.ClientConn = nil
			}
			conn.mu.Unlock()
		}
		return true
	})

	// 唤醒所有等待的 goroutine
	p.waiters.Broadcast()
}

// Get will return the next available client. If capacity
// has not been reached, it will create a new one using the factory. Otherwise,
// it will wait till the next client becomes available or a timeout.
// A timeout of 0 is an indefinite wait
func (p *GrpcPool) Get(ctx context.Context) (*ClientConn, error) {
	if p.IsClosed() {
		return nil, ErrClosed
	}

	// 使用切片存储所有可用连接
	type connInfo struct {
		conn     *sharedConn
		key      string
		refCount int32
		version  int64
	}
	var availableConns []connInfo
	now := time.Now().UnixNano()

	// 遍历所有连接，找到可用的连接
	p.clients.Range(func(key, value interface{}) bool {
		conn := value.(*sharedConn)

		// 先进行无锁的原子检查
		if atomic.LoadInt32(&conn.closed) == 1 {
			return true
		}

		refCount := atomic.LoadInt32(&conn.refCount)
		if refCount >= p.maxConnsPerConn {
			return true
		}

		// 检查空闲超时
		if p.idleTimeout > 0 {
			lastUsed := atomic.LoadInt64(&conn.timeUsed)
			if time.Duration(now-lastUsed) > p.idleTimeout {
				// 需要修改连接状态时才加锁
				conn.mu.Lock()
				if atomic.CompareAndSwapInt32(&conn.closed, 0, 1) {
					conn.ClientConn.Close()
					conn.ClientConn = nil
				}
				conn.mu.Unlock()
				return true
			}
		}

		// 检查连接是否健康
		if atomic.LoadInt32(&conn.unhealthy) == 1 {
			return true
		}

		// 获取连接版本号
		version := atomic.LoadInt64(&conn.version)

		// 将可用连接添加到切片
		availableConns = append(availableConns, connInfo{
			conn:     conn,
			key:      key.(string),
			refCount: refCount,
			version:  version,
		})
		return true
	})

	// 从可用连接中选择引用计数最小的
	var selectedConn *sharedConn
	var selectedKey string
	var selectedVersion int64

	if len(availableConns) > 0 {
		minRefCount := p.maxConnsPerConn
		for _, info := range availableConns {
			if info.refCount < minRefCount {
				minRefCount = info.refCount
				selectedConn = info.conn
				selectedKey = info.key
				selectedVersion = info.version
			}
		}
	}

	// 如果没有可用连接，尝试创建新连接
	if selectedConn == nil {
		// 使用 CAS 操作检查和增加连接数
		current := atomic.LoadInt32(&p.activeConns)
		if current >= p.maxConns {
			// 等待连接可用
			p.mu.Lock()
			for atomic.LoadInt32(&p.activeConns) >= p.maxConns && !p.closed {
				select {
				case <-ctx.Done():
					p.mu.Unlock()
					return nil, ErrTimeout
				default:
					p.waiters.Wait()
				}
			}
			p.mu.Unlock()

			if p.IsClosed() {
				return nil, ErrClosed
			}
		}

		// 再次尝试获取可用连接
		p.clients.Range(func(key, value interface{}) bool {
			conn := value.(*sharedConn)
			if atomic.LoadInt32(&conn.closed) == 0 &&
				atomic.LoadInt32(&conn.unhealthy) == 0 &&
				atomic.LoadInt32(&conn.refCount) < p.maxConnsPerConn {
				selectedConn = conn
				selectedKey = key.(string)
				selectedVersion = atomic.LoadInt64(&conn.version)
				return false
			}
			return true
		})

		// 如果仍然没有可用连接，创建新连接
		if selectedConn == nil {
			// 使用 CAS 操作增加连接数
			if !atomic.CompareAndSwapInt32(&p.activeConns, current, current+1) {
				return nil, ErrTimeout
			}

			// 创建新连接
			conn, err := p.factory(ctx)
			if err != nil {
				// 创建失败，回滚连接计数
				atomic.AddInt32(&p.activeConns, -1)
				// 唤醒等待的 goroutine
				p.waiters.Broadcast()
				return nil, err
			}

			// 创建新连接时增加版本号
			version := atomic.AddInt64(&p.version, 1)
			key := fmt.Sprintf("conn_%d", atomic.AddInt64(&p.connID, 1))

			selectedConn = &sharedConn{
				ClientConn:    conn,
				pool:          p,
				timeUsed:      now,
				timeInitiated: now,
				lastUsed:      now,
				version:       version,
			}

			// 存储连接
			p.clients.Store(key, selectedConn)
			selectedKey = key
			selectedVersion = version
		}
	}

	if selectedConn == nil {
		return nil, ErrTimeout
	}

	// 原子性更新引用计数和使用时间
	atomic.AddInt32(&selectedConn.refCount, 1)
	atomic.StoreInt64(&selectedConn.timeUsed, now)
	atomic.StoreInt64(&selectedConn.lastUsed, now)

	// 创建带超时的 context
	ctx, cancel := context.WithTimeout(ctx, p.connUseTimeout)

	return &ClientConn{
		sharedConn: selectedConn,
		key:        selectedKey,
		version:    selectedVersion,
		cancel:     cancel,
	}, nil
}

// Unhealthy marks the client conn as unhealthy, so that the connection
// gets reset when closed
func (c *ClientConn) Unhealthy() {
	if c == nil || c.sharedConn == nil {
		return
	}
	// 使用 CAS 确保只触发一次
	atomic.CompareAndSwapInt32(&c.unhealthy, 0, 1)
}

// Close returns a ClientConn to the pool. It is safe to call multiple time,
// but will return an error after first time
func (c *ClientConn) Close() error {
	if c == nil || c.sharedConn == nil {
		return nil
	}

	// 取消超时 context
	if c.cancel != nil {
		c.cancel()
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	if c.ClientConn == nil || atomic.LoadInt32(&c.closed) == 1 {
		return ErrAlreadyClosed
	}

	// 原子性减少引用计数
	refCount := atomic.AddInt32(&c.refCount, -1)
	if refCount < 0 {
		atomic.StoreInt32(&c.refCount, 0)
	}

	// 检查是否需要关闭连接
	if refCount == 0 {
		// 检查最大生命周期
		if c.pool.maxLifeDuration > 0 {
			initiated := atomic.LoadInt64(&c.timeInitiated)
			if time.Duration(time.Now().UnixNano()-initiated) > c.pool.maxLifeDuration {
				atomic.CompareAndSwapInt32(&c.unhealthy, 0, 1)
			}
		}

		if atomic.LoadInt32(&c.unhealthy) == 1 || c.pool.IsClosed() {
			// 使用 CAS 确保版本一致性
			if atomic.CompareAndSwapInt64(&c.version, c.version, c.version) {
				c.ClientConn.Close()
				c.ClientConn = nil
				atomic.StoreInt32(&c.closed, 1)
				c.pool.clients.Delete(c.key)
				atomic.AddInt32(&c.pool.activeConns, -1)
				c.pool.waiters.Broadcast()
			}
		}
	}

	return nil
}

// Capacity returns the maximum number of connections
func (p *GrpcPool) Capacity() int {
	return int(p.maxConns)
}

// Available returns the number of currently active connections
func (p *GrpcPool) Available() int {
	return int(atomic.LoadInt32(&p.activeConns))
}

// GRPCPoolManager manages connection pools for multiple gRPC backend addresses.
type GrpcPoolManager struct {
	mu    sync.RWMutex
	pools map[string]*GrpcPool
	opts  []grpc.DialOption

	minConns    int
	maxConns    int
	idleTimeout time.Duration
}

// NewGrpcPoolManager initializes a new manager.
func NewGrpcPoolManager(minConns, maxConns int, idleTimeout time.Duration, opts ...grpc.DialOption) *GrpcPoolManager {
	return &GrpcPoolManager{
		pools:       make(map[string]*GrpcPool),
		opts:        opts,
		minConns:    minConns,
		maxConns:    maxConns,
		idleTimeout: idleTimeout,
	}
}

// GetConn returns a pooled connection for the given target.
func (m *GrpcPoolManager) GetConn(ctx context.Context, target string) (*ClientConn, error) {
	pool, err := m.getOrCreatePool(target)
	if err != nil {
		return nil, err
	}
	return pool.Get(ctx)
}

func (m *GrpcPoolManager) getOrCreatePool(target string) (*GrpcPool, error) {
	m.mu.RLock()
	pool, exists := m.pools[target]
	m.mu.RUnlock()
	if exists {
		return pool, nil
	}

	m.mu.Lock()
	defer m.mu.Unlock()
	if pool, exists = m.pools[target]; exists {
		return pool, nil
	}

	factory := func() (*grpc.ClientConn, error) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		return grpc.DialContext(ctx, target, m.opts...)
	}

	pool, err := NewGrpcPool(factory, m.minConns, m.maxConns, WithIdleTimeout(m.idleTimeout))
	if err != nil {
		return nil, err
	}
	m.pools[target] = pool
	return pool, nil
}

// ClosePool closes and removes the pool for a target.
func (m *GrpcPoolManager) ClosePool(target string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	pool, exists := m.pools[target]
	if !exists {
		return errors.New("pool does not exist")
	}
	delete(m.pools, target)
	pool.Close()
	return nil
}

// CloseAll closes all pools.
func (m *GrpcPoolManager) CloseAll() error {
	m.mu.Lock()
	defer m.mu.Unlock()
	for target, pool := range m.pools {
		delete(m.pools, target)
		pool.Close()
	}
	return nil
}
