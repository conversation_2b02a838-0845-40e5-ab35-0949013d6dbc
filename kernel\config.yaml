name: kernel
config: null
shutdown_grace_sec: 30
data_dir: /mnt/h/soc-server/kernel/data
logger:
  level: debug
  stdout: true
  file: ''
  rotation: false
  max_size: 100
  max_age: 0
  max_backups: 0
  local_time: false
  compress: false
  format: json
  encoderConfig:
    messageKey: msg  # 日志消息的键名 (默认 "msg")
    levelKey: level  # 日志级别的键名 (默认 "level")
    timeKey: ts1     # 时间戳的键名 (默认 "ts")
    callerKey: caller # 调用者信息的键名 (默认 "caller")
    nameKey: logger  # 日志记录器名称的键名 (默认 "logger")
    stacktraceKey: stacktrace # 堆栈跟踪信息的键名 (默认 "stacktrace")
    lineEnding: "\n" # 行尾符
    timeEncoder: rfc3339 # 时间戳编码器 (rfc3339, rfc3339nano, iso8601, millis, nanos)
    durationEncoder: string # 持续时间编码器 (string, seconds, nanos)
    callerEncoder: full # 调用者信息编码器 (full, short)
    encodeLowercaseColorLevel: false # 是否将日志级别颜色化 (console 格式)
metrics:
  reporting_freq_sec: 60
  namespace: ''
  prometheus_port: 0
  prefix: kernel
  custom_prefix: custom
session:
  encryption_key: defaultencryptionkey
  token_expiry_sec: 3600000
  refresh_encryption_key: defaultrefreshencryptionkey
  refresh_token_expiry_sec: 3600000
  single_socket: false
  single_match: false
  single_party: false
  single_session: false
socket:
  server_key: defaultkey
  port: 7350
  address: ''
  protocol: tcp
  max_message_size_bytes: 4096
  max_request_size_bytes: 262144
  read_buffer_size_bytes: 4096
  write_buffer_size_bytes: 4096
  read_timeout_ms: 10000
  write_timeout_ms: 10000
  idle_timeout_ms: 60000
  write_wait_ms: 5000
  pong_wait_ms: 25000
  ping_period_ms: 15000
  ping_backoff_threshold: 20
  outgoing_queue_size: 64
  ssl_certificate: ''
  ssl_private_key: ''
  response_headers: null
database:
  disable: false
  address:
    - 'postgres:soctest123.@************:5432'
  conn_max_lifetime_ms: 3600000
  max_open_conns: 100
  max_idle_conns: 100
  dns_scan_interval_sec: 60
social:
  steam:
    publisher_key: ''
    app_id: 0
  facebook_instant_game:
    app_secret: REDACTED
  facebook_limited_login:
    app_id: ''
  apple:
    bundle_id: ''
runtime:
  env: ["LOCAL_CUSTOM_CONFIG_PATH=/root/soc-server/kernel/config.yaml", "key2=value2"]
  path: /mnt/h/soc-server/kernel/data/modules
  http_key: defaulthttpkey
  min_count: 0
  lua_min_count: 16
  max_count: 0
  lua_max_count: 48
  js_min_count: 16
  js_max_count: 32
  call_stack_size: 0
  lua_call_stack_size: 128
  registry_size: 0
  lua_registry_size: 512
  event_queue_size: 65536
  event_queue_workers: 8
  read_only_globals: true
  lua_read_only_globals: true
  js_read_only_globals: true
  lua_api_stacktrace: false
  js_entrypoint: ''
match:
  input_queue_size: 128
  call_queue_size: 128
  signal_queue_size: 10
  join_attempt_queue_size: 128
  deferred_queue_size: 128
  join_marker_deadline_ms: 15000
  max_empty_sec: 0
  label_update_interval_ms: 1000
tracker:
  event_queue_size: 1024
console:
  port: 7351
  address: ''
  max_message_size_bytes: 4194304
  read_timeout_ms: 10000
  write_timeout_ms: 60000
  idle_timeout_ms: 300000
  token_expiry_sec: 86400
  signing_key: defaultsigningkey
  mfa:
    storage_encryption_key: the-key-has-to-be-32-bytes-long!
    admin_account_enabled: false
leaderboard:
  blacklist_rank_cache: []
  callback_queue_size: 65536
  callback_queue_workers: 8
  rank_cache_workers: 1
matchmaker:
  max_tickets: 3
  interval_sec: 15
  max_intervals: 2
  rev_precision: false
  rev_threshold: 1
nacos:
  urls:
    - 'http://soc-dev.mini1.cn:8848/nacos'
  namespace_id: 'd8214867-51b3-40bf-bf75-c01f33781271'
  username: 'nacos'
  password: 'socpwd123.'
  timeout_ms: 5000
redis:
  addrs:
    - '************:6379'
  username: ''
  password: ''
  dial_timeout_ms: 5000
  read_timeout_ms: 5000
  write_timeout_ms: 5000
  pool_size: 50
  min_idle_conns: 10
  pool_timeout_ms: 5000
  idle_timeout_ms: 30000
  db: 1
access_log:
  is_disable: false
  write_to_log_file: false
  dir: ""
  record_resp_body: true
  disable_record_req_body: false
  max_backups: 0 # 0表示不限制
  rpc_ids:
dbagent_server_config:
  node_config:
    node_id: 'dbagentserver401'
    svc_group: 'dbagentserver@@omo'
    host: '127.0.0.1:7350'
    nethost: '127.0.0.1:7350'
    auth_key: 'defaulthttpkey'
    weight: 10
    scheme: http
gate_server_config:
  node_config:
    node_id: 'gateserver101'
    svc_group: 'gateserver@@omo'
    host: '127.0.0.1:7350'
    nethost: '127.0.0.1:7350'
    auth_key: 'defaulthttpkey'
    weight: 10
    scheme: http
  routes:
    # /v2/account/authenticate:
    #   type: svc
    #   data: loginserver@@omo
    #   lbtype: rr
    #   scheme: http
    #   authkey: 'defaultkey'
  watch_svc_group_list:
    - 'loginserver@@omo'
    - 'logicserver@@omo'
    - 'queueserver@@omo'
    - 'sessionserver@@omo'
    - 'statusserver@@omo'
login_server_config:
  node_config:
    node_id: 'loginserver201'
    svc_group: 'loginserver@@omo'
    host: '127.0.0.1:7350'
    nethost: '127.0.0.1:7350'
    auth_key: 'defaulthttpkey'
    weight: 10
    scheme: http
  watch_svc_group_list:
    - 'gateserver@@omo'
    - 'dbagentserver@@omo'
logic_server_config:
  node_config:
    node_id: 'logicserver301'
    svc_group: 'logicserver@@omo'
    host: '127.0.0.1:7350'
    nethost: '127.0.0.1:7350'
    auth_key: 'defaulthttpkey'
    weight: 10
    scheme: http
  watch_svc_group_list:
    - 'gateserver@@omo'
    - 'dbagentserver@@omo'
    - 'statusserver@@omo'
queue_server_config:
  node_config:
    node_id: 'queueserver501'
    svc_group: 'queueserver@@omo'
    host: '127.0.0.1:7350'
    nethost: '127.0.0.1:7350'
    auth_key: 'defaulthttpkey'
  watch_svc_group_list:
    - 'gateserver@@omo'
    - 'dbagentserver@@omo'
  gsmgr_config:
    addr: 'soc-dev.mini1.cn:8082'
    appid: '1000'
    appsecret: 'mbW5Wh7@CILChaW^6RqvRJtkntsie3'
  bucket_list: ['0-9']
  msgbus_config:
    addr: 'soc-dev.mini1.cn:8085'
    appid: '1006'
    appsecret: 'QDcQjeKzNxavtT8q'
friend_server_config:
  node_config:
    node_id: 'friendserver601'
    svc_group: 'friendserver@@omo'
    host: '127.0.0.1:7350'
    nethost: '127.0.0.1:7350'
    auth_key: 'defaulthttpkey'
    weight: 10
    scheme: http
  watch_svc_group_list:
    - 'gateserver@@omo'
    - 'dbagentserver@@omo'
    - 'statusserver@@omo'
user_server_config:
  node_config:
    node_id: 'userserver701'
    svc_group: 'userserver@@omo'
    host: '127.0.0.1:7350'
    nethost: '127.0.0.1:7350'
    auth_key: 'defaulthttpkey'
  watch_svc_group_list:
    - 'gateserver@@omo'
    - 'dbagentserver@@omo'
session_server_config:
  node_config:
    node_id: 'sessionserver801'
    svc_group: 'sessionserver@@omo'
    host: '127.0.0.1:7350'
    nethost: '127.0.0.1:7350'
    auth_key: 'defaulthttpkey'
  watch_svc_group_list:
    - 'gateserver@@omo'
status_server_config:
  node_config:
    node_id: 'statusserver901'
    svc_group: 'statusserver@@omo'
    host: '127.0.0.1:7350'
    nethost: '127.0.0.1:7350'
    auth_key: 'defaulthttpkey'
