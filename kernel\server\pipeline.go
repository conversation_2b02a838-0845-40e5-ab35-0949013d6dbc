// Copyright 2018 The Nakama Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package server

import (
	"database/sql"
	"fmt"
	"net/url"
	"strings"
	"time"

	"kernel/kernel-common/accesslog"
	"kernel/kernel-common/rtapi"

	"go.opentelemetry.io/otel"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
)

type Pipeline struct {
	logger               *zap.Logger
	config               Config
	db                   *sql.DB
	protojsonMarshaler   *protojson.MarshalOptions
	protojsonUnmarshaler *protojson.UnmarshalOptions
	sessionRegistry      SessionRegistry
	statusRegistry       StatusRegistry
	matchRegistry        MatchRegistry
	partyRegistry        PartyRegistry
	matchmaker           Matchmaker
	tracker              Tracker
	router               MessageRouter
	runtime              *Runtime
	node                 string
	accessLog            *accesslog.AccessLog
	metrics              Metrics
}

func NewPipeline(logger *zap.Logger, config Config, db *sql.DB, protojsonMarshaler *protojson.MarshalOptions, protojsonUnmarshaler *protojson.UnmarshalOptions, sessionRegistry SessionRegistry, statusRegistry StatusRegistry, matchRegistry MatchRegistry, partyRegistry PartyRegistry, matchmaker Matchmaker, tracker Tracker, router MessageRouter, runtime *Runtime, accessLog *accesslog.AccessLog, metrics Metrics) *Pipeline {
	return &Pipeline{
		logger:               logger,
		config:               config,
		db:                   db,
		protojsonMarshaler:   protojsonMarshaler,
		protojsonUnmarshaler: protojsonUnmarshaler,
		sessionRegistry:      sessionRegistry,
		statusRegistry:       statusRegistry,
		matchRegistry:        matchRegistry,
		partyRegistry:        partyRegistry,
		matchmaker:           matchmaker,
		tracker:              tracker,
		router:               router,
		runtime:              runtime,
		node:                 config.GetName(),
		accessLog:            accessLog,
		metrics:              metrics,
	}
}

func (p *Pipeline) ProcessRequest(logger *zap.Logger, session Session, in *rtapi.Envelope) bool {
	retcode := 200
	start := time.Now()
	method := "WS"
	path := ""
	ip := session.ClientIP()
	reqBody := in.String()
	respBody := ""
	bytesReceived := len(reqBody)
	bytesSent := 0
	fields := url.Values{}
	vars := session.Vars()
	fields.Set("uid", session.UserID().String())
	if uin, ok := vars["uin"]; ok {
		fields.Set("uin", uin)
	}
	id := "" // rpcid
	requestCtx := session.Context()
	var messageName, messageNameID string
	defer func() {
		subSince := time.Since(start)
		// After this point the RPC will be captured in metrics.
		if in.Message != nil && messageNameID != "*rtapi.envelope_ping" && messageNameID != "*rtapi.envelope_pong" {
			switch in.Message.(type) {
			case *rtapi.Envelope_Rpc:
				p.metrics.OnlyRpc(id, subSince, int64(bytesReceived), int64(bytesSent), nil)
			default:
				//metricsid = strings.TrimPrefix(messageNameID, RTAPI_PREFIX_LOWERCASE)
				//p.metrics.OnlyRpc(metricsid, time.Since(start), int64(bytesReceived), int64(bytesSent), nil)
			}
		}

		if p.config.GetAccessLog().GetIsDisable() {
			return
		}
		found := false
		if len(p.config.GetAccessLog().GetRpcIds()) > 0 && id != "" {
			if islog, ok := p.config.GetAccessLog().GetRpcIds()[id]; !ok {
				for rpcId, islog := range p.config.GetAccessLog().GetRpcIds() {
					if strings.HasPrefix(id, rpcId) && islog {
						found = true
						break
					}
				}
			} else {
				found = islog
			}
		}
		if len(p.config.GetAccessLog().GetRpcIds()) > 0 && messageNameID != "" && !found {
			if islog, ok := p.config.GetAccessLog().GetRpcIds()[messageNameID]; !ok {
				if islog, ok := p.config.GetAccessLog().GetRpcIds()["*"]; ok {
					found = islog
				} else {
					for rpcId, islog := range p.config.GetAccessLog().GetRpcIds() {
						if strings.HasPrefix(messageNameID, rpcId) && islog {
							found = true
							break
						}
					}
				}
			} else {
				found = islog
			}
		} else {
			found = true
		}
		if !found {
			return
		}
		if traceid, ok := session.GetData("traceid"); ok {
			fields.Set("traceid", traceid.(string))
		}
		p.accessLog.Print(&requestCtx, subSince, p.accessLog.TimeFormat, retcode, method, path, ip, reqBody, respBody, bytesReceived, bytesSent, nil, nil, &fields)
		session.DeleteData("traceid")
	}()

	// if logger.Core().Enabled(zap.DebugLevel) { // remove extra heavy reflection processing
	// 	logger.Debug(fmt.Sprintf("Received %T message", in.Message), zap.Any("message", in.Message))
	// }

	if in.Message == nil {
		msg := &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_MISSING_PAYLOAD),
			Message: "Missing message.",
		}}}
		respBody = msg.String()
		bytesSent = len(respBody)
		_ = session.SendBytes([]byte(respBody), true)
		return false
	}

	var pipelineFn func(*zap.Logger, Session, *rtapi.Envelope) (bool, *rtapi.Envelope)
	messageName = fmt.Sprintf("%T", in.Message) // strings.ToLower(RTAPI_PREFIX) + RTID
	messageNameID = strings.ToLower(messageName)
	if messageNameID != "*rtapi.envelope_ping" && messageNameID != "*rtapi.envelope_pong" && messageNameID != "*rtapi.envelope_rpc" && p.runtime.Rt(messageNameID) != nil {
		pipelineFn = p.rtFunction
	} else {
		switch in.Message.(type) {
		case *rtapi.Envelope_ChannelJoin:
			pipelineFn = p.channelJoin
		case *rtapi.Envelope_ChannelLeave:
			pipelineFn = p.channelLeave
		case *rtapi.Envelope_ChannelMessageSend:
			pipelineFn = p.channelMessageSend
		case *rtapi.Envelope_ChannelMessageUpdate:
			pipelineFn = p.channelMessageUpdate
		case *rtapi.Envelope_ChannelMessageRemove:
			pipelineFn = p.channelMessageRemove
		case *rtapi.Envelope_MatchCreate:
			pipelineFn = p.matchCreate
		case *rtapi.Envelope_MatchDataSend:
			pipelineFn = p.matchDataSend
		case *rtapi.Envelope_MatchJoin:
			pipelineFn = p.matchJoin
		case *rtapi.Envelope_MatchLeave:
			pipelineFn = p.matchLeave
		case *rtapi.Envelope_MatchmakerAdd:
			pipelineFn = p.matchmakerAdd
		case *rtapi.Envelope_MatchmakerRemove:
			pipelineFn = p.matchmakerRemove
		case *rtapi.Envelope_Ping:
			pipelineFn = p.ping
		case *rtapi.Envelope_Pong:
			pipelineFn = p.pong
		case *rtapi.Envelope_Rpc:
			pipelineFn = p.rpc
			rpcMessage := in.GetRpc()
			if rpcMessage != nil {
				id = strings.ToLower(rpcMessage.Id)
			}
		case *rtapi.Envelope_StatusFollow:
			pipelineFn = p.statusFollow
		case *rtapi.Envelope_StatusUnfollow:
			pipelineFn = p.statusUnfollow
		case *rtapi.Envelope_StatusUpdate:
			pipelineFn = p.statusUpdate
		case *rtapi.Envelope_PartyCreate:
			pipelineFn = p.partyCreate
		case *rtapi.Envelope_PartyJoin:
			pipelineFn = p.partyJoin
		case *rtapi.Envelope_PartyLeave:
			pipelineFn = p.partyLeave
		case *rtapi.Envelope_PartyPromote:
			pipelineFn = p.partyPromote
		case *rtapi.Envelope_PartyAccept:
			pipelineFn = p.partyAccept
		case *rtapi.Envelope_PartyRemove:
			pipelineFn = p.partyRemove
		case *rtapi.Envelope_PartyClose:
			pipelineFn = p.partyClose
		case *rtapi.Envelope_PartyJoinRequestList:
			pipelineFn = p.partyJoinRequestList
		case *rtapi.Envelope_PartyMatchmakerAdd:
			pipelineFn = p.partyMatchmakerAdd
		case *rtapi.Envelope_PartyMatchmakerRemove:
			pipelineFn = p.partyMatchmakerRemove
		case *rtapi.Envelope_PartyDataSend:
			pipelineFn = p.partyDataSend
		default:
			// If we reached this point the envelope was valid but the contents are missing or unknown.
			// Usually caused by a version mismatch, and should cause the session making this pipeline request to close.
			logger.Error("Unrecognizable payload received.", zap.Any("payload", in))
			msg := &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
				Code:    int32(rtapi.Error_UNRECOGNIZED_PAYLOAD),
				Message: "Unrecognized message.",
			}}}
			respBody = msg.String()
			bytesSent = len(respBody)
			_ = session.SendBytes([]byte(respBody), true)
			return false
		}
	}

	switch in.Message.(type) {
	//case *rtapi.Envelope_Rpc:
	// No before/after hooks on RPC.
	default:
		if fn := p.runtime.BeforeRt(messageNameID); fn != nil {
			hookResult, hookErr := fn(requestCtx, logger, session.UserID().String(), session.Username(), session.Vars(), session.Expiry(), session.ID().String(), session.ClientIP(), session.ClientPort(), session.Lang(), in)

			if hookErr != nil {
				// Errors from before hooks do not close the session.
				msg := &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
					Code:    int32(rtapi.Error_RUNTIME_FUNCTION_EXCEPTION),
					Message: hookErr.Error(),
				}}}
				respBody = msg.String()
				bytesSent = len(respBody)
				_ = session.SendBytes([]byte(respBody), true)
				return true
			} else if hookResult == nil {
				// If result is nil, requested resource is disabled. Sessions calling disabled resources will be closed.
				logger.Warn("Intercepted a disabled resource.", zap.String("resource", messageName))
				msg := &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
					Code:    int32(rtapi.Error_UNRECOGNIZED_PAYLOAD),
					Message: "Requested resource was not found.",
				}}}
				respBody = msg.String()
				bytesSent = len(respBody)
				_ = session.SendBytes([]byte(respBody), true)
				return false
			}

			in = hookResult
		}
	}

	success, out := pipelineFn(logger, session, in)
	if success && messageName != "" {
		// Unsuccessful operations do not trigger after hooks.
		if fn := p.runtime.AfterRt(messageNameID); fn != nil {
			_ = fn(requestCtx, logger, session.UserID().String(), session.Username(), session.Vars(), session.Expiry(), session.ID().String(), session.ClientIP(), session.ClientPort(), session.Lang(), out, in)
		}
	}
	respBody = out.String()
	bytesSent = len(respBody)

	return true
}

// 自定义rt函数
func (p *Pipeline) rtFunction(logger *zap.Logger, session Session, envelope *rtapi.Envelope) (bool, *rtapi.Envelope) {
	messageName := fmt.Sprintf("%T", envelope.Message) // strings.ToLower(RTAPI_PREFIX) + RTID
	messageNameID := strings.ToLower(messageName)
	if messageNameID == "" {
		msg := &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_BAD_INPUT),
			Message: "RPC ID must be set",
		}}}
		session.Send(msg, true)
		return false, msg
	}

	tr := otel.Tracer(p.config.GetName())
	requestCtx, span := tr.Start(session.Context(), messageNameID)
	traceid := span.SpanContext().TraceID().String()
	session.SetData("traceid", traceid)
	defer span.End()

	fn := p.runtime.Rt(messageNameID)
	if fn == nil {
		msg := &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_RUNTIME_FUNCTION_NOT_FOUND),
			Message: "RPC function not found",
		}}}
		session.Send(msg, true)
		return false, msg
	}

	out, err := fn(requestCtx, logger, session.UserID().String(), session.Username(), session.Vars(), session.Expiry(), session.ID().String(), session.ClientIP(), session.ClientPort(), session.Lang(), envelope)
	if err != nil {
		msg := &rtapi.Envelope{Cid: envelope.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(rtapi.Error_RUNTIME_FUNCTION_EXCEPTION),
			Message: err.Error(),
		}}}
		session.Send(msg, true)
		return false, msg
	}
	_ = session.Send(out, true)

	return true, out
}
