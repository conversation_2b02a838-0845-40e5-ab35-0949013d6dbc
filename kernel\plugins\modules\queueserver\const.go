package main

const QUEUE_BUCKET_SIZE = 10 // 排队桶大小, 房间id hash后取模

// queueserver rpcid
const (
	RPCID_QUEUESERVER_JOIN_QUEUE       string = "queueserver.joinqueue"      // 加入排队
	RPCID_QUEUESERVER_LEAVE_QUEUE      string = "queueserver.leavequeue"     // 离开排队
	RPCID_QUEUESERVER_GET_QUEUE_STATUS string = "queueserver.getqueuestatus" // 获取排队状态
)

// push subject
const (
	SUBJECT_QUEUESERVER_PUSH_JOINRESULT string = "queueserver.push.joinresult" // 加入排队结果
)
