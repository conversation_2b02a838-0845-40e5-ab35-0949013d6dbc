// Copyright 2018 The Nakama Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package server

import (
	"context"
	"encoding/json"
	"errors"
	"io"
	"kernel/kernel-common/runtime"
	"net/http"
	"net/url"
	"strings"
	"time"

	"kernel/kernel-common/api"

	"github.com/gofrs/uuid/v5"
	"github.com/gorilla/mux"
	grpcgw "github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

var (
	authTokenInvalidBytes    = []byte(`{"error":"Auth token invalid","message":"Auth token invalid","code":16}`)
	httpKeyInvalidBytes      = []byte(`{"error":"HTTP key invalid","message":"HTTP key invalid","code":16}`)
	noAuthBytes              = []byte(`{"error":"Auth token or HTTP key required","message":"Auth token or HTTP key required","code":16}`)
	rpcIDMustBeSetBytes      = []byte(`{"error":"RPC ID must be set","message":"RPC ID must be set","code":3}`)
	rpcFunctionNotFoundBytes = []byte(`{"error":"RPC function not found","message":"RPC function not found","code":5}`)
	internalServerErrorBytes = []byte(`{"error":"Internal Server Error","message":"Internal Server Error","code":13}`)
	badJSONBytes             = []byte(`{"error":"json: cannot unmarshal object into Go value of type string","message":"json: cannot unmarshal object into Go value of type string","code":3}`)
	requestBodyTooLargeBytes = []byte(`{"code":3, "message":"http: request body too large"}`)
)

func (s *ApiServer) RpcFuncHttp(w http.ResponseWriter, r *http.Request) {
	var success bool
	var recvBytes, sentBytes int
	var err error
	var id string

	retcode := 200
	start := time.Now()
	method := r.Method
	path := r.URL.Path
	respBody := ""
	fields := url.Values{}

	// Check first token then HTTP key for authentication, and add user info to the context.
	queryParams := r.URL.Query()
	var isTokenAuth bool
	var userID uuid.UUID
	var username string
	var vars map[string]string
	var expiry int64
	var payload string
	clientIP, clientPort := extractClientAddressFromRequest(s.logger, r)

	tr := otel.Tracer(s.config.GetName())
	requestCtx, span := tr.Start(r.Context(), path)
	traceid := span.SpanContext().TraceID().String()

	defer func() {
		if span != nil {
			span.SetAttributes(attribute.String("rpc_id", id))
			if retcode != http.StatusOK {
				span.RecordError(errors.New(respBody))
			}
			span.End()
		}
		if s.config.GetAccessLog().GetIsDisable() {
			return
		}
		if len(s.config.GetAccessLog().GetRpcIds()) > 0 && id != "" {
			found := false
			if islog, ok := s.config.GetAccessLog().GetRpcIds()[id]; !ok {
				if islog, ok := s.config.GetAccessLog().GetRpcIds()["*"]; ok {
					found = islog
				} else {
					for rpcId, islog := range s.config.GetAccessLog().GetRpcIds() {
						if strings.HasPrefix(id, rpcId) && islog {
							found = true
							break
						}
					}
				}
			} else {
				found = islog
			}
			if !found {
				return
			}
		}

		var isSetUid bool
		var isSetUin bool
		if !userID.IsNil() {
			fields.Set("uid", userID.String())
			isSetUid = true
		}
		if vars != nil {
			if uin, ok := vars["uin"]; ok {
				fields.Set("uin", uin)
				isSetUin = true
			}
		}
		if !isSetUid {
			tmpuid := r.Header.Get(runtime.RUNTIME_CTX_HEADER_USER_ID)
			if tmpuid != "" {
				fields.Set("uid", tmpuid)
			}
		}
		if !isSetUin {
			tmpuin := r.Header.Get(runtime.RUNTIME_CTX_HEADER_UIN)
			if tmpuin != "" {
				fields.Set("uin", tmpuin)
			}
		}
		fields.Set("traceid", traceid)
		s.accessLog.Print(&requestCtx, time.Since(start), s.accessLog.TimeFormat, retcode, method, path, clientIP, payload, respBody, recvBytes, sentBytes, nil, &queryParams, &fields)
	}()

	if httpKey := queryParams.Get("http_key"); httpKey != "" {
		if httpKey != s.config.GetRuntime().HTTPKey {
			// HTTP key did not match.
			w.Header().Set("content-type", "application/json")
			w.WriteHeader(http.StatusUnauthorized)
			_, err := w.Write(httpKeyInvalidBytes)
			if err != nil {
				s.logger.Debug("Error writing response to client", zap.Error(err))
			}
			retcode = http.StatusUnauthorized
			respBody = string(httpKeyInvalidBytes)
			sentBytes = len(respBody)
			return
		}
	} else if auth := r.Header["Authorization"]; len(auth) >= 1 {
		if httpKey, _, ok := parseBasicAuth(auth[0]); ok {
			if httpKey != s.config.GetRuntime().HTTPKey {
				// HTTP key did not match.
				w.Header().Set("content-type", "application/json")
				w.WriteHeader(http.StatusUnauthorized)
				_, err := w.Write(httpKeyInvalidBytes)
				if err != nil {
					s.logger.Debug("Error writing response to client", zap.Error(err))
				}
				retcode = http.StatusUnauthorized
				respBody = string(httpKeyInvalidBytes)
				sentBytes = len(respBody)
				return
			}
		} else {
			var tokenId string
			var tokenIssuedAt int64
			userID, username, vars, expiry, tokenId, tokenIssuedAt, isTokenAuth = parseBearerAuth([]byte(s.config.GetSession().EncryptionKey), auth[0])
			requestCtx = context.WithValue(requestCtx, ctxTokenIDKey{}, tokenId)
			requestCtx = context.WithValue(requestCtx, ctxExpiryKey{}, expiry)
			requestCtx = context.WithValue(requestCtx, ctxTokenIssuedAtKey{}, tokenIssuedAt)
			requestCtx = context.WithValue(requestCtx, ctxVarsKey{}, vars)

			if !isTokenAuth || !s.sessionCache.IsValidSession(userID, expiry, tokenId) {
				// Auth token not valid or expired.
				w.Header().Set("content-type", "application/json")
				w.WriteHeader(http.StatusUnauthorized)
				_, err := w.Write(authTokenInvalidBytes)
				if err != nil {
					s.logger.Debug("Error writing response to client", zap.Error(err))
				}
				retcode = http.StatusUnauthorized
				respBody = string(authTokenInvalidBytes)
				sentBytes = len(respBody)
				return
			}
		}
	} else {
		// No authentication present.
		w.Header().Set("content-type", "application/json")
		w.WriteHeader(http.StatusUnauthorized)
		_, err := w.Write(noAuthBytes)
		if err != nil {
			s.logger.Debug("Error writing response to client", zap.Error(err))
		}
		retcode = http.StatusUnauthorized
		respBody = string(noAuthBytes)
		sentBytes = len(respBody)
		return
	}

	// Afte	r this point the RPC will be captured in metrics.
	defer func() {
		s.metrics.ApiRpc(id, time.Since(start), int64(recvBytes), int64(sentBytes), !success)
	}()

	// Check the RPC function ID.
	maybeID, ok := mux.Vars(r)["id"]
	if !ok || maybeID == "" {
		// Missing RPC function ID.
		w.Header().Set("content-type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		sentBytes, err = w.Write(rpcIDMustBeSetBytes)
		if err != nil {
			s.logger.Debug("Error writing response to client", zap.Error(err))
		}
		retcode = http.StatusBadRequest
		respBody = string(rpcIDMustBeSetBytes)
		sentBytes = len(respBody)
		return
	}
	id = strings.ToLower(maybeID)

	// Find the correct RPC function.
	fn := s.runtime.Rpc(id)
	if fn == nil {
		// No function registered for this ID.
		w.Header().Set("content-type", "application/json")
		w.WriteHeader(http.StatusNotFound)
		sentBytes, err = w.Write(rpcFunctionNotFoundBytes)
		if err != nil {
			s.logger.Debug("Error writing response to client", zap.Error(err))
		}
		retcode = http.StatusNotFound
		respBody = string(rpcFunctionNotFoundBytes)
		sentBytes = len(respBody)
		return
	}

	// Check if we need to mimic existing GRPC Gateway behaviour or expect to receive/send unwrapped data.
	// Any value for this query parameter, including the parameter existing with an empty value, will
	// indicate that raw behaviour is expected.
	_, unwrap := queryParams["unwrap"]

	// Prepare input to function.
	if r.Method == http.MethodPost {
		b, err := io.ReadAll(r.Body)
		if err != nil {
			// Request body too large.
			if err.Error() == "http: request body too large" {
				w.Header().Set("content-type", "application/json")
				w.WriteHeader(http.StatusBadRequest)
				sentBytes, err = w.Write(requestBodyTooLargeBytes)
				if err != nil {
					s.logger.Debug("Error writing response to client", zap.Error(err))
				}
				retcode = http.StatusBadRequest
				respBody = string(requestBodyTooLargeBytes)
				sentBytes = len(respBody)
				return
			}

			// Other error reading request body.
			w.Header().Set("content-type", "application/json")
			w.WriteHeader(http.StatusInternalServerError)
			sentBytes, err = w.Write(internalServerErrorBytes)
			if err != nil {
				s.logger.Debug("Error writing response to client", zap.Error(err))
			}
			retcode = http.StatusInternalServerError
			respBody = string(internalServerErrorBytes)
			sentBytes = len(respBody)
			return
		}
		recvBytes = len(b)

		// Maybe attempt to decode to a JSON string to mimic existing GRPC Gateway behaviour.
		if recvBytes > 0 && !unwrap {
			err = json.Unmarshal(b, &payload)
			if err != nil {
				w.Header().Set("content-type", "application/json")
				w.WriteHeader(http.StatusBadRequest)
				sentBytes, err = w.Write(badJSONBytes)
				if err != nil {
					s.logger.Debug("Error writing response to client", zap.Error(err))
				}
				retcode = http.StatusBadRequest
				respBody = string(badJSONBytes)
				sentBytes = len(respBody)
				return
			}
		} else {
			payload = string(b)
		}
	}

	queryParams.Del("http_key")
	r.Header.Set(runtime.RUNTIME_CTX_HEADER_RPC_ID, id)

	uid := ""
	if isTokenAuth {
		uid = userID.String()
	}

	// Extract http headers
	headers := make(map[string][]string)
	for k, v := range r.Header {
		if k == "Grpc-Timeout" {
			continue
		}
		headers[k] = make([]string, 0, len(v))
		headers[k] = append(headers[k], v...)
	}

	// Execute the function.
	result, fnErr, code := fn(requestCtx, headers, queryParams, uid, username, vars, expiry, "", clientIP, clientPort, "", payload)
	if fnErr != nil {
		response, _ := json.Marshal(map[string]interface{}{"error": fnErr, "message": fnErr.Error(), "code": code})
		w.Header().Set("content-type", "application/json")
		w.WriteHeader(grpcgw.HTTPStatusFromCode(code))
		sentBytes, err = w.Write(response)
		if err != nil {
			s.logger.Debug("Error writing response to client", zap.Error(err))
		}
		retcode = grpcgw.HTTPStatusFromCode(code)
		respBody = string(response)
		sentBytes = len(respBody)
		return
	}

	// Return the successful result.
	var response []byte
	if !unwrap {
		// GRPC Gateway equivalent behaviour.
		var err error
		response, err = json.Marshal(map[string]interface{}{"payload": result})
		if err != nil {
			// Failed to encode the wrapped response.
			s.logger.Error("Error marshaling wrapped response to client", zap.Error(err))
			w.Header().Set("content-type", "application/json")
			w.WriteHeader(http.StatusInternalServerError)
			sentBytes, err = w.Write(internalServerErrorBytes)
			if err != nil {
				s.logger.Debug("Error writing response to client", zap.Error(err))
			}
			retcode = http.StatusInternalServerError
			respBody = string(internalServerErrorBytes)
			sentBytes = len(respBody)
			return
		}
	} else {
		// "Unwrapped" response.
		response = []byte(result)
	}
	if unwrap {
		if contentType := r.Header["Content-Type"]; len(contentType) > 0 {
			// Assume the request input content type is the same as the expected response.
			w.Header().Set("content-type", contentType[0])
		} else {
			// Don't know payload content-type.
			w.Header().Set("content-type", "text/plain")
		}
	} else {
		// Fall back to default response content type application/json.
		w.Header().Set("content-type", "application/json")
	}
	w.WriteHeader(http.StatusOK)
	sentBytes, err = w.Write(response)
	if err != nil {
		s.logger.Debug("Error writing response to client", zap.Error(err))
		return
	}
	retcode = http.StatusOK
	respBody = string(response)
	sentBytes = len(respBody)
	success = true
}

func (s *ApiServer) RpcFunc(ctx context.Context, in *api.Rpc) (*api.Rpc, error) {
	id := strings.ToLower(in.Id)
	uid := ""
	username := ""
	clientIP, clientPort := extractClientAddressFromContext(s.logger, ctx)
	var vars map[string]string
	expiry := int64(0)
	headers := http.Header{}
	queryParams := url.Values{}

	retcode := 200
	start := time.Now()
	fields := url.Values{}
	respBody := ""
	recvBytes := len(in.Payload)
	sentBytes := 0
	method := "GRPC"
	path := "/v2/rpc/" + id

	tr := otel.Tracer(s.config.GetName())
	requestCtx, span := tr.Start(ctx, path)
	traceid := span.SpanContext().TraceID().String()

	defer func() {
		if span != nil {
			span.SetAttributes(attribute.String("rpc_id", id))
			if retcode != http.StatusOK {
				span.RecordError(errors.New(respBody))
			}
			span.End()
		}
		if s.config.GetAccessLog().GetIsDisable() {
			return
		}
		if len(s.config.GetAccessLog().GetRpcIds()) > 0 && id != "" {
			found := false
			if islog, ok := s.config.GetAccessLog().GetRpcIds()[id]; !ok {
				if islog, ok := s.config.GetAccessLog().GetRpcIds()["*"]; ok {
					found = islog
				} else {
					for rpcId, islog := range s.config.GetAccessLog().GetRpcIds() {
						if strings.HasPrefix(id, rpcId) && islog {
							found = true
							break
						}
					}
				}
			} else {
				found = islog
			}
			if !found {
				return
			}
		}

		var isSetUid bool
		var isSetUin bool
		if uid != "" {
			fields.Set("uid", uid)
			isSetUid = true
		}
		if vars != nil {
			if uin, ok := vars["uin"]; ok {
				fields.Set("uin", uin)
				isSetUin = true
			}
		}
		if !isSetUid {
			tmpuid := headers.Get(runtime.RUNTIME_CTX_HEADER_USER_ID)
			if tmpuid != "" {
				fields.Set("uid", tmpuid)
			}
		}
		if !isSetUin {
			tmpuin := headers.Get(runtime.RUNTIME_CTX_HEADER_UIN)
			if tmpuin != "" {
				fields.Set("uin", tmpuin)
			}
		}
		fields.Set("traceid", traceid)
		s.accessLog.Print(&ctx, time.Since(start), s.accessLog.TimeFormat, retcode, method, path, clientIP, in.Payload, respBody, recvBytes, sentBytes, nil, &queryParams, &fields)
	}()

	if in.Id == "" {
		respBody = status.Error(codes.InvalidArgument, "RPC ID must be set").Error()
		sentBytes = len(respBody)
		return nil, errors.New(respBody)
	}

	fn := s.runtime.Rpc(id)
	if fn == nil {
		respBody = status.Error(codes.NotFound, "RPC function not found").Error()
		sentBytes = len(respBody)
		return nil, errors.New(respBody)
	}

	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		respBody = status.Error(codes.Internal, "RPC function could not get incoming context").Error()
		sentBytes = len(respBody)
		return nil, errors.New(respBody)
	}
	for k, vs := range md {
		// Only process the keys representing custom query parameters.
		if strings.HasPrefix(k, "q_") {
			queryParams.Set(k[2:], strings.Join(vs, ","))
		} else {
			headers.Set(k, strings.Join(vs, ","))
		}
	}
	headers.Set(runtime.RUNTIME_CTX_HEADER_RPC_ID, id)
	headers.Del("Authorization")

	if u := ctx.Value(ctxUserIDKey{}); u != nil {
		uid = u.(uuid.UUID).String()
	}
	if u := ctx.Value(ctxUsernameKey{}); u != nil {
		username = u.(string)
	}
	if v := ctx.Value(ctxVarsKey{}); v != nil {
		vars = v.(map[string]string)
	}
	if e := ctx.Value(ctxExpiryKey{}); e != nil {
		expiry = e.(int64)
	}

	// After this point the RPC will be captured in metrics.
	defer func() {
		s.metrics.OnlyRpc(id, time.Since(start), int64(recvBytes), int64(sentBytes), nil)
	}()

	var fnErr error
	var code codes.Code
	respBody, fnErr, code = fn(requestCtx, headers, queryParams, uid, username, vars, expiry, "", clientIP, clientPort, "", in.Payload)
	if fnErr != nil {
		respBody = status.Error(code, fnErr.Error()).Error()
		sentBytes = len(respBody)
		return nil, errors.New(respBody)
	}

	sentBytes = len(respBody)
	return &api.Rpc{Payload: respBody}, nil
}
