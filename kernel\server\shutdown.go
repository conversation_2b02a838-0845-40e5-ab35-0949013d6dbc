// Copyright 2024 The Nakama Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package server

import (
	"context"
	"os"
	"time"

	"kernel/kernel-common/runtime"

	"go.uber.org/zap"
)

func HandleShutdown(ctx context.Context, logger *zap.Logger, matchRegistry MatchRegistry, graceSeconds int, shutdownFns []RuntimeShutdownFunction, c chan os.Signal, reload bool) {
	// If a shutdown grace period is allowed, prepare a timer.
	var timer *time.Timer
	timerCh := make(<-chan time.Time, 1)
	runtimeShutdownFnDone := make(chan struct{}, len(shutdownFns))

	if graceSeconds != 0 {
		graceDuration := time.Duration(graceSeconds) * time.Second
		if len(shutdownFns) > 0 {
			for _, shutdownFn := range shutdownFns {
				go func(fn RuntimeShutdownFunction) {
					shCtx, _ := context.WithTimeoutCause(context.WithoutCancel(ctx), graceDuration, runtime.ErrGracePeriodExpired)
					fn(shCtx, reload)
					runtimeShutdownFnDone <- struct{}{}
				}(shutdownFn)
			}
		}

		timer = time.NewTimer(graceDuration)
		timerCh = timer.C

		logger.Info("Shutdown started - use CTRL^C to force stop server", zap.Int("grace_period_sec", graceSeconds))
	} else {
		// No grace period.
		logger.Info("Shutdown started")
	}

	timerExpired := false
	// Stop any running authoritative matches and do not accept any new ones.
	select {
	case <-matchRegistry.Stop(graceSeconds):
		// Graceful shutdown has completed.
		logger.Info("All authoritative matches stopped")
	case <-timerCh:
		// Timer has expired, terminate matches immediately.
		logger.Info("Shutdown grace period expired")
		<-matchRegistry.Stop(0)
		timerExpired = true
	case <-c:
		// A second interrupt has been received.
		logger.Info("Skipping graceful shutdown")
		<-matchRegistry.Stop(0)
		timerExpired = true // Ensure shutdown function is not awaited.
	}

	// Wait for shutdown functions to complete if grace period is set and hasn't expired.
	if graceSeconds != 0 && len(shutdownFns) > 0 && !timerExpired {
		shutdownFnsCompleted := 0
		for shutdownFnsCompleted < len(shutdownFns) {
			select {
			case <-timerCh:
				logger.Info("Shutdown functions grace period expired")
				shutdownFnsCompleted = len(shutdownFns) // 强制结束等待
			case <-runtimeShutdownFnDone:
				shutdownFnsCompleted++
				logger.Debug("Shutdown function completed", zap.Int("completed", shutdownFnsCompleted), zap.Int("total", len(shutdownFns)))
			case <-c:
				logger.Info("Skipping graceful shutdown")
				shutdownFnsCompleted = len(shutdownFns) // 强制结束等待
			}
		}
	}

	if timer != nil {
		timer.Stop()
	}
}
