// "id" uuid NOT NULL,
//   "username" varchar(128) COLLATE "pg_catalog"."default" NOT NULL,
//   "display_name" varchar(255) COLLATE "pg_catalog"."default",
//   "avatar_url" varchar(512) COLLATE "pg_catalog"."default",
//   "lang_tag" varchar(18) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'en'::character varying,
//   "location" varchar(255) COLLATE "pg_catalog"."default",
//   "timezone" varchar(255) COLLATE "pg_catalog"."default",
//   "metadata" jsonb NOT NULL DEFAULT '{}'::jsonb,
//   "wallet" jsonb NOT NULL DEFAULT '{}'::jsonb,
//   "email" varchar(255) COLLATE "pg_catalog"."default",
//   "password" bytea,
//   "facebook_id" varchar(128) COLLATE "pg_catalog"."default",
//   "google_id" varchar(128) COLLATE "pg_catalog"."default",
//   "gamecenter_id" varchar(128) COLLATE "pg_catalog"."default",
//   "steam_id" varchar(128) COLLATE "pg_catalog"."default",
//   "custom_id" varchar(128) COLLATE "pg_catalog"."default",
//   "edge_count" int4 NOT NULL DEFAULT 0,
//   "create_time" timestamptz(6) NOT NULL DEFAULT now(),
//   "update_time" timestamptz(6) NOT NULL DEFAULT now(),
//   "verify_time" timestamptz(6) NOT NULL DEFAULT '1970-01-01 08:00:00+08'::timestamp with time zone,
//   "disable_time" timestamptz(6) NOT NULL DEFAULT '1970-01-01 08:00:00+08'::timestamp with time zone,
//   "facebook_instant_game_id" varchar(128) COLLATE "pg_catalog"."default",

package bo

import (
	"bytes"
	"fmt"
	"kernel/plugins/common"
	"kernel/plugins/models"
	"reflect"
	"time"

	json "github.com/json-iterator/go"
)

type User struct {
	UserId                string    `json:"id,omitempty"`
	Username              string    `json:"username,omitempty"`
	DisplayName           string    `json:"display_name,omitempty"`
	AvatarUrl             string    `json:"avatar_url,omitempty"`
	LangTag               string    `json:"lang_tag,omitempty"`
	Location              string    `json:"location,omitempty"`
	Timezone              string    `json:"timezone,omitempty"`
	Metadata              string    `json:"metadata,omitempty"`
	Wallet                string    `json:"wallet,omitempty"`
	Email                 string    `json:"email,omitempty"`
	Password              string    `json:"password,omitempty"`
	FacebookId            string    `json:"facebook_id,omitempty"`
	GoogleId              string    `json:"google_id,omitempty"`
	GamecenterId          string    `json:"gamecenter_id,omitempty"`
	SteamId               string    `json:"steam_id,omitempty"`
	CustomId              string    `json:"custom_id,omitempty"`
	EdgeCount             int       `json:"edge_count,omitempty"`
	CreateTime            time.Time `json:"create_time,omitempty"`
	UpdateTime            time.Time `json:"update_time,omitempty"`
	VerifyTime            time.Time `json:"verify_time,omitempty"`
	DisableTime           time.Time `json:"disable_time,omitempty"`
	FacebookInstantGameId string    `json:"facebook_instant_game_id,omitempty"`
	AppleId               string    `json:"apple_id,omitempty"`
	Uin                   int32     `json:"uin,omitempty"`
}

func (f *User) GetTable() string {
	return "users"
}

func (f *User) GetKeyName() string {
	return "id"
}

func (f *User) GetUniqueKeys() []string {
	return []string{"id", "username", "email", "facebook_id", "google_id", "gamecenter_id", "steam_id", "custom_id", "apple_id", "facebook_instant_game_id", "uin"}
}

func (f *User) GetSecondKeyName() string {
	return ""
}

func (f *User) GetRowResultType() models.RowResultType {
	return models.ROWRESULT_TYPE_ONLYONE
}

func (f *User) GetQueryArgs() string {
	return "*"
}

func (f *User) GetFlushType() models.FlushType {
	return models.FLUSH_TYPE_SYNC
}

func (f *User) GetCacheLevel() models.CacheLevel {
	return models.CACHE_LEVEL_IMPORT
}

func (f *User) GetVersionName() string {
	return ""
}

func (f *User) Marshal() ([]byte, error) {
	return json.Marshal(f)
}

func (f *User) MarshalToMap(filter map[string]struct{}) map[string]interface{} {
	dst := make(map[string]interface{})
	err := common.StrcutToMap(f, &dst)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	for k := range filter {
		delete(dst, k)
	}
	return dst
}

func (f *User) Unmarshal(buf []byte) error {
	d := json.NewDecoder(bytes.NewReader(buf))
	d.UseNumber()
	return d.Decode(f)
}

func (f *User) Clear() {
	p := reflect.ValueOf(f).Elem()
	p.Set(reflect.Zero(p.Type()))
}
