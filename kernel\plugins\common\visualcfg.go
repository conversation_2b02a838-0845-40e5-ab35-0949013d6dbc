package common

import (
	"encoding/json"
	"fmt"
	"kernel/kernel-common/runtime"
	"sync"
)

type VisualCfg struct {
	logger      runtime.Logger
	NacosClient *NacosClient
	//VersionConfig  *versionConfig
	SubConfig      sync.Map // key: 模块code, value: md5
	SubKeyCallback sync.Map // key: 模块code, value: 回调函数 VisualConfigEventFunc
	lock           sync.Mutex
}

// 主版本配置
type versionConfig struct {
	CurrentVersion   string                       `json:"currentVersion"`
	UinWhiteList     []string                     `json:"uinWhiteList"`
	ConfigVersionMap map[string]map[string]string `json:"configIndexMap"`
}

// 主配置
const (
	VisualMainConfigDataId = "configVersionControl.json" // 主配置
	VisualPublicGroup      = "PUBLIC_GROUP"              // 公共组 - 存放 主配置 和 非私密配置
	VisualPrivateGroup     = "PRIVATE_GROUP"             // 私有组 - 存放 私密配置
	VisualDefaultGroup     = "DEFAULT_GROUP"             // 默认组
	VisualDefaultLang      = "defaultLang"               // 默认语言
)

// 配置改变事件回调函数
type VisualConfigEventFunc func(dataId, data string)

func NewVisualCfg(logger runtime.Logger, nacosClient *NacosClient) *VisualCfg {
	return &VisualCfg{
		logger:      logger,
		NacosClient: nacosClient,
	}
}

// 监听 主配置
// 主配置 包含 主配置 和 主配置的 子配置。主配置变化了，则递归刷新所有子配置
func (n *VisualCfg) ListenMainConfig() error {
	return n.NacosClient.ListenConfig(VisualMainConfigDataId, VisualDefaultGroup, func(namespace, group, dataId, data string) {
		// 主配置变化时，刷新所有层级配置
		n.refreshAllConfigs(data)
	})
}

// refreshAllConfigs 刷新所有层级的配置
func (n *VisualCfg) refreshAllConfigs(mainConfigContent string) {
	n.lock.Lock()
	defer n.lock.Unlock()

	// 解析主配置，获取二级配置引用列表
	mainConfig := &versionConfig{}

	if err := json.Unmarshal([]byte(mainConfigContent), mainConfig); err != nil {
		n.logger.Error("parse main config error: %v", err)
		return
	}

	// 处理二级配置，获取最大版本号
	maxVersion := ""
	for version := range mainConfig.ConfigVersionMap {
		if version > maxVersion || maxVersion == "" {
			maxVersion = version
		}
	}
	n.logger.Info("VisualCfg max version %s", maxVersion)
	langConfigMapMd5, ok := mainConfig.ConfigVersionMap[maxVersion][VisualDefaultLang]
	if !ok {
		n.logger.Error("max version %s lang config not found", maxVersion)
		return
	}

	// 读取配置
	subContent, err := n.GetConfigWithFallback(langConfigMapMd5)
	if err != nil {
		n.logger.Error("get sub config failed [%s]: %v", langConfigMapMd5, err)
		return
	}

	// 解析二级配置
	var newSubConfig map[string]string
	if err := json.Unmarshal([]byte(subContent), &newSubConfig); err != nil {
		n.logger.Error("parse sub config failed [%s]: %v, %s", langConfigMapMd5, err, subContent)
		return
	}

	// 更新主版本配置
	//n.VersionConfig = mainConfig

	// {"registre":"b756d02efc791f766650c1f559654b53","mall_test_cfg":"a6795ae7543e441537eff9756c0c9549"}
	for moduleKey, moduleMd5 := range newSubConfig {
		if md5, ok := n.SubConfig.Load(moduleKey); !ok || moduleMd5 != md5 {
			callback, _ := n.SubKeyCallback.Load(moduleKey)
			if callback == nil {
				continue
			}

			thirdContent, err := n.GetConfigWithFallback(moduleMd5)
			if err != nil {
				n.logger.Error("get third config failed [%s, %s]: %v", moduleKey, moduleMd5, err)
				continue
			}
			// 通知更新
			callback.(VisualConfigEventFunc)(moduleKey, thirdContent)
		}
		n.SubConfig.Store(moduleKey, moduleMd5)
	}
}

// 根据 dataId 获取配置，如果 Private Group 没有找到，则尝试从 Public Group 获取
func (n *VisualCfg) GetConfigWithFallback(dataIdMd5 string) (string, error) {
	// 先尝试从 Private Group 获取配置
	content, err := n.NacosClient.GetConfig(dataIdMd5, VisualPrivateGroup)

	if err == nil && content != "" {
		return content, nil
	}

	// 如果 Private Group 没有找到，尝试从 Public Group 获取
	content, err = n.NacosClient.GetConfig(dataIdMd5, VisualPublicGroup)

	if err != nil {
		return "", fmt.Errorf("config not found in both groups [%s, %s]: %v", dataIdMd5, VisualPublicGroup, err)
	}

	return content, nil
}

// dataId 是模块code
func (n *VisualCfg) ListenConfig(dataId string, callback VisualConfigEventFunc) error {
	n.SubKeyCallback.Store(dataId, callback)
	if md5, ok := n.SubConfig.Load(dataId); ok {
		content, err := n.GetConfigWithFallback(md5.(string))
		if err != nil {
			return fmt.Errorf("get config failed [%s]: %v", md5.(string), err)
		}
		callback(dataId, content)
	}
	return nil
}

// 取消监听
func (n *VisualCfg) UnListenConfig(dataId string) {
	n.SubKeyCallback.Delete(dataId)
}
