package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"sync"

	"go.uber.org/zap/zapcore"
)

type LogicServerModule struct {
	name            string
	logger          runtime.Logger
	db              *sql.DB
	nk              runtime.NakamaModule
	config          runtime.Config // nakama 配置
	CustomConfig    *CustomConfig  // 自定义配置
	isRegisterSelf  bool
	ProxyPassConfig sync.Map
	controller      *Controller

	common  *logic.CommonGlobalDataStruct
	online  *logic.OnlineGlobalDataStruct
	send    *logic.SendGlobalDataStruct
	dbagent *logic.DbAgentGlobalDataStruct
	channel *logic.ChannelGlobalDataStruct
	stream  *logic.StreamGlobalDataStruct
	notify  *logic.NotifyGlobalDataStruct

	//selflog *log.SelfLogger
}

var LogicServerData *LogicServerModule

func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
	LogicServerData = new(LogicServerModule)
	LogicServerData.name = models.SERVER_NAME_LOGIC
	LogicServerData.logger = logger
	LogicServerData.db = db
	LogicServerData.nk = nk
	LogicServerData.common = logic.NewCommonGlobalDataStruct()
	LogicServerData.online = logic.NewOnlineGlobalDataStruct(LogicServerData)
	LogicServerData.send = logic.NewSendGlobalDataStruct(LogicServerData)
	LogicServerData.dbagent = logic.NewDbAgentGlobalDataStruct(LogicServerData)
	LogicServerData.channel = logic.NewChannelGlobalDataStruct(LogicServerData)
	LogicServerData.stream = logic.NewStreamGlobalDataStruct(LogicServerData)
	LogicServerData.notify = logic.NewNotifyGlobalDataStruct(LogicServerData)
	config, err := initializer.GetConfig()
	if err != nil {
		return err
	}
	LogicServerData.config = config
	if err := LogicServerData.common.Init(LogicServerData, LogicServerData.CustomConfig, LogicServerData.customConfigEvent); err != nil {
		logger.Error("init common logic error %v", err)
		return err
	}
	// LogicServerData.selflog, err = log.NewLogger(slog.LevelDebug, "./log", "json", "logicserver", false)
	// if err != nil {
	// 	logger.Error("init logicserver log error %v", err)
	// 	return err
	// }

	initializer.RegisterShutdown(LogicServerData.Shutdown)

	// 自定义路由注册
	LogicServerData.controller = NewController(LogicServerData)
	initializer.RegisterRpc(models.RPCID_LOGICSERVER_LOGINFINISH, LogicServerData.controller.LoginFinish)
	initializer.RegisterRpc(RPCID_LOGICSERVER_CREATEROLE, LogicServerData.controller.CreateRole)
	initializer.RegisterRpc(RPCID_LOGICSERVER_GETFAVORITEROOMS, LogicServerData.controller.GetFavoriteRooms)
	initializer.RegisterRpc(RPCID_LOGICSERVER_ADDFAVORITEROOM, LogicServerData.controller.AddFavoriteRoom)
	initializer.RegisterRpc(RPCID_LOGICSERVER_DELFAVORITEROOMS, LogicServerData.controller.DelFavoriteRooms)
	initializer.RegisterRpc(RPCID_LOGICSERVER_GETHISTORYROOMS, LogicServerData.controller.GetHistoryRooms)
	initializer.RegisterRpc(RPCID_LOGICSERVER_ADDHISTORYROOM, LogicServerData.controller.AddHistoryRoom)
	initializer.RegisterRpc(RPCID_LOGICSERVER_DELHISTORYROOMS, LogicServerData.controller.DelHistoryRooms)
	initializer.RegisterRpc(RPCID_LOGICSERVER_QUERYSTATUS, LogicServerData.controller.QueryStatus)

	// 举报相关接口
	initializer.RegisterRpc(RPCID_LOGICSERVER_GETUPLOADURL, LogicServerData.controller.GetUploadURL)
	initializer.RegisterRpc(RPCID_LOGICSERVER_REPORT, LogicServerData.controller.Report)
	initializer.RegisterRpc(RPCID_LOGICSERVER_QUERYREPORT, LogicServerData.controller.QueryReport)
	initializer.RegisterRpc(RPCID_LOGICSERVER_REPORTCALLBACK, LogicServerData.controller.ReportCallback)

	return nil
}

func (s *LogicServerModule) GetCommon() *logic.CommonGlobalDataStruct {
	return s.common
}
func (s *LogicServerModule) GetOnline() *logic.OnlineGlobalDataStruct {
	return s.online
}
func (s *LogicServerModule) GetSend() *logic.SendGlobalDataStruct {
	return s.send
}
func (s *LogicServerModule) GetDbAgent() *logic.DbAgentGlobalDataStruct {
	return s.dbagent
}
func (s *LogicServerModule) GetNotify() *logic.NotifyGlobalDataStruct {
	return s.notify
}
func (s *LogicServerModule) GetStream() *logic.StreamGlobalDataStruct {
	return s.stream
}
func (s *LogicServerModule) GetChannel() *logic.ChannelGlobalDataStruct {
	return s.channel
}
func (s *LogicServerModule) GetLogger() runtime.Logger {
	return s.logger
}
func (s *LogicServerModule) GetConfig() runtime.Config {
	return s.config
}
func (s *LogicServerModule) GetName() string {
	return s.name
}
func (s *LogicServerModule) GetDb() *sql.DB {
	return s.db
}
func (s *LogicServerModule) GetNk() runtime.NakamaModule {
	return s.nk
}

// 配置读取回调
func (s *LogicServerModule) customConfigEvent(dataId string, newobj any) {
	tmpobj, ok := newobj.(*CustomConfig)
	if !ok {
		return
	}
	if tmpobj.Config == nil {
		tmpobj.Config = &LogicServerConfig{}
	}
	defaultNodeCfg := false
	if tmpobj.Config.NodeConfig == nil {
		// 使用默认节点配置
		scheme := "http"
		if s.config.GetSocket().GetUseTLS() {
			scheme = "https"
		}
		tmpobj.Config.NodeConfig = &models.NodeConfig{
			NodeId:   s.config.GetName(),
			SvcGroup: fmt.Sprintf("%s@@%s", s.name, models.NACOS_DEFAULT_GROUP),
			Host:     fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			NetHost:  fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			AuthKey:  s.config.GetRuntime().GetHTTPKey(),
			Weight:   10,
			Scheme:   scheme,
		}
		defaultNodeCfg = true
	}
	tmp, _ := json.Marshal(tmpobj)
	s.logger.Debug("customConfigEvent defaultNodeCfg: %v, customCfg: %s", defaultNodeCfg, string(tmp))
	s.common.SetNodeConfig(tmpobj.Config.NodeConfig)
	s.common.WatchNodeList.InitWatchSvr(tmpobj.Config.WatchSvcGroupList, tmpobj.Config.NodeConfig.SvcGroup)
	s.CustomConfig = tmpobj

	// 注册服务
	selfcfg := map[string]interface{}{}
	if err := common.StrcutToMap(tmpobj.Config.NodeConfig, &selfcfg); err == nil {
		if !s.isRegisterSelf {
			go s.common.RegisterAndUpdateSelfToNacos(false, selfcfg)
			s.isRegisterSelf = true
		} else {
			go s.common.RegisterAndUpdateSelfToNacos(true, selfcfg)
		}
	} else {
		s.logger.Error("register self, config error %v", err)
	}

	s.common.UpdateAccessLogConfig(tmpobj.AccessLog)
	loglevel, err := zapcore.ParseLevel(tmpobj.Logger.Level)
	if err != nil {
		s.logger.Error("parse log level error %v", err)
	} else {
		s.nk.SetLoggerLevel(loglevel)
	}
}

func (s *LogicServerModule) Shutdown(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, reload bool) {
	logger.Info("%s shutdown", s.name)
	if !reload {
		s.common.UnRegisterSelfToNacos()
	}
	// s.selflog.Sync()
}
