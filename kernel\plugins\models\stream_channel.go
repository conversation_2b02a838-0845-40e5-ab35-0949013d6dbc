package models

import (
	"kernel/kernel-common/runtime"
	"sync/atomic"
)

type StreamReq struct {
	Mode             uint8       `json:"mode,omitempty"`
	Subject          string      `json:"subject,omitempty"`
	Subcontext       string      `json:"subcontext,omitempty"`
	Label            string      `json:"label,omitempty"`
	Hidden           bool        `json:"hidden,omitempty"`
	IncludeHidden    bool        `json:"include_hidden,omitempty"`
	IncludeNotHidden bool        `json:"include_not_hidden,omitempty"`
	UserId           string      `json:"user_id,omitempty"`
	SessionId        string      `json:"session_id,omitempty"`
	Reliable         bool        `json:"reliable,omitempty"`
	Persistence      bool        `json:"persistence,omitempty"`
	Status           string      `json:"status,omitempty"`
	Presences        []*Presence `json:"presences,omitempty"`
	Data             string      `json:"data,omitempty"` // must be rtapi.Envelope struct
}

type StreamUserListResp struct {
	Code      ErrorCode   `json:"code,omitempty"`
	Msg       string      `json:"message,omitempty"`
	Presences []*Presence `json:"presences,omitempty"`
}

type StreamUserGetResp struct {
	Code ErrorCode     `json:"code,omitempty"`
	Msg  string        `json:"message,omitempty"`
	Meta *PresenceMeta `json:"meta,omitempty"`
}

type StreamCountResp struct {
	Code  ErrorCode `json:"code,omitempty"`
	Msg   string    `json:"message,omitempty"`
	Count int       `json:"count,omitempty"`
}

type SessionFormat uint8

const (
	SessionFormatJson SessionFormat = iota
	SessionFormatProtobuf
)

const (
	StreamModeNotifications uint8 = iota
	StreamModeStatus
	StreamModeChannel
	StreamModeGroup
	StreamModeDM
	StreamModeMatchRelayed
	StreamModeMatchAuthoritative
	StreamModeParty
)

type PresenceID struct {
	Node      string `json:"node,omitempty"`
	SessionID string `json:"session_id,omitempty"`
}

type PresenceStream struct {
	Mode       uint8  `json:"mode,omitempty"`
	Subject    string `json:"subject,omitempty"`
	Subcontext string `json:"subcontext,omitempty"`
	Label      string `json:"label,omitempty"`
}

type PresenceMeta struct {
	Format      SessionFormat `json:"format,omitempty"`
	Hidden      bool          `json:"hidden,omitempty"`
	Persistence bool          `json:"persistence,omitempty"`
	Username    string        `json:"username,omitempty"`
	Status      string        `json:"status,omitempty"`
	Reason      uint32        `json:"reason,omitempty"`
}

func (pm *PresenceMeta) GetHidden() bool {
	return pm.Hidden
}
func (pm *PresenceMeta) GetPersistence() bool {
	return pm.Persistence
}
func (pm *PresenceMeta) GetUsername() string {
	return pm.Username
}
func (pm *PresenceMeta) GetStatus() string {
	return pm.Status
}
func (pm *PresenceMeta) GetReason() runtime.PresenceReason {
	return runtime.PresenceReason(atomic.LoadUint32(&pm.Reason))
}

type Presence struct {
	ID     PresenceID     `json:"id,omitempty"`
	Stream PresenceStream `json:"stream,omitempty"`
	UserID string         `json:"user_id,omitempty"`
	Meta   PresenceMeta   `json:"meta,omitempty"`
}

func (p *Presence) GetUserId() string {
	return p.UserID
}
func (p *Presence) GetSessionId() string {
	return p.ID.SessionID
}
func (p *Presence) GetNodeId() string {
	return p.ID.Node
}
func (p *Presence) GetHidden() bool {
	return p.Meta.Hidden
}
func (p *Presence) GetPersistence() bool {
	return p.Meta.Persistence
}
func (p *Presence) GetUsername() string {
	return p.Meta.Username
}
func (p *Presence) GetStatus() string {
	return p.Meta.Status
}
func (p *Presence) GetReason() runtime.PresenceReason {
	return runtime.PresenceReason(atomic.LoadUint32(&p.Meta.Reason))
}

const (
	ChannelMessageTypeChat int32 = iota
	ChannelMessageTypeChatUpdate
	ChannelMessageTypeChatRemove
	ChannelMessageTypeGroupJoin
	ChannelMessageTypeGroupAdd
	ChannelMessageTypeGroupLeave
	ChannelMessageTypeGroupKick
	ChannelMessageTypeGroupPromote
	ChannelMessageTypeGroupBan
	ChannelMessageTypeGroupDemote
)

type ChannelIdToStreamResult struct {
	Stream PresenceStream
}

type channelMessageListCursor struct {
	StreamMode       uint8
	StreamSubject    string
	StreamSubcontext string
	StreamLabel      string
	CreateTime       int64
	Id               string
	Forward          bool
	IsNext           bool
}

// 系统通知码
const (
	NotificationCodeDmRequest        int32 = -1
	NotificationCodeFriendRequest    int32 = -2
	NotificationCodeFriendAccept     int32 = -3
	NotificationCodeGroupAdd         int32 = -4
	NotificationCodeGroupJoinRequest int32 = -5
	NotificationCodeFriendJoinGame   int32 = -6
	NotificationCodeSingleSocket     int32 = -7
	NotificationCodeUserBanned       int32 = -8
)

// 自定义通知码
const (
	NotificationCodeRealMsg int32 = 1 // 通用即时消息推送
)
