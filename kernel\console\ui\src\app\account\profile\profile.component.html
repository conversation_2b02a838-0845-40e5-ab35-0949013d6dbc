<ngb-alert [dismissible]="false" type="danger" class="mb-3" *ngIf="error">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">An error occurred: {{error}}</h6>
</ngb-alert>

<ngb-alert [dismissible]="false" type="success" class="mb-3" *ngIf="updated">
  <img src="/static/svg/green-tick.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">Account was modified successfully.</h6>
</ngb-alert>

<form [formGroup]="accountForm" (ngSubmit)="updateAccount()" class="add-border">
  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="user_id">User ID</label>
      </div>
      <input type="text" id="user_id" [value]="account.user.id" class="form-control-plaintext form-control-sm my-2" placeholder="User ID" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="username">Username</label>
      </div>
      <input type="text" id="username" class="form-control form-control-sm my-2" placeholder="Username" required formControlName="username" [ngClass]="{'is-invalid': f.username.dirty && f.username.errors}">
      <div class="invalid-tooltip" [hidden]="f.username.disabled || f.username.valid || f.username.pristine">Username is required</div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="display_name">Display Name</label>
      </div>
      <input type="text" id="display_name" class="form-control form-control-sm my-2" placeholder="Display Name" formControlName="display_name">
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline align-self-center">
      <div class="col-3 pl-0">
        <label class="d-inline" for="avatar_url">Avatar URL</label>
      </div>
      <input type="text" id="avatar_url" class="form-control form-control-sm my-2" placeholder="Avatar URL" formControlName="avatar_url">
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="location">Location</label>
      </div>
      <input type="text" id="location" class="form-control form-control-sm my-2" placeholder="Location" formControlName="location">
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline align-self-center">
      <div class="col-3 pl-0">
        <label class="d-inline" for="timezone">Timezone</label>
      </div>
      <input type="text" id="timezone" class="form-control form-control-sm my-2" placeholder="Timezone" formControlName="timezone">
    </div>
  </div>

  <div class="row mb-3 add-border-single-row-bottom">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="create_time">Create Time</label>
      </div>
      <input type="text" id="create_time" [value]="account.user.create_time" class="form-control-plaintext form-control-sm my-2" placeholder="Create Time" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="update_time">Update Time</label>
      </div>
      <input type="text" id="update_time" [value]="account.user.update_time" class="form-control-plaintext form-control-sm my-2" placeholder="Update Time" disabled readonly>
    </div>
  </div>

  <div class="card p-2 mb-3 jsoneditor" style="height: 400px">
    <div #editor style="height: 400px"></div>
  </div>
  <button type="submit" class="btn btn-primary" [disabled]="updating" *ngIf="updateAllowed()">Save</button>
</form>
