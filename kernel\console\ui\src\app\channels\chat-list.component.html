<h2 class="pb-1">Chat Messages</h2>

<div class="btn-group mb-1" ngbDropdown>
  <button type="button" class="btn btn-outline-secondary" ngbDropdownToggle>
    <span *ngIf="!activeFilter || activeFilter === ''">Filter by type</span>
    <span *ngIf="activeFilter && activeFilter !== ''">{{activeFilter}}</span>
  </button>
  <div class="dropdown-menu" ngbDropdownMenu>
    <button *ngFor="let f of filters" type="button" ngbDropdownItem (click)="activeFilter = f;">{{f}}</button>
  </div>
</div>
<div class="row no-gutters mb-4">
  <div class="col d-flex justify-content-between no-gutters align-items-center">
    <div class="col-md-9">
      <form [hidden]="activeFilter !== 'Chat Room'" [formGroup]="searchForm1">
        <div class="input-group">
          <input type="text" class="form-control border-right-0" formControlName="label" placeholder="Search by chat room label"/>
          <div class="input-group-append">
            <div class="btn-group">
              <button type="submit" class="btn btn-primary dropdown-radius-left" (click)="type=2; search(0)">Search</button>
            </div>
          </div>
        </div>
      </form>
      <form [hidden]="activeFilter !== 'Group Chat'" [formGroup]="searchForm2">
        <div class="input-group">
          <input type="text" class="form-control border-right-0" formControlName="group_id" placeholder="Search by group ID"/>
          <div class="input-group-append">
            <div class="btn-group">
              <button type="submit" class="btn btn-primary dropdown-radius-left" (click)="type=3; search(0)">Search
              </button>
            </div>
          </div>
        </div>
      </form>
      <form [hidden]="activeFilter !== 'Direct Chat'" [formGroup]="searchForm3">
        <div class="input-group">
          <input type="text" class="form-control border-right-0" formControlName="user_id_one" placeholder="Search by user ID 1"/>
          <div class="input-group-append">
          <span class="input-group-text" (click)="f3.user_id_one.setValue(systemUserId);">
            <img class="mr-1" src="/static/svg/purple-cog-1.svg" alt="" width="20" height=""></span>
          </div>
          <input type="text" class="form-control border-right-0" formControlName="user_id_two" placeholder="Search by user ID 2"/>
          <div class="input-group-append">
            <div class="btn-group">
              <button type="submit" class="btn btn-primary dropdown-radius-left" (click)="type=4; search(0)">Search
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
    <div class="col-md-3 justify-content-end text-right">
      <div class="btn-group page-btns" role="group" aria-label="Search">
        <button type="button" class="btn btn-outline-secondary" (click)="search(0)" [disabled]="messages.length === 0">
          <img src="/static/svg/page-first.svg" alt="" width="20" height=""></button>
        <button type="button" class="btn btn-outline-secondary" (click)="search(1)" [disabled]="nextCursor === '' || nextCursor === null"><img
          src="/static/svg/page-next.svg" alt="" width="20" height=""></button>
      </div>
    </div>
  </div>
</div>

<h6 *ngIf="messages.length != 0 && error === '' && type==2">Showing results for chat room label: {{this.f1.label.value}}</h6>
<h6 *ngIf="messages.length != 0 && error === '' && type==3">Showing results for group ID:
  <a [routerLink]="['/groups', this.route.snapshot.queryParamMap.get('group_id')]" style="width: 100%">{{this.f2.group_id.value}}</a>
</h6>
<h6 *ngIf="messages.length != 0 && error === '' && type==4">Showing results for user IDs:
  <a [routerLink]="['/accounts', this.route.snapshot.queryParamMap.get('user_id_one')]" style="width: 100%">{{this.f3.user_id_one.value}}</a>,
  <a [routerLink]="['/accounts', this.route.snapshot.queryParamMap.get('user_id_two')]" style="width: 100%">{{this.f3.user_id_two.value}}</a>
</h6>

<ngb-alert [dismissible]="false" type="danger" class="mb-3" *ngIf="error">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">Error when querying messages: {{error}}</h6>
</ngb-alert>

<div class="row no-gutters">
  <table class="table table-sm table-hover table-bordered" style="table-layout: fixed;">
    <thead class="thead-light">
    <tr>
      <th style="width: 60px">Code</th>
      <th style="width: 320px">Sender ID</th>
      <th style="width: 150px">Username</th>
      <th>Content</th>
      <th style="width: 180px">Create Time</th>
      <th style="width: 90px" *ngIf="deleteAllowed()">Remove</th>
    </tr>
    </thead>
    <tbody>
    <tr *ngIf="messages.length === 0">
      <td [attr.colspan]="deleteAllowed()?6:5" class="text-muted">No messages found.</td>
    </tr>

    <ng-template ngFor let-item [ngForOf]="messages" let-i="index">
      <tr>
        <td>{{item.code}}</td>
        <td (click)="viewAccount(item)">{{item.sender_id}}</td>
        <td (click)="viewAccount(item)" style="text-overflow:ellipsis; overflow: hidden;">{{item.username}}</td>
        <td style="white-space: nowrap; text-overflow:ellipsis; overflow: hidden;">
          <div class="arrow" (click)="messageStatesOpen[i]=!messageStatesOpen[i];">
            <div class="arrow-right" *ngIf="!messageStatesOpen[i]"></div>
            <div class="arrow-down" *ngIf="messageStatesOpen[i]"></div>
          </div>
          {{item.content}}
        </td>
        <td>{{item.create_time}}</td>
        <td *ngIf="deleteAllowed() && item.sender_id === systemUserId"></td>
        <td *ngIf="deleteAllowed() && item.sender_id !== systemUserId" class="text-center">
          <button type="button" class="btn btn-sm btn-danger" (click)="deleteMessage($event, i, item);">Delete</button>
        </td>
      </tr>
      <tr *ngIf="messageStatesOpen[i]" class="open-row">
        <td [attr.colspan]="deleteAllowed()?6:5">
          {{item.content}}
        </td>
      </tr>
    </ng-template>
    </tbody>
  </table>
</div>

<ngb-alert [dismissible]="false" type="danger" *ngIf="deleteError">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">Failed to delete data.</h6>
  <p class="mb-0 pl-4">{{deleteError}}</p>
</ngb-alert>

<ngb-alert type="success" *ngIf="deleteSuccess" [dismissible]="true" (close)="deleteSuccess=false">
  <img src="/static/svg/green-tick.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">{{totalDeleted}} messages {{totalDeleted > 0?"successfully":""}} deleted.</h6>
</ngb-alert>

<div class="d-flex justify-content-between align-items-center" role="alert">
  <button *ngIf="deleteMessagesAllowed()" [disabled]="deleting" (click)="deleteData()" type="button" class="btn btn-danger">Delete messages</button>
</div>

