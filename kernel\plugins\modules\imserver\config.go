package main

import (
	"kernel/plugins/models"
)

type CustomConfig struct {
	Config    *ImServerConfig    `json:"im_server_config,omitempty" yaml:"im_server_config,omitempty"`
	AccessLog *models.AccessLogConfig `json:"access_log,omitempty" yaml:"access_log,omitempty"`
	Logger    *models.LoggerConfig    `json:"logger,omitempty" yaml:"logger,omitempty"`
}

type ImServerConfig struct {
	NodeConfig        *models.NodeConfig `json:"node_config,omitempty" yaml:"node_config,omitempty"`
	WatchSvcGroupList []string           `json:"watch_svc_group_list,omitempty" yaml:"watch_svc_group_list,omitempty"`
}
