package main

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/gob"
	"errors"
	"fmt"
	"kernel/kernel-common/api"
	"kernel/kernel-common/runtime"
	commbo "kernel/plugins/bo"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"kernel/plugins/modules/friendserver/bo"
	"sort"
	"strings"
	"sync"
	"time"

	json "github.com/json-iterator/go"

	"github.com/gofrs/uuid/v5"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

type Controller struct {
	server *FriendServerModule
	notify *logic.NotifyGlobalDataStruct
}

func NewController(s *FriendServerModule, n *logic.NotifyGlobalDataStruct) *Controller {
	return &Controller{
		server: s,
		notify: n,
	}
}

// 获取好友列表，包括(好友信息, 好友状态)
// state: 0	用户为双向好友, 1 用户A已发送邀请，等待用户B接受, 2	用户A已收到邀请但尚未接受, 3 用户A已封禁用户B
func (c *Controller) FriendsListDetail(ctx context.Context, userID string, limit int, state *int, cursor string) ([]*api.Friend, string, error) {
	var incomingCursor *edgeListCursor
	if cursor != "" {
		cb, err := base64.StdEncoding.DecodeString(cursor)
		if err != nil {
			return nil, "", runtime.ErrFriendInvalidCursor
		}
		incomingCursor = &edgeListCursor{}
		if err := gob.NewDecoder(bytes.NewReader(cb)).Decode(incomingCursor); err != nil {
			return nil, "", runtime.ErrFriendInvalidCursor
		}

		// Cursor and filter mismatch. Perhaps the caller has sent an old cursor with a changed filter.
		if state != nil && int64(*state) != incomingCursor.State {
			return nil, "", runtime.ErrFriendInvalidCursor
		}
	}

	// 从缓存中获取数据
	tmpbo := bo.UserEdge{}
	obj := c.server.dbagent.Create(ctx, &tmpbo, models.WithKeys(map[string]interface{}{tmpbo.GetKeyName(): userID})).WithOption(logic.WithAutoRLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()

	list := []*bo.UserEdge{}
	err := obj.Query(ctx).As(&list)
	if err != nil {
		if err == models.ErrDBResEmpty {
			return []*api.Friend{}, "", nil
		}
		return nil, "", err
	}

	// 根据state过滤数据
	tmplist := []*bo.UserEdge{}
	if state != nil {
		for _, v := range list {
			if v.State == *state {
				tmplist = append(tmplist, v)
			}
		}
	} else {
		// 如果state为nil，则使用所有数据
		tmplist = list
	}

	// 根据incomingCursor过滤数据
	tmplist2 := []*bo.UserEdge{}
	if incomingCursor != nil {
		for _, v := range tmplist {
			if state == nil {
				// 修复类型不匹配问题：v.State是int类型，incomingCursor.State是int64类型
				if int64(v.State) >= incomingCursor.State && v.Position >= incomingCursor.Position {
					tmplist2 = append(tmplist2, v)
				}
			} else {
				if v.Position >= incomingCursor.Position {
					tmplist2 = append(tmplist2, v)
				}
			}
		}
	} else {
		// 如果incomingCursor为nil，则使用所有过滤后的数据
		tmplist2 = tmplist
	}

	// state ASC, position ASC
	sort.Slice(tmplist2, func(i, j int) bool {
		return tmplist2[i].State < tmplist2[j].State || (tmplist2[i].State == tmplist2[j].State && tmplist2[i].Position < tmplist2[j].Position)
	})

	// 根据limit过滤数据
	var outgoingCursor string
	friends := make([]*api.Friend, 0, limit)

	// 检查是否需要生成游标
	needCursor := limit != 0 && len(tmplist2) > limit

	// 如果需要限制结果数量
	if limit != 0 && len(tmplist2) > limit {
		tmplist2 = tmplist2[:limit]
	}

	// 查询好友信息
	uids := make([]string, 0, len(tmplist2))
	friendsChan := make(chan *api.Friend, len(tmplist2))

	// 使用信号量控制并发数为8
	semaphore := make(chan struct{}, 8)

	// 等待所有查询完成
	var wg sync.WaitGroup

	for _, v := range tmplist2 {
		wg.Add(1)
		tmpfriend := v

		c.server.common.GroutinePool.Submit(func() {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			user := commbo.User{}
			userobj := c.server.dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{user.GetKeyName(): tmpfriend.DestinationId})).WithOption(logic.WithAutoRLock())

			defer func() {
				c.server.dbagent.Release(userobj)
			}()

			err := userobj.Query(ctx).As(&user)
			if err != nil {
				c.server.logger.Error("Failed to query friend user, user_id:%s, error:%v", tmpfriend.DestinationId, err)
				return
			}

			apiUser := &api.User{
				Id:           user.UserId,
				Username:     user.Username,
				DisplayName:  user.DisplayName,
				AvatarUrl:    user.AvatarUrl,
				LangTag:      user.LangTag,
				Location:     user.Location,
				Timezone:     user.Timezone,
				Metadata:     user.Metadata,
				FacebookId:   user.FacebookId,
				GoogleId:     user.GoogleId,
				GamecenterId: user.GamecenterId,
				SteamId:      user.SteamId,
				EdgeCount:    int32(user.EdgeCount),
				//Online: 后设置
				CreateTime:            &timestamppb.Timestamp{Seconds: user.CreateTime.Unix()},
				UpdateTime:            &timestamppb.Timestamp{Seconds: user.UpdateTime.Unix()},
				FacebookInstantGameId: user.FacebookInstantGameId,
				AppleId:               user.AppleId,
			}

			friendsChan <- &api.Friend{
				User: apiUser,
				State: &wrapperspb.Int32Value{
					Value: int32(tmpfriend.State),
				},
				UpdateTime: &timestamppb.Timestamp{Seconds: tmpfriend.UpdateTime.Unix()},
			}
		})
	}

	// 等待所有goroutine完成
	c.server.common.GroutinePool.Submit(func() {
		wg.Wait()
		close(friendsChan)
	})

	// 收集结果
	for friend := range friendsChan {
		friends = append(friends, friend)
		uids = append(uids, friend.User.Id)
	}

	// 如果需要生成游标且有数据
	if needCursor && len(tmplist2) > 0 {
		lastItem := tmplist2[len(tmplist2)-1]
		cursorBuf := new(bytes.Buffer)
		stateValue := int64(lastItem.State)
		if state != nil {
			stateValue = int64(*state)
		}
		if err := gob.NewEncoder(cursorBuf).Encode(&edgeListCursor{State: stateValue, Position: lastItem.Position}); err != nil {
			c.server.logger.Error("Error creating friend list cursor, error:%v", err)
			return nil, "", err
		}
		outgoingCursor = base64.StdEncoding.EncodeToString(cursorBuf.Bytes())
	}

	// 设置在线状态
	if len(uids) > 0 {
		onlineUsers, err := c.server.online.QueryOnlineUsers(ctx, uids)
		if err != nil {
			c.server.logger.Error("Failed to query online users, error:%v", err)
			return nil, "", err
		}
		for _, v := range friends {
			if online, ok := onlineUsers[v.User.Id]; ok {
				v.User.Online = online
			} else {
				v.User.Online = false
			}
		}
	}

	return friends, outgoingCursor, nil
}

// 获取好友列表，只有好友id, 不包括(好友信息, 好友在线状态)
func (c *Controller) FriendsListOnlyId(ctx context.Context, userID string, state *int) ([]string, error) {
	// 从缓存中获取数据
	tmpbo := bo.UserEdge{}
	obj := c.server.dbagent.Create(ctx, &tmpbo, models.WithKeys(map[string]interface{}{tmpbo.GetKeyName(): userID})).WithOption(logic.WithAutoRLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()

	list := []*bo.UserEdge{}
	err := obj.Query(ctx).As(&list)
	if err != nil {
		if err == models.ErrDBResEmpty {
			return []string{}, nil
		}
		return nil, err
	}

	// 根据state过滤数据
	tmplist := []*bo.UserEdge{}
	if state != nil {
		for _, v := range list {
			if v.State == *state {
				tmplist = append(tmplist, v)
			}
		}
	} else {
		// 如果state为nil，则使用所有数据
		tmplist = list
	}

	// 查询好友信息
	friend_uids := make([]string, 0, len(tmplist))
	for _, v := range tmplist {
		friend_uids = append(friend_uids, v.DestinationId)
	}

	return friend_uids, nil
}

// 获取好友列表，只有好友id，好友状态, 不包括好友信息
func (c *Controller) FriendsListStatus(ctx context.Context, userID string, state *int) ([]*api.Friend, error) {
	// 从缓存中获取数据
	tmpbo := bo.UserEdge{}
	obj := c.server.dbagent.Create(ctx, &tmpbo, models.WithKeys(map[string]interface{}{tmpbo.GetKeyName(): userID})).WithOption(logic.WithAutoRLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()

	list := []*bo.UserEdge{}
	err := obj.Query(ctx).As(&list)
	if err != nil {
		if err == models.ErrDBResEmpty {
			return []*api.Friend{}, nil
		}
		return nil, err
	}

	// 根据state过滤数据
	tmplist := []*bo.UserEdge{}
	if state != nil {
		for _, v := range list {
			if v.State == *state {
				tmplist = append(tmplist, v)
			}
		}
	} else {
		// 如果state为nil，则使用所有数据
		tmplist = list
	}

	// 查询好友信息
	uids := make([]string, 0, len(tmplist))
	friends := make([]*api.Friend, 0, len(tmplist))
	for _, v := range tmplist {
		uids = append(uids, v.DestinationId)
		friends = append(friends, &api.Friend{
			User: &api.User{
				Id: v.DestinationId,
			},
			State: &wrapperspb.Int32Value{
				Value: int32(v.State),
			},
		})
	}

	// 设置在线状态
	if len(uids) > 0 {
		onlineUsers, err := c.server.online.QueryOnlineUsers(ctx, uids)
		if err != nil {
			c.server.logger.Error("Failed to query online users, error:%v", err)
			return nil, err
		}
		for _, v := range friends {
			if online, ok := onlineUsers[v.User.Id]; ok {
				v.User.Online = online
			} else {
				v.User.Online = false
			}
		}
	}

	return friends, nil
}

func (c *Controller) FriendsOfFriendsList(ctx context.Context, userID string, limit int, cursor string) ([]*api.FriendsOfFriendsList_FriendOfFriend, string, error) {
	return c.server.nk.FriendsOfFriendsList(ctx, userID, limit, cursor)
}

func (c *Controller) FriendsAdd(ctx context.Context, userID string, username string, ids []string, usernames []string) error {
	if len(ids) == 0 && len(usernames) == 0 {
		return nil
	}

	for _, id := range ids {
		if userID == id {
			return errors.New("cannot add self as friend")
		}
		if uid, err := uuid.FromString(id); err != nil || uid == uuid.Nil {
			return fmt.Errorf("invalid user ID '%v'", id)
		}
	}

	for _, u := range usernames {
		if u == "" {
			return errors.New("username to add must not be empty")
		}
		if username == u {
			return errors.New("cannot add self as friend")
		}
	}

	fetchIDs, err := fetchUserID(ctx, c.server.db, usernames)
	if err != nil {
		c.server.logger.Error("Could not fetch user IDs. %v usernames: %v", err, usernames)
		return errors.New("error while trying to add friends")
	}

	if len(fetchIDs)+len(ids) == 0 {
		return errors.New("no valid ID or username was provided")
	}

	allIDs := make([]string, 0, len(ids)+len(fetchIDs))
	allIDs = append(allIDs, ids...)
	allIDs = append(allIDs, fetchIDs...)

	uniqueFriendIDs := make(map[string]struct{})
	for _, fid := range allIDs {
		uniqueFriendIDs[fid] = struct{}{}
	}

	var notificationToSend map[string]bool = make(map[string]bool)

	// if err := ExecuteInTx(ctx, db, func(tx *sql.Tx) error {
	// 	// If the transaction is retried ensure we wipe any notifications that may have been prepared by previous attempts.
	// 	notificationToSend = make(map[string]bool)

	// 	for id := range uniqueFriendIDs {
	// 		// Check to see if user has already blocked friend, if so, don't add friend or send notification.
	// 		var blockState int
	// 		err := tx.QueryRowContext(ctx, "SELECT state FROM user_edge WHERE source_id = $1 AND destination_id = $2 AND state = 3", userID, id).Scan(&blockState)
	// 		// ignore if the error is sql.ErrNoRows as means block was not found - continue as intended.
	// 		if err != nil && err != sql.ErrNoRows {
	// 			// genuine DB error was found.
	// 			logger.Debug("Failed to check edge state.", zap.Error(err), zap.String("user", userID.String()), zap.String("friend", id))
	// 			return err
	// 		} else if err == nil {
	// 			// the block was found, don't add friend or send notification.
	// 			logger.Info("Ignoring previously blocked friend. Delete friend first before attempting to add.", zap.String("user", userID.String()), zap.String("friend", id))
	// 			continue
	// 		}

	// 		isFriendAccept, addFriendErr := addFriend(ctx, logger, tx, userID, id)
	// 		if addFriendErr == nil {
	// 			notificationToSend[id] = isFriendAccept
	// 		} else if addFriendErr != sql.ErrNoRows { // Check to see if friend had blocked user.
	// 			return addFriendErr
	// 		}
	// 	}
	// 	return nil
	// }); err != nil {
	// 	logger.Error("Error adding friends.", zap.Error(err))
	// 	return err
	// }

	for friend_uid := range uniqueFriendIDs {
		// Check to see if user has already blocked friend, if so, don't add friend or send notification.
		edge := bo.UserEdge{}
		obj := c.server.dbagent.Create(ctx, &edge, models.WithKeys(map[string]interface{}{edge.GetKeyName(): userID})).WithOption(logic.WithAutoRLock())
		defer func() {
			c.server.dbagent.Release(obj)
		}()
		edgeList := []*bo.UserEdge{}
		err = obj.Query(ctx, friend_uid).As(&edgeList)
		if err != nil {
			if err != models.ErrDBResEmpty {
				c.server.logger.Error("Failed to query edge user_id: %s friend_uid: %s %v", userID, friend_uid, err)
				return err
			}
		} else {
			if len(edgeList) > 0 {
				edge = *edgeList[0]
				if edge.State == 3 { // 3 表示已屏蔽
					c.server.logger.Info("Failed to check edge state. already blocked friend user_id: %s friend_uid: %s", userID, friend_uid)
					return errors.New("user has already blocked friend")
				}
			}
		}
		obj.UnlockAll() // 这里先解锁，避免死锁

		// 添加好友
		isFriendAccept, addFriendErr := c.addFriend(ctx, userID, friend_uid)
		if addFriendErr == nil {
			notificationToSend[friend_uid] = isFriendAccept
		} else if addFriendErr != sql.ErrNoRows { // Check to see if friend had blocked user.
			return addFriendErr
		}

		// 	logger.Debug("Added new friend invitation.", zap.String("user", userID.String()), zap.String("friend", friendID))
		// 	return false, nil
	}

	notifications := make(map[uuid.UUID][]*api.Notification)
	content, _ := json.Marshal(map[string]interface{}{"username": username})
	for id, isFriendAccept := range notificationToSend {
		uid := uuid.FromStringOrNil(id)
		code := models.NotificationCodeFriendRequest
		subject := fmt.Sprintf("%v wants to add you as a friend", username)
		if isFriendAccept {
			code = models.NotificationCodeFriendAccept
			subject = fmt.Sprintf("%v accepted your friend request", username)
		}
		notifications[uid] = []*api.Notification{{
			Id:         uuid.Must(uuid.NewV4()).String(),
			Subject:    subject,
			Content:    string(content),
			SenderId:   userID,
			Code:       code,
			Persistent: true,
			CreateTime: &timestamppb.Timestamp{Seconds: time.Now().UTC().Unix()},
		}}
	}

	// Any error is already logged before it's returned here.
	_ = c.notify.NotificationsSendMap(ctx, notifications)

	return nil
}

func (c *Controller) FriendsDelete(ctx context.Context, userID string, username string, ids []string, usernames []string) error {
	if len(ids) == 0 && len(usernames) == 0 {
		return nil
	}

	for _, id := range ids {
		if userID == id {
			return errors.New("cannot delete self")
		}
		if uid, err := uuid.FromString(id); err != nil || uid == uuid.Nil {
			return fmt.Errorf("invalid user ID '%v'", id)
		}
	}

	for _, u := range usernames {
		if u == "" {
			return errors.New("username to delete must not be empty")
		}
		if username == u {
			return errors.New("cannot delete self")
		}
	}

	fetchIDs, err := fetchUserID(ctx, c.server.db, usernames)
	if err != nil {
		c.server.logger.Error("Could not fetch user IDs.", zap.Error(err), zap.Strings("usernames", usernames))
		return errors.New("error while trying to delete friends")
	}

	if len(fetchIDs)+len(ids) == 0 {
		return errors.New("no valid ID or username was provided")
	}

	allIDs := make([]string, 0, len(ids)+len(fetchIDs))
	allIDs = append(allIDs, ids...)
	allIDs = append(allIDs, fetchIDs...)

	uniqueFriendIDs := make(map[string]struct{})
	for _, fid := range allIDs {
		uniqueFriendIDs[fid] = struct{}{}
	}

	for id := range uniqueFriendIDs {
		if deleteFriendErr := c.deleteFriend(ctx, userID, id); deleteFriendErr != nil {
			c.server.logger.Error("Failed to delete friend user_id: %s friend_uid: %s %v", userID, id, deleteFriendErr)
			continue
		}
	}

	return nil
}

func (c *Controller) FriendsBlock(ctx context.Context, userID string, username string, ids []string, usernames []string) error {
	if len(ids) == 0 && len(usernames) == 0 {
		return nil
	}

	for _, id := range ids {
		if userID == id {
			return errors.New("cannot block self")
		}
		if uid, err := uuid.FromString(id); err != nil || uid == uuid.Nil {
			return fmt.Errorf("invalid user ID '%v'", id)
		}
	}

	for _, u := range usernames {
		if u == "" {
			return errors.New("username to block must not be empty")
		}
		if username == u {
			return errors.New("cannot block self")
		}
	}

	fetchIDs, err := fetchUserID(ctx, c.server.db, usernames)
	if err != nil {
		c.server.logger.Error("Could not fetch user IDs.", zap.Error(err), zap.Strings("usernames", usernames))
		return errors.New("error while trying to block friends")
	}

	if len(fetchIDs)+len(ids) == 0 {
		return errors.New("no valid ID or username was provided")
	}

	allIDs := make([]string, 0, len(ids)+len(fetchIDs))
	allIDs = append(allIDs, ids...)
	allIDs = append(allIDs, fetchIDs...)

	uniqueFriendIDs := make(map[string]struct{})
	for _, fid := range allIDs {
		uniqueFriendIDs[fid] = struct{}{}
	}

	for id := range uniqueFriendIDs {
		if blockFriendErr := c.blockFriend(ctx, userID, id); blockFriendErr != nil {
			c.server.logger.Error("Failed to block friend user_id: %s friend_uid: %s %v", userID, id, blockFriendErr)
			continue
		}
	}

	return nil
}

func fetchUserID(ctx context.Context, db *sql.DB, usernames []string) ([]string, error) {
	ids := make([]string, 0, len(usernames))
	if len(usernames) == 0 {
		return ids, nil
	}

	query := "SELECT id FROM users WHERE username = ANY($1::text[])"
	rows, err := db.QueryContext(ctx, query, usernames)
	if err != nil {
		if err == sql.ErrNoRows {
			return ids, nil
		}
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var id string
		err := rows.Scan(&id)
		if err != nil {
			return nil, err
		}
		ids = append(ids, id)
	}
	if err = rows.Err(); err != nil {
		return nil, err
	}

	return ids, nil
}

// Returns "true" if accepting an invite, otherwise false
func (c *Controller) addFriend(ctx context.Context, userID string, friendID string) (bool, error) {
	// Mark an invite as accepted, if one was in place.
	// 	res, err := tx.ExecContext(ctx, `
	// UPDATE user_edge SET state = 0, update_time = now()
	// WHERE (source_id = $1 AND destination_id = $2 AND state = 1)
	// OR (source_id = $2 AND destination_id = $1 AND state = 2)
	//   `, friendID, userID)
	// 	if err != nil {
	// 		logger.Debug("Failed to update user state.", zap.Error(err), zap.String("user", userID.String()), zap.String("friend", friendID))
	// 		return false, err
	// 	}

	// 	// If both edges were updated, it was accepting an invite was successful.
	// 	if rowsAffected, _ := res.RowsAffected(); rowsAffected == 2 {
	// 		logger.Debug("Accepting friend invitation.", zap.String("user", userID.String()), zap.String("friend", friendID))
	// 		return true, nil
	// 	}
	// 检查是否存在好友关系
	user_edge := bo.UserEdge{}
	useredgeobj := c.server.dbagent.Create(ctx, &user_edge, models.WithKeys(map[string]interface{}{user_edge.GetKeyName(): userID})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(useredgeobj)
	}()
	userEdgeList := []*bo.UserEdge{}
	err1 := useredgeobj.Query(ctx, friendID).As(&userEdgeList)
	if err1 != nil {
		if err1 != models.ErrDBResEmpty {
			c.server.logger.Error("Failed to query edge user_id: %s friend_uid: %s %v", userID, friendID, err1)
			return false, err1
		}
	}
	if len(userEdgeList) > 0 {
		user_edge = *userEdgeList[0]
	}

	friend_edge := bo.UserEdge{}
	friendedgeobj := c.server.dbagent.Create(ctx, &friend_edge, models.WithKeys(map[string]interface{}{friend_edge.GetKeyName(): friendID})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(friendedgeobj)
	}()
	friendEdgeList := []*bo.UserEdge{}
	err2 := friendedgeobj.Query(ctx, userID).As(&friendEdgeList)
	if err2 != nil {
		if err2 != models.ErrDBResEmpty {
			c.server.logger.Error("Failed to query edge user_id: %s friend_uid: %s %v", userID, friendID, err2)
			return false, err2
		}
	}
	if len(friendEdgeList) > 0 {
		friend_edge = *friendEdgeList[0]
	}

	// 已经是好友
	if user_edge.State == 0 && friend_edge.State == 0 && len(userEdgeList) > 0 && len(friendEdgeList) > 0 {
		c.server.logger.Debug("Already friends. user_id: %s friend_uid: %s", userID, friendID)
		return false, nil
	}

	// 已经发送请求，等待对方同意
	if user_edge.State == 1 && friend_edge.State == 2 {
		c.server.logger.Debug("Friend request already sent, waiting for approval. user_id: %s friend_uid: %s", userID, friendID)
		return false, nil
	}

	if friend_edge.State == 1 && user_edge.State == 2 {
		user_edge.State = 0
		user_edge.UpdateTime = time.Now()
		_, err := useredgeobj.WithReqOption(models.WithValuesFromDBModel(&user_edge, nil)).Update(ctx).Result()
		if err != nil {
			c.server.logger.Error("Failed to update user edge user_id: %s friend_uid: %s %v", userID, friendID, err)
			return false, err
		}

		friend_edge.State = 0
		friend_edge.UpdateTime = time.Now()
		_, err = friendedgeobj.WithReqOption(models.WithValuesFromDBModel(&friend_edge, nil)).Update(ctx).Result()
		if err != nil {
			c.server.logger.Error("Failed to update friend edge user_id: %s friend_uid: %s %v", userID, friendID, err)
			return false, err
		}

		c.server.logger.Debug("Accepted friend request. user_id: %s friend_uid: %s", userID, friendID)
		return true, nil
	}

	// If no edge updates took place, it's either a new invite being set up, or user was blocked off by friend.
	// 	_, err = tx.ExecContext(ctx, `
	// INSERT INTO user_edge (source_id, destination_id, state, position, update_time)
	// SELECT source_id, destination_id, state, position, update_time
	// FROM (VALUES
	//   ($1::UUID, $2::UUID, 1, $3::BIGINT, now()),
	//   ($2::UUID, $1::UUID, 2, $3::BIGINT, now())
	// ) AS ue(source_id, destination_id, state, position, update_time)
	// WHERE
	// 	EXISTS (SELECT id FROM users WHERE id = $2::UUID)
	// 	AND
	// 	NOT EXISTS
	// 	(SELECT state
	//    FROM user_edge
	//    WHERE source_id = $2::UUID AND destination_id = $1::UUID AND state = 3
	//   )
	// ON CONFLICT (source_id, destination_id) DO NOTHING
	// `, userID, friendID, position)
	// 	if err != nil {
	// 		logger.Debug("Failed to insert new user edge link.", zap.Error(err), zap.String("user", userID.String()), zap.String("friend", friendID))
	// 		return false, err
	// 	}
	// 如果没有任何边缘更新，则要么是新的邀请被设置，要么是用户被朋友屏蔽。
	position := time.Now().UTC().UnixNano()
	if err1 == models.ErrDBResEmpty {
		// 插入
		user_edge.SourceId = userID
		user_edge.DestinationId = friendID
		user_edge.State = 1
		user_edge.Position = position
		user_edge.UpdateTime = time.Now()
		_, err := useredgeobj.WithReqOption(models.WithValuesFromDBModel(&user_edge, nil)).Insert(ctx).Result()
		if err != nil {
			c.server.logger.Error("Failed to insert user edge user_id: %s friend_uid: %s %v", userID, friendID, err)
			return false, err
		}
	} else {
		// 更新
		user_edge.State = 1
		user_edge.Position = position
		user_edge.UpdateTime = time.Now()
		_, err := useredgeobj.WithReqOption(models.WithValuesFromDBModel(&user_edge, nil)).Update(ctx).Result()
		if err != nil {
			c.server.logger.Error("Failed to update user edge user_id: %s friend_uid: %s %v", userID, friendID, err)
			return false, err
		}
	}
	if err2 == models.ErrDBResEmpty {
		// 插入
		friend_edge.SourceId = friendID
		friend_edge.DestinationId = userID
		friend_edge.State = 2
		friend_edge.Position = position
		friend_edge.UpdateTime = time.Now()
		_, err := friendedgeobj.WithReqOption(models.WithValuesFromDBModel(&friend_edge, nil)).Insert(ctx).Result()
		if err != nil {
			c.server.logger.Error("Failed to insert friend edge user_id: %s friend_uid: %s %v", userID, friendID, err)
			return false, err
		}
	} else {
		if friend_edge.State == 3 { // 3 表示已屏蔽
			return false, nil
		}

		// 更新
		friend_edge.State = 2
		friend_edge.Position = position
		friend_edge.UpdateTime = time.Now()
		_, err := friendedgeobj.WithReqOption(models.WithValuesFromDBModel(&friend_edge, nil)).Update(ctx).Result()
		if err != nil {
			c.server.logger.Error("Failed to update friend edge user_id: %s friend_uid: %s %v", userID, friendID, err)
			return false, err
		}
	}
	useredgeobj.UnlockAll()
	friendedgeobj.UnlockAll()

	// Update friend count if we've just created the relationship.
	// This check is done by comparing the the timestamp(position) to the timestamp available.
	// i.e. only increase count when the relationship was first formed.
	// This is caused by an existing bug in CockroachDB: https://github.com/cockroachdb/cockroach/issues/10264
	// 	if res, err = tx.ExecContext(ctx, `
	// UPDATE users
	// SET edge_count = edge_count +1, update_time = now()
	// WHERE
	// 	(id = $1::UUID OR id = $2::UUID)
	// AND EXISTS
	// 	(SELECT state
	//    FROM user_edge
	//    WHERE
	//    	(source_id = $1::UUID AND destination_id = $2::UUID AND position = $3::BIGINT)
	//    	OR
	//    	(source_id = $2::UUID AND destination_id = $1::UUID AND position = $3::BIGINT)
	//   )
	// `, userID, friendID, position); err != nil {
	// 		logger.Debug("Failed to update user count.", zap.Error(err), zap.String("user", userID.String()), zap.String("friend", friendID))
	// 		return false, err
	// 	}

	// 	// An invite was successfully added if both components were inserted.
	// 	if rowsAffected, _ := res.RowsAffected(); rowsAffected != 2 {
	// 		logger.Debug("Did not add new friend as friend connection already exists or user is blocked.", zap.String("user", userID.String()), zap.String("friend", friendID))
	// 		return false, sql.ErrNoRows
	// 	}
	// 更新用户好友数量
	user := commbo.User{}
	userobj := c.server.dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{user.GetKeyName(): userID})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(userobj)
	}()
	err := userobj.Query(ctx, "edge_count").As(&user)
	if err != nil {
		c.server.logger.Error("Failed to query user user_id: %s friend_uid: %s %v", userID, friendID, err)
		return false, err
	}
	user.EdgeCount++
	_, err = userobj.WithReqOption(models.WithValues(map[string]interface{}{"edge_count": user.EdgeCount})).Update(ctx).Result()
	if err != nil {
		c.server.logger.Error("Failed to update user user_id: %s friend_uid: %s %v", userID, friendID, err)
		return false, err
	}
	friend := commbo.User{}
	friendobj := c.server.dbagent.Create(ctx, &friend, models.WithKeys(map[string]interface{}{friend.GetKeyName(): friendID})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(friendobj)
	}()
	err = friendobj.Query(ctx, "edge_count").As(&friend)
	if err != nil {
		c.server.logger.Error("Failed to query friend user_id: %s friend_uid: %s %v", userID, friendID, err)
		return false, err
	}
	friend.EdgeCount++
	_, err = friendobj.WithReqOption(models.WithValues(map[string]interface{}{"edge_count": friend.EdgeCount})).Update(ctx).Result()
	if err != nil {
		c.server.logger.Error("Failed to update friend user_id: %s friend_uid: %s %v", userID, friendID, err)
		return false, err
	}

	c.server.logger.Debug("Added new friend invitation. user_id: %s friend_id: %s", userID, friendID)
	return false, nil
}

func (c *Controller) deleteFriend(ctx context.Context, userID string, friendID string) error {
	// res, err := tx.ExecContext(ctx, "DELETE FROM user_edge WHERE (source_id = $1 AND destination_id = $2) OR (source_id = $2 AND destination_id = $1 AND state <> 3)", userID, friendID)
	// if err != nil {
	// 	logger.Debug("Failed to delete user edge relationships.", zap.Error(err), zap.String("user", userID.String()), zap.String("friend", friendID))
	// 	return err
	// }

	// if rowsAffected, _ := res.RowsAffected(); rowsAffected == 0 {
	// 	logger.Debug("Could not delete user relationships as prior relationship did not exist.", zap.String("user", userID.String()), zap.String("friend", friendID))
	// 	return nil
	// } else if rowsAffected == 1 {
	// 	if _, err = tx.ExecContext(ctx, "UPDATE users SET edge_count = edge_count - 1, update_time = now() WHERE id = $1::UUID", userID); err != nil {
	// 		logger.Debug("Failed to update user edge counts.", zap.Error(err), zap.String("user", userID.String()), zap.String("friend", friendID))
	// 		return err
	// 	}
	// } else if rowsAffected == 2 {
	// 	if _, err = tx.ExecContext(ctx, "UPDATE users SET edge_count = edge_count - 1, update_time = now() WHERE id IN ($1, $2)", userID, friendID); err != nil {
	// 		logger.Debug("Failed to update user edge counts.", zap.Error(err), zap.String("user", userID.String()), zap.String("friend", friendID))
	// 		return err
	// 	}
	// } else {
	// 	logger.Debug("Unexpected number of edges were deleted.", zap.String("user", userID.String()), zap.String("friend", friendID), zap.Int64("rows_affected", rowsAffected))
	// 	return errors.New("unexpected number of edges were deleted")
	// }

	user_edge := bo.UserEdge{}
	useredgeobj := c.server.dbagent.Create(ctx, &user_edge, models.WithKeys(map[string]interface{}{user_edge.GetKeyName(): userID})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(useredgeobj)
	}()
	result, err := useredgeobj.WithReqOption(models.WithValues(map[string]interface{}{user_edge.GetSecondKeyName(): friendID})).Delete(ctx).Result()
	if err != nil {
		if err != models.ErrDBResEmpty {
			c.server.logger.Error("Failed to query edge user_id: %s friend_uid: %s %v", userID, friendID, err)
			return err
		}
	}
	useredgeobj.UnlockAll()

	if result.RowsAffected > 0 {
		user := commbo.User{}
		userobj := c.server.dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{user.GetKeyName(): userID})).WithOption(logic.WithAutoLock())
		defer func() {
			c.server.dbagent.Release(userobj)
		}()
		err := userobj.Query(ctx, "edge_count").As(&user)
		if err != nil {
			c.server.logger.Error("Failed to query user user_id: %s friend_uid: %s %v", userID, friendID, err)
			return err
		}
		user.EdgeCount--
		_, err = userobj.WithReqOption(models.WithValues(map[string]interface{}{"edge_count": user.EdgeCount})).Update(ctx).Result()
		if err != nil {
			c.server.logger.Error("Failed to update user user_id: %s friend_uid: %s %v", userID, friendID, err)
			return err
		}
		c.server.logger.Debug("Deleted friend user_id: %s friend_uid: %s", userID, friendID)
		userobj.UnlockAll()
	}

	friend_edge := bo.UserEdge{}
	friendedgeobj := c.server.dbagent.Create(ctx, &friend_edge, models.WithKeys(map[string]interface{}{friend_edge.GetKeyName(): friendID})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(friendedgeobj)
	}()
	friendEdgeList := []*bo.UserEdge{}
	err = friendedgeobj.Query(ctx, userID).As(&friendEdgeList)
	if err != nil {
		if err == models.ErrDBResEmpty {
			return nil
		}
		c.server.logger.Error("Failed to query edge user_id: %s friend_uid: %s %v", userID, friendID, err)
		return err
	}
	if len(friendEdgeList) > 0 {
		friend_edge = *friendEdgeList[0]
	}
	if friend_edge.State == 3 { // 3 表示已屏蔽
		return nil
	}
	result, err = friendedgeobj.WithReqOption(models.WithValues(map[string]interface{}{friend_edge.GetSecondKeyName(): userID})).Delete(ctx).Result()
	if err != nil {
		c.server.logger.Error("Failed to delete edge user_id: %s friend_uid: %s %v", userID, friendID, err)
		return err
	}
	friendedgeobj.UnlockAll()

	if result.RowsAffected > 0 {
		friend := commbo.User{}
		friendobj := c.server.dbagent.Create(ctx, &friend, models.WithKeys(map[string]interface{}{friend.GetKeyName(): friendID})).WithOption(logic.WithAutoLock())
		defer func() {
			c.server.dbagent.Release(friendobj)
		}()
		err := friendobj.Query(ctx, "edge_count").As(&friend)
		if err != nil {
			c.server.logger.Error("Failed to query friend user_id: %s friend_uid: %s %v", userID, friendID, err)
			return err
		}
		friend.EdgeCount--
		_, err = friendobj.WithReqOption(models.WithValues(map[string]interface{}{"edge_count": friend.EdgeCount})).Update(ctx).Result()
		if err != nil {
			c.server.logger.Error("Failed to update friend user_id: %s friend_uid: %s %v", userID, friendID, err)
			return err
		}
		c.server.logger.Debug("Deleted friend user_id: %s friend_uid: %s", friendID, userID)
		friendobj.UnlockAll()
	}
	return nil
}

func (c *Controller) blockFriend(ctx context.Context, userID string, friendID string) error {
	// Try to update any previous edge between these users.
	// 	res, err := tx.ExecContext(ctx, "UPDATE user_edge SET state = 3, update_time = now() WHERE source_id = $1 AND destination_id = $2",
	// 		userID, friendID)
	// 	if err != nil {
	// 		logger.Debug("Failed to update user edge state.", zap.Error(err), zap.String("user", userID.String()), zap.String("friend", friendID))
	// 		return err
	// 	}

	// 	position := fmt.Sprintf("%v", time.Now().UTC().UnixNano())

	// 	if rowsAffected, _ := res.RowsAffected(); rowsAffected == 0 {
	// 		// If there was no previous edge then create one.
	// 		query := `
	// INSERT INTO user_edge (source_id, destination_id, state, position, update_time)
	// SELECT source_id, destination_id, state, position, update_time
	// FROM (VALUES
	//   ($1::UUID, $2::UUID, 3, $3::BIGINT, now())
	// ) AS ue(source_id, destination_id, state, position, update_time)
	// WHERE EXISTS (SELECT id FROM users WHERE id = $2::UUID)`
	// 		res, err = tx.ExecContext(ctx, query, userID, friendID, position)
	// 		if err != nil {
	// 			logger.Debug("Failed to block user.", zap.Error(err), zap.String("user", userID.String()), zap.String("friend", friendID))
	// 			return err
	// 		}

	// 		if rowsAffected, _ := res.RowsAffected(); rowsAffected == 0 {
	// 			logger.Debug("Could not block user as user may not exist.", zap.String("user", userID.String()), zap.String("friend", friendID))
	// 			return nil
	// 		}

	// 		// Update the edge count.
	// 		if _, err = tx.ExecContext(ctx, "UPDATE users SET edge_count = edge_count + 1, update_time = now() WHERE id = $1", userID); err != nil {
	// 			logger.Debug("Failed to update user edge count.", zap.Error(err), zap.String("user", userID.String()), zap.String("friend", friendID))
	// 			return err
	// 		}
	// 	}

	// 	// Delete opposite relationship if user hasn't blocked you already
	// 	res, err = tx.ExecContext(ctx, "DELETE FROM user_edge WHERE source_id = $1 AND destination_id = $2 AND state != 3", friendID, userID)
	// 	if err != nil {
	// 		logger.Debug("Failed to update user edge state.", zap.Error(err), zap.String("user", userID.String()), zap.String("friend", friendID))
	// 		return err
	// 	}

	// 	if rowsAffected, _ := res.RowsAffected(); rowsAffected == 1 {
	// 		if _, err = tx.ExecContext(ctx, "UPDATE users SET edge_count = edge_count - 1, update_time = now() WHERE id = $1", friendID); err != nil {
	// 			logger.Debug("Failed to update user edge count.", zap.Error(err), zap.String("user", userID.String()), zap.String("friend", friendID))
	// 			return err
	// 		}
	// 	}

	// 	stream := PresenceStream{
	// 		Mode: StreamModeDM,
	// 	}
	// 	fuid := uuid.Must(uuid.FromString(friendID))
	// 	if friendID > userID.String() {
	// 		stream.Subject = userID
	// 		stream.Subcontext = fuid
	// 	} else {
	// 		stream.Subject = fuid
	// 		stream.Subcontext = userID
	// 	}

	// 	tracker.UntrackByStream(stream)

	// Try to update any previous edge between these users.
	user_edge := bo.UserEdge{}
	useredgeobj := c.server.dbagent.Create(ctx, &user_edge, models.WithKeys(map[string]interface{}{user_edge.GetKeyName(): userID})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(useredgeobj)
	}()
	userEdgeList := []*bo.UserEdge{}
	err := useredgeobj.Query(ctx, friendID).As(&userEdgeList)
	if err != nil {
		if err != models.ErrDBResEmpty {
			c.server.logger.Error("Failed to query edge user_id: %s friend_uid: %s %v", userID, friendID, err)
			return err
		} else {
			// 插入
			position := time.Now().UTC().UnixNano()
			user_edge.SourceId = userID
			user_edge.DestinationId = friendID
			user_edge.State = 3
			user_edge.Position = position
			user_edge.UpdateTime = time.Now()
			result, err := useredgeobj.WithReqOption(models.WithValuesFromDBModel(&user_edge, nil)).Insert(ctx).Result()
			if err != nil {
				c.server.logger.Error("Failed to insert edge user_id: %s friend_uid: %s %v", userID, friendID, err)
				return err
			}
			if result.RowsAffected > 0 {
				// 更新好友数量
				user := commbo.User{}
				userobj := c.server.dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{user.GetKeyName(): userID})).WithOption(logic.WithAutoLock())
				defer func() {
					c.server.dbagent.Release(userobj)
				}()
				err := userobj.Query(ctx, "edge_count").As(&user)
				if err != nil {
					c.server.logger.Error("Failed to query user user_id: %s friend_uid: %s %v", userID, friendID, err)
					return err
				}
				user.EdgeCount++
				_, err = userobj.WithReqOption(models.WithValues(map[string]interface{}{"edge_count": user.EdgeCount})).Update(ctx).Result()
				if err != nil {
					c.server.logger.Error("Failed to update user user_id: %s friend_uid: %s %v", userID, friendID, err)
					return err
				}
			}
		}
	} else {
		if len(userEdgeList) > 0 {
			user_edge = *userEdgeList[0]
		}
		if user_edge.State != 3 { // 未屏蔽
			user_edge.State = 3
			user_edge.UpdateTime = time.Now()
			_, err := useredgeobj.WithReqOption(models.WithValuesFromDBModel(&user_edge, nil)).Update(ctx).Result()
			if err != nil {
				c.server.logger.Error("Failed to update edge user_id: %s friend_uid: %s %v", userID, friendID, err)
				return err
			}
		}
	}
	useredgeobj.UnlockAll()

	// Delete opposite relationship if user hasn't blocked you already
	friend_edge := bo.UserEdge{}
	friendedgeobj := c.server.dbagent.Create(ctx, &friend_edge, models.WithKeys(map[string]interface{}{friend_edge.GetKeyName(): friendID})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(friendedgeobj)
	}()
	friendEdgeList := []*bo.UserEdge{}
	err = friendedgeobj.Query(ctx, userID).As(&friendEdgeList)
	if err != nil {
		if err == models.ErrDBResEmpty {
			return nil
		}
		c.server.logger.Error("Failed to query edge user_id: %s friend_uid: %s %v", userID, friendID, err)
		return err
	}
	if len(friendEdgeList) > 0 {
		friend_edge = *friendEdgeList[0]
	}
	if friend_edge.State == 3 { // 3 表示已屏蔽
		return nil
	}
	result, err := friendedgeobj.WithReqOption(models.WithValues(map[string]interface{}{friend_edge.GetSecondKeyName(): userID})).Delete(ctx).Result()
	if err != nil {
		c.server.logger.Error("Failed to delete edge user_id: %s friend_uid: %s %v", userID, friendID, err)
		return err
	}
	friendedgeobj.UnlockAll()

	if result.RowsAffected > 0 {
		// 更新好友数量
		friend := commbo.User{}
		friendobj := c.server.dbagent.Create(ctx, &friend, models.WithKeys(map[string]interface{}{friend.GetKeyName(): friendID})).WithOption(logic.WithAutoLock())
		defer func() {
			c.server.dbagent.Release(friendobj)
		}()
		err := friendobj.Query(ctx, "edge_count").As(&friend)
		if err != nil {
			c.server.logger.Error("Failed to query friend user_id: %s friend_uid: %s %v", userID, friendID, err)
			return err
		}
		friend.EdgeCount--
		_, err = friendobj.WithReqOption(models.WithValues(map[string]interface{}{"edge_count": friend.EdgeCount})).Update(ctx).Result()
		if err != nil {
			c.server.logger.Error("Failed to update friend user_id: %s friend_uid: %s %v", userID, friendID, err)
			return err
		}
		c.server.logger.Debug("Blocked friend user_id: %s friend_uid: %s", userID, friendID)
	}

	// 解除状态跟踪，后续加上

	return nil
}

// @Summary 搜索好友
// @Description 搜索好友
// @Tags friendserver
// @Accept json
// @Produce json
// @Param request body ReqSearchFriend true "请求参数"
// @Success 200 {object} models.CommonResp "成功"
// @Router /v2/rpc/friendserver.search [post]
func (c *Controller) SearchFriend(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var req ReqSearchFriend
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&models.CommonResp{Code: models.PARAM_ERR, Msg: err.Error(), Data: nil})
	}

	// 参数验证
	if req.Keyword == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.PARAM_ERR, Msg: "keyword cannot be empty", Data: nil})
	}

	user := commbo.User{}
	userobj := c.server.dbagent.Create(ctx, &user).WithReqOption(models.WithNotOptCache(true))
	defer func() {
		c.server.dbagent.Release(userobj)
	}()
	userList := []*commbo.User{}

	// 转义特殊字符防止SQL注入
	escapedKeyword := strings.ReplaceAll(req.Keyword, "'", "''")

	var sqlstr string
	intuin := common.Interface2Int64(req.Keyword)
	if intuin > 0 {
		sqlstr = fmt.Sprintf("SELECT id, uin, username, display_name, avatar_url, lang_tag, location, timezone, metadata FROM users WHERE uin = %d or display_name = '%s' limit 20", intuin, escapedKeyword)
	} else {
		sqlstr = fmt.Sprintf("SELECT id, uin, username, display_name, avatar_url, lang_tag, location, timezone, metadata FROM users WHERE display_name = '%s' limit 20", escapedKeyword)
	}

	err := userobj.RawQuery(ctx, sqlstr).As(&userList)
	if err != nil {
		logger.Error("Failed to search friend: %v", err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error(), Data: nil})
	}

	tmp_set_user_func := func(user *commbo.User, apiuser *models.User) {
		if user == nil || apiuser == nil {
			return
		}

		apiuser.Id = user.UserId
		apiuser.Username = user.Username
		apiuser.DisplayName = user.DisplayName
		apiuser.Metadata = user.Metadata
		apiuser.Online = c.server.online.IsOnline(user.UserId)
	}

	users := make([]*models.User, 0, len(userList))
	for _, user := range userList {
		if user == nil {
			continue
		}
		if user.DisableTime.Unix() > 0 && user.DisableTime.Unix() > time.Now().Unix() {
			continue
		}
		apiuser := &models.User{}
		tmp_set_user_func(user, apiuser)
		users = append(users, apiuser)
	}

	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "success", Data: users})
}
