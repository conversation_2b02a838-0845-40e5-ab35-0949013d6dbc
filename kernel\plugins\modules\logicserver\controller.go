package main

import (
	"context"
	"database/sql"
	"kernel/kernel-common/runtime"
	commbo "kernel/plugins/bo"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"kernel/plugins/modules/logicserver/bo"
	"math/rand"
	"strings"
	"time"

	json "github.com/json-iterator/go"
)

type Controller struct {
	server *LogicServerModule
}

func NewController(s *LogicServerModule) *Controller {
	return &Controller{
		server: s,
	}
}

// @Summary 登录完成
// @Description 登录完成
// @Tags logicserver
// @Accept json
// @Produce json
// @Success 200 {object} models.CommonResp "响应结果"
// @Router /v2/rpc/logicserver.loginfinish [post]
func (c *Controller) LoginFinish(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "uid is empty", Data: nil})
	}

	//根据user_id 查询 role_id(uin)
	metadata := models.UserMetaData{}

	user := commbo.User{}
	obj := c.server.dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{user.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err := obj.Query(ctx).As(&user)
	if err != nil {
		logger.Error("Failed to query user for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}

	// 检查用户是否被封禁
	tmptime := user.DisableTime.Unix()
	if tmptime > 0 && tmptime > time.Now().Unix() {
		c.server.send.Push(ctx, user_id, SUBJECT_LOGICSERVER_LOGINFAILED, &models.CommonResp{Code: models.FAILED, Msg: "user is banned", Data: tmptime}, "", 0)
		logger.Info("user %s is banned", user_id)
		return json.MarshalToString(&models.CommonResp{Code: models.OK})
	}

	err = json.Unmarshal([]byte(user.Metadata), &metadata)
	if err != nil {
		logger.Error("Failed to unmarshal metadata for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}

	if metadata.LastLoginTime == 0 && metadata.RoleData == "" {
		// 等待客户端发起创建角色
	} else {
		//更新数据
		role_data := map[string]interface{}{}
		if metadata.RoleData != "" {
			err = json.Unmarshal([]byte(metadata.RoleData), &role_data)
			if err != nil {
				logger.Error("Failed to unmarshal role_data for user_id %s: %v", user_id, err)
				return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
			}
			if role_data["model"] == nil {
				role_data["model"] = rand.Intn(3) // 随机0,1,2
				metadata.RoleData, _ = json.MarshalToString(role_data)
			}
		} else {
			role_data["model"] = rand.Intn(3) // 随机0,1,2
			metadata.RoleData, _ = json.MarshalToString(role_data)
		}
		metadata.LastLoginTime = time.Now().Unix()
		user.Metadata, _ = json.MarshalToString(metadata)
		user.UpdateTime = time.Now()
		obj.WithReqOption(models.WithValues(map[string]interface{}{"metadata": user.Metadata, "update_time": user.UpdateTime}))
		if err := obj.Update(ctx).Error(); err != nil {
			logger.Error("Failed to update user for user_id %s: %v", user_id, err)
		}
	}

	c.server.send.Push(ctx, user_id, SUBJECT_LOGICSERVER_ROLEDATA, &RoleInfo{
		RoleID:        metadata.RoleId,
		RoleData:      metadata.RoleData,
		LastLoginTime: metadata.LastLoginTime,
	}, "", 0)

	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "ok", Data: nil})
}

// CreateRole 创建角色
// @Summary 创建一个新角色
// @Description 根据用户ID创建一个新角色，如果角色已存在则返回失败
// @Tags logicserver
// @Accept json
// @Produce json
// @Param payload body string true "角色数据，JSON格式"
// @Success 200 {object} RespCreateRole "成功创建角色"
// @Router /v2/rpc/logicserver.createrole [post]
func (c *Controller) CreateRole(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uid := c.server.common.GetUserID(ctx)
	if uid == "" {
		return json.MarshalToString(&RespCreateRole{Code: models.EXCEPTION, Msg: "uid is empty", Data: nil})
	}
	if payload == "" {
		payload = "{}"
	}

	user := commbo.User{}
	obj := c.server.dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{user.GetKeyName(): uid})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err := obj.Query(ctx).As(&user)
	if err != nil {
		logger.Error("Failed to query user info for uid %s: %v", uid, err)
		return json.MarshalToString(&RespCreateRole{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}

	metadata := models.UserMetaData{}
	err = json.Unmarshal([]byte(user.Metadata), &metadata)
	if err != nil {
		logger.Error("Failed to unmarshal metadata for user_id %s: %v", uid, err)
		return json.MarshalToString(&RespCreateRole{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}

	if metadata.LastLoginTime == 0 && metadata.RoleData == "" {
		upval := map[string]interface{}{}
		if metadata.RoleId == 0 {
			metadata.RoleId = user.Uin
		}
		role_data := map[string]interface{}{}
		err = json.Unmarshal([]byte(payload), &role_data)
		if err != nil {
			logger.Error("Failed to unmarshal role_data for %v", err)
			return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
		}
		if role_data["model"] == nil {
			role_data["model"] = rand.Intn(3) // 随机0,1,2
		}
		metadata.RoleData, _ = json.MarshalToString(role_data)
		metadata.LastLoginTime = time.Now().Unix()
		user.Metadata, _ = json.MarshalToString(metadata)
		user.UpdateTime = time.Now()
		upval["metadata"] = user.Metadata
		upval["update_time"] = user.UpdateTime
		if err := obj.WithReqOption(models.WithValues(upval)).Update(ctx).Error(); err != nil {
			logger.Error("Failed to update user for user_id %s: %v", uid, err)
		}
		return json.MarshalToString(&RespCreateRole{Code: models.OK, Msg: "ok", Data: &RoleInfo{
			RoleID:        metadata.RoleId,
			RoleData:      metadata.RoleData,
			LastLoginTime: metadata.LastLoginTime,
		}})
	}

	// 如果已经存在，不能创建
	return json.MarshalToString(&RespCreateRole{Code: models.FAILED, Msg: "already exist, create fail", Data: nil})
}

// @Summary 获取收藏房间列表
// @Description 获取用户的收藏列表
// @Tags logicserver
// @Accept json
// @Produce json
// @Success 200 {object} RespGetFavoriteRooms
// @Router /v2/rpc/logicserver.getfavoriterooms [post]
func (c *Controller) GetFavoriteRooms(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uid := c.server.common.GetUserID(ctx)
	if uid == "" {
		return json.MarshalToString(&RespGetFavoriteRooms{Code: models.EXCEPTION, Msg: "uid is empty", Data: nil})
	}

	m := bo.Favorites{}
	obj := c.server.dbagent.Create(ctx, &m, models.WithKeys(map[string]interface{}{m.GetKeyName(): uid})).WithOption(logic.WithAutoRLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()

	err := obj.Query(ctx).As(&m)
	if err != nil {
		if err == models.ErrDBResEmpty {
			return json.MarshalToString(&RespGetFavoriteRooms{Code: models.OK, Msg: "ok", Data: map[string]*Favorite{}})
		}
		logger.Error("Failed to query table favorites %s: %v", uid, err)
		return json.MarshalToString(&RespGetFavoriteRooms{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}

	result := map[string]*Favorite{}
	err = json.Unmarshal([]byte(m.List), &result)
	if err != nil {
		logger.Error("Failed to unmarshal list: %v", err)
		return json.MarshalToString(&RespGetFavoriteRooms{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}
	return json.MarshalToString(&RespGetFavoriteRooms{Code: models.OK, Msg: "ok", Data: result})
}

// @Summary 添加房间收藏
// @Description 添加一个收藏
// @Tags logicserver
// @Accept json
// @Produce json
// @Param payload body string true "房间ID"
// @Success 200 {object} models.CommonResp "成功添加收藏"
// @Router /v2/rpc/logicserver.addfavoriteroom [post]
func (c *Controller) AddFavoriteRoom(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uid := c.server.common.GetUserID(ctx)
	if uid == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "payload is empty", Data: nil})
	}
	if payload == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "payload is empty", Data: nil})
	}

	room_id := strings.ReplaceAll(payload, "'", "''") // 替换单引号为 PostgreSQL 转义字符
	m := bo.Favorites{}
	obj := c.server.dbagent.Create(ctx, &m, models.WithKeys(map[string]interface{}{m.GetKeyName(): uid})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err := obj.Query(ctx).As(&m)
	if err != nil {
		if err == models.ErrDBResEmpty {
			m.UserId = uid
			m.List, _ = json.MarshalToString(map[string]interface{}{room_id: &Favorite{
				Time: time.Now().Unix(),
			}})
			obj.WithReqOption(models.WithValuesFromDBModel(&m, nil))
			if err := obj.Insert(ctx).Error(); err != nil {
				logger.Error("Failed to insert favorites for user %s: %v", uid, err)
				return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
			}
			return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "ok", Data: nil})
		} else {
			logger.Error("Failed to query table favorites %s: %v", uid, err)
			return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
		}
	}

	result := map[string]*Favorite{}
	err = json.Unmarshal([]byte(m.List), &result)
	if err != nil {
		logger.Error("Failed to unmarshal list: %v", err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}
	if _, ok := result[room_id]; ok {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "already exist, add fail", Data: nil})
	}
	result[room_id] = &Favorite{
		Time: time.Now().Unix(),
	}
	m.List, _ = json.MarshalToString(result)
	obj.WithReqOption(models.WithValuesFromDBModel(&m, nil))
	if err := obj.Update(ctx).Error(); err != nil {
		logger.Error("Failed to update favorites for user %s: %v", uid, err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}
	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "ok", Data: nil})
}

// @Summary 删除收藏房间
// @Description 删除收藏房间
// @Tags logicserver
// @Accept json
// @Produce json
// @Param payload body []string true "房间ID列表 json"
// @Success 200 {object} models.CommonResp "成功删除收藏"
// @Router /v2/rpc/logicserver.delfavoriterooms [post]
func (c *Controller) DelFavoriteRooms(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uid := c.server.common.GetUserID(ctx)
	if uid == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "payload is empty", Data: nil})
	}
	if payload == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "payload is empty", Data: nil})
	}

	dellist := []string{}
	err := json.UnmarshalFromString(payload, &dellist)
	if err != nil {
		logger.Error("Failed to unmarshal list: %v", err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}

	m := bo.Favorites{}
	obj := c.server.dbagent.Create(ctx, &m, models.WithKeys(map[string]interface{}{m.GetKeyName(): uid})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx).As(&m)
	if err != nil {
		if err == models.ErrDBResEmpty {
			return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "not found", Data: nil})
		} else {
			logger.Error("Failed to query table favorites %s: %v", uid, err)
			return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
		}
	}

	result := map[string]*Favorite{}
	err = json.Unmarshal([]byte(m.List), &result)
	if err != nil {
		logger.Error("Failed to unmarshal list: %v", err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}

	for _, v := range dellist {
		delete(result, v)
	}

	if len(result) == 0 {
		err := obj.Delete(ctx).Error()
		if err != nil {
			logger.Error("Failed to delete favorites for user %s: %v", uid, err)
			return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
		}
		return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "ok", Data: nil})
	}

	m.List, _ = json.MarshalToString(result)
	obj.WithReqOption(models.WithValuesFromDBModel(&m, nil))
	if err := obj.Update(ctx).Error(); err != nil {
		logger.Error("Failed to update favorites for user %s: %v", uid, err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}
	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "ok", Data: nil})
}

// @Summary 获取历史房间列表
// @Description 获取用户的历史列表
// @Tags logicserver
// @Accept json
// @Produce json
// @Success 200 {object} RespGetHistoryRooms
// @Router /v2/rpc/logicserver.gethistoryrooms [post]
func (c *Controller) GetHistoryRooms(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uid := c.server.common.GetUserID(ctx)
	if uid == "" {
		return json.MarshalToString(&RespGetHistoryRooms{Code: models.EXCEPTION, Msg: "uid is empty", Data: nil})
	}

	m := bo.Historys{}
	obj := c.server.dbagent.Create(ctx, &m, models.WithKeys(map[string]interface{}{m.GetKeyName(): uid})).WithOption(logic.WithAutoRLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err := obj.Query(ctx).As(&m)
	if err != nil {
		if err == models.ErrDBResEmpty {
			return json.MarshalToString(&RespGetHistoryRooms{Code: models.OK, Msg: "ok", Data: map[string]*History{}})
		}
	}
	result := map[string]*History{}
	err = json.Unmarshal([]byte(m.List), &result)
	if err != nil {
		logger.Error("Failed to unmarshal list: %v", err)
		return json.MarshalToString(&RespGetHistoryRooms{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}
	return json.MarshalToString(&RespGetHistoryRooms{Code: models.OK, Msg: "ok", Data: result})
}

// @Summary 添加历史房间
// @Description 添加一个历史房间
// @Tags logicserver
// @Accept json
// @Produce json
// @Param payload body string true "房间ID"
// @Success 200 {object} models.CommonResp "成功添加历史房间"
// @Router /v2/rpc/logicserver.addhistoryroom [post]
func (c *Controller) AddHistoryRoom(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uid := c.server.common.GetUserID(ctx)
	if uid == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "payload is empty", Data: nil})
	}
	if payload == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "payload is empty", Data: nil})
	}
	room_id := strings.ReplaceAll(payload, "'", "''") // 替换单引号为 PostgreSQL 转义字符

	m := bo.Historys{}
	obj := c.server.dbagent.Create(ctx, &m, models.WithKeys(map[string]interface{}{m.GetKeyName(): uid})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err := obj.Query(ctx).As(&m)
	if err != nil {
		if err == models.ErrDBResEmpty {
			m.UserId = uid
			m.List, _ = json.MarshalToString(map[string]interface{}{room_id: &History{
				Time: time.Now().Unix(),
			}})
			obj.WithReqOption(models.WithValuesFromDBModel(&m, nil))
			if err := obj.Insert(ctx).Error(); err != nil {
				logger.Error("Failed to insert historys for user %s: %v", uid, err)
				return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
			}
			return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "ok", Data: nil})
		} else {
			logger.Error("Failed to query table historys %s: %v", uid, err)
			return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
		}
	}
	result := map[string]*History{}
	err = json.Unmarshal([]byte(m.List), &result)
	if err != nil {
		logger.Error("Failed to unmarshal list: %v", err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}
	if _, ok := result[room_id]; ok {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "already exist, add fail", Data: nil})
	}
	result[room_id] = &History{
		Time: time.Now().Unix(),
	}
	m.List, _ = json.MarshalToString(result)
	obj.WithReqOption(models.WithValuesFromDBModel(&m, nil))
	if err := obj.Update(ctx).Error(); err != nil {
		logger.Error("Failed to update historys for user %s: %v", uid, err)
	}
	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "ok", Data: nil})
}

// @Summary 删除历史房间
// @Description 删除历史房间
// @Tags logicserver
// @Accept json
// @Produce json
// @Param payload body []string true "房间ID列表"
// @Success 200 {object} models.CommonResp "成功删除历史房间"
// @Router /v2/rpc/logicserver.delhistoryrooms [post]
func (c *Controller) DelHistoryRooms(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uid := c.server.common.GetUserID(ctx)
	if uid == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.SESSION_ERR, Msg: "session error", Data: nil})
	}
	if payload == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "payload is empty", Data: nil})
	}
	dellist := []string{}
	err := json.UnmarshalFromString(payload, &dellist)
	if err != nil {
		logger.Error("Failed to unmarshal list: %v", err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}

	m := bo.Historys{}
	obj := c.server.dbagent.Create(ctx, &m, models.WithKeys(map[string]interface{}{m.GetKeyName(): uid})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx).As(&m)
	if err != nil {
		if err == models.ErrDBResEmpty {
			return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "ok", Data: nil})
		} else {
			logger.Error("Failed to query table historys %s: %v", uid, err)
			return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
		}
	}
	result := map[string]*History{}
	err = json.Unmarshal([]byte(m.List), &result)
	if err != nil {
		logger.Error("Failed to unmarshal list: %v", err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}
	for _, v := range dellist {
		delete(result, v)
	}
	if len(result) == 0 {
		err := obj.Delete(ctx).Error()
		if err != nil {
			logger.Error("Failed to delete historys for user %s: %v", uid, err)
			return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
		}
		return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "ok", Data: nil})
	}
	m.List, _ = json.MarshalToString(result)
	obj.WithReqOption(models.WithValuesFromDBModel(&m, nil))
	if err := obj.Update(ctx).Error(); err != nil {
		logger.Error("Failed to update historys for user %s: %v", uid, err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}
	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "ok", Data: nil})
}

// @Summary 查询用户状态
// @Description 查询用户状态 logicserver.querystatus
// @Tags logicserver
// @Produce  json
// @Accept	 application/json
// @Param request body ReqQueryStatus true "请求参数"
// @Success 200 {object} RespQueryStatus "返回结果"
// @Router /v2/rpc/logicserver.querystatus [post]
func (c *Controller) QueryStatus(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if ctx == nil {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "context is nil", Data: nil})
	}
	if payload == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "payload is empty", Data: nil})
	}
	var req ReqQueryStatus
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}

	// 解析user_ids
	if len(req.UserIDs) == 0 && len(req.Uins) == 0 {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "user_ids or uins is required", Data: nil})
	}

	resp := RespQueryStatus{
		Code: models.OK,
		Msg:  "ok",
		Data: &QueryStatusData{},
	}

	if len(req.UserIDs) > 0 {
		uids, err := c.server.online.QueryOnlineUsers(ctx, req.UserIDs)
		if err != nil {
			return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
		}
		resp.Data.UserIDs = uids
	}
	if len(req.Uins) > 0 {
		uins, err := c.server.online.QueryOnlineUsersByUin(ctx, req.Uins)
		if err != nil {
			return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
		}
		resp.Data.Uins = uins
	}

	return json.MarshalToString(resp)
}
