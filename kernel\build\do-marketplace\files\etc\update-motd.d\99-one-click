#!/bin/sh
#
# Configured as part of the DigitalOcean 1-Click Image build process

myip=$(hostname -I | awk '{print$1}')
cat << "EOF"
********************************************************************************
                                                                                                                                                                                                  
  ,,                                                                                                
  ,,                                                                                                
  ,`       `,,,,,.                                                                                  
  ,`     ,,,,,,,,,,,`                                                                               
  ,`   .,,,,,,,,,,,,,,                                                                              
  ,`  ,,,,,,,,,   `,,,,                                                                             
  ,` ,,,,:,:;,,,,,,,,,,,                                                                            
  ,``,,,::';;,,,,,,, `,,,                                                                           
  ,`,,,::::';,,,,,,,, .,,`         +++     `++           ++                                         
  ,,,,:::::';:,,,,,,,, ,,,         +++;    `++           ++                                         
  ,,,,:::::';;,,,,,,,,` ,,         ++++    `++           ++                                         
  ,,,:::::::::::,,,,,,, ,,,        +++++   `++    ;';    ++   ,::   .'',    ,. :':   ;':     `;':   
  ,,,::::::::::::,,,,,, ,,,        ++,+++  `++  ++++++;  ++  .++   ++++++   +++++++:+++++   ++++++` 
  ,,,:::::::::::::,,,,,,,,,        ++, ++` `++  ..   ++  ++ `++    +   ;++  ++'  +++:  ++'  +`  .++ 
  .,,::::::::  :::,,,,,,,,,        ++, ;++ `++       ++  ++ ++`         ++  ++   `++   .++       ++ 
  .,,::::::::  :::::::'':,,        ++,  +++`++   ++++++  +++++     `++++++  ++    ++   .++   ++++++ 
  `,,:::::::::::::::::'',,,        ++,   ++;++  +++;:++  ++ ++,   `+++::++  ++    ++   .++  +++::++ 
   ,,:::::::::::::::::;',,.        ++,   .++++ `++   ++  ++ `++   ++`   ++  ++    ++   .++ '+:   ++ 
   ,,,::::::::::::::::::,,         ++,    ++++ `++  :++  ++  ;++  ++:  +++  ++    ++   .++ '+'  '++ 
   `,,:::::::::::::::::,,,         ++,     +++  +++++++  ++   +++ `+++++++  ++    ++   .++  +++++++ 
    ,,,:::::::::::::::,,,          ,,`      ..   ;++``;  ,,    ,,.  ++' ;;  ::    ::   `::   '+' ;; 
     ,,,::::::::::::::,,,                                                                           
     .,,,:::::::::::,,,,                                                                            
      .,,,,::::::::,,,,                                                                             
        ,,,,,,,,,,,,,,                                                                              
         .,,,,,,,,,,                                                                                
            `,,,.                                                                                   
                                                                                                    
********************************************************************************

To delete this message of the day: rm -rf $(readlink -f ${0})
EOF