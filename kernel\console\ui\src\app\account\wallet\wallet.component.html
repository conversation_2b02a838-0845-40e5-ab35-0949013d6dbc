<ngb-alert [dismissible]="false" type="danger" class="mb-3" *ngIf="error">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">An error occurred: {{error}}</h6>
</ngb-alert>

<ngb-alert [dismissible]="false" type="success" class="mb-3" *ngIf="updated">
  <img src="/static/svg/green-tick.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">Account was modified successfully.</h6>
</ngb-alert>

<div class="card p-2 mb-3 jsoneditor" style="height: 400px">
  <div #editor style="height: 400px"></div>
</div>

<button type="button" class="btn btn-primary" [disabled]="updating" (click)="updateWallet();" *ngIf="updateAllowed()">Save</button>

<hr class="my-4" />

 
<div class="row no-gutters mb-3">
  <div class="col d-flex justify-content-between no-gutters">
    <div class="col-md-9"></div>
    <div class="col-md-3 justify-content-end text-right">
      <div class="btn-group page-btns" role="group">
        <button type="button" class="btn btn-outline-secondary" (click)="loadData('')" [disabled]="walletLedger.length === 0"><img src="/static/svg/page-first.svg" alt="" width="20" height=""></button>
        <button type="button" class="btn btn-outline-secondary" (click)="loadData(prevCursor)" [disabled]="prevCursor === ''"><img src="/static/svg/page-prev.svg" alt="" width="20" height=""></button>
        <button type="button" class="btn btn-outline-secondary" (click)="loadData(nextCursor)" [disabled]="nextCursor === ''"><img src="/static/svg/page-next.svg" alt="" width="20" height=""></button>
      </div>
    </div>
  </div>
</div>

<div class="row no-gutters">
  <table class="table table-sm table-bordered">
    <thead class="thead-light">
      <tr>
        <th style="width: 315px">ID</th>
        <th>Changeset</th>
        <th style="width: 180px">Update Time</th>
        <th style="width: 90px" *ngIf="deleteAllowed()">Remove</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngIf="walletLedger.length === 0">
        <td colSpan="5" class="text-muted">No wallet ledger items found.</td>
      </tr>

      <ng-template ngFor let-i="index" let-w [ngForOf]="walletLedger">
        <tr>
          <td>
            <div class="arrow" (click)="walletLedgerMetadataOpen[i]=!walletLedgerMetadataOpen[i]">
              <div class="arrow-right" *ngIf="!walletLedgerMetadataOpen[i]"></div>
              <div class="arrow-down" *ngIf="walletLedgerMetadataOpen[i]"></div>
            </div>

            {{w.id}}
          </td>
          <td class="align-middle"><pre class="m-0 p-0">{{w.changeset}}</pre></td>
          <td>{{w.update_time}}</td>
          <td *ngIf="deleteAllowed()"><button type="button" class="btn btn-sm btn-danger" (click)="deleteLedgerItem($event, i, w);">Delete</button></td>
        </tr>
        <tr *ngIf="walletLedgerMetadataOpen[i]">
          <td colspan="5" class="align-middle"><pre class="pre-wrap m-0 p-0"><small>{{w.metadata}}</small></pre></td>
        </tr>
      </ng-template>
    </tbody>
  </table>
</div>
