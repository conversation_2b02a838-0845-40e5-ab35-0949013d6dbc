package common

import (
	"context"
	"math/rand"
	"net"
	"sync"
	"time"
)

type SelfDialer struct {
	Dialer   *net.Dialer
	Resolver *Resolver
}

func NewSelfDialer() *SelfDialer {
	return &SelfDialer{
		Dialer: &net.Dialer{
			Timeout:   3 * time.Second,
			KeepAlive: 30 * time.Second,
		},
		Resolver: NewResolver(time.Second * 30),
	}
}

func (dialer *SelfDialer) DialContext(ctx context.Context, network, address string) (net.Conn, error) {
	host, port, err := net.SplitHostPort(address)
	if err != nil {
		return nil, err
	}

	_ip := net.ParseIP(host)
	if _ip == nil {
		// 解析域名
		ips, err1 := dialer.Resolver.Get(ctx, host)
		dst := make([]string, len(ips))
		copy(dst, ips)
		if len(dst) > 0 && err1 == nil {
			// 从数组中随机选择一个元素
			tmprand := rand.New(rand.NewSource(time.Now().UnixNano()))
			index := tmprand.Intn(len(dst))
			start := index
			for index >= 0 {
				ip := dst[start]
				conn, err := dialer.Dialer.DialContext(ctx, network, ip+":"+port)
				if err == nil {
					return conn, nil
				}
				start = (start + 1) % len(dst)
				if start == index {
					break
				}
			}
		}
	}
	return dialer.Dialer.DialContext(ctx, network, address)
}

type Resolver struct {
	lock            sync.RWMutex
	cache           map[string][]string
	ResolverTimeout time.Duration
}

func NewResolver(refreshRate time.Duration) *Resolver {
	resolver := &Resolver{
		cache:           make(map[string][]string, 128),
		ResolverTimeout: 5 * time.Second,
	}
	if refreshRate > 0 {
		go resolver.autoRefresh(refreshRate)
	}
	return resolver
}

func (r *Resolver) Get(ctx context.Context, host string) ([]string, error) {
	r.lock.RLock()
	ips, exists := r.cache[host]
	r.lock.RUnlock()
	if exists {
		return ips, nil
	}
	return r.Lookup(ctx, host)
}

func (r *Resolver) Refresh() {
	i := 0
	r.lock.RLock()
	addresses := make([]string, len(r.cache))
	for key, _ := range r.cache {
		addresses[i] = key
		i++
	}
	r.lock.RUnlock()

	clearhosts := []string{}
	for _, host := range addresses {
		ctx, _ := context.WithTimeout(context.Background(), r.ResolverTimeout)
		ips, err := r.Lookup(ctx, host)
		if err == nil && len(ips) == 0 {
			clearhosts = append(clearhosts, host)
		}
		time.Sleep(time.Millisecond * 100)
	}
	r.lock.Lock()
	for _, host := range clearhosts {
		delete(r.cache, host)
	}
	r.lock.Unlock()
}

func (r *Resolver) Lookup(ctx context.Context, host string) ([]string, error) {
	if ctx == nil {
		return nil, nil
	}
	ips, err := net.DefaultResolver.LookupIPAddr(ctx, host) //调用默认的resolver
	if err != nil {
		return nil, err
	}
	if len(ips) == 0 {
		return nil, nil
	}
	strIPs := make([]string, len(ips))
	for index, ip := range ips {
		strIPs[index] = ip.String()
	}
	r.lock.Lock()
	r.cache[host] = strIPs
	r.lock.Unlock()
	return strIPs, nil
}

func (r *Resolver) autoRefresh(rate time.Duration) {
	for {
		time.Sleep(rate)
		r.Refresh()
	}
}
