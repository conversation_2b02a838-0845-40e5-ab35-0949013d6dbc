package cronexpr

import (
	"github.com/google/gofuzz"
	"sync"
	"testing"
	"time"
)

var Expressions = []*Expression{
	Must<PERSON><PERSON>e("* * * * *"),
	<PERSON><PERSON><PERSON><PERSON>("0 * * * *"),
	<PERSON><PERSON><PERSON><PERSON>("0 0 * * *"),
	<PERSON><PERSON><PERSON><PERSON>("1 0 * * *"),
	<PERSON><PERSON><PERSON><PERSON>("1 1 * * *"),
	<PERSON><PERSON><PERSON><PERSON>("0 1 * * *"),
	<PERSON><PERSON><PERSON><PERSON>("0 8 * * *"),
	<PERSON><PERSON><PERSON><PERSON>("30 * * * *"),
	<PERSON><PERSON><PERSON><PERSON>("30 0 * * *"),
	<PERSON><PERSON><PERSON><PERSON>("30 10 * * *"),
	<PERSON><PERSON><PERSON><PERSON>("30 10 31 * *"),
}

var Times = []time.Time{
	time.Unix(0, 0),
	time.Unix(1548902941, 0),
	time.Unix(1548902942, 0),
	time.Unix(1548902943, 0),
	time.Unix(1548902944, 0),
	time.Unix(1548902945, 0),
	time.Unix(1548019288, 0),
	time.Unix(1548883288, 0),
	time.Unix(1548969688, 0),
	time.Unix(1548892799, 0),
	time.Unix(1548892800, 0),
	time.Unix(1548892801, 0),
	time.Unix(1548979200, 0),
	time.Unix(1548979201, 0),
}

func TestCronexpr(t *testing.T) {
	var wg sync.WaitGroup
	wg.Add(len(Expressions))
	for _, e := range Expressions {
		go func() {
			for _, t := range Times {
				_ = e.Next(t)
				_ = e.NextN(t, 2)
				_ = e.NextN(t, 5)
				_ = e.NextN(t, 10)
				_ = e.NextN(t, 12)
				_ = e.NextN(t, 1)
				_ = e.NextN(t, 7)
				_ = e.NextN(t, 70)
			}
			wg.Done()
		}()
	}
	wg.Wait()
}

func TestFuzz(t *testing.T) {
	f := fuzz.New().NilChance(0)
	var wg sync.WaitGroup
	wg.Add(len(Expressions))
	for _, e := range Expressions {
		go func() {
			for i := 0; i < 10000; i++ {
				var ts int64
				f.Fuzz(&ts)
				t := time.Unix(ts, 0)

				_ = e.Next(t)
				_ = e.NextN(t, 2)
				_ = e.NextN(t, 5)
				_ = e.NextN(t, 10)
				_ = e.NextN(t, 12)
				_ = e.NextN(t, 1)
				_ = e.NextN(t, 7)
				_ = e.NextN(t, 70)
			}

			wg.Done()
		}()
	}
	wg.Wait()
}
