package main

import (
	"kernel/plugins/models"
)

type CustomConfig struct {
	Config    *QueueServerConfig      `json:"queue_server_config,omitempty" yaml:"queue_server_config,omitempty"`
	AccessLog *models.AccessLogConfig `json:"access_log,omitempty" yaml:"access_log,omitempty"`
	Logger    *models.LoggerConfig    `json:"logger,omitempty" yaml:"logger,omitempty"`
}

type QueueServerConfig struct {
	NodeConfig        *models.NodeConfig `json:"node_config,omitempty" yaml:"node_config,omitempty"`
	WatchSvcGroupList []string           `json:"watch_svc_group_list,omitempty" yaml:"watch_svc_group_list,omitempty"`
	GsmgrConfig       *GsmgrConfig       `json:"gsmgr_config,omitempty" yaml:"gsmgr_config,omitempty"`
	MsgBusConfig      *MsgBusConfig      `json:"msgbus_config,omitempty" yaml:"msgbus_config,omitempty"`
	BucketList        []string           `json:"bucket_list,omitempty" yaml:"bucket_list,omitempty"` // 支持语法, 0-9, 单独的数字0,1,2这样
}

type MsgBusConfig struct {
	Addr      string `json:"addr,omitempty" yaml:"addr,omitempty"`
	Appid     string `json:"appid,omitempty" yaml:"appid,omitempty"`
	Appsecret string `json:"appsecret,omitempty" yaml:"appsecret,omitempty"`
}

type GsmgrConfig struct {
	Addr      string `json:"addr,omitempty" yaml:"addr,omitempty"`
	Appid     string `json:"appid,omitempty" yaml:"appid,omitempty"`
	Appsecret string `json:"appsecret,omitempty" yaml:"appsecret,omitempty"`
}
