package main

import (
	"context"
	"database/sql"
	"kernel/kernel-common/runtime"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"kernel/plugins/modules/userserver/bo"
	"kernel/plugins/pb"
	"time"

	json "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
)

// @Summary 获取坐骑列表
// @Description 获取坐骑列表
// @Tags userserver
// @Accept json
// @Produce json
// @Success 200 {object} RespMountList "返回结果"
// @Router /v2/rpc/userserver.mount.list [post]
func (c *Controller) MountList(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		return json.MarshalToString(&RespMountList{Code: int64(models.FAILED), Message: "session error"})
	}

	mounts := bo.Mounts{}
	obj := c.server.dbagent.Create(ctx, &mounts, models.WithKeys(map[string]interface{}{mounts.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err := obj.Query(ctx).As(&mounts)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query mounts for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespMountList{Code: int64(models.FAILED), Message: err.Error()})
		}
		mounts.Items = "{}"
	}
	items := map[int64]*Mount{} // item_id -> item
	err = json.Unmarshal([]byte(mounts.Items), &items)
	if err != nil {
		logger.Error("Failed to unmarshal mounts items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespMountList{Code: int64(models.FAILED), Message: err.Error()})
	}

	// 检查坐骑是否过期，并更新
	isupdate := false
	now := time.Now()
	for itemId, item := range items {
		if item.ExpireTime > 0 && item.ExpireTime < now.Unix() {
			delete(items, itemId)
			isupdate = true
		}
	}

	if isupdate {
		mounts.UpdatedAt = now
		mounts.Items, err = json.MarshalToString(items)
		if err != nil {
			logger.Error("Failed to marshal mounts items for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespMountList{Code: int64(models.FAILED), Message: err.Error()})
		}
		resp, err := obj.WithReqOption(models.WithValues(map[string]interface{}{"items": mounts.Items, "updated_at": mounts.UpdatedAt})).Update(ctx).Result()
		if err != nil {
			logger.Error("Failed to update mounts for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespMountList{Code: int64(models.FAILED), Message: err.Error()})
		}
		if resp.RowsAffected > 0 {
			// nothing to do
		} else {
			return json.MarshalToString(&RespMountList{Code: int64(models.FAILED), Message: "update failed"})
		}
	}

	list := pb.MountList{}
	list.List = make([]*pb.MountInfo, 0, len(items))
	for _, item := range items {
		id := uint32(item.MountId)
		expireTime := item.ExpireTime
		list.List = append(list.List, &pb.MountInfo{
			Id:         &id,
			ExpireTime: &expireTime,
		})
	}
	bytes, err := proto.Marshal(&list)
	if err != nil {
		logger.Error("Failed to marshal mount list for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespMountList{Code: int64(models.FAILED), Message: err.Error()})
	}
	return json.MarshalToString(&RespMountList{Code: int64(models.OK), Message: "success", Data: string(bytes)})
}

// @Summary 添加坐骑
// @Description 添加坐骑
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqMountOpt true "请求参数"
// @Success 200 {object} models.CommonResp "返回结果"
// @Router /v2/rpc/userserver.mount.add [post]
func (c *Controller) MountAdd(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if c.server.common.GetUserID(ctx) != "" {
		logger.Error("session not allowed, add mount")
		return json.MarshalToString(&models.CommonResp{Code: models.ACCESS_DENIED, Msg: "session not allowed"})
	}

	var reqMount ReqMountOpt
	err := json.Unmarshal([]byte(payload), &reqMount)
	if err != nil {
		logger.Error("Failed to unmarshal mount add: %v", err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}

	if reqMount.UserId == "" || reqMount.Mount == nil {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "param is invalid"})
	}

	user_id := reqMount.UserId
	isInsert := false
	mounts := bo.Mounts{}
	obj := c.server.dbagent.Create(ctx, &mounts, models.WithKeys(map[string]interface{}{mounts.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx).As(&mounts)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query mounts for user_id %s: %v", user_id, err)
			return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
		}
		mounts.Items = "{}"
		isInsert = true
	}
	items := map[int64]*Mount{} // item_id -> item
	err = json.Unmarshal([]byte(mounts.Items), &items)
	if err != nil {
		logger.Error("Failed to unmarshal mounts items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}

	// 检查装扮是否过期，并更新
	isupdate := false
	now := time.Now()
	for itemId, item := range items {
		if item.ExpireTime > 0 && item.ExpireTime < now.Unix() {
			delete(items, itemId)
			isupdate = true
		}
	}

	// 检查是否已经存在
	if _, ok := items[reqMount.Mount.MountId]; ok {
		if isupdate {
			mounts.UpdatedAt = now
			_, err := obj.WithReqOption(models.WithValues(map[string]interface{}{"items": mounts.Items, "updated_at": mounts.UpdatedAt})).Update(ctx).Result()
			if err != nil {
				logger.Error("Failed to update mounts for user_id %s: %v", user_id, err)
				return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
			}
		}
		return json.MarshalToString(&models.CommonResp{Code: models.DATA_EXIST, Msg: "mount already exists"})
	}

	// 添加新的坐骑
	items[reqMount.Mount.MountId] = reqMount.Mount

	mounts.UpdatedAt = now
	mounts.Items, err = json.MarshalToString(items)
	if err != nil {
		logger.Error("Failed to marshal mounts items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}
	var resp *models.DBAgentResp
	obj.WithReqOption(models.WithValues(map[string]interface{}{"items": mounts.Items, "updated_at": mounts.UpdatedAt}))
	if isInsert {
		resp, err = obj.Insert(ctx).Result()
	} else {
		resp, err = obj.Update(ctx).Result()
	}
	if err != nil {
		logger.Error("Failed to update mounts for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}
	if resp.RowsAffected > 0 {
		// nothing to do
	} else {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "update failed"})
	}

	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "success"})
}

// @Summary 删除坐骑
// @Description 删除坐骑
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqMountOpt true "请求参数"
// @Success 200 {object} models.CommonResp "返回结果"
// @Router /v2/rpc/userserver.mount.delete [post]
func (c *Controller) MountDelete(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if c.server.common.GetUserID(ctx) != "" {
		logger.Error("session not allowed, delete mount")
		return json.MarshalToString(&models.CommonResp{Code: models.ACCESS_DENIED, Msg: "session not allowed"})
	}

	var reqMount ReqMountOpt
	err := json.Unmarshal([]byte(payload), &reqMount)
	if err != nil {
		logger.Error("Failed to unmarshal mount delete: %v", err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}

	if reqMount.UserId == "" || reqMount.Mount == nil {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "param is invalid"})
	}

	user_id := reqMount.UserId
	mounts := bo.Mounts{}
	obj := c.server.dbagent.Create(ctx, &mounts, models.WithKeys(map[string]interface{}{mounts.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx).As(&mounts)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query mounts for user_id %s: %v", user_id, err)
			return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
		}
		mounts.Items = "{}"
	}
	items := map[int64]*Mount{} // item_id -> item
	err = json.Unmarshal([]byte(mounts.Items), &items)
	if err != nil {
		logger.Error("Failed to unmarshal mounts items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}

	// 检查装扮是否存在
	if _, ok := items[reqMount.Mount.MountId]; !ok {
		return json.MarshalToString(&models.CommonResp{Code: models.DATA_NOT_EXIST, Msg: "mount not exists"})
	}

	// 检查装扮是否过期
	now := time.Now()
	for itemId, item := range items {
		if item.ExpireTime > 0 && item.ExpireTime < now.Unix() {
			delete(items, itemId)
		}
	}

	// 添加新的装扮
	delete(items, reqMount.Mount.MountId)

	mounts.UpdatedAt = now
	mounts.Items, err = json.MarshalToString(items)
	if err != nil {
		logger.Error("Failed to marshal mounts items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}
	resp, err := obj.WithReqOption(models.WithValues(map[string]interface{}{"items": mounts.Items, "updated_at": mounts.UpdatedAt})).Update(ctx).Result()
	if err != nil {
		logger.Error("Failed to update mounts for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}
	if resp.RowsAffected > 0 {
		// nothing to do
	} else {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "update failed"})
	}

	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "success"})
}

// @Summary 查询坐骑
// @Description 查询坐骑
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqMountOpt true "请求参数"
// @Success 200 {object} RespMountQuery "返回结果"
// @Router /v2/rpc/userserver.mount.query [post]
func (c *Controller) MountQuery(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if c.server.common.GetUserID(ctx) != "" {
		logger.Error("session not allowed, query mount")
		return json.MarshalToString(&RespMountQuery{Code: int64(models.ACCESS_DENIED), Message: "session not allowed", Data: nil})
	}

	var reqMount ReqMountOpt
	err := json.Unmarshal([]byte(payload), &reqMount)
	if err != nil {
		logger.Error("Failed to unmarshal mount query: %v", err)
		return json.MarshalToString(&RespMountQuery{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	if reqMount.UserId == "" || reqMount.Mount == nil {
		return json.MarshalToString(&RespMountQuery{Code: int64(models.FAILED), Message: "param is invalid", Data: nil})
	}

	user_id := reqMount.UserId
	mounts := bo.Mounts{}
	obj := c.server.dbagent.Create(ctx, &mounts, models.WithKeys(map[string]interface{}{mounts.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx).As(&mounts)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query mounts for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespMountQuery{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
		}
		mounts.Items = "{}"
	}
	items := map[int64]*Mount{} // item_id -> item
	err = json.Unmarshal([]byte(mounts.Items), &items)
	if err != nil {
		logger.Error("Failed to unmarshal mounts items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespMountQuery{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	// 检查装扮是否存在
	if _, ok := items[reqMount.Mount.MountId]; !ok {
		return json.MarshalToString(&RespMountQuery{Code: int64(models.DATA_NOT_EXIST), Message: "mount not exists", Data: nil})
	} else {
		return json.MarshalToString(&RespMountQuery{Code: int64(models.OK), Message: "success", Data: items[reqMount.Mount.MountId]})
	}
}

// @Summary 更新坐骑
// @Description 更新坐骑
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqMountOpt true "请求参数"
// @Success 200 {object} RespMountUpdate "返回结果"
// @Router /v2/rpc/userserver.mount.update [post]
func (c *Controller) MountUpdate(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if c.server.common.GetUserID(ctx) != "" {
		logger.Error("session not allowed, update mount")
		return json.MarshalToString(&RespMountUpdate{Code: int64(models.ACCESS_DENIED), Message: "session not allowed", Data: nil})
	}

	var reqMount ReqMountOpt
	err := json.Unmarshal([]byte(payload), &reqMount)
	if err != nil {
		logger.Error("Failed to unmarshal mount update: %v", err)
		return json.MarshalToString(&RespMountUpdate{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	if reqMount.UserId == "" || reqMount.Mount == nil {
		return json.MarshalToString(&RespMountUpdate{Code: int64(models.FAILED), Message: "param is invalid", Data: nil})
	}

	user_id := reqMount.UserId
	mounts := bo.Mounts{}
	obj := c.server.dbagent.Create(ctx, &mounts, models.WithKeys(map[string]interface{}{mounts.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx).As(&mounts)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query mounts for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespMountUpdate{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
		}
		mounts.Items = "{}"
	}
	items := map[int64]*Mount{} // item_id -> item
	err = json.Unmarshal([]byte(mounts.Items), &items)
	if err != nil {
		logger.Error("Failed to unmarshal mounts items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespMountUpdate{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	// 检查装扮是否存在
	if _, ok := items[reqMount.Mount.MountId]; !ok {
		return json.MarshalToString(&RespMountUpdate{Code: int64(models.DATA_NOT_EXIST), Message: "mount not exists", Data: nil})
	}

	// 检查装扮是否过期
	now := time.Now()
	for itemId, item := range items {
		if item.ExpireTime > 0 && item.ExpireTime < now.Unix() {
			delete(items, itemId)
		}
	}

	// 更新新的装扮
	items[reqMount.Mount.MountId].ExpireTime = reqMount.Mount.ExpireTime

	mounts.UpdatedAt = now
	mounts.Items, err = json.MarshalToString(items)
	if err != nil {
		logger.Error("Failed to marshal mounts items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespMountUpdate{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}
	resp, err := obj.WithReqOption(models.WithValues(map[string]interface{}{"items": mounts.Items, "updated_at": mounts.UpdatedAt})).Update(ctx).Result()
	if err != nil {
		logger.Error("Failed to update mounts for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespMountUpdate{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}
	if resp.RowsAffected > 0 {
		// nothing to do
	} else {
		return json.MarshalToString(&RespMountUpdate{Code: int64(models.FAILED), Message: "update failed", Data: nil})
	}

	return json.MarshalToString(&RespMountUpdate{Code: int64(models.OK), Message: "success", Data: items[reqMount.Mount.MountId]})
}
