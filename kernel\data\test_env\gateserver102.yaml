name: gateserver102
config: null
shutdown_grace_sec: 30
data_dir: /data/kernel/data/gateserver
disable_nakama_handler: true
logger:
  level: debug
  stdout: true
  file: ''
  rotation: false
  max_size: 100
  max_age: 0
  max_backups: 0
  local_time: false
  compress: false
  format: json
metrics:
  reporting_freq_sec: 60
  namespace: ''
  prometheus_port: 0
  prefix: kernel
  custom_prefix: custom
session:
  encryption_key: 05522ff8cb703981d636a74a6b81ba86
  token_expiry_sec: 3600
  refresh_encryption_key: 410a50c1a54c441d6179033874a3e040
  refresh_token_expiry_sec: 3600
  single_socket: false
  single_match: false
  single_party: false
  single_session: false
socket:
  server_key: 9c7275aab20d18f700e9d9fbf2df0ede
  port: 7250
  address: ''
  protocol: tcp
  max_message_size_bytes: 4096
  max_request_size_bytes: 262144
  read_buffer_size_bytes: 4096
  write_buffer_size_bytes: 4096
  read_timeout_ms: 10000
  write_timeout_ms: 10000
  idle_timeout_ms: 60000
  write_wait_ms: 5000
  pong_wait_ms: 25000
  ping_period_ms: 15000
  ping_backoff_threshold: 20
  outgoing_queue_size: 64
  ssl_certificate: '/data/kernel/mini1.cn.2025.crt'
  ssl_private_key: '/data/kernel/mini1.cn.2025.key'
  response_headers: null
database:
  address:
    - 'postgres:soctest123.@************:5432'
  conn_max_lifetime_ms: 3600000
  max_open_conns: 100
  max_idle_conns: 100
  dns_scan_interval_sec: 60
social:
  steam:
    publisher_key: 'F0B0259F7BD8F1B23CC1C17F7CACD954'
    app_id: 3420320
  facebook_instant_game:
    app_secret: REDACTED
  facebook_limited_login:
    app_id: ''
  apple:
    bundle_id: ''
runtime:
  env: ["LOCAL_CUSTOM_CONFIG_PATH=/data/kernel/data/gateserver102.yaml"]
  path: /data/kernel/data/gateserver
  http_key: defaulthttpkey
  min_count: 0
  lua_min_count: 16
  max_count: 0
  lua_max_count: 48
  js_min_count: 16
  js_max_count: 32
  call_stack_size: 0
  lua_call_stack_size: 128
  registry_size: 0
  lua_registry_size: 512
  event_queue_size: 65536
  event_queue_workers: 8
  read_only_globals: true
  lua_read_only_globals: true
  js_read_only_globals: true
  lua_api_stacktrace: false
  js_entrypoint: ''
match:
  input_queue_size: 128
  call_queue_size: 128
  signal_queue_size: 10
  join_attempt_queue_size: 128
  deferred_queue_size: 128
  join_marker_deadline_ms: 15000
  max_empty_sec: 0
  label_update_interval_ms: 1000
tracker:
  event_queue_size: 1024
console:
  port: 7251
  address: ''
  max_message_size_bytes: 4194304
  read_timeout_ms: 10000
  write_timeout_ms: 60000
  idle_timeout_ms: 300000
  token_expiry_sec: 86400
  signing_key: defaultsigningkey
  mfa:
    storage_encryption_key: the-key-has-to-be-32-bytes-long!
    admin_account_enabled: false
leaderboard:
  blacklist_rank_cache: []
  callback_queue_size: 65536
  callback_queue_workers: 8
  rank_cache_workers: 1
matchmaker:
  max_tickets: 3
  interval_sec: 15
  max_intervals: 2
  rev_precision: false
  rev_threshold: 1
nacos:
  urls:
    - 'http://soc-dev.mini1.cn:8848/nacos'
  namespace_id: '9b5553f2-0768-4928-b604-8e681eb794ab'
  username: 'nacos'
  password: 'socpwd123.'
  timeout_ms: 5000
redis:
  addrs:
    - '127.0.0.1:6379'
  username: ''
  password: ''
  dial_timeout_ms: 5000
  read_timeout_ms: 5000
  write_timeout_ms: 5000
  pool_size: 10
  min_idle_conns: 10
  pool_timeout_ms: 5000
  idle_timeout_ms: 5000
access_log:
  is_disable: false
  write_to_log_file: false
  dir: ""
  record_resp_body: true
  disable_record_req_body: false
  max_backups: 0 # 0表示不限制
  rpc_ids:
    "*": true
    "*rtapi.envelope_ping": false
gate_server_config:
  node_config:
    node_id: 'gateserver102'
    svc_group: 'gateserver@@omo'
    host: '127.0.0.1:7250'
    nethost: '127.0.0.1:7250'
    auth_key: 'defaulthttpkey'
    weight: 10
    scheme: https
  routes:
    '/v2/account/authenticate':
      type: svc
      data: loginserver@@omo
      lbtype: rr
      scheme: http
      prefix: true
    '/api/v1/report_callback':
      type: svc
      data: logicserver@@omo
      lbtype: rr
      scheme: http
      authkey: 'defaulthttpkey'
      routepath: /v2/rpc/logicserver.reportcallback
      unwrap: true
    '/v1/account/create/email':
      type: svc
      data: loginserver@@omo
      lbtype: rr
      scheme: http
      routepath: /v2/rpc/loginserver.createaccount.email
      unwrap: true
    '/v2/account/session/refresh':
      type: svc
      data: loginserver@@omo
      lbtype: rr
      scheme: http
    '/v2/friend':
      type: svc
      data: friendserver@@omo
      lbtype: rr
      scheme: http
      unwrap: true
      prefix: true
    '/v2/channel/':
      type: svc
      data: imserver@@omo
      lbtype: rr
      scheme: http
      unwrap: true
      prefix: true
    '/v2/notification':
      type: svc
      data: imserver@@omo
      lbtype: rr
      scheme: http
      unwrap: true
      prefix: true  
  watch_svc_group_list:
    - 'sessionserver@@omo'
    - 'loginserver@@omo'
    - 'logicserver@@omo'
    - 'queueserver@@omo'
    - 'userserver@@omo'
    - 'friendserver@@omo'
    - 'imserver@@omo'
  ipwhite_list:
    '/api/v1/report_callback':
      - '***************'
      - '**************'
      - '***************'
      - '*************'