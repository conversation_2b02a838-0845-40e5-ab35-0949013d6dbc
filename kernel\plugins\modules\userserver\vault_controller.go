package main

import (
	"context"
	"database/sql"
	"kernel/kernel-common/runtime"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"kernel/plugins/modules/userserver/bo"
	"kernel/plugins/pb"
	"time"

	json "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
)

// @Summary 获取仓库状态及物品详情
// @Description 获取仓库状态及物品详情
// @Tags userserver
// @Accept json
// @Produce json
// @Success 200 {object} RespVaultsList "返回结果"
// @Router /v2/rpc/userserver.storage.info [post]
func (c *Controller) VaultsList(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		return json.MarshalToString(&RespVaultsList{Code: int64(models.FAILED), Message: "session error"})
	}

	vault := bo.Vault{}
	obj := c.server.dbagent.Create(ctx, &vault, models.WithKeys(map[string]interface{}{vault.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err := obj.Query(ctx).As(&vault)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query vault for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespVaultsList{Code: int64(models.FAILED), Message: err.Error()})
		}
		vault.Items = "{}"
	}
	items := map[int64]*VaultItem{} // item_id -> item
	err = json.Unmarshal([]byte(vault.Items), &items)
	if err != nil {
		logger.Error("Failed to unmarshal vault items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespVaultsList{Code: int64(models.FAILED), Message: err.Error()})
	}

	// 检查仓库物品是否过期，并更新
	isupdate := false
	now := time.Now()
	for itemId, item := range items {
		if item.ExpireTime > 0 && item.ExpireTime < now.Unix() {
			delete(items, itemId)
			isupdate = true
		}
	}
	if isupdate {
		vault.UpdatedAt = now
		vault.Items, err = json.MarshalToString(items)
		if err != nil {
			logger.Error("Failed to marshal vault items for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespVaultsList{Code: int64(models.FAILED), Message: err.Error()})
		}
		resp, err := obj.WithReqOption(models.WithValues(map[string]interface{}{"items": vault.Items, "updated_at": vault.UpdatedAt})).Update(ctx).Result()
		if err != nil {
			logger.Error("Failed to update vault for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespVaultsList{Code: int64(models.FAILED), Message: err.Error()})
		}
		if resp.RowsAffected > 0 {
			// nothing to do
		} else {
			return json.MarshalToString(&RespVaultsList{Code: int64(models.FAILED), Message: "update failed"})
		}
	}

	list := pb.VaultList{}
	list.List = make([]*pb.VaultInfo, 0, len(items))
	for _, item := range items {
		id := uint32(item.ItemId)
		expireTime := item.ExpireTime
		count := item.Count
		list.List = append(list.List, &pb.VaultInfo{
			Id:         &id,
			Count:      &count,
			ExpireTime: &expireTime,
		})
	}
	bytes, err := proto.Marshal(&list)
	if err != nil {
		logger.Error("Failed to marshal vault list for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespVaultsList{Code: int64(models.FAILED), Message: err.Error()})
	}

	return json.MarshalToString(&RespVaultsList{Code: int64(models.OK), Message: "success", Data: string(bytes)})
}

// @Summary 存入指定物品列表
// @Description 存入指定物品列表
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqVaultsDeposit true "请求参数"
// @Success 200 {object} RespVaultsDeposit "返回结果"
// @Router /v2/rpc/userserver.storage.deposit [post]
func (c *Controller) VaultsDeposit(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		return json.MarshalToString(&RespVaultsDeposit{Code: models.FAILED, Msg: "session error", Items: nil})
	}

	var req ReqVaultsDeposit
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&RespVaultsDeposit{Code: models.PARAM_ERR, Msg: err.Error(), Items: nil})
	}

	// 检查参数
	reqMap := map[int64]int64{} // item_id -> count
	for _, item := range req.Items {
		if item.Count <= 0 {
			logger.Error("Failed to deposit vault items for user_id %s: %d:%d %s", user_id, item.ItemId, item.Count)
			return json.MarshalToString(&RespVaultsDeposit{Code: models.PARAM_ERR, Msg: "item count must be greater than 0"})
		}
		reqMap[item.ItemId] = item.Count
	}

	// 查询仓库
	isInsert := false
	vault := bo.Vault{}
	obj := c.server.dbagent.Create(ctx, &vault, models.WithKeys(map[string]interface{}{vault.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err := obj.Query(ctx).As(&vault)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query vault for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespVaultsDeposit{Code: models.FAILED, Msg: err.Error(), Items: nil})
		}
		vault.Items = "{}"
		isInsert = true
	}
	dbItems := map[int64]*VaultItem{} // item_id -> item
	err = json.Unmarshal([]byte(vault.Items), &dbItems)
	if err != nil {
		logger.Error("Failed to unmarshal vault items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespVaultsDeposit{Code: models.FAILED, Msg: err.Error(), Items: nil})
	}

	// 仓库物品是否过期，是否超过容量
	existItemCount := 0 // 已存在物品数量
	now := time.Now()
	for itemId, item := range dbItems {
		if item.ExpireTime > 0 && item.ExpireTime < now.Unix() {
			delete(dbItems, itemId)
			continue
		}
		if _, ok := reqMap[itemId]; ok {
			existItemCount++
		}
	}
	// 仓库物品是否超过容量
	addItemCount := len(reqMap) - existItemCount
	if int64(len(dbItems)+addItemCount) > vault.Capacity {
		logger.Error("Failed to deposit vault, storage capacity not enough  user_id %s: dbitems:%d, reqitems:%d, additems:%d", user_id, len(dbItems), len(reqMap), addItemCount)
		return json.MarshalToString(&RespVaultsDeposit{Code: models.FAILED, Msg: "storage capacity not enough"})
	}

	// 添加物品
	for _, item := range req.Items {
		if _, ok := dbItems[item.ItemId]; ok {
			dbItems[item.ItemId].Count += item.Count
			item.Count = dbItems[item.ItemId].Count // 返回给前端
		} else {
			dbItems[item.ItemId] = item
		}
	}

	// 更新仓库
	vault.UpdatedAt = now
	vault.Items, err = json.MarshalToString(dbItems)
	if err != nil {
		logger.Error("Failed to marshal vault items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespVaultsDeposit{Code: models.FAILED, Msg: err.Error(), Items: nil})
	}
	var resp *models.DBAgentResp
	obj.WithReqOption(models.WithValues(map[string]interface{}{"items": vault.Items, "updated_at": vault.UpdatedAt}))
	if isInsert {
		resp, err = obj.Insert(ctx).Result()
	} else {
		resp, err = obj.Update(ctx).Result()
	}
	if err != nil {
		logger.Error("Failed to update vault for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespVaultsDeposit{Code: models.FAILED, Msg: err.Error(), Items: nil})
	}
	if resp.RowsAffected > 0 {
		return json.MarshalToString(&RespVaultsDeposit{Code: models.OK, Msg: "success", Items: req.Items})
	} else {
		return json.MarshalToString(&RespVaultsDeposit{Code: models.FAILED, Msg: "update failed"})
	}
}

// @Summary 提取指定物品列表
// @Description 提取指定物品列表
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqVaultsExtract true "请求参数"
// @Success 200 {object} RespVaultsExtract "返回结果"
// @Router /v2/rpc/userserver.storage.extract [post]
func (c *Controller) VaultsExtract(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		return json.MarshalToString(&RespVaultsExtract{Code: models.FAILED, Msg: "session error", Items: nil})
	}

	var req ReqVaultsExtract
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&RespVaultsExtract{Code: models.FAILED, Msg: err.Error(), Items: nil})
	}

	// 检查参数
	for _, item := range req.Items {
		if item.Count <= 0 {
			logger.Error("Failed to extract vault items for user_id %s: %d:%d", user_id, item.ItemId, item.Count)
			return json.MarshalToString(&RespVaultsExtract{Code: models.PARAM_ERR, Msg: "item count must be greater than 0"})
		}
	}

	// 查询仓库
	vault := bo.Vault{}
	obj := c.server.dbagent.Create(ctx, &vault, models.WithKeys(map[string]interface{}{vault.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err := obj.Query(ctx).As(&vault)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query vault for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespVaultsExtract{Code: models.FAILED, Msg: err.Error(), Items: nil})
		}
		vault.Items = "{}"
	}

	// 检查冷却时间
	if vault.Cooldown > 0 && vault.ExtractTime.After(time.Now().Add(-time.Duration(vault.Cooldown)*time.Second)) {
		logger.Error("Failed to extract vault items for user_id %s: cooldown:%d, extract_time:%s", user_id, vault.Cooldown, vault.ExtractTime.Format(time.RFC3339))
		return json.MarshalToString(&RespVaultsExtract{Code: models.BUSY, Msg: "please wait for cooldown"})
	}

	dbItems := map[int64]*VaultItem{} // item_id -> item
	err = json.Unmarshal([]byte(vault.Items), &dbItems)
	if err != nil {
		logger.Error("Failed to unmarshal vault items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespVaultsExtract{Code: models.FAILED, Msg: err.Error(), Items: nil})
	}

	// 检查物品是否存在,够不够
	for _, item := range req.Items {
		if _, ok := dbItems[item.ItemId]; !ok || dbItems[item.ItemId].Count < item.Count {
			logger.Error("Failed to extract vault items for user_id %s: %d:%d", user_id, item.ItemId, item.Count)
			return json.MarshalToString(&RespVaultsExtract{Code: models.PARAM_ERR, Msg: "item error"})
		}
	}

	// 提取物品
	for _, item := range req.Items {
		dbItems[item.ItemId].Count -= item.Count
		item.Count = dbItems[item.ItemId].Count // 返回给前端
		if dbItems[item.ItemId].Count == 0 {
			delete(dbItems, item.ItemId)
		}
	}

	// 更新仓库
	vault.ExtractTime = time.Now()
	vault.UpdatedAt = time.Now()
	vault.Items, err = json.MarshalToString(dbItems)
	if err != nil {
		logger.Error("Failed to marshal vault items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespVaultsExtract{Code: models.FAILED, Msg: err.Error(), Items: nil})
	}
	resp, err := obj.WithReqOption(models.WithValues(map[string]interface{}{"items": vault.Items, "extract_time": vault.ExtractTime, "updated_at": vault.UpdatedAt})).Update(ctx).Result()
	if err != nil {
		logger.Error("Failed to update vault for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespVaultsExtract{Code: models.FAILED, Msg: err.Error(), Items: nil})
	}
	if resp.RowsAffected > 0 {
		return json.MarshalToString(&RespVaultsExtract{Code: models.OK, Msg: "success", Items: req.Items})
	} else {
		return json.MarshalToString(&RespVaultsExtract{Code: models.FAILED, Msg: "update failed"})
	}
}

// @Summary 仓库扩容
// @Description 仓库扩容
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqVaultsExpand true "请求参数"
// @Success 200 {object} RespVaultsExpand "返回结果"
// @Router /v2/rpc/userserver.storage.expand [post]
func (c *Controller) VaultsExpand(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		return json.MarshalToString(&RespVaultsExpand{Code: models.FAILED, Msg: "session error"})
	}

	var req ReqVaultsExpand
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&RespVaultsExpand{Code: models.FAILED, Msg: err.Error()})
	}

	if req.Count <= 0 {
		return json.MarshalToString(&RespVaultsExpand{Code: models.PARAM_ERR, Msg: "count must be greater than 0"})
	}

	// 查询仓库
	isInsert := false
	vault := bo.Vault{}
	obj := c.server.dbagent.Create(ctx, &vault, models.WithKeys(map[string]interface{}{vault.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err := obj.Query(ctx).As(&vault)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query vault for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespVaultsExpand{Code: models.FAILED, Msg: err.Error()})
		}
		vault.Items = "{}"
		isInsert = true
	}

	// 更新仓库容量
	vault.Capacity += req.Count
	vault.UpdatedAt = time.Now()
	var resp *models.DBAgentResp
	obj.WithReqOption(models.WithValues(map[string]interface{}{"capacity": vault.Capacity, "updated_at": vault.UpdatedAt}))
	if isInsert {
		resp, err = obj.Insert(ctx).Result()
	} else {
		resp, err = obj.Update(ctx).Result()
	}
	if err != nil {
		logger.Error("Failed to update vault for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespVaultsExpand{Code: models.FAILED, Msg: err.Error()})
	}
	if resp.RowsAffected > 0 {
		return json.MarshalToString(&RespVaultsExpand{Code: models.OK, Msg: "success", Capacity: vault.Capacity})
	} else {
		return json.MarshalToString(&RespVaultsExpand{Code: models.FAILED, Msg: "update failed"})
	}
}

// 赛季继承物资标记
func (c *Controller) VaultsSeasonInherit(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	return "", nil
}

// @Summary 删除仓库中指定物品
// @Description 删除仓库中指定物品
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqVaultsDelete true "请求参数"
// @Success 200 {object} RespVaultsDelete "返回结果"
// @Router /v2/rpc/userserver.storage.delete [post]
func (c *Controller) VaultsDelete(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		return json.MarshalToString(&RespVaultsDelete{Code: models.FAILED, Msg: "session error"})
	}

	var req ReqVaultsDelete
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&RespVaultsDelete{Code: models.FAILED, Msg: err.Error()})
	}

	// 查询仓库
	vault := bo.Vault{}
	obj := c.server.dbagent.Create(ctx, &vault, models.WithKeys(map[string]interface{}{vault.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err := obj.Query(ctx).As(&vault)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query vault for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespVaultsDelete{Code: models.FAILED, Msg: err.Error()})
		}
		vault.Items = "{}"
	}
	dbItems := map[int64]*VaultItem{} // item_id -> item
	err = json.Unmarshal([]byte(vault.Items), &dbItems)
	if err != nil {
		logger.Error("Failed to unmarshal vault items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespVaultsDelete{Code: models.FAILED, Msg: err.Error()})
	}

	// 检查要删除的物品是否存在
	for _, item := range req.Items {
		if _, ok := dbItems[item.ItemId]; !ok {
			logger.Error("Failed to delete vault items for user_id %s: %d", user_id, item.ItemId)
			return json.MarshalToString(&RespVaultsDelete{Code: models.PARAM_ERR, Msg: "item not found"})
		}
		delete(dbItems, item.ItemId)
	}

	// 更新仓库
	vault.UpdatedAt = time.Now()
	vault.Items, err = json.MarshalToString(dbItems)
	if err != nil {
		logger.Error("Failed to marshal vault items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespVaultsDelete{Code: models.FAILED, Msg: err.Error()})
	}
	resp, err := obj.WithReqOption(models.WithValues(map[string]interface{}{"items": vault.Items, "updated_at": vault.UpdatedAt})).Update(ctx).Result()
	if err != nil {
		logger.Error("Failed to update vault for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespVaultsDelete{Code: models.FAILED, Msg: err.Error()})
	}
	if resp.RowsAffected > 0 {
		return json.MarshalToString(&RespVaultsDelete{Code: models.OK, Msg: "success"})
	} else {
		return json.MarshalToString(&RespVaultsDelete{Code: models.FAILED, Msg: "update failed"})
	}
}
