Angular service code gen
=======

#### An utility tool to generate an angular REST client from the swagger/openapi spec service definitions generated by the protoc toolchain.


### Install
```sh
  go install github.com/heroiclabs/nakama/console/openapi-gen-angular
```
### Usage

#### Options

* `input`: The pathname for the swagger input.
* `output`: The pathname of the generated TypeScript service class.
* `rm_prefix`: Optional list of prefixes, delimited by `,`, to remove from types and function names.
#### Generate the Angular service
##### Example
```shell
go run . -i '../console.swagger.json' -o '../ui/src/app/console.service.ts' -rm_prefix='console,nakamaconsole,nakama,Console_'
```

The output file is: `console.service.ts`.

### Limitations

The code generator has __only__ been checked against a limited set of grpc-gateway service definitions YMMV.
