local nk = require("nakama")
local du = require("debug_utils")

local function add_numbers(context, payload)
    nk.logger_info("Received RPC payload: " .. payload)
    nk.logger_info("Context: " .. nk.json_encode(context))
    local data = nk.json_decode(payload)
    local sum = (data.a or 0) + (data.b or 0)
    return nk.json_encode({ result = sum })
end

nk.register_rpc(add_numbers, "add_numbers")

