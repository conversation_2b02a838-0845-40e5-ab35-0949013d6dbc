package main

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/common/timerwheel"
	"kernel/plugins/models"
	"strings"
	"sync"
	"time"

	"github.com/dgraph-io/ristretto/v2"
	"github.com/jmoiron/sqlx"
	json "github.com/json-iterator/go"
)

type Controller struct {
	server      *DbagentServerModule
	cache       *ristretto.Cache[string, CacheItem]
	timerPool   *timerwheel.TimeWheelPool
	reqPool     sync.Pool
	dirtySet    sync.Map // key: string, val: struct{}
	remotelocks sync.Map // key: string, val: *RemoteRWLock
	sqlxDB      *sqlx.DB
}

func NewController(s *DbagentServerModule) *Controller {
	cache, err := ristretto.NewCache(&ristretto.Config[string, CacheItem]{
		NumCounters: 1e7,     // number of keys to track frequency of (10M).
		MaxCost:     1 << 30, // maximum cost of cache (1GB).
		BufferItems: 64,      // number of keys per Get buffer.
		Metrics:     true,
		OnEvict: func(item *ristretto.Item[CacheItem]) {
			if item != nil && item.Value.FlushType() == models.FLUSH_TYPE_TIMER {
				if item.Value.IsDirty() {
					item.Value.Flush()
				}
			}
		},
		OnExit: func(item CacheItem) {
			if item != nil && item.FlushType() == models.FLUSH_TYPE_TIMER {
				if item.IsDirty() {
					item.Flush()
				}
			}
		},
	})
	if err != nil {
		panic(err)
	}
	c := &Controller{
		server: s,
		cache:  cache,
		reqPool: sync.Pool{New: func() interface{} {
			return new(models.DBAgentReq)
		}},
		timerPool: s.common.TimerPool,
		sqlxDB:    sqlx.NewDb(s.db, "postgres"),
	}

	go c.SystemTask()

	return c
}

func (c *Controller) ReqLocker(ctx context.Context, req *models.DBAgentReq) error {
	if req.LockId > 0 {
		lockid := req.LockId
		lockopt := req.LockOpt
		locktimeout := time.Duration(req.LockTimeout) * time.Millisecond
		lockkey := req.LockKey
		if lockkey == "" {
			vals := []string{}
			for _, v := range req.Keys {
				vals = append(vals, fmt.Sprintf("%v", v))
			}
			lockkey = fmt.Sprintf("%s:%v", req.Table, strings.Join(vals, ":"))
		}
		if lockkey == "" {
			return errors.New("lock key is empty")
		}
		if locktimeout == 0 {
			locktimeout = 3000 * time.Millisecond
		}
		if lockopt == models.LOCKOPT_LOCK_READ { // 加读锁
			locker, _ := c.remotelocks.LoadOrStore(lockkey, common.NewRemoteRWLock(c.timerPool))
			if locker != nil {
				if !locker.(*common.RemoteRWLock).RLockTimeout(ctx, lockid, locktimeout) {
					return errors.New("rlock timeout")
				}
			}
		} else if lockopt == models.LOCKOPT_LOCK_WRITE { // 加写锁
			locker, _ := c.remotelocks.LoadOrStore(lockkey, common.NewRemoteRWLock(c.timerPool))
			if locker != nil {
				if !locker.(*common.RemoteRWLock).LockTimeout(ctx, lockid, locktimeout) {
					return errors.New("wlock timeout")
				}
			}
		}
	}
	return nil
}

func (c *Controller) ReqUnLocker(req *models.DBAgentReq) error {
	if req.LockId > 0 {
		lockid := req.LockId
		lockopt := req.LockOpt
		lockkey := req.LockKey
		if lockkey == "" {
			vals := []string{}
			for _, v := range req.Keys {
				vals = append(vals, fmt.Sprintf("%v", v))
			}
			lockkey = fmt.Sprintf("%s:%v", req.Table, strings.Join(vals, ":"))
		}
		if lockkey == "" {
			return errors.New("lock key is empty")
		}
		if lockopt == models.LOCKOPT_UNLOCK_READ { // 解读锁
			locker, ok := c.remotelocks.Load(lockkey)
			if ok {
				locker.(*common.RemoteRWLock).RUnlock(lockid)
			}
		} else if lockopt == models.LOCKOPT_UNLOCK_WRITE { // 解写锁
			locker, ok := c.remotelocks.Load(lockkey)
			if ok {
				locker.(*common.RemoteRWLock).Unlock(lockid)
			}
		}
	} else {
		return errors.New("lock id is empty")
	}
	return nil
}

func (c *Controller) SystemTask() {
	dirtyticker := c.timerPool.Get().NewTicker(time.Minute * 1)
	for {
		select {
		case <-dirtyticker.C:
			c.dirtySet.Range(func(key, value interface{}) bool {
				item, found := c.cache.Get(key.(string))
				if !found {
					c.dirtySet.Delete(key)
				}
				if item != nil {
					if item.IsDirty() {
						if err := item.Flush(); err != nil && err == models.ErrDBResVersionConflict {
							c.DelCache(key.(string), item, false, item.GetRowResultType())
							c.server.logger.Error("version conflict %s", key)
						} else if err == nil {
							c.dirtySet.Delete(key)
						}
					} else {
						c.dirtySet.Delete(key)
					}
				}
				return true
			})
		}
	}
}

func (c *Controller) Unlock(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	req := c.reqPool.Get().(*models.DBAgentReq)
	defer func() {
		req.Reset()
		c.reqPool.Put(req)
	}()
	//logger.Debug("unlock payload %s", payload)
	if err := req.Unmarshal([]byte(payload)); err != nil {
		logger.Error("json unmarshal error %v", err)
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: err.Error()})
	}
	if err := c.ReqUnLocker(req); err != nil {
		return json.MarshalToString(&models.DBAgentResp{Code: models.LOCK_ERR, Msg: err.Error()})
	}
	return json.MarshalToString(&models.DBAgentResp{Code: models.OK, Msg: ""})
}

// @Summary 数据库代理查询
// @Description 数据库代理查询
// @Tags dbagentserver
// @Accept json
// @Produce json
// @Param payload body models.DBAgentReq true "请求参数"
// @Success 200 {object} models.DBAgentResp "成功"
// @Router /v2/rpc/dbagentserver.query [post]
func (c *Controller) Query(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	req := c.reqPool.Get().(*models.DBAgentReq)
	defer func() {
		req.Reset()
		c.reqPool.Put(req)
	}()

	//logger.Debug("query payload %s", payload)
	if err := req.Unmarshal([]byte(payload)); err != nil {
		logger.Error("json unmarshal error %v", err)
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: err.Error()})
	}

	// 检查keys是否存在
	if len(req.Keys) == 0 {
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: "keys is empty"})
	}
	// 检查表名是否存在
	if req.Table == "" {
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: "table is empty"})
	}
	// 多行结构，检查二级键名称是否存在
	if req.RowResultType == models.ROWRESULT_TYPE_MULTI {
		if req.RowsSecondName == "" {
			return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: "rowssecondname is empty"})
		}
	}

	if err := c.ReqLocker(ctx, req); err != nil {
		return json.MarshalToString(&models.DBAgentResp{Code: models.LOCK_ERR, Msg: err.Error()})
	}

	var (
		cachekey string
		item     CacheItem
		found    bool
	)

	// 尝试从缓存中获取数据
	if !req.NotOptCache {
		vals := []string{req.Table}
		for k, v := range req.Keys {
			vals = append(vals, k, fmt.Sprintf("%v", v))
		}
		cachekey = strings.Join(vals, ":")
		item, found = c.cache.Get(cachekey)
		if found {
			c.cache.SetWithTTL(cachekey, item, int64(req.CacheLevel), time.Duration(req.TTL)*time.Second) // 续期key
			c.UpdateTTL(item, req.CacheLevel, req.TTL, req.Keys)
			return json.MarshalToString(&models.DBAgentResp{Code: models.OK, Msg: "", Data: item.String(req.Values)})
		}
	}

	// 缓存未命中，调用加载函数
	item = NewHashCacheItem(db, req.Keys, req.Table, req.QueryArgSql, req.FlushType, req.RowResultType, req.RowsSecondName, req.UniqueKeys, req.VersionName)
	if _, err := item.Query(); err != nil {
		return json.MarshalToString(&models.DBAgentResp{Code: models.EXCEPTION, Msg: err.Error()})
	}
	if !req.NotOptCache {
		if c.cache.SetWithTTL(cachekey, item, int64(req.CacheLevel), time.Duration(req.TTL)*time.Second) {
			c.cache.Wait()
			c.UpdateTTL(item, req.CacheLevel, req.TTL, req.Keys)
		} else {
			return json.MarshalToString(&models.DBAgentResp{Code: models.EXCEPTION, Msg: "cache set error"})
		}
	}
	return json.MarshalToString(&models.DBAgentResp{Code: models.OK, Msg: "", Data: item.String(req.Values)})
}

// @Summary 数据库代理插入
// @Description 数据库代理插入
// @Tags dbagentserver
// @Accept json
// @Produce json
// @Param payload body models.DBAgentReq true "请求参数"
// @Success 200 {object} models.DBAgentResp "成功"
// @Router /v2/rpc/dbagentserver.insert [post]
func (c *Controller) Insert(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	req := c.reqPool.Get().(*models.DBAgentReq)
	defer func() {
		req.Reset()
		c.reqPool.Put(req)
	}()

	//logger.Debug("insert payload %s", payload)
	if err := req.Unmarshal([]byte(payload)); err != nil {
		logger.Error("json unmarshal error %v", err)
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: err.Error()})
	}

	// 检查keys是否存在
	if len(req.Keys) == 0 {
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: "keys is empty"})
	}
	// 检查表名是否存在
	if req.Table == "" {
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: "table is empty"})
	}
	// 检查键名称是否存在在values
	for key, _ := range req.Keys {
		if _, ok := req.Values[key]; !ok {
			return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: fmt.Sprintf("key %s is empty, not found in values", key)})
		}
	}
	// 多行结构，检查二级键名称是否存在在values
	if req.RowResultType == models.ROWRESULT_TYPE_MULTI {
		if req.RowsSecondName == "" {
			return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: "rowssecondname is empty"})
		}
		if _, ok := req.Values[req.RowsSecondName]; !ok {
			return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: fmt.Sprintf("rowssecondname %s is empty, not found in values", req.RowsSecondName)})
		}
	}

	if err := c.ReqLocker(ctx, req); err != nil {
		return json.MarshalToString(&models.DBAgentResp{Code: models.LOCK_ERR, Msg: err.Error()})
	}

	var (
		cachekey string
		item     CacheItem
		found    bool
	)

	if !req.NotOptCache {
		vals := []string{req.Table}
		for k, v := range req.Keys {
			vals = append(vals, k, fmt.Sprintf("%v", v))
		}
		cachekey = strings.Join(vals, ":")
		item, found = c.cache.Get(cachekey)
		if !found {
			item = NewHashCacheItem(db, req.Keys, req.Table, req.QueryArgSql, req.FlushType, req.RowResultType, req.RowsSecondName, req.UniqueKeys, req.VersionName)
			if _, err := item.Query(); err != nil {
				return json.MarshalToString(&models.DBAgentResp{Code: models.EXCEPTION, Msg: err.Error()})
			}

			if c.cache.SetWithTTL(cachekey, item, int64(req.CacheLevel), time.Duration(req.TTL)*time.Second) {
				c.cache.Wait()
				c.UpdateTTL(item, req.CacheLevel, req.TTL, req.Keys)
			} else {
				return json.MarshalToString(&models.DBAgentResp{Code: models.EXCEPTION, Msg: "cache set error"})
			}
		} else {
			c.cache.SetWithTTL(cachekey, item, int64(req.CacheLevel), time.Duration(req.TTL)*time.Second) // 续期key
			c.UpdateTTL(item, req.CacheLevel, req.TTL, req.Keys)
		}
	} else {
		item = NewHashCacheItem(db, req.Keys, req.Table, req.QueryArgSql, req.FlushType, req.RowResultType, req.RowsSecondName, req.UniqueKeys, req.VersionName)
	}

	if req.FlushType == models.FLUSH_TYPE_SYNC {
		if result, err := item.Insert(req.Values); result == nil && err != nil {
			return json.MarshalToString(&models.DBAgentResp{Code: models.EXCEPTION, Msg: err.Error()})
		} else {
			lastid, _ := result.LastInsertId()
			affected, _ := result.RowsAffected()
			return json.MarshalToString(&models.DBAgentResp{Code: models.OK, Msg: "", LastInsertId: lastid, RowsAffected: affected})
		}
	} else if req.FlushType == models.FLUSH_TYPE_ASYNC {
		item.AsyncUpdate(req.Values)
		c.server.common.GroutinePool.Submit(func() {
			item.Flush()
		})
		return json.MarshalToString(&models.DBAgentResp{Code: models.OK, Msg: ""})
	} else if req.FlushType == models.FLUSH_TYPE_TIMER {
		item.AsyncUpdate(req.Values)
		c.dirtySet.Store(cachekey, struct{}{})
		return json.MarshalToString(&models.DBAgentResp{Code: models.OK, Msg: ""})
	} else {
		item.AsyncUpdate(req.Values)
	}
	return json.MarshalToString(&models.DBAgentResp{Code: models.OK, Msg: ""})
}

// @Summary 数据库代理更新
// @Description 数据库代理更新
// @Tags dbagentserver
// @Accept json
// @Produce json
// @Param payload body models.DBAgentReq true "请求参数"
// @Success 200 {object} models.DBAgentResp "成功"
// @Router /v2/rpc/dbagentserver.update [post]
func (c *Controller) Update(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	req := c.reqPool.Get().(*models.DBAgentReq)
	defer func() {
		req.Reset()
		c.reqPool.Put(req)
	}()

	//logger.Debug("update payload %s", payload)
	if err := req.Unmarshal([]byte(payload)); err != nil {
		logger.Error("json unmarshal error %v", err)
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: err.Error()})
	}

	// 检查keys是否存在
	if len(req.Keys) == 0 {
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: "keys is empty"})
	}
	// 检查表名是否存在
	if req.Table == "" {
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: "table is empty"})
	}
	// 检查键名称是否存在在values
	for key, _ := range req.Keys {
		if _, ok := req.Values[key]; !ok {
			return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: fmt.Sprintf("key %s is empty, not found in values", key)})
		}
	}
	// 多行结构，检查二级键名称是否存在在values
	if req.RowResultType == models.ROWRESULT_TYPE_MULTI {
		if req.RowsSecondName == "" {
			return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: "rowssecondname is empty"})
		}
		if _, ok := req.Values[req.RowsSecondName]; !ok {
			return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: fmt.Sprintf("rowssecondname %s is empty, not found in values", req.RowsSecondName)})
		}
	}

	if err := c.ReqLocker(ctx, req); err != nil {
		return json.MarshalToString(&models.DBAgentResp{Code: models.LOCK_ERR, Msg: err.Error()})
	}

	var (
		cachekey string
		item     CacheItem
		found    bool
	)

	if !req.NotOptCache {
		vals := []string{req.Table}
		for k, v := range req.Keys {
			vals = append(vals, k, fmt.Sprintf("%v", v))
		}
		cachekey = strings.Join(vals, ":")
		item, found = c.cache.Get(cachekey)
		if !found {
			item = NewHashCacheItem(db, req.Keys, req.Table, req.QueryArgSql, req.FlushType, req.RowResultType, req.RowsSecondName, req.UniqueKeys, req.VersionName)
			if _, err := item.Query(); err != nil {
				return json.MarshalToString(&models.DBAgentResp{Code: models.EXCEPTION, Msg: err.Error()})
			}
			if c.cache.SetWithTTL(cachekey, item, int64(req.CacheLevel), time.Duration(req.TTL)*time.Second) {
				c.cache.Wait()
				c.UpdateTTL(item, req.CacheLevel, req.TTL, req.Keys)
			} else {
				return json.MarshalToString(&models.DBAgentResp{Code: models.EXCEPTION, Msg: "cache set error"})
			}
		} else {
			c.cache.SetWithTTL(cachekey, item, int64(req.CacheLevel), time.Duration(req.TTL)*time.Second) // 续期key
			c.UpdateTTL(item, req.CacheLevel, req.TTL, req.Keys)
		}
	} else {
		item = NewHashCacheItem(db, req.Keys, req.Table, req.QueryArgSql, req.FlushType, req.RowResultType, req.RowsSecondName, req.UniqueKeys, req.VersionName)
	}

	if req.FlushType == models.FLUSH_TYPE_SYNC {
		if result, err := item.Update(req.Values); result == nil && err != nil {
			if err == models.ErrDBResVersionConflict { // 版本冲突，删除缓存
				c.DelCache(cachekey, item, req.NotOptCache, req.RowResultType)
				logger.Error("version conflict %s", cachekey)
			}
			return json.MarshalToString(&models.DBAgentResp{Code: models.EXCEPTION, Msg: err.Error()})
		} else {
			affected, _ := result.RowsAffected()
			return json.MarshalToString(&models.DBAgentResp{Code: models.OK, Msg: "", RowsAffected: affected})
		}
	} else if req.FlushType == models.FLUSH_TYPE_ASYNC {
		notOptCache := req.NotOptCache
		rowResultType := req.RowResultType
		item.AsyncUpdate(req.Values)
		c.server.common.GroutinePool.Submit(func() {
			if err := item.Flush(); err != nil && err == models.ErrDBResVersionConflict { // 版本冲突，删除缓存
				c.DelCache(cachekey, item, notOptCache, rowResultType)
				logger.Error("version conflict %s", cachekey)
			}
		})
		return json.MarshalToString(&models.DBAgentResp{Code: models.OK, Msg: ""})
	} else if req.FlushType == models.FLUSH_TYPE_TIMER {
		item.AsyncUpdate(req.Values)
		c.dirtySet.Store(cachekey, struct{}{})
		return json.MarshalToString(&models.DBAgentResp{Code: models.OK, Msg: ""})
	} else {
		item.AsyncUpdate(req.Values)
	}
	return json.MarshalToString(&models.DBAgentResp{Code: models.OK, Msg: ""})
}

// @Summary 数据库代理删除
// @Description 数据库代理删除
// @Tags dbagentserver
// @Accept json
// @Produce json
// @Param payload body models.DBAgentReq true "请求参数"
// @Success 200 {object} models.DBAgentResp "成功"
// @Router /v2/rpc/dbagentserver.delete [post]
func (c *Controller) Delete(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	req := c.reqPool.Get().(*models.DBAgentReq)
	defer func() {
		req.Reset()
		c.reqPool.Put(req)
	}()

	//logger.Debug("delete payload %s", payload)
	if err := req.Unmarshal([]byte(payload)); err != nil {
		logger.Error("json unmarshal error %v", err)
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: err.Error()})
	}

	// 检查keys是否存在
	if len(req.Keys) == 0 {
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: "keys is empty"})
	}
	// 检查表名是否存在
	if req.Table == "" {
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: "table is empty"})
	}

	// 多行结构，检查二级键名称是否存在在values
	if req.RowResultType == models.ROWRESULT_TYPE_MULTI {
		if req.RowsSecondName == "" {
			return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: "rowssecondname is empty"})
		}
		if _, ok := req.Values[req.RowsSecondName]; !ok {
			return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: fmt.Sprintf("rowssecondname %s is empty, not found in values", req.RowsSecondName)})
		}
	}

	if err := c.ReqLocker(ctx, req); err != nil {
		return json.MarshalToString(&models.DBAgentResp{Code: models.LOCK_ERR, Msg: err.Error()})
	}

	var (
		cachekey string
		item     CacheItem
		found    bool
	)

	secondval := req.Values[req.RowsSecondName]
	vals := []string{req.Table}
	for k, v := range req.Keys {
		vals = append(vals, k, fmt.Sprintf("%v", v))
	}
	cachekey = strings.Join(vals, ":")
	if !req.NotOptCache {
		item, found = c.cache.Get(cachekey)
		if !found {
			item = NewHashCacheItem(db, req.Keys, req.Table, req.QueryArgSql, req.FlushType, req.RowResultType, req.RowsSecondName, req.UniqueKeys, req.VersionName)
		}
	} else {
		item = NewHashCacheItem(db, req.Keys, req.Table, req.QueryArgSql, req.FlushType, req.RowResultType, req.RowsSecondName, req.UniqueKeys, req.VersionName)
	}

	if req.FlushType == models.FLUSH_TYPE_SYNC {
		if result, err := item.Delete(secondval); err != nil {
			return json.MarshalToString(&models.DBAgentResp{Code: models.EXCEPTION, Msg: err.Error()})
		} else {
			affected, _ := result.RowsAffected()
			c.DelCache(cachekey, item, req.NotOptCache, req.RowResultType)
			return json.MarshalToString(&models.DBAgentResp{Code: models.OK, Msg: "", RowsAffected: affected})
		}
	} else {
		notOptCache := req.NotOptCache
		rowResultType := req.RowResultType
		c.server.common.GroutinePool.Submit(func() {
			if result, err := item.Delete(secondval); err != nil {
				logger.Error("delete error %v", err)
				return
			} else {
				c.DelCache(cachekey, item, notOptCache, rowResultType)
				affected, _ := result.RowsAffected()
				logger.Debug("delete affected %s %d", cachekey, affected)
			}
		})
	}

	return json.MarshalToString(&models.DBAgentResp{Code: models.OK, Msg: ""})
}

// @Summary 数据库代理执行原始sql
// @Description 数据库代理执行原始sql
// @Tags dbagentserver
// @Accept json
// @Produce json
// @Param payload body models.DBAgentReq true "请求参数"
// @Success 200 {object} models.DBAgentResp "成功"
// @Router /v2/rpc/dbagentserver.rawexec [post]
func (c *Controller) RawExec(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	req := c.reqPool.Get().(*models.DBAgentReq)
	defer func() {
		req.Reset()
		c.reqPool.Put(req)
	}()

	//logger.Debug("rawexec payload %s", payload)
	if err := req.Unmarshal([]byte(payload)); err != nil {
		logger.Error("json unmarshal error %v", err)
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: err.Error()})
	}

	// 检查 SQL 语句是否为空
	if req.QueryArgSql == "" {
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: "SQL query cannot be empty"})
	}

	if req.FlushType == models.FLUSH_TYPE_SYNC {
		result, err := db.Exec(req.QueryArgSql)
		if err != nil {
			return json.MarshalToString(&models.DBAgentResp{Code: models.EXCEPTION, Msg: err.Error()})
		}
		lastid, _ := result.LastInsertId()
		affected, _ := result.RowsAffected()
		return json.MarshalToString(&models.DBAgentResp{Code: models.OK, Msg: "", LastInsertId: lastid, RowsAffected: affected})
	} else {
		strsql := req.QueryArgSql
		c.server.common.GroutinePool.Submit(func() {
			_, err := db.Exec(strsql)
			if err != nil {
				logger.Error("rawexec error %s %v", strsql, err)
			}
		})
	}
	return json.MarshalToString(&models.DBAgentResp{Code: models.OK, Msg: ""})
}

// @Summary 数据库代理执行原始sql
// @Description 数据库代理执行原始sql，不支持联表查询(因为更新会失败)
// @Tags dbagentserver
// @Accept json
// @Produce json
// @Param payload body models.DBAgentReq true "请求参数"
// @Success 200 {object} models.DBAgentResp "成功"
// @Router /v2/rpc/dbagentserver.rawquery [post]
func (c *Controller) RawQuery(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	req := c.reqPool.Get().(*models.DBAgentReq)
	defer func() {
		req.Reset()
		c.reqPool.Put(req)
	}()

	//logger.Debug("rawquery payload %s", payload)
	if err := req.Unmarshal([]byte(payload)); err != nil {
		logger.Error("json unmarshal error %v", err)
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: err.Error()})
	}

	// 检查 SQL 语句是否为空
	if req.QueryArgSql == "" {
		return json.MarshalToString(&models.DBAgentResp{Code: models.PARAM_ERR, Msg: "SQL query cannot be empty"})
	}

	// 使用 sqlx 的 Queryx 获取 *sqlx.Rows
	rows, err := c.sqlxDB.QueryxContext(ctx, req.QueryArgSql)
	if err != nil {
		logger.Error("query error: %v", err)
		return json.MarshalToString(&models.DBAgentResp{Code: models.EXCEPTION, Msg: err.Error()})
	}
	defer rows.Close()

	// 预分配内存,避免频繁扩容
	rowMap := make(map[string]interface{}, 32)
	results := make([]map[string]interface{}, 0, 100)

	// 使用 MapScan 直接获取 map
	for rows.Next() {
		for k := range rowMap {
			delete(rowMap, k)
		}

		if err := rows.MapScan(rowMap); err != nil {
			logger.Error("scan row error: %v", err)
			return json.MarshalToString(&models.DBAgentResp{Code: models.EXCEPTION, Msg: err.Error()})
		}
		convertedMap := ConvertRowMap(rowMap)
		results = append(results, convertedMap)
	}

	// 检查迭代过程中是否有错误
	if err = rows.Err(); err != nil {
		logger.Error("rows iteration error: %v", err)
		return json.MarshalToString(&models.DBAgentResp{Code: models.EXCEPTION, Msg: err.Error()})
	}

	// 写缓存
	if !req.NotOptCache {
		columns, err := rows.Columns()
		if err != nil {
			logger.Error("get columns error: %v", err)
			return json.MarshalToString(&models.DBAgentResp{Code: models.EXCEPTION, Msg: err.Error()})
		}
		for _, row := range results {
			vals := []string{}
			keys := map[string]interface{}{}
			for k, _ := range req.Keys {
				keys[k] = row[k]
				vals = append(vals, fmt.Sprintf("%v", row[k]))
			}
			item := NewHashCacheItem(db, keys, req.Table, strings.Join(columns, ","), req.FlushType, req.RowResultType, req.RowsSecondName, req.UniqueKeys, req.VersionName)
			item.MultiHset(row)
			cachekey := fmt.Sprintf("%s:%v", req.Table, strings.Join(vals, ":"))
			c.cache.SetWithTTL(cachekey, item, int64(req.CacheLevel), time.Duration(req.TTL)*time.Second)
			c.UpdateTTL(item, req.CacheLevel, req.TTL, req.Keys)
		}
	}
	jsonstr, _ := json.MarshalToString(results)
	return json.MarshalToString(&models.DBAgentResp{Code: models.OK, Msg: "", Data: jsonstr})
}

func (c *Controller) UpdateTTL(item CacheItem, cachelevel models.CacheLevel, ttl int64, filter map[string]interface{}) {
	if item == nil {
		return
	}

	if item.GetRowResultType() == models.ROWRESULT_TYPE_ONLYONE {
		for k, v := range item.GetUniques() {
			if _, ok := filter[k]; ok {
				continue
			}
			if v == nil {
				continue
			}
			tmpv := common.Interface2String(v)
			if tmpv == "" || tmpv == "0" {
				continue
			}
			cachekey := fmt.Sprintf("%s:%s:%s", item.TableName(), k, tmpv)
			c.cache.SetWithTTL(cachekey, item, int64(cachelevel), time.Duration(ttl)*time.Second)
		}
	}
}

func (c *Controller) DelCache(cachekey string, item CacheItem, notOptCache bool, rowResultType models.RowResultType) {
	if item == nil || cachekey == "" {
		return
	}

	if !notOptCache {
		c.cache.Del(cachekey)
	}
	c.dirtySet.Delete(cachekey)
	if rowResultType == models.ROWRESULT_TYPE_ONLYONE {
		uniques := item.GetUniques()
		for k, v := range uniques {
			tmpcachekey := fmt.Sprintf("%s:%s:%v", item.TableName(), k, v)
			c.cache.Del(tmpcachekey)
		}
	}
}
