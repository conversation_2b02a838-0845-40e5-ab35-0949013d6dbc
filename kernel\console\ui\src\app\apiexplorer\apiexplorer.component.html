<h2 class="pb-4">API Explorer</h2>

<ngb-alert [dismissible]="false" type="danger" class="mb-3" *ngIf="error">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">Error invoking RPC call: {{error}}</h6>
</ngb-alert>

<div class="row no-gutters mb-3">
  <div class="col d-flex justify-content-between no-gutters">
    <div class="col-md-9">
      <form [formGroup]="endpointCallForm" (ngSubmit)="sendRequest()">
        <div class="input-group">
          <div class="input-group-prepend">
            <select class="form-control custom-select dropdown-radius" id="method" formControlName="method">
              <option [value]="''" disabled>Select Endpoint</option>
              <option *ngFor="let e of rpcEndpoints; index as i" value="{{e.method}}">{{e.method}}</option>
              <option disabled class="text-muted">----</option>
              <option *ngFor="let e of endpoints; index as i" value="{{e.method}}">{{e.method}}</option>
            </select>
          </div>
          <input type="text" class="form-control border-right-0" formControlName="user_id" placeholder="set user ID as request context"/>
          <div class="input-group-append">
            <button class="btn btn-primary" type="submit" [disabled]="f.method.invalid">Send Request</button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<button class="btn btn-primary" type="button" (click)="addSessionVars();" *ngIf="!addVars">Add Session Variables</button>
<div [hidden]="!addVars" class="row mb-3">
  <div class="col-md-6 py-3">
    <h5>Session Vars</h5>
    <div class="text-muted pt-2">Add variables as object of string to string key-value pairs.</div>
    <div class="card p-2 mb-3 jsoneditor">
      <div #editorVars style="height: 200px"></div>
    </div>
  </div>
</div>
<div class="row mb-3">
  <div class="col-lg-6 py-3">
    <h5>Request Body</h5>
    <div class="card p-2 mb-3 jsoneditor">
      <div #editorReq style="height: 500px"></div>
    </div>
  </div>
  <div class="col-lg-6 py-3">
    <h5>Response</h5>
    <div class="card p-2 mb-3 jsoneditor">
      <div #editorRes style="height: 500px"></div>
    </div>
  </div>
</div>
