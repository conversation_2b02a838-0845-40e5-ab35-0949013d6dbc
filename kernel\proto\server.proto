syntax = "proto3";
package pb;
option go_package = "plugins/pb";

message VaultInfo {
  optional uint32 Id = 1;
  optional int64 ExpireTime = 2;
  optional int64 count = 3;
}

// 仓库列表
message VaultList {
  repeated VaultInfo list = 1;
}

message SkinInfo {
  optional uint32 Id = 1;
  optional int64 ExpireTime = 2;
}

// 皮肤列表
message SkinList {
  repeated SkinInfo list = 1;
}

message AvatarInfo {
  optional uint32 Id = 1;
  optional int64 ExpireTime = 2;
}

// avatar列表
message AvatarList {
  repeated AvatarInfo list = 1;
}

message MountInfo {
  optional uint32 Id = 1;
  optional int64 ExpireTime = 2;
}

// 坐骑列表
message MountList {
  repeated MountInfo list = 1;
}