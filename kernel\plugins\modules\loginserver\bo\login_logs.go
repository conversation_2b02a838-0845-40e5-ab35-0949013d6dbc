package bo

import (
	"bytes"
	"fmt"
	"kernel/plugins/common"
	"kernel/plugins/models"
	"reflect"
	"time"

	json "github.com/json-iterator/go"
)

type LoginLogs struct {
	UserId    string    `json:"user_id,omitempty"`
	Uin       int32     `json:"uin,omitempty"`
	LoginTime time.Time `json:"login_time,omitempty"`
	IpAddress string    `json:"ip_address,omitempty"`
	Device    string    `json:"device,omitempty"`
	Metadata  string    `json:"metadata,omitempty"`
}

func (o *LoginLogs) GetTable() string {
	return "login_logs"
}

func (o *LoginLogs) GetKeyName() string {
	return "id"
}

func (o *LoginLogs) GetUniqueKeys() []string {
	return []string{"user_id", "uin"}
}

func (o *LoginLogs) GetSecondKeyName() string {
	return ""
}

func (o *LoginLogs) GetRowResultType() models.RowResultType {
	return models.ROWRESULT_TYPE_ONLYONE
}

func (o *LoginLogs) GetQueryArgs() string {
	return "*"
}
func (o *LoginLogs) GetFlushType() models.FlushType {
	return models.FLUSH_TYPE_ASYNC
}

func (o *LoginLogs) GetCacheLevel() models.CacheLevel {
	return models.CACHE_LEVEL_NORMAL
}

func (o *LoginLogs) GetVersionName() string {
	return ""
}

func (o *LoginLogs) Marshal() ([]byte, error) {
	return json.Marshal(o)
}

func (o *LoginLogs) MarshalToMap(filter map[string]struct{}) map[string]interface{} {
	dst := make(map[string]interface{})
	err := common.StrcutToMap(o, &dst)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	for k := range filter {
		delete(dst, k)
	}
	return dst
}

func (o *LoginLogs) Unmarshal(buf []byte) error {
	d := json.NewDecoder(bytes.NewReader(buf))
	d.UseNumber()
	return d.Decode(o)
}

func (o *LoginLogs) Clear() {
	p := reflect.ValueOf(o).Elem()
	p.Set(reflect.Zero(p.Type()))
}
