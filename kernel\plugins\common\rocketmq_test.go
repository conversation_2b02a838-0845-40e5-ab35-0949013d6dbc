package common

import (
	"context"
	"fmt"
	"testing"

	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/consumer"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/apache/rocketmq-client-go/v2/producer"
)

func TestRocketMQProducer(t *testing.T) {
	// 创建 Producer
	p, err := rocketmq.NewProducer(
		producer.WithGroupName("testGroup"),
		producer.WithNameServer([]string{"127.0.0.1:9876"}), // NameServer 地址
	)
	if err != nil {
		panic(err)
	}

	// 启动 Producer
	if err = p.Start(); err != nil {
		panic(err)
	}
	defer p.Shutdown()

	// 发送消息
	msg := &primitive.Message{
		Topic: "TestTopic",
		Body:  []byte("Hello RocketMQ"),
	}

	res, err := p.SendSync(context.Background(), msg)
	if err != nil {
		fmt.Printf("发送失败: %s\n", err)
	} else {
		fmt.Printf("发送结果: %s\n", res.String())
	}
}

func TestRocketMQConsumer(t *testing.T) {
	c, _ := rocketmq.NewPushConsumer(
		consumer.WithGroupName("testGroup"),
		consumer.WithNameServer([]string{"127.0.0.1:9876"}),
	)

	// 订阅主题
	err := c.Subscribe("TestTopic", consumer.MessageSelector{}, func(ctx context.Context, msgs ...*primitive.MessageExt) (consumer.ConsumeResult, error) {
		for _, msg := range msgs {
			fmt.Printf("收到消息: %s\n", string(msg.Body))
		}
		return consumer.ConsumeSuccess, nil
	})
	if err != nil {
		panic(err)
	}

	// 启动 Consumer
	_ = c.Start()
	select {} // 阻塞运行
}
