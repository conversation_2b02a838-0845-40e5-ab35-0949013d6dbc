插件plugins使用指南
## 目录结构

* common - 插件模块公共调用
* logic - 插件模块公共基础逻辑
* models - 插件模块公共模型定义
* modules - 所有的插件模块，开发的逻辑服务都在这里面
* gen_module.sh - 创建新插件模块脚本，根据exampleserver生成其他业务模块
* pb - google protobuf文件

## 配置文件认证key说明

* socket.server_key - 用于客户端登录basic验证，可以提供给客户端，因为登录验证都有第三方验证+密码验证
* session.encryption_key - 用于生成客户端访问的token，禁止对外
* runtime.http_key - 用户服务器间通信认证，禁止对外暴露，服务器间可以共享

* 客户端/v2/rpc/{rpcid} 请求，认证使用 "Authorization: Bearer {token}"
* 服务器/v2/rpc/{rpcid} 请求，认证使用 "Authorization: Basic {http_key}"

## 模块特殊说明

* http rpc 示例，有unwrap=1的时候，参数差异
请求：curl -X POST 'http://127.0.0.1:7350/v2/rpc/userserver.query?unwrap=1' -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************.YqlQGvsXbQGDbJem9na6n7-0V7zoZzI9VBeUSeQfzbY" -d '{"uins":[*********],"fields":["*"]}'
响应：{"users":{"f085fa4f-61b2-44ff-b7ec-cb95bfcf5973":{"id":"f085fa4f-61b2-44ff-b7ec-cb95bfcf5973","username":"FyYUXghXKZ","lang_tag":"en","metadata":"{\"role_id\": *********}","create_time":{"seconds":**********},"update_time":{"seconds":**********}}}}

* http rpc 示例，无unwrap=1的时候
请求：curl -X POST 'http://127.0.0.1:7350/v2/rpc/userserver.query' -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************.YqlQGvsXbQGDbJem9na6n7-0V7zoZzI9VBeUSeQfzbY" -d '"{\"uins\":[*********],\"fields\":[\"*\"]}"'
响应：{"payload":"{\"users\":{\"f085fa4f-61b2-44ff-b7ec-cb95bfcf5973\":{\"id\":\"f085fa4f-61b2-44ff-b7ec-cb95bfcf5973\",\"username\":\"FyYUXghXKZ\",\"lang_tag\":\"en\",\"metadata\":\"{\\\"role_id\\\": *********}\",\"create_time\":{\"seconds\":**********},\"update_time\":{\"seconds\":**********}}}}"}

* 登录，认证使用的是socket.server_key
curl -X POST 'http://127.0.0.1:7310/v2/account/authenticate/email' --header 'Content-Type: application/json' -H "Authorization: Basic ZGVmYXVsdGtleTo=" --data-raw '{"email": "<EMAIL>", "password": "********"}'
curl -X POST 'http://soc-dev.mini1.cn:7350/v2/account/authenticate/email' --header 'Content-Type: application/json' -H "Authorization: Basic OWM3Mjc1YWFiMjBkMThmNzAwZTlkOWZiZjJkZjBlZGU6OWM3Mjc1YWFiMjBkMThmNzAwZTlkOWZiZjJkZjBlZGU=" --data-raw '{"email": "<EMAIL>", "password": "********"}'
curl -X POST 'https://api.ashcraft.world:7250/v2/account/authenticate/email' --header 'Content-Type: application/json' -H "Authorization: Basic MmY3MmQ1MjQzNzg0MDNiYjZmMTZmNzMxMGFjNTMyMzg6MmY3MmQ1MjQzNzg0MDNiYjZmMTZmNzMxMGFjNTMyMzg=" --data-raw '{"email": "<EMAIL>", "password": "********"}'
