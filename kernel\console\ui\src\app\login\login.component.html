<div class="d-flex flex-column flex-lg-row h-100 relative">
  <img src="/static/svg/logo-white.svg" alt="Nakama Logo" width="175" height="43" class="logo">

  <div class="login-panel">
    <div class="align-self-center login-form">
      <div *ngIf="!router.url.endsWith('mfa'); else mfa" class="px-4">
        <h3 class="login-title mb-4">Sign in to continue to the Nakama Dashboard</h3>
        <div class="alert alert-warning" role="alert" *ngIf="error">
          {{error}}
        </div>
        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" [ngClass]="{'was-validated': submitted}">
          <div class="form-group">
            <label for="username">Username</label>
            <input type="text" id="username" class="form-control" formControlName="username" required autofocus
                    [ngClass]="{'is-invalid': f.username.dirty && f.username.errors}" />
            <div class="invalid-tooltip" [hidden]="f.username.disabled || f.username.pristine || f.username.valid">Please enter a username.</div>
          </div>
          <div class="form-group">
            <label for="password">Password</label>
            <input type="password" id="password" class="form-control" formControlName="password" required [ngClass]="{'is-invalid': f.password.dirty && f.password.errors}" />
            <div class="invalid-tooltip" [hidden]="f.password.disabled || f.password.pristine || f.password.valid">Password must be at least 8 characters.</div>
          </div>
          <div [hidden]="!mfaEnabled" class="form-group">
            <label for="code">MFA Code</label>
            <input type="code" id="code" class="form-control" formControlName="code" required [ngClass]="{'is-invalid': f.code.dirty && f.code.errors}"/>
            <div class="invalid-tooltip" [hidden]="f.code.disabled || f.code.pristine || f.code.valid">Code must contain 6 digits.</div>
          </div>
          <button class="btn btn-primary btn-lg btn-block" [disabled]="!loginForm.valid">{{mfaEnabled ? 'Verify' : 'Login'}}</button>
        </form>
      </div>
      <ng-template #mfa>
        <router-outlet></router-outlet>
      </ng-template>
    </div>
  </div>

  <div class="login-panel">
    <div class="heroic-stack">
      <div class="">
        <img src="/static/svg/hiro-logo.svg" alt="Hiro Logo" width="110" height="50" class="mb-3">
        <p>A metagame framework built on Nakama to add virtual store, rewards, event leaderboards and much more rapidly and flexibly to your game. <a href="https://www.youtube.com/playlist?list=PLOAExZcDNj9sWny_J8J5ARkxDTwAV0i6f&utm_source=NOSS%20Sign-In&utm_medium=Banner&utm_campaign=NOSS%20Reconstructing%20Fun%20Sign-In" target="_blank">RECONSTRUCTING FUN</a> on YouTube shows you how.</p>
        <a class="login-link" href="https://heroiclabs.com/hiro/?utm_source=NOSS%20Sign-In&utm_medium=Banner&utm_campaign=NOSS%20Hiro%20Sign-In" target="_blank">Explore Hiro</a>
      </div>
      <hr>
      <div class="">
        <img src="/static/svg/satori-logo.svg" alt="Satori Logo" width="134" height="50" class="mb-3">
        <p>LiveOps for your live services game. Live events, Experiments, Feature Flags, Audience Segmentation and Analytics built to work independently but beautifully with Nakama.</p>
        <a class="login-link" href="https://heroiclabs.com/satori/?utm_source=NOSS%20Sign-In&utm_medium=Banner&utm_campaign=NOSS%20Satori%20Sign-In" target="_blank">Explore Satori</a>
      </div>
      <hr>
      <div>
        <div class="d-flex align-items-center mb-3">
          <img src="/static/svg/calendar.svg" alt="Calendar">
          <span class="font-weight-bold ml-2" style="font-size: 20px;">Working at a Game Studio?</span>
        </div>
        <p>Ready to take your game development to the next level? Contact us for a free 45 minute demo call.</p>

        <a class="login-link-pink" href="https://heroiclabs.com/bookademo?utm_source=NOSS%20Sign-In&utm_medium=Banner&utm_campaign=NOSS%20Book%20a%20Demo%20Sign-In" target="_blank">
          <img src="/static/svg/phone.svg" alt="Phone">
          <span>Schedule a call with us</span>
        </a>
      </div>
    </div>
  </div>
</div>
