package models

// desc: 这里面的rpcid， 是跨服务rpc，不是跨服务的rpcid放到对应的服务内

// sessionserver rpcid
const (
	RPCID_SESSIONSERVER_SESSION_ONLINE       string = "sessionserver.session.online"       // 上线
	RPCID_SESSIONSERVER_SESSION_OFFLINE      string = "sessionserver.session.offline"      // 下线
	RPCID_SESSIONSERVER_SESSION_BATCHOFFLINE string = "sessionserver.session.batchoffline" // 批量下线,网关关闭的时候调用
)

// logicserver rpcid
const (
	RPCID_LOGICSERVER_LOGINFINISH string = "logicserver.loginfinish" // 登录完成
)

// gateserver rpcid
const (
	RPCID_GATESERVER_SEND      string = "gateserver.send"
	RPCID_GATESERVER_MULTICAST string = "gateserver.multicast"
	RPCID_GATESERVER_BOARDCAST string = "gateserver.boardcast"
	RPCID_GATESERVER_CLOSE     string = "gateserver.close"

	// 流操作
	RPCID_GATESERVER_STREAM_USER_LIST            string = "gateserver.stream.userlist"
	RPCID_GATESERVER_STREAM_USER_GET             string = "gateserver.stream.userget"
	RPCID_GATESERVER_STREAM_USER_JOIN            string = "gateserver.stream.userjoin"
	RPCID_GATESERVER_STREAM_USER_UPDATE          string = "gateserver.stream.userupdate"
	RPCID_GATESERVER_STREAM_USER_LEAVE           string = "gateserver.stream.userleave"
	RPCID_GATESERVER_STREAM_USER_KICK            string = "gateserver.stream.userkick"
	RPCID_GATESERVER_STREAM_COUNT                string = "gateserver.stream.count"
	RPCID_GATESERVER_STREAM_CLOSE                string = "gateserver.stream.close"
	RPCID_GATESERVER_STREAM_SEND                 string = "gateserver.stream.send"
	RPCID_GATESERVER_STREAM_DIRECTSEND           string = "gateserver.stream.directsend"
	RPCID_GATESERVER_STREAM_DIRECTSEND_BOARDCAST string = "gateserver.stream.directsend.boardcast"
)

// dbagentserver rpcid
const (
	RPCID_DBAGENTSERVER_UNLOCK   string = "dbagentserver.unlock"   // 解锁
	RPCID_DBAGENTSERVER_QUERY    string = "dbagentserver.query"    // 查询
	RPCID_DBAGENTSERVER_INSERT   string = "dbagentserver.insert"   // 插入
	RPCID_DBAGENTSERVER_UPDATE   string = "dbagentserver.update"   // 更新
	RPCID_DBAGENTSERVER_DELETE   string = "dbagentserver.delete"   // 删除
	RPCID_DBAGENTSERVER_RAWEXEC  string = "dbagentserver.rawexec"  // 执行原始sql(update,insert,delete)
	RPCID_DBAGENTSERVER_RAWQUERY string = "dbagentserver.rawquery" // 执行原始sql(select)
)

// statusserver rpcid
const (
	RPCID_STATUSSERVER_QUERYBYUID       string = "statusserver.querybyuid"       // 根据uid查询用户在线信息
	RPCID_STATUSSERVER_QUERYBYUIN       string = "statusserver.querybyuin"       // 根据uin查询用户在线信息
	RPCID_STATUSSERVER_QUERYONLINEBYUID string = "statusserver.queryonlinebyuid" // 根据uid查询用户是否在线
	RPCID_STATUSSERVER_QUERYONLINEBYUIN string = "statusserver.queryonlinebyuin" // 根据uin查询用户是否在线
)

// imserver rpcid
const (
	RPCID_IMSERVER_CHANNEL_RT string = "imserver.channel.rt" // channel rt消息
	RPCID_IMSERVER_PARTY_RT   string = "imserver.party.rt"   // party rt消息
)
