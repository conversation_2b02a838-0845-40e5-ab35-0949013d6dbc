package common

import (
	"strings"
	"sync"
	"time"

	"golang.org/x/time/rate"
)

type LimiterManager struct {
	myLimiters      sync.Map // map[string]*rate.Limiter = make(map[string]*rate.Limiter)
	myLimitersTotal int
	allLimiter      *rate.Limiter
}

type LimitSet struct {
	Second int32 // n秒内
	Value  int32 // 平均访问n次
	Burst  int32 // 突发上限
}

func NewLimiterManager() *LimiterManager {
	return &LimiterManager{
		myLimiters:      sync.Map{},
		myLimitersTotal: 0,
		allLimiter:      nil,
	}
}

func (lm *LimiterManager) SetLimiter(limiters map[string]string) {
	// 清理不存在的限流器
	lm.myLimiters.Range(func(key, value interface{}) bool {
		if _, ok := limiters[key.(string)]; !ok {
			lm.myLimiters.Delete(key)
		}
		return true
	})

	for k, v := range limiters {
		limitset := &LimitSet{Value: 0, Burst: 0, Second: 0}
		limtersets := strings.Split(v, "-")
		if len(limtersets) == 3 {
			limitset.Value = int32(StrToInt(limtersets[0]))
			limitset.Burst = int32(StrToInt(limtersets[1]))
			limitset.Second = int32(StrToInt(limtersets[2]))
		} else {
			limitset.Value = int32(StrToInt(limtersets[0]))
			limitset.Burst = limitset.Value
			limitset.Second = 1
		}

		// 检查参数有效性
		if limitset.Value <= 0 || limitset.Burst <= 0 || limitset.Second <= 0 {
			continue
		}

		secondvalue := rate.Every(time.Duration(limitset.Second) * time.Second / time.Duration(limitset.Value)) // 每秒平均速率
		if k == "*" {                                                                                           // 通配
			if lm.allLimiter == nil {
				lm.allLimiter = rate.NewLimiter(secondvalue, int(limitset.Burst))
			} else {
				if lm.allLimiter.Burst() != int(limitset.Burst) || lm.allLimiter.Limit() != secondvalue {
					lm.allLimiter = rate.NewLimiter(secondvalue, int(limitset.Burst))
				}
			}
			continue
		}

		l, ok := lm.myLimiters.Load(k)
		if !ok {
			lm.myLimiters.Store(k, rate.NewLimiter(secondvalue, int(limitset.Burst)))
		} else {
			if l.(*rate.Limiter).Burst() != int(limitset.Burst) || l.(*rate.Limiter).Limit() != secondvalue {
				lm.myLimiters.Store(k, rate.NewLimiter(secondvalue, int(limitset.Burst)))
			}
		}
	}
	lm.myLimitersTotal = len(limiters)
}

func (lm *LimiterManager) GetLimiter(name string) *rate.Limiter {
	if lm.myLimitersTotal == 0 {
		return nil
	}
	l, ok := lm.myLimiters.Load(name)
	if !ok {
		return lm.allLimiter
	}
	return l.(*rate.Limiter)
}

func (lm *LimiterManager) GetLimiterTotal() int {
	return lm.myLimitersTotal
}
