package models

import (
	"google.golang.org/protobuf/types/known/timestamppb"
)

// A user in the server.
type User struct {
	// The id of the user's account.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The username of the user's account.
	Username string `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	// The display name of the user.
	DisplayName string `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// A URL for an avatar image.
	AvatarUrl string `protobuf:"bytes,4,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	// The language expected to be a tag which follows the BCP-47 spec.
	LangTag string `protobuf:"bytes,5,opt,name=lang_tag,json=langTag,proto3" json:"lang_tag,omitempty"`
	// The location set by the user.
	Location string `protobuf:"bytes,6,opt,name=location,proto3" json:"location,omitempty"`
	// The timezone set by the user.
	Timezone string `protobuf:"bytes,7,opt,name=timezone,proto3" json:"timezone,omitempty"`
	// Additional information stored as a JSON object.
	Metadata string `protobuf:"bytes,8,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// The Facebook id in the user's account.
	FacebookId string `protobuf:"bytes,9,opt,name=facebook_id,json=facebookId,proto3" json:"facebook_id,omitempty"`
	// The Google id in the user's account.
	GoogleId string `protobuf:"bytes,10,opt,name=google_id,json=googleId,proto3" json:"google_id,omitempty"`
	// The Apple Game Center in of the user's account.
	GamecenterId string `protobuf:"bytes,11,opt,name=gamecenter_id,json=gamecenterId,proto3" json:"gamecenter_id,omitempty"`
	// The Steam id in the user's account.
	SteamId string `protobuf:"bytes,12,opt,name=steam_id,json=steamId,proto3" json:"steam_id,omitempty"`
	// Indicates whether the user is currently online.
	Online bool `protobuf:"varint,13,opt,name=online,proto3" json:"online,omitempty"`
	// Number of related edges to this user.
	EdgeCount int32 `protobuf:"varint,14,opt,name=edge_count,json=edgeCount,proto3" json:"edge_count,omitempty"`
	// The UNIX time (for gRPC clients) or ISO string (for REST clients) when the user was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty" swaggertype:"string" format:"date-time"`
	// The UNIX time (for gRPC clients) or ISO string (for REST clients) when the user was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty" swaggertype:"string" format:"date-time"`
	// The Facebook Instant Game ID in the user's account.
	FacebookInstantGameId string `protobuf:"bytes,17,opt,name=facebook_instant_game_id,json=facebookInstantGameId,proto3" json:"facebook_instant_game_id,omitempty"`
	// The Apple Sign In ID in the user's account.
	AppleId string `protobuf:"bytes,18,opt,name=apple_id,json=appleId,proto3" json:"apple_id,omitempty"`
	// DisableTime
	DisableTime *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=disable_time,json=disableTime,proto3" json:"disable_time,omitempty" swaggertype:"string" format:"date-time"`
}

// 用户元数据
type UserMetaData struct {
	RoleId        int32  `json:"role_id,omitempty"`
	RoleData      string `json:"role_data,omitempty"`
	LastLoginTime int64  `json:"last_login_time,omitempty"`
}

type RoleData struct {
	Gender int32 `json:"gender,omitempty"`
}
