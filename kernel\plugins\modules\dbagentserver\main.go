package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"sync"

	"go.uber.org/zap/zapcore"
)

type DbagentServerModule struct {
	name            string
	logger          runtime.Logger
	db              *sql.DB
	nk              runtime.NakamaModule
	config          runtime.Config // nakama 配置
	CustomConfig    *CustomConfig  // 自定义配置
	isRegisterSelf  bool
	ProxyPassConfig sync.Map
	controller      *Controller
	tableSchema     *TableSchema

	common *logic.CommonGlobalDataStruct
	online *logic.OnlineGlobalDataStruct
	send   *logic.SendGlobalDataStruct
}

var DbagentServerData *DbagentServerModule

func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
	DbagentServerData = new(DbagentServerModule)
	DbagentServerData.name = models.SERVER_NAME_DBAGENT
	DbagentServerData.logger = logger
	DbagentServerData.db = db
	DbagentServerData.nk = nk
	DbagentServerData.common = logic.NewCommonGlobalDataStruct()
	DbagentServerData.send = logic.NewSendGlobalDataStruct(DbagentServerData)
	DbagentServerData.online = logic.NewOnlineGlobalDataStruct(DbagentServerData)
	//DbagentServerData.tableSchema = NewTableSchema(db, logger)
	config, err := initializer.GetConfig()
	if err != nil {
		return err
	}
	DbagentServerData.config = config
	if err := DbagentServerData.common.Init(DbagentServerData, DbagentServerData.CustomConfig, DbagentServerData.customConfigEvent); err != nil {
		logger.Error("init common logic error %v", err)
		return err
	}

	initializer.RegisterShutdown(DbagentServerData.Shutdown)

	// 自定义路由注册
	DbagentServerData.controller = NewController(DbagentServerData)
	initializer.RegisterRpc(models.RPCID_DBAGENTSERVER_UNLOCK, DbagentServerData.controller.Unlock)
	initializer.RegisterRpc(models.RPCID_DBAGENTSERVER_QUERY, DbagentServerData.controller.Query)
	initializer.RegisterRpc(models.RPCID_DBAGENTSERVER_INSERT, DbagentServerData.controller.Insert)
	initializer.RegisterRpc(models.RPCID_DBAGENTSERVER_UPDATE, DbagentServerData.controller.Update)
	initializer.RegisterRpc(models.RPCID_DBAGENTSERVER_DELETE, DbagentServerData.controller.Delete)
	initializer.RegisterRpc(models.RPCID_DBAGENTSERVER_RAWEXEC, DbagentServerData.controller.RawExec)
	initializer.RegisterRpc(models.RPCID_DBAGENTSERVER_RAWQUERY, DbagentServerData.controller.RawQuery)

	return nil
}

func (s *DbagentServerModule) GetCommon() *logic.CommonGlobalDataStruct {
	return s.common
}
func (s *DbagentServerModule) GetOnline() *logic.OnlineGlobalDataStruct {
	return s.online
}
func (s *DbagentServerModule) GetSend() *logic.SendGlobalDataStruct {
	return s.send
}
func (s *DbagentServerModule) GetDbAgent() *logic.DbAgentGlobalDataStruct {
	return nil
}
func (s *DbagentServerModule) GetNotify() *logic.NotifyGlobalDataStruct {
	return nil
}
func (s *DbagentServerModule) GetStream() *logic.StreamGlobalDataStruct {
	return nil
}
func (s *DbagentServerModule) GetChannel() *logic.ChannelGlobalDataStruct {
	return nil
}
func (s *DbagentServerModule) GetLogger() runtime.Logger {
	return s.logger
}
func (s *DbagentServerModule) GetConfig() runtime.Config {
	return s.config
}
func (s *DbagentServerModule) GetName() string {
	return s.name
}
func (s *DbagentServerModule) GetDb() *sql.DB {
	return s.db
}
func (s *DbagentServerModule) GetNk() runtime.NakamaModule {
	return s.nk
}

// 配置读取回调
func (s *DbagentServerModule) customConfigEvent(dataId string, newobj any) {
	tmpobj, ok := newobj.(*CustomConfig)
	if !ok {
		return
	}
	if tmpobj.Config == nil {
		tmpobj.Config = &DbagentServerConfig{}
	}
	defaultNodeCfg := false
	if tmpobj.Config.NodeConfig == nil {
		// 使用默认节点配置
		scheme := "http"
		if s.config.GetSocket().GetUseTLS() {
			scheme = "https"
		}
		tmpobj.Config.NodeConfig = &models.NodeConfig{
			NodeId:   s.config.GetName(),
			SvcGroup: fmt.Sprintf("%s@@%s", s.name, models.NACOS_DEFAULT_GROUP),
			Host:     fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			NetHost:  fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			AuthKey:  s.config.GetRuntime().GetHTTPKey(),
			Weight:   10,
			Scheme:   scheme,
		}
		defaultNodeCfg = true
	}
	tmp, _ := json.Marshal(tmpobj)
	s.logger.Debug("customConfigEvent defaultNodeCfg: %v, customCfg: %s", defaultNodeCfg, string(tmp))
	s.common.SetNodeConfig(tmpobj.Config.NodeConfig)
	s.common.WatchNodeList.InitWatchSvr(tmpobj.Config.WatchSvcGroupList, tmpobj.Config.NodeConfig.SvcGroup)
	s.CustomConfig = tmpobj

	// 注册服务
	selfcfg := map[string]interface{}{}
	if err := common.StrcutToMap(tmpobj.Config.NodeConfig, &selfcfg); err == nil {
		if !s.isRegisterSelf {
			go s.common.RegisterAndUpdateSelfToNacos(false, selfcfg)
			s.isRegisterSelf = true
		} else {
			go s.common.RegisterAndUpdateSelfToNacos(true, selfcfg)
		}
	} else {
		s.logger.Error("register self, config error %v", err)
	}

	s.common.UpdateAccessLogConfig(tmpobj.AccessLog)
	loglevel, err := zapcore.ParseLevel(tmpobj.Logger.Level)
	if err != nil {
		s.logger.Error("parse log level error %v", err)
	} else {
		s.nk.SetLoggerLevel(loglevel)
	}
}

func (s *DbagentServerModule) Shutdown(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, reload bool) {
	logger.Info("%s shutdown", s.name)
	if !reload {
		s.common.UnRegisterSelfToNacos()
	}
}
