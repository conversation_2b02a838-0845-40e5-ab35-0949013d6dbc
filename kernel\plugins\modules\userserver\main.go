package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"sync"

	"go.uber.org/zap/zapcore"
)

type UserServerModule struct {
	name            string
	logger          runtime.Logger
	db              *sql.DB
	nk              runtime.NakamaModule
	config          runtime.Config // nakama 配置
	CustomConfig    *CustomConfig  // 自定义配置
	isRegisterSelf  bool
	ProxyPassConfig sync.Map
	controller      *Controller

	common  *logic.CommonGlobalDataStruct
	online  *logic.OnlineGlobalDataStruct
	send    *logic.SendGlobalDataStruct
	dbagent *logic.DbAgentGlobalDataStruct
	notify  *logic.NotifyGlobalDataStruct
}

var UserServerData *UserServerModule

func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
	UserServerData = new(UserServerModule)
	UserServerData.name = models.SERVER_NAME_USER
	UserServerData.logger = logger
	UserServerData.db = db
	UserServerData.nk = nk
	UserServerData.common = logic.NewCommonGlobalDataStruct()
	UserServerData.online = logic.NewOnlineGlobalDataStruct(UserServerData)
	UserServerData.send = logic.NewSendGlobalDataStruct(UserServerData)
	UserServerData.dbagent = logic.NewDbAgentGlobalDataStruct(UserServerData)
	UserServerData.notify = logic.NewNotifyGlobalDataStruct(UserServerData)
	config, err := initializer.GetConfig()
	if err != nil {
		return err
	}
	UserServerData.config = config
	if err := UserServerData.common.Init(UserServerData, UserServerData.CustomConfig, UserServerData.customConfigEvent); err != nil {
		logger.Error("init common logic error %v", err)
		return err
	}

	initializer.RegisterShutdown(UserServerData.Shutdown)

	// 自定义路由注册
	UserServerData.controller = NewController(UserServerData)
	initializer.RegisterRpc(RPCID_USERSERVER_QUERY, UserServerData.controller.Query)
	initializer.RegisterRpc(RPCID_USERSERVER_MODIFY, UserServerData.controller.Modify)
	initializer.RegisterRpc(RPCID_USERSERVER_BAN, UserServerData.controller.Ban)
	initializer.RegisterRpc(RPCID_USERSERVER_UNBAN, UserServerData.controller.Unban)

	// 仓库相关
	initializer.RegisterRpc(RPCID_USERSERVER_STORAGE_LIST, UserServerData.controller.VaultsList)
	initializer.RegisterRpc(RPCID_USERSERVER_STORAGE_DEPOSIT, UserServerData.controller.VaultsDeposit)
	initializer.RegisterRpc(RPCID_USERSERVER_STORAGE_EXTRACT, UserServerData.controller.VaultsExtract)
	initializer.RegisterRpc(RPCID_USERSERVER_STORAGE_EXPAND, UserServerData.controller.VaultsExpand)
	initializer.RegisterRpc(RPCID_USERSERVER_STORAGE_SEASON_INHERIT, UserServerData.controller.VaultsSeasonInherit)
	initializer.RegisterRpc(RPCID_USERSERVER_STORAGE_DELETE, UserServerData.controller.VaultsDelete)

	// 钱包相关
	initializer.RegisterRpc(RPCID_USERSERVER_WALLET_QUERY, UserServerData.controller.WalletQuery)
	initializer.RegisterRpc(RPCID_USERSERVER_WALLET_ADD, UserServerData.controller.WalletAdd)
	initializer.RegisterRpc(RPCID_USERSERVER_WALLET_SUB, UserServerData.controller.WalletSub)

	// 皮肤相关
	initializer.RegisterRpc(RPCID_USERSERVER_SKIN_LIST, UserServerData.controller.SkinList)
	initializer.RegisterRpc(RPCID_USERSERVER_SKIN_ADD, UserServerData.controller.SkinAdd)
	initializer.RegisterRpc(RPCID_USERSERVER_SKIN_DELETE, UserServerData.controller.SkinDelete)
	initializer.RegisterRpc(RPCID_USERSERVER_SKIN_QUERY, UserServerData.controller.SkinQuery)
	initializer.RegisterRpc(RPCID_USERSERVER_SKIN_UPDATE, UserServerData.controller.SkinUpdate)

	// 坐骑相关
	initializer.RegisterRpc(RPCID_USERSERVER_MOUNT_LIST, UserServerData.controller.MountList)
	initializer.RegisterRpc(RPCID_USERSERVER_MOUNT_ADD, UserServerData.controller.MountAdd)
	initializer.RegisterRpc(RPCID_USERSERVER_MOUNT_DELETE, UserServerData.controller.MountDelete)
	initializer.RegisterRpc(RPCID_USERSERVER_MOUNT_QUERY, UserServerData.controller.MountQuery)
	initializer.RegisterRpc(RPCID_USERSERVER_MOUNT_UPDATE, UserServerData.controller.MountUpdate)

	// avatar相关
	initializer.RegisterRpc(RPCID_USERSERVER_AVATAR_LIST, UserServerData.controller.AvatarList)
	initializer.RegisterRpc(RPCID_USERSERVER_AVATAR_ADD, UserServerData.controller.AvatarAdd)
	initializer.RegisterRpc(RPCID_USERSERVER_AVATAR_DELETE, UserServerData.controller.AvatarDelete)
	initializer.RegisterRpc(RPCID_USERSERVER_AVATAR_QUERY, UserServerData.controller.AvatarQuery)
	initializer.RegisterRpc(RPCID_USERSERVER_AVATAR_UPDATE, UserServerData.controller.AvatarUpdate)

	return nil
}

func (s *UserServerModule) GetCommon() *logic.CommonGlobalDataStruct {
	return s.common
}
func (s *UserServerModule) GetOnline() *logic.OnlineGlobalDataStruct {
	return s.online
}
func (s *UserServerModule) GetSend() *logic.SendGlobalDataStruct {
	return s.send
}
func (s *UserServerModule) GetDbAgent() *logic.DbAgentGlobalDataStruct {
	return s.dbagent
}
func (s *UserServerModule) GetNotify() *logic.NotifyGlobalDataStruct {
	return s.notify
}
func (s *UserServerModule) GetStream() *logic.StreamGlobalDataStruct {
	return nil
}
func (s *UserServerModule) GetChannel() *logic.ChannelGlobalDataStruct {
	return nil
}
func (s *UserServerModule) GetLogger() runtime.Logger {
	return s.logger
}
func (s *UserServerModule) GetConfig() runtime.Config {
	return s.config
}
func (s *UserServerModule) GetName() string {
	return s.name
}
func (s *UserServerModule) GetDb() *sql.DB {
	return s.db
}
func (s *UserServerModule) GetNk() runtime.NakamaModule {
	return s.nk
}

// 配置读取回调
func (s *UserServerModule) customConfigEvent(dataId string, newobj any) {
	tmpobj, ok := newobj.(*CustomConfig)
	if !ok {
		return
	}
	if tmpobj.Config == nil {
		tmpobj.Config = &UserServerConfig{}
	}
	defaultNodeCfg := false
	if tmpobj.Config.NodeConfig == nil {
		// 使用默认节点配置
		scheme := "http"
		if s.config.GetSocket().GetUseTLS() {
			scheme = "https"
		}
		tmpobj.Config.NodeConfig = &models.NodeConfig{
			NodeId:   s.config.GetName(),
			SvcGroup: fmt.Sprintf("%s@@%s", s.name, models.NACOS_DEFAULT_GROUP),
			Host:     fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			NetHost:  fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			AuthKey:  s.config.GetRuntime().GetHTTPKey(),
			Weight:   10,
			Scheme:   scheme,
		}
		defaultNodeCfg = true
	}
	tmp, _ := json.Marshal(tmpobj)
	s.logger.Debug("customConfigEvent defaultNodeCfg: %v, customCfg: %s", defaultNodeCfg, string(tmp))
	s.common.SetNodeConfig(tmpobj.Config.NodeConfig)
	s.common.WatchNodeList.InitWatchSvr(tmpobj.Config.WatchSvcGroupList, tmpobj.Config.NodeConfig.SvcGroup)
	s.CustomConfig = tmpobj

	// 注册服务
	selfcfg := map[string]interface{}{}
	if err := common.StrcutToMap(tmpobj.Config.NodeConfig, &selfcfg); err == nil {
		if !s.isRegisterSelf {
			go s.common.RegisterAndUpdateSelfToNacos(false, selfcfg)
			s.isRegisterSelf = true
		} else {
			go s.common.RegisterAndUpdateSelfToNacos(true, selfcfg)
		}
	} else {
		s.logger.Error("register self, config error %v", err)
	}

	s.common.UpdateAccessLogConfig(tmpobj.AccessLog)
	loglevel, err := zapcore.ParseLevel(tmpobj.Logger.Level)
	if err != nil {
		s.logger.Error("parse log level error %v", err)
	} else {
		s.nk.SetLoggerLevel(loglevel)
	}
}

func (s *UserServerModule) Shutdown(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, reload bool) {
	logger.Info("%s shutdown", s.name)
	if !reload {
		s.common.UnRegisterSelfToNacos()
	}
}
