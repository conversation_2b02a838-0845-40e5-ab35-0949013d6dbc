package logic

import (
	"bytes"
	"context"
	"crypto/md5"
	"crypto/tls"
	"encoding/hex"
	"errors"
	"fmt"
	"kernel/apigrpc"
	"kernel/kernel-common/api"
	"kernel/kernel-common/rtapi"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/models"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gofrs/uuid/v5"
	json "github.com/json-iterator/go"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"

	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"go.opentelemetry.io/otel"
)

type SendGlobalDataStruct struct {
	i          ServerModule
	grpcMgr    *common.GrpcPoolManager // 不使用tls
	grpcTlsMgr *common.GrpcPoolManager // 使用tls
	useGrpc    bool
	breaker    *common.Breaker
}

func NewSendGlobalDataStruct(i ServerModule) *SendGlobalDataStruct {
	return &SendGlobalDataStruct{
		i: i,
		grpcMgr: common.NewGrpcPoolManager(2, 50, 60*time.Second,
			grpc.WithReadBufferSize(1<<20),  // 1MB
			grpc.WithWriteBufferSize(1<<20), // 1MB
			grpc.WithTransportCredentials(insecure.NewCredentials()),
			grpc.WithStatsHandler(
				otelgrpc.NewClientHandler(
					otelgrpc.WithTracerProvider(otel.GetTracerProvider()),
					otelgrpc.WithPropagators(otel.GetTextMapPropagator()),
				),
			),
		),
		grpcTlsMgr: common.NewGrpcPoolManager(2, 50, 60*time.Second,
			grpc.WithReadBufferSize(1<<20),  // 1MB
			grpc.WithWriteBufferSize(1<<20), // 1MB
			grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{
				InsecureSkipVerify: true,
			})),
			grpc.WithStatsHandler(
				otelgrpc.NewClientHandler(
					otelgrpc.WithTracerProvider(otel.GetTracerProvider()),
					otelgrpc.WithPropagators(otel.GetTextMapPropagator()),
				),
			),
		),
		useGrpc: true,
		breaker: common.NewBreaker(),
	}
}

// 通过header透传转发上下文信息
func RuntimeHeaders(ctx context.Context, nodeconfig *models.NodeConfig) map[string]string {
	headers := make(map[string]string)
	headers["Content-Type"] = "application/json"
	if nodeconfig.AuthKey != "" {
		headers["Authorization"] = common.BasicAuth(nodeconfig.AuthKey, nodeconfig.AuthKey)
	}

	// 传递已有的 headers 中的业务信息
	if ctx.Value(runtime.RUNTIME_CTX_HEADERS) != nil {
		ctxheaders := ctx.Value(runtime.RUNTIME_CTX_HEADERS)
		for k, v := range ctxheaders.(map[string][]string) {
			if k == runtime.RUNTIME_CTX_HEADER_USER_ID || k == runtime.RUNTIME_CTX_HEADER_SESSION_ID || k == runtime.RUNTIME_CTX_HEADER_USERNAME || k == runtime.RUNTIME_CTX_HEADER_UIN || k == runtime.RUNTIME_CTX_HEADER_NICK {
				if k == runtime.RUNTIME_CTX_HEADER_NICK || k == runtime.RUNTIME_CTX_HEADER_USERNAME {
					headers[k] = url.QueryEscape(v[0])
				} else {
					headers[k] = v[0]
				}
			}
		}
	}

	// 传递业务相关的上下文信息
	if ctx.Value(runtime.RUNTIME_CTX_USER_ID) != nil {
		headers[runtime.RUNTIME_CTX_HEADER_USER_ID] = ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	}
	if ctx.Value(runtime.RUNTIME_CTX_SESSION_ID) != nil {
		headers[runtime.RUNTIME_CTX_HEADER_SESSION_ID] = ctx.Value(runtime.RUNTIME_CTX_SESSION_ID).(string)
	}
	if ctx.Value(runtime.RUNTIME_CTX_USERNAME) != nil {
		username := ctx.Value(runtime.RUNTIME_CTX_USERNAME).(string)
		headers[runtime.RUNTIME_CTX_HEADER_USERNAME] = url.QueryEscape(username)
	}
	if ctx.Value(runtime.RUNTIME_CTX_VARS) != nil {
		vars := ctx.Value(runtime.RUNTIME_CTX_VARS).(map[string]string)
		if uin, ok := vars["uin"]; ok {
			headers[runtime.RUNTIME_CTX_HEADER_UIN] = uin
		}
		if nick, ok := vars["nick"]; ok {
			headers[runtime.RUNTIME_CTX_HEADER_NICK] = url.QueryEscape(nick)
		}
	}
	if ctx.Value(runtime.RUNTIME_CTX_CLIENT_IP) != nil {
		headers["X-Forwarded-For"] = ctx.Value(runtime.RUNTIME_CTX_CLIENT_IP).(string)
	}

	return headers
}

// 只透传认证信息
func RuntimeHeadersOnlyAuth(nodeconfig *models.NodeConfig) map[string]string {
	headers := make(map[string]string)
	headers["Content-Type"] = "application/json"
	if nodeconfig.AuthKey != "" {
		headers["Authorization"] = common.BasicAuth(nodeconfig.AuthKey, nodeconfig.AuthKey)
	}
	return headers
}

func (s *SendGlobalDataStruct) GrpcRpc(ctx context.Context, nodeconfig *models.NodeConfig, rpc_id string, payload []byte, headers map[string]string, timeout time.Duration) ([]byte, error) {
	if nodeconfig == nil || headers == nil {
		return nil, errors.New("invalid nodeconfig or headers")
	}

	hosts := strings.Split(nodeconfig.Host, ":")
	if len(hosts) != 2 {
		s.i.GetLogger().Error("invalid host %s %s", nodeconfig.NodeId, nodeconfig.Host)
		return nil, errors.New("invalid host")
	}
	port, _ := strconv.Atoi(hosts[1])
	grpcport := port - 1
	reqCtx, reqCancel := context.WithTimeout(ctx, timeout)
	defer reqCancel() // 确保在函数返回时取消上下文

	targetAddr := fmt.Sprintf("%s:%d", hosts[0], grpcport)
	payload, err := common.CallWithBreaker(s.breaker, targetAddr, func() ([]byte, error) {
		var conn *common.ClientConn
		var err error
		if nodeconfig.Scheme == "https" {
			conn, err = s.grpcTlsMgr.GetConn(reqCtx, targetAddr)
		} else {
			conn, err = s.grpcMgr.GetConn(reqCtx, targetAddr)
		}
		if err != nil {
			return nil, err
		}
		defer conn.Close()

		// 创建客户端
		client := apigrpc.NewNakamaClient(conn)
		reqCtx = metadata.NewOutgoingContext(reqCtx, metadata.New(headers))

		// 创建请求
		request := &api.Rpc{
			Id:      rpc_id,
			Payload: string(payload),
			HttpKey: nodeconfig.AuthKey,
		}

		response, err := client.RpcFunc(reqCtx, request)
		if response == nil || err != nil {
			return nil, err
		}
		return []byte(response.Payload), err
	})
	if common.IsBreakerOpen(err) {
		s.i.GetLogger().Warn("grpc %s %s request breaker open: %s", nodeconfig.NodeId, targetAddr, err)
	}
	return payload, err
}

// 服务间rpc调用, 加权轮询
func (s *SendGlobalDataStruct) Rpc(ctx context.Context, node_type string, group string, rpc_id string, payload []byte, headers map[string]string, timeout time.Duration) ([]byte, error) {
	nodeconfig, err := s.i.GetCommon().WatchNodeList.WRRAllocServiceCfg(node_type, group)
	if err != nil {
		return nil, err
	}
	if headers == nil {
		headers = RuntimeHeaders(ctx, nodeconfig)
	} else {
		if _, ok := headers["Authorization"]; !ok {
			headers = RuntimeHeadersOnlyAuth(nodeconfig)
		}
		if _, ok := headers["Content-Type"]; !ok {
			headers["Content-Type"] = "application/json"
		}
	}

	if s.useGrpc {
		return s.GrpcRpc(ctx, nodeconfig, rpc_id, payload, headers, timeout)
	}
	tmpurl := fmt.Sprintf("%s://%s/v2/rpc/%s?unwrap=1", nodeconfig.Scheme, nodeconfig.Host, rpc_id)
	return common.HttpPost(ctx, tmpurl, payload, headers, timeout)
}

// 服务间rpc调用, 加权轮询
func (s *SendGlobalDataStruct) RpcObj(ctx context.Context, node_type string, group string, rpc_id string, obj interface{}, headers map[string]string, timeout time.Duration) ([]byte, error) {
	payload, err := json.Marshal(obj)
	if err != nil {
		return nil, err
	}
	nodeconfig, err := s.i.GetCommon().WatchNodeList.WRRAllocServiceCfg(node_type, group)
	if err != nil {
		return nil, err
	}
	if headers == nil {
		headers = RuntimeHeaders(ctx, nodeconfig)
	} else {
		if _, ok := headers["Authorization"]; !ok {
			headers = RuntimeHeadersOnlyAuth(nodeconfig)
		}
		if _, ok := headers["Content-Type"]; !ok {
			headers["Content-Type"] = "application/json"
		}
	}

	if s.useGrpc {
		return s.GrpcRpc(ctx, nodeconfig, rpc_id, payload, headers, timeout)
	}
	tmpurl := fmt.Sprintf("%s://%s/v2/rpc/%s?unwrap=1", nodeconfig.Scheme, nodeconfig.Host, rpc_id)
	return common.HttpPost(ctx, tmpurl, payload, headers, timeout)
}

// 服务间rpc调用，一致性hash
func (s *SendGlobalDataStruct) RpcHash(ctx context.Context, hashkey string, node_type string, group string, rpc_id string, payload []byte, headers map[string]string, timeout time.Duration) ([]byte, error) {
	nodeconfig, err := s.i.GetCommon().WatchNodeList.AllocServiceConfig(node_type, group, hashkey)
	if err != nil {
		return nil, err
	}
	if headers == nil {
		headers = RuntimeHeaders(ctx, nodeconfig)
	} else {
		if _, ok := headers["Authorization"]; !ok {
			headers = RuntimeHeadersOnlyAuth(nodeconfig)
		}
		if _, ok := headers["Content-Type"]; !ok {
			headers["Content-Type"] = "application/json"
		}
	}

	if s.useGrpc {
		return s.GrpcRpc(ctx, nodeconfig, rpc_id, payload, headers, timeout)
	}
	tmpurl := fmt.Sprintf("%s://%s/v2/rpc/%s?unwrap=1", nodeconfig.Scheme, nodeconfig.Host, rpc_id)
	return common.HttpPost(ctx, tmpurl, payload, headers, timeout)
}

// 服务间rpc调用，一致性hash
func (s *SendGlobalDataStruct) RpcHashObj(ctx context.Context, hashkey string, node_type string, group string, rpc_id string, obj interface{}, headers map[string]string, timeout time.Duration) ([]byte, error) {
	payload, err := json.Marshal(obj)
	if err != nil {
		return nil, err
	}
	nodeconfig, err := s.i.GetCommon().WatchNodeList.AllocServiceConfig(node_type, group, hashkey)
	if err != nil {
		return nil, err
	}
	if headers == nil {
		headers = RuntimeHeaders(ctx, nodeconfig)
	} else {
		if _, ok := headers["Authorization"]; !ok {
			headers = RuntimeHeadersOnlyAuth(nodeconfig)
		}
		if _, ok := headers["Content-Type"]; !ok {
			headers["Content-Type"] = "application/json"
		}
	}

	if s.useGrpc {
		return s.GrpcRpc(ctx, nodeconfig, rpc_id, payload, headers, timeout)
	}
	tmpurl := fmt.Sprintf("%s://%s/v2/rpc/%s?unwrap=1", nodeconfig.Scheme, nodeconfig.Host, rpc_id)
	return common.HttpPost(ctx, tmpurl, payload, headers, timeout)
}

// 服务间rpc调用, 指定节点id
func (s *SendGlobalDataStruct) RpcToNodeId(ctx context.Context, node_id string, node_type string, group string, rpc_id string, payload []byte, headers map[string]string, timeout time.Duration) ([]byte, error) {
	nodeconfig, err := s.i.GetCommon().WatchNodeList.GetSvrCfgByID(node_type, group, node_id)
	if err != nil {
		return nil, err
	}
	if headers == nil {
		headers = RuntimeHeaders(ctx, nodeconfig)
	} else {
		if _, ok := headers["Authorization"]; !ok {
			headers = RuntimeHeadersOnlyAuth(nodeconfig)
		}
		if _, ok := headers["Content-Type"]; !ok {
			headers["Content-Type"] = "application/json"
		}
	}

	if s.useGrpc {
		return s.GrpcRpc(ctx, nodeconfig, rpc_id, payload, headers, timeout)
	}
	tmpurl := fmt.Sprintf("%s://%s/v2/rpc/%s?unwrap=1", nodeconfig.Scheme, nodeconfig.Host, rpc_id)
	return common.HttpPost(ctx, tmpurl, payload, headers, timeout)
}

func (s *SendGlobalDataStruct) RpcToNodeIdObj(ctx context.Context, node_id string, node_type string, group string, rpc_id string, obj interface{}, headers map[string]string, timeout time.Duration) ([]byte, error) {
	payload, err := json.Marshal(obj)
	if err != nil {
		return nil, err
	}
	nodeconfig, err := s.i.GetCommon().WatchNodeList.GetSvrCfgByID(node_type, group, node_id)
	if err != nil {
		return nil, err
	}
	if headers == nil {
		headers = RuntimeHeaders(ctx, nodeconfig)
	} else {
		if _, ok := headers["Authorization"]; !ok {
			headers = RuntimeHeadersOnlyAuth(nodeconfig)
		}
		if _, ok := headers["Content-Type"]; !ok {
			headers["Content-Type"] = "application/json"
		}
	}

	if s.useGrpc {
		return s.GrpcRpc(ctx, nodeconfig, rpc_id, payload, headers, timeout)
	}
	tmpurl := fmt.Sprintf("%s://%s/v2/rpc/%s?unwrap=1", nodeconfig.Scheme, nodeconfig.Host, rpc_id)
	return common.HttpPost(ctx, tmpurl, payload, headers, timeout)
}

// 服务间rpc调用, 指定节点地址
func (s *SendGlobalDataStruct) RpcToNodeAddr(ctx context.Context, node_addr string, node_type string, group string, rpc_id string, payload []byte, headers map[string]string, timeout time.Duration) ([]byte, error) {
	nodeconfig, err := s.i.GetCommon().WatchNodeList.GetSvrCfgByHost(node_type, group, node_addr)
	if err != nil {
		return nil, err
	}
	if headers == nil {
		headers = RuntimeHeaders(ctx, nodeconfig)
	} else {
		if _, ok := headers["Authorization"]; !ok {
			headers = RuntimeHeadersOnlyAuth(nodeconfig)
		}
		if _, ok := headers["Content-Type"]; !ok {
			headers["Content-Type"] = "application/json"
		}
	}

	if s.useGrpc {
		return s.GrpcRpc(ctx, nodeconfig, rpc_id, payload, headers, timeout)
	}
	tmpurl := fmt.Sprintf("%s://%s/v2/rpc/%s?unwrap=1", nodeconfig.Scheme, nodeconfig.Host, rpc_id)
	return common.HttpPost(ctx, tmpurl, payload, headers, timeout)
}

func (s *SendGlobalDataStruct) RpcToNodeAddrObj(ctx context.Context, node_addr string, node_type string, group string, rpc_id string, obj interface{}, headers map[string]string, timeout time.Duration) ([]byte, error) {
	payload, err := json.Marshal(obj)
	if err != nil {
		return nil, err
	}
	nodeconfig, err := s.i.GetCommon().WatchNodeList.GetSvrCfgByHost(node_type, group, node_addr)
	if err != nil {
		return nil, err
	}
	if headers == nil {
		headers = RuntimeHeaders(ctx, nodeconfig)
	} else {
		if _, ok := headers["Authorization"]; !ok {
			headers = RuntimeHeadersOnlyAuth(nodeconfig)
		}
		if _, ok := headers["Content-Type"]; !ok {
			headers["Content-Type"] = "application/json"
		}
	}

	if s.useGrpc {
		return s.GrpcRpc(ctx, nodeconfig, rpc_id, payload, headers, timeout)
	}
	tmpurl := fmt.Sprintf("%s://%s/v2/rpc/%s?unwrap=1", nodeconfig.Scheme, nodeconfig.Host, rpc_id)
	return common.HttpPost(ctx, tmpurl, payload, headers, timeout)
}

// 发送给指定类型的所有节点
func (s *SendGlobalDataStruct) RpcAll(ctx context.Context, node_type string, group string, rpc_id string, payload []byte, headers map[string]string, timeout time.Duration) {
	key := common.GenWatchSvrKey(node_type, group)
	list, ok := s.i.GetCommon().WatchNodeList.WatchSvrList.Load(key)
	if !ok {
		return
	}

	if headers == nil {
		headers = make(map[string]string)
	} else {
		if _, ok := headers["Content-Type"]; !ok {
			headers["Content-Type"] = "application/json"
		}
	}

	svrlist := list.(*common.SvrCfgList)
	for _, nodeconfig := range svrlist.GetAll() {
		if nodeconfig.Host == "" || nodeconfig.Scheme == "" {
			continue
		}
		if nodeconfig.AuthKey != "" {
			headers["Authorization"] = common.BasicAuth(nodeconfig.AuthKey, nodeconfig.AuthKey)
		} else {
			delete(headers, "Authorization")
		}

		var err error
		if s.useGrpc {
			_, err = s.GrpcRpc(ctx, nodeconfig, rpc_id, payload, headers, time.Second*3)
		} else {
			tmpurl := fmt.Sprintf("%s://%s/v2/rpc/%s?unwrap=1", nodeconfig.Scheme, nodeconfig.Host, rpc_id)
			_, err = common.HttpPost(ctx, tmpurl, payload, headers, time.Second*3)
		}
		if err != nil {
			s.i.GetLogger().Error("RpcAll send to %s %s failed %v", nodeconfig.Host, nodeconfig.Scheme, err)
		}
	}
}

func (s *SendGlobalDataStruct) Push(ctx context.Context, user_id string, subject string, content interface{}, msgid string, msglevel int32) (bool, error) {
	// 查询所在节点
	list := map[string][]*models.UserOnlineInfo{}
	var err error
	if uuid.FromStringOrNil(user_id) != uuid.Nil { // uid
		list, err = s.i.GetOnline().QueryOnlineUsersInfo(ctx, []string{user_id})
	} else { // uin
		tmplist, err := s.i.GetOnline().QueryOnlineUsersInfoByStrUin(ctx, []string{user_id})
		if err != nil {
			return false, err
		}
		list[user_id] = []*models.UserOnlineInfo{tmplist[user_id]}
	}
	if err != nil {
		return false, err
	}
	onlineusers, ok := list[user_id]
	if !ok {
		return false, nil
	}

	var contentStr string
	switch content.(type) {
	case string:
		contentStr = content.(string)
	case []byte:
		contentStr = string(content.([]byte))
	default:
		contentStr, err = json.MarshalToString(content)
		if err != nil {
			s.i.GetLogger().Error("Push marshal content failed %v", err)
			return false, err
		}
	}

	push := &models.CommonPush{
		SessionIDs: make([]string, 0),
		Data: &rtapi.Notifications{
			Notifications: []*api.Notification{
				{
					Id:      msgid,
					Subject: subject,
					Content: contentStr,
					Code:    models.NotificationCodeRealMsg,
				},
			},
		},
	}
	nodes := map[string]bool{}
	for i := 0; i < len(onlineusers); i++ {
		onlineuser := onlineusers[i]
		if onlineuser == nil || onlineuser.NodeId == "" || onlineuser.SessionId == "" {
			continue
		}
		push.SessionIDs = append(push.SessionIDs, onlineuser.SessionId)
		nodes[onlineuser.NodeId] = true
	}

	data, err := json.Marshal(push)
	if err != nil {
		return false, err
	}

	for nodeid := range nodes {
		resp, err := s.RpcToNodeId(ctx, nodeid, models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, models.RPCID_GATESERVER_SEND, data, nil, models.RPC_TIMEOUT_DEFAULT)
		if err != nil {
			s.i.GetLogger().Error("send to %s %s failed %v %s", user_id, nodeid, err, string(resp))
			continue
		}
	}

	return true, nil
}

func (s *SendGlobalDataStruct) PushMulticast(ctx context.Context, user_ids []string, subject string, content interface{}, msgid string, msglevel int32) (bool, error) {
	if len(user_ids) == 0 {
		return true, nil
	}

	// 查询所在节点
	g := s.i.GetCommon()
	list := map[string][]*models.UserOnlineInfo{}
	var err error
	if uuid.FromStringOrNil(user_ids[0]) != uuid.Nil { // uid
		list, err = s.i.GetOnline().QueryOnlineUsersInfo(ctx, user_ids)
	} else { // uin
		tmplist, err := s.i.GetOnline().QueryOnlineUsersInfoByStrUin(ctx, user_ids)
		if err != nil {
			return false, err
		}
		for _, user_id := range user_ids {
			list[user_id] = []*models.UserOnlineInfo{tmplist[user_id]}
		}
	}
	if err != nil {
		return false, err
	}

	var contentStr string
	switch content.(type) {
	case string:
		contentStr = content.(string)
	case []byte:
		contentStr = string(content.([]byte))
	default:
		contentStr, err = json.MarshalToString(content)
		if err != nil {
			s.i.GetLogger().Error("PushMulticast marshal content failed %v", err)
			return false, err
		}
	}

	svrlist := map[string][]string{}
	for struid, onlineusers := range list {
		for i := 0; i < len(onlineusers); i++ {
			onlineuser := onlineusers[i]
			if onlineuser == nil || onlineuser.NodeId == "" || onlineuser.SessionId == "" {
				continue
			}
			cfg, err := g.WatchNodeList.GetSvrCfgByID(models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, onlineuser.NodeId)
			if err != nil {
				s.i.GetLogger().Error("%s %s %s", onlineuser.NodeId, struid, err.Error())
				continue
			}
			if cfg == nil || cfg.Host == "" || cfg.Scheme == "" {
				continue
			}
			_, ok := svrlist[onlineuser.NodeId]
			if ok {
				svrlist[onlineuser.NodeId] = append(svrlist[onlineuser.NodeId], onlineuser.SessionId)
			} else {
				svrlist[onlineuser.NodeId] = []string{onlineuser.SessionId}
			}
		}
	}

	for k, v := range svrlist {
		push := &models.CommonPush{
			SessionIDs: v,
			Data: &rtapi.Notifications{
				Notifications: []*api.Notification{
					{
						Id:      msgid,
						Subject: subject,
						Content: contentStr,
						Code:    models.NotificationCodeRealMsg,
					},
				},
			},
		}
		data, err := json.Marshal(push)
		if err != nil {
			return false, err
		}

		_, err = s.RpcToNodeId(ctx, k, models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, models.RPCID_GATESERVER_MULTICAST, data, nil, models.RPC_TIMEOUT_DEFAULT)
		if err != nil {
			s.i.GetLogger().Error("multicast to %s failed %v", k, err)
			continue
		}
	}
	return true, nil
}

func (s *SendGlobalDataStruct) PushBoardcast(ctx context.Context, subject string, content interface{}, msgid string, msglevel int32) {
	var contentStr string
	switch content.(type) {
	case string:
		contentStr = content.(string)
	case []byte:
		contentStr = string(content.([]byte))
	default:
		var err error
		contentStr, err = json.MarshalToString(content)
		if err != nil {
			s.i.GetLogger().Error("boardcast to gate failed %v", err)
			return
		}
	}
	push := &models.CommonPush{
		Data: &rtapi.Notifications{
			Notifications: []*api.Notification{
				{
					Id:      msgid,
					Subject: subject,
					Content: contentStr,
					Code:    models.NotificationCodeRealMsg,
				},
			},
		},
	}
	data, err := json.Marshal(push)
	if err != nil {
		s.i.GetLogger().Error("boardcast to gate failed %v", err)
		return
	}
	s.RpcAll(ctx, models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, models.RPCID_GATESERVER_BOARDCAST, data, nil, models.RPC_TIMEOUT_DEFAULT)
}

func (s *SendGlobalDataStruct) PushClose(ctx context.Context, user_ids []string, reason runtime.PresenceReason, notification *api.Notification) error {
	g := s.i.GetCommon()
	// 查询所在节点
	list, err := s.i.GetOnline().QueryOnlineUsersInfo(ctx, user_ids)
	if err != nil {
		return err
	}

	svrlist := map[string][]string{}
	for struid, onlineusers := range list {
		for i := 0; i < len(onlineusers); i++ {
			onlineuser := onlineusers[i]
			if onlineuser == nil || onlineuser.NodeId == "" || onlineuser.SessionId == "" {
				continue
			}
			cfg, err := g.WatchNodeList.GetSvrCfgByID(models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, onlineuser.NodeId)
			if err != nil {
				s.i.GetLogger().Error("%s %s %s", onlineuser.NodeId, struid, err.Error())
				continue
			}
			if cfg == nil || cfg.Host == "" || cfg.Scheme == "" {
				continue
			}
			_, ok := svrlist[onlineuser.NodeId]
			if ok {
				svrlist[onlineuser.NodeId] = append(svrlist[onlineuser.NodeId], onlineuser.SessionId)
			} else {
				svrlist[onlineuser.NodeId] = []string{onlineuser.SessionId}
			}
		}
	}

	for k, v := range svrlist {
		closepush := &models.ClosePush{
			SessionIDs: v,
			Reason:     reason,
			Data: &rtapi.Notifications{
				Notifications: []*api.Notification{
					notification,
				},
			},
		}
		data, err := json.Marshal(closepush)
		if err != nil {
			return err
		}

		_, err = s.RpcToNodeId(ctx, k, models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, models.RPCID_GATESERVER_CLOSE, data, nil, models.RPC_TIMEOUT_DEFAULT)
		if err != nil {
			s.i.GetLogger().Error("close to %s failed %v", k, err)
			continue
		}
	}
	return nil
}

func (s *SendGlobalDataStruct) PushCloseToSession(ctx context.Context, node_id, session_id string, reason runtime.PresenceReason, notification *api.Notification) error {
	closepush := &models.ClosePush{
		SessionIDs: []string{session_id},
		Reason:     reason,
		Data: &rtapi.Notifications{
			Notifications: []*api.Notification{
				notification,
			},
		},
	}
	data, err := json.Marshal(closepush)
	if err != nil {
		return err
	}

	_, err = s.RpcToNodeId(ctx, node_id, models.SERVER_NAME_GATE, models.NACOS_DEFAULT_GROUP, models.RPCID_GATESERVER_CLOSE, data, nil, models.RPC_TIMEOUT_DEFAULT)
	if err != nil {
		s.i.GetLogger().Error("close to %s failed %v", node_id, err)
		return err
	}
	return nil
}

// 发送消息到游戏服，通过msgbus
func (s *SendGlobalDataStruct) PushToMsgBus(ctx context.Context, msgbusAddr string, appid string, appsecret string, msg *models.MsgBusReqPub) {
	genSign := func(params map[string]interface{}, secretKey string) string {
		keys := make([]string, 0, len(params))
		for k := range params {
			if k == "sign" {
				continue
			}

			keys = append(keys, k)
		}
		sort.Strings(keys)

		buffer := new(bytes.Buffer)
		for i := 0; i < len(keys); i++ {
			key := keys[i]
			value := fmt.Sprintf("%v", params[key])
			//fmt.Println(fmt.Sprintf("key=%s, value=%s", key, value))

			if i > 0 {
				buffer.WriteString("&")
			}

			buffer.WriteString(key + "=" + value)
		}

		// buffer.WriteString("&key=")
		buffer.WriteString("&" + secretKey)
		//fmt.Println("str=", buffer.String())

		h := md5.New()
		h.Write(buffer.Bytes())
		svrAuth := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
		//fmt.Println("svrAuth=", svrAuth)
		return svrAuth
	}

	params := make(map[string]interface{})
	params["appid"] = msg.AppId
	params["timestamp"] = msg.Timestamp
	params["topic"] = msg.Topic
	params["event"] = msg.Event
	params["payload"] = msg.Payload
	params["sign"] = genSign(params, appsecret)

	formData := url.Values{}
	for k, v := range params {
		formData.Add(k, fmt.Sprintf("%v", v))
	}
	tmpurl := fmt.Sprintf("http://%s/v1/pub", msgbusAddr)
	resp, err := common.HttpPost(ctx, tmpurl, []byte(formData.Encode()), map[string]string{"Content-Type": "application/x-www-form-urlencoded"}, time.Second*3)
	if err != nil {
		s.i.GetLogger().Error("PushToMsgBus http post failed %v", err)
		return
	}

	s.i.GetLogger().Info("PushToMsgBus resp=%s", string(resp))
}
