#!/bin/bash

# Example command for running kernel with specific database and session settings
# ./kernel --database.address "root@127.0.0.1:26257" -session.token_expiry_sec 3600

# Default target: runs "all" if no target is specified
TARGET=${1:-help}

# Target: kernel
# Runs kernel with the configuration file specified
kernel() {
  go build -trimpath -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn"
}

# Target: release
# Builds the project with release enabled and starts a Delve debugging session
release() {
  go build -trimpath -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn" -o ./data/prod_env/kernel
}

# Target: test
# Builds the project with release enabled and starts a Delve debugging session
test() {
  go build -trimpath -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn" -o ./data/test_env/kernel
}


# Target: debug
# Builds the project with debugging enabled and starts a Delve debugging session
# -trimpath: Removes file system paths from compiled binaries to improve reproducibility
# -mod=vendor: Uses the local vendor directory for module dependencies
# -gcflags "all=-N -l": Disables optimizations and inlining for easier debugging
debug() {
  go build -gcflags "all=-N -l" -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn"
}

# 删除指定目录下./data的的所有 .cookie
clear_cache() {
  find ./data -name "*.cookie" -delete
}

# help
help() {
  echo "Usage: ./build.sh [target]"
  echo "Targets:"
  echo "  help: 查看帮助"
  echo "  kernel: 构建kernel核心，release版本"
  echo "  release: 构建kernel核心，release版本到./data/prod_env"
  echo "  test: 构建kernel核心，test版本到./data/test_env"
  echo "  debug: 构建kernel核心，debug版本"
  echo "  clear_cache: 清空./data的的所有 .cookie"
}

# Execute the specified target
$TARGET

