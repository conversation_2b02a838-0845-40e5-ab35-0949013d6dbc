package obs

import (
	"bytes"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io/ioutil"
	"log"
	"net/url"
	"strings"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

type OSA<PERSON>yun struct {
	OSClientBase
	client *oss.Client
	bucket *oss.Bucket
}

func NewOSAliyun(osconfig *OSConfig) IOSClient {
	aliyun := &OSAliyun{}
	aliyun.OSConfig = *osconfig
	aliyun.Name = ObsTypeNameAliyun

	ossClient, err := oss.New(osconfig.Endpoint, osconfig.AccessKey, osconfig.Secretkey)
	if err != nil {
		log.Panic(err.Error())
	}
	aliyun.client = ossClient

	bucket, err := ossClient.Bucket(osconfig.Bucket)
	if err != nil {
		log.Panic(err.Error())
	}
	aliyun.bucket = bucket

	return aliyun
}

func (O *OSAliyun) PutObject(key string, data []byte) error {
	return O.PutObjectWithMetadata(key, data, make(map[string]string, 0))
}

// put raw data to os with metadata
func (O *OSAliyun) PutObjectWithMetadata(objectKey string, data []byte, metaData map[string]string) error {
	base64str := base64.StdEncoding.EncodeToString(data)
	md5 := md5.Sum([]byte(base64str))
	base64md5 := hex.EncodeToString(md5[:])
	options := []oss.Option{
		oss.Meta(Base64MetaKey, base64md5),
	}
	for key, value := range metaData {
		options = append(options, oss.Meta(key, value))
	}
	err := O.bucket.PutObject(objectKey, bytes.NewReader(data), options...)
	if err != nil {
		log.Printf("[aliyun]put object fail, key=%s, err=%s", objectKey, err.Error())
		return err
	}
	return nil
}

func (O *OSAliyun) GetObject(key string) ([]byte, error) {
	object, err := O.bucket.GetObject(key)
	if err != nil {
		log.Printf("[aliyun]get object fail, key=%s, err=%s", key, err.Error())
		return nil, err
	}

	data, err := ioutil.ReadAll(object)
	if err != nil {
		log.Printf("[aliyun]get object fail, key=%s, err=%s", key, err.Error())
		return nil, err
	}
	object.Close()

	return data, nil
}

func (o *OSAliyun) GetObjectMetadata(key string) (map[string]string, error) {
	props, err := o.bucket.GetObjectDetailedMeta(key)
	if err != nil {
		log.Printf("[aliyun]get object metadata fail, key=%s, err=%s", key, err.Error())
		return nil, err
	}
	var metadata = make(map[string]string)
	for key, values := range props {
		if strings.HasPrefix(key, oss.HTTPHeaderOssMetaPrefix) {
			key1 := strings.TrimPrefix(key, oss.HTTPHeaderOssMetaPrefix)
			key1 = strings.ToLower(key1)
			metadata[key1] = values[0]
		}
	}
	return metadata, nil
}

func (O *OSAliyun) SetObjectMetadata(key string, metaData map[string]string) error {
	if len(metaData) == 0 {
		return fmt.Errorf("[aliyun]set object metadata fail, meta size 0 key:%v", key)
	}

	options := make([]oss.Option, 0, len(metaData))
	for key, value := range metaData {
		options = append(options, oss.Meta(key, value))
	}
	err := O.bucket.SetObjectMeta(key, options...)
	if err != nil {
		log.Printf("[aliyun]set object metadata fail, key=%s, err=%s", key, err.Error())
		return err
	}
	return nil
}

func (O *OSAliyun) DelObject(key string) error {
	err := O.bucket.DeleteObject(key)
	if err != nil {
		log.Printf("[aliyun]del object fail, key=%s, err=%s", key, err.Error())
		return err
	}
	return nil
}

func (O *OSAliyun) DelObjects(keys ...string) error {
	_, err := O.bucket.DeleteObjects(keys)
	if err != nil {
		log.Printf("[aliyun]del objects fail, key=%s, err=%s", keys, err.Error())
		return err
	}
	return nil
}

func (O *OSAliyun) PutFile(path string, key string) error {
	base64md5, err := getFileMd5(path)
	if err != nil {
		log.Printf("[aliyun]put object from file fail, key=%s, path=%s, err=%s", key, path, err.Error())
		return err
	}
	options := []oss.Option{
		oss.Meta(Base64MetaKey, base64md5),
	}
	err = O.bucket.PutObjectFromFile(key, path, options...)
	if err != nil {
		log.Printf("[aliyun]put object from file fail, key=%s, path=%s, err=%s", key, path, err.Error())
		return err
	}
	return nil
}

func (O *OSAliyun) GetFile(key string, file string) error {
	err := O.bucket.GetObjectToFile(key, file)
	if err != nil {
		log.Printf("[aliyun]get object to file fail, key=%s, file=%s, err=%s", key, file, err.Error())
		return err
	}
	return nil
}
func (O *OSAliyun) GetObjectMD5(key string) string {
	meta, err := O.bucket.GetObjectDetailedMeta(key)
	if err != nil {
		log.Printf("[aliyun]get object md5 fail, key=%s, err=%s", key, err.Error())
		return ""
	}
	md5 := meta.Get(AliyunBase64MetaKey)

	decodeBytes, err := base64.StdEncoding.DecodeString(md5)
	if err != nil {
		log.Printf("[aliyun]object metadata decode fail, key=%v, err=%s", key, err.Error())
		return ""
	}

	metaMd5 := hex.EncodeToString(decodeBytes)
	return metaMd5
}

func (O *OSAliyun) GetObjectDownloadURL(key string) string {
	reqUrl, err := url.Parse(O.Endpoint)
	if err != nil {
		log.Printf("[aliyun]get object download url fail, key=%v, err=%s", key, err.Error())
		return ""
	}

	// 修复reqUrl.Host为空字符串的bug
	host := reqUrl.Host
	if host == "" {
		// 从Endpoint中提取域名部分
		endpoint := O.Endpoint
		if strings.HasPrefix(endpoint, "http://") {
			endpoint = endpoint[7:]
		} else if strings.HasPrefix(endpoint, "https://") {
			endpoint = endpoint[8:]
		}

		// 移除可能的路径部分
		if idx := strings.Index(endpoint, "/"); idx > 0 {
			host = endpoint[:idx]
		} else {
			host = endpoint
		}
	}

	buff := new(bytes.Buffer)
	buff.WriteString("http://")
	buff.WriteString(O.Bucket + ".")
	buff.WriteString(host)
	buff.WriteString("/" + key)
	return buff.String()
}

func (O *OSAliyun) Close() {

}

func (o *OSAliyun) ListObjects(prefix string) ([]string, error) {
	objects, err := o.bucket.ListObjects(oss.Prefix(prefix))
	if err != nil {
		log.Printf("[aliyun]list objects fail, prefix=%s, err=%s", prefix, err.Error())
		return nil, err
	}
	var keys = []string{}
	for _, object := range objects.Objects {
		keys = append(keys, object.Key)
	}
	return keys, nil
}

// 获取一个可供上传的安全url
func (o *OSAliyun) GetSafeUploadURL(key string, expiresSeconds int, maxSize int64, expectedMD5 string) string {
	// 设置安全相关的选项
	options := []oss.Option{
		// 设置Content-Type为二进制流，防止XSS攻击
		oss.ContentType("application/octet-stream"),
		// 设置Cache-Control为不缓存
		oss.CacheControl("no-cache"),
		// 设置Content-Disposition为attachment，强制下载
		oss.ContentDisposition("attachment"),
		// 禁止覆盖已存在的文件
		oss.ForbidOverWrite(true),
		// 设置ACL为私有访问
		oss.ACL(oss.ACLPrivate),
		// 设置对象ACL为私有访问
		oss.ObjectACL(oss.ACLPrivate),
	}

	// 如果设置了文件大小限制
	if maxSize > 0 {
		options = append(options, oss.ContentLength(maxSize))
	}

	// 如果设置了MD5校验，OSS会自动校验上传文件的MD5
	if expectedMD5 != "" {
		options = append(options, oss.ContentMD5(expectedMD5))
	}

	// 生成带签名的URL
	signedURL, err := o.bucket.SignURL(key, oss.HTTPPut, int64(expiresSeconds), options...)
	if err != nil {
		log.Printf("[aliyun]create signed url fail, key=%s, err=%s", key, err.Error())
		return ""
	}

	return signedURL
}
