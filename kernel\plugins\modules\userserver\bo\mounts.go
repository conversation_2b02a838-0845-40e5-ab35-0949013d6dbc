package bo

import (
	"bytes"
	"encoding/json"
	"fmt"
	"kernel/plugins/common"
	"kernel/plugins/models"
	"reflect"
	"time"
)

// CREATE TABLE IF NOT EXISTS mounts (
// 	FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,

// 	user_id UUID NOT NULL PRIMARY KEY,
// 	items JSONB NOT NULL DEFAULT '{}'::JSONB,
// 	version int4 NOT NULL DEFAULT 0,
// 	created_at TIMESTAMPTZ DEFAULT now(),
// 	updated_at TIMESTAMPTZ DEFAULT now()
//   );

type Mounts struct {
	UserId    string    `json:"user_id,omitempty"`
	Items     string    `json:"items,omitempty"`
	Version   int32     `json:"version,omitempty"`
	CreatedAt time.Time `json:"created_at,omitempty"`
	UpdatedAt time.Time `json:"updated_at,omitempty"`
}

func (v *Mounts) GetTable() string {
	return "mounts"
}

func (v *Mounts) GetKeyName() string {
	return "user_id"
}

func (v *Mounts) GetUniqueKeys() []string {
	return []string{}
}

func (v *Mounts) GetSecondKeyName() string {
	return ""
}

func (v *Mounts) GetRowResultType() models.RowResultType {
	return models.ROWRESULT_TYPE_ONLYONE
}

func (v *Mounts) GetQueryArgs() string {
	return "*"
}

func (v *Mounts) GetFlushType() models.FlushType {
	return models.FLUSH_TYPE_SYNC
}

func (v *Mounts) GetCacheLevel() models.CacheLevel {
	return models.CACHE_LEVEL_IMPORT
}

func (v *Mounts) GetVersionName() string {
	return "version"
}

func (v *Mounts) Marshal() ([]byte, error) {
	return json.Marshal(v)
}

func (v *Mounts) MarshalToMap(filter map[string]struct{}) map[string]interface{} {
	dst := make(map[string]interface{})
	err := common.StrcutToMap(v, &dst)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	for k := range filter {
		delete(dst, k)
	}
	return dst
}

func (v *Mounts) Unmarshal(buf []byte) error {
	d := json.NewDecoder(bytes.NewReader(buf))
	d.UseNumber()
	return d.Decode(v)
}

func (v *Mounts) Clear() {
	p := reflect.ValueOf(v).Elem()
	p.Set(reflect.Zero(p.Type()))
}
