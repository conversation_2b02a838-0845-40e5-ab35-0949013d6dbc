package graceful

import (
	"fmt"
	"log"
	"net"
	"os"
	"os/exec"
	"os/signal"
	"syscall"
	"time"
)

type master struct {
	addr           Address
	opt            *option
	socketFiles    []*os.File
	workerPid      int
	workerExit     chan error
	lastReloadTime int64
	isCheckWorker  bool
	stopTimeout    time.Duration
}

func (m *master) run(services []*service) error {
	err := m.createFDs(services)
	if err != nil {
		return err
	}

	pid, err := m.forkWorker()
	if err != nil {
		return err
	}

	m.workerPid = pid
	m.isCheckWorker = true
	go m.checkWorker()
	m.waitSignal()

	// 等worker退出
	log.Printf("[info] master wait worker exit, worker pid: %d", m.workerPid)
	if m.workerPid > 1 {
		timeout := int(m.opt.stopTimeout/(time.Millisecond*100)) + 2
		for i := 0; i < timeout; i++ {
			p, err := os.FindProcess(m.workerPid)
			if p != nil && err == nil {
				err := p.Signal(syscall.Signal(0))
				if err == nil {
					time.Sleep(time.Millisecond * 100)
					continue
				}
				log.Println(err)
			} else {
				log.Println(err)
			}
			break
		}
	}
	log.Println("master exit")
	return nil
}

func (m *master) waitSignal() {
	ch := make(chan os.Signal, 1)
	sigs := make([]os.Signal, 0, len(m.opt.reloadSignals)+len(m.opt.stopSignals))
	for _, s := range m.opt.reloadSignals {
		sigs = append(sigs, s)
	}
	for _, s := range m.opt.stopSignals {
		sigs = append(sigs, s)
	}
	signal.Notify(ch, sigs...)

	isrun := true
	for isrun {
		var sig os.Signal
		select {
		case err := <-m.workerExit:
			if _, ok := err.(*exec.ExitError); ok {
				log.Printf("[warning] worker exit with error: %+v, master is going to shutdown.", err)
				m.stop()
				isrun = false
				break
			}
		case sig = <-ch:
			log.Printf("[info] master got signal: %v\n", sig)
		}

		for _, s := range m.opt.stopSignals {
			if s == sig {
				m.stop()
				isrun = false
				break
			}
		}

		for _, s := range m.opt.reloadSignals {
			if s == sig {
				m.reload()
				break
			}
		}
	}
}

func (m *master) reload() {
	if time.Now().Unix() < (m.lastReloadTime + 5) {
		log.Printf("[error] reload fail, too more")
		return
	}

	m.lastReloadTime = time.Now().Unix()
	pid, err := m.forkWorker()
	if err != nil {
		log.Printf("[warning] fork error: %v\n", err)
	}

	m.workerPid = pid
}

func (m *master) stopWorker() {
	p, err := os.FindProcess(m.workerPid)
	if err != nil {
		log.Printf("[warning] find process error: %v", err)
		return
	}
	m.isCheckWorker = false
	err = p.Signal(workerStopSignal)
	if err != nil {
		log.Printf("[warning]kill old worker error: %v", err)
	}
}

func (m *master) stop() {
	m.stopWorker()
}

func (m *master) checkWorker() {
	for m.isCheckWorker {
		p, err := os.FindProcess(m.workerPid)
		if p != nil && err == nil {
			err := p.Signal(syscall.Signal(0))
			if err == nil {
				time.Sleep(time.Second * 3)
				continue
			}
		}

		log.Printf("[warning] check worker exit, restart ...")
		pid, err := m.forkWorker()
		if err != nil {
			log.Printf("[warning] fork error: %v\n", err)
		}
		m.workerPid = pid

		time.Sleep(time.Second * 3)
	}
}

func (m *master) forkWorker() (int, error) {
	// 获取 args
	var args []string
	path := os.Args[0]
	if len(os.Args) > 1 {
		args = os.Args[1:]
	}
	args = append(args, "worker")

	workerFlag := fmt.Sprintf("%s=%s", EnvWorker, EnvWorkerVal)
	oldWorkerPid := fmt.Sprintf("%s=%d", EnvOldWorkerPid, m.workerPid)
	env := append(os.Environ(), workerFlag, oldWorkerPid)

	cmd := exec.Command(path, args...)
	cmd.Stdin = os.Stdin
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	cmd.ExtraFiles = m.socketFiles
	cmd.Env = env
	err := cmd.Start()
	if err != nil {
		return 0, err
	}

	go func() {
		m.workerExit <- cmd.Wait()
	}()

	forkId := cmd.Process.Pid
	log.Printf("[info] start new process success, pid %d\n", forkId)

	return forkId, nil
}

func (m *master) createFDs(services []*service) error {
	m.socketFiles = make([]*os.File, 0, len(services))
	for _, service := range services {
		f, err := m.listen(service.addr)
		if err != nil {
			return fmt.Errorf("failed to listen on addr: %s, err: %v", m.addr, err)
		}
		m.socketFiles = append(m.socketFiles, f)
	}
	return nil
}

func (m *master) listen(addr Address) (*os.File, error) {
	if addr.network == "tcp" {
		a, err := net.ResolveTCPAddr("tcp", addr.addr)
		if err != nil {
			return nil, err
		}
		l, err := net.ListenTCP("tcp", a)
		if err != nil {
			return nil, err
		}
		f, err := l.File()
		if err != nil {
			return nil, err
		}
		if err := l.Close(); err != nil {
			return nil, err
		}
		return f, nil
	}

	return nil, fmt.Errorf("unknown network: %v", addr.network)
}
