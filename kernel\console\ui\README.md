# Nakama Console

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 10.0.8.

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The app will automatically reload if you change any of the source files.

## Code Generation

To generate the ConsoleService definition, run `go generate -x ./...` in the parent Console package. Make sure to install the `protoc-gen-angular` plugin locally on your machine.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `npm run-script build` (not `ng build`) to build the project. The build artifacts will be stored in the `dist/` directory with the `production` configuration.
