package common

import (
	"fmt"
	"strconv"

	"github.com/shopspring/decimal"
)

func StrToInt(str string) int {
	if str == "" {
		return 0
	}
	val, _ := strconv.Atoi(str)
	return val
}

func StrToBool(str string) bool {
	if str == "" {
		return false
	}
	val, _ := strconv.ParseBool(str)
	return val
}

func StrToInt64(str string) int64 {
	if str == "" {
		return 0
	}
	val, _ := strconv.ParseInt(str, 10, 64)
	return val
}

func StrToUInt64(str string) uint64 {
	if str == "" {
		return 0
	}
	val, _ := strconv.ParseUint(str, 10, 64)
	return val
}

func StrToFloat64(str string) float64 {
	if str == "" {
		return 0
	}
	v, _ := strconv.ParseFloat(str, 64)
	return v
}

func Interface2String(value interface{}) string {
	if value == nil {
		return ""
	}
	switch value.(type) {
	case string:
		return value.(string)
	case []byte:
		return string(value.([]byte))
	case float64:
		return decimal.NewFromFloat(value.(float64)).String()
	default:
		return fmt.Sprint(value)
	}
}

func InterfaceToInt32(value interface{}) int32 {
	if value == nil {
		return 0
	}
	return int32(Interface2Int64(value))
}

func Interface2Int64(value interface{}) int64 {
	if value == nil {
		return 0
	}
	switch value.(type) {
	case string:
		return StrToInt64(value.(string))
	case int64:
		return value.(int64)
	case float64:
		return decimal.NewFromFloat(value.(float64)).IntPart()
	default:
		return StrToInt64(fmt.Sprint(value))
	}
}

func Interface2UInt64(value interface{}) uint64 {
	if value == nil {
		return 0
	}
	switch value.(type) {
	case string:
		return StrToUInt64(value.(string))
	case int64:
		return uint64(value.(int64))
	case uint64:
		return value.(uint64)
	case float64:
		return decimal.NewFromFloat(value.(float64)).BigInt().Uint64()
	default:
		return StrToUInt64(fmt.Sprint(value))
	}
}

func Interface2Float64(value interface{}) float64 {
	if value == nil {
		return 0
	}
	switch value.(type) {
	case string:
		return StrToFloat64(value.(string))
	case float64:
		return value.(float64)
	default:
		return StrToFloat64(fmt.Sprint(value))
	}
}

func Interface2Bool(value interface{}) bool {
	if value == nil {
		return false
	}
	switch value.(type) {
	case string:
		val, _ := strconv.ParseBool(value.(string))
		return val
	case bool:
		return value.(bool)
	default:
		return false
	}
}
