#!/bin/bash

# Example command for running kernel with specific database and session settings
# ./kernel --database.address "root@127.0.0.1:26257" -session.token_expiry_sec 3600

# Default target: runs "all" if no target is specified
TARGET=${1:-all}

# Target: all
# Runs kernel with the configuration file specified
all() {
  GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn
  ./kernel -config ./config.yaml
}

# Target: debug
# Builds the project with debugging enabled and starts a Delve debugging session
# -trimpath: Removes file system paths from compiled binaries to improve reproducibility
# -mod=vendor: Uses the local vendor directory for module dependencies
# -gcflags "all=-N -l": Disables optimizations and inlining for easier debugging
debug() {
  go build -buildmode=plugin  -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn" -trimpath -gcflags "all=-N -l"
}

# Target: rundebug
# Runs the kernel server with Delve debugger
rundebug() {
  GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn
  dlv exec ./kernel -- -config ./data/gateserver101.yaml
}

# Execute the specified target
$TARGET
