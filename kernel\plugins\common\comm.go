package common

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha256"
	"crypto/tls"
	"encoding/base64"
	"encoding/gob"
	"encoding/hex"
	"errors"
	"fmt"
	nkruntime "kernel/kernel-common/runtime"
	"math"
	"math/rand"
	"net"
	"net/http"
	"net/smtp"
	"net/url"
	"os"
	"reflect"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/jordan-wright/email"
	jsoniter "github.com/json-iterator/go"
	"go.opentelemetry.io/otel/trace"
)

// http请求获取获取客户端ip
func GetClientIP(r *http.Request) string {
	xForwardedFor := r.Header.Get("X-Forwarded-For")
	ip := strings.TrimSpace(strings.Split(xForwardedFor, ",")[0])
	if ip != "" {
		if net.ParseIP(ip) != nil {
			return ip
		}
	}

	ip = strings.TrimSpace(r.Header.Get("X-Real-Ip"))
	if ip != "" {
		if net.ParseIP(ip) != nil {
			return ip
		}
	}

	if ip, _, err := net.SplitHostPort(strings.TrimSpace(r.RemoteAddr)); err == nil {
		return ip
	}

	return ""
}

func ExtractClientAddressFromRequest(r *http.Request) (string, string) {
	var clientAddr string
	if ips := r.Header.Get("x-forwarded-for"); len(ips) > 0 {
		clientAddr = strings.Split(ips, ",")[0]
	} else {
		clientAddr = r.RemoteAddr
	}

	return ExtractClientAddress(clientAddr, r, "request")
}

func ExtractClientAddress(clientAddr string, source interface{}, sourceType string) (string, string) {
	var clientIP, clientPort string

	if clientAddr != "" {
		// It's possible the request metadata had no client address string.

		clientAddr = strings.TrimSpace(clientAddr)
		if host, port, err := net.SplitHostPort(clientAddr); err == nil {
			clientIP = host
			clientPort = port
		} else {
			var addrErr *net.AddrError
			if errors.As(err, &addrErr) {
				switch addrErr.Err {
				case "missing port in address":
					fallthrough
				case "too many colons in address":
					clientIP = clientAddr
				default:
					// Unknown address error, ignore the address.
				}
			}
		}
		// At this point err may still be a non-nil value that's not a *net.AddrError, ignore the address.
	}

	if clientIP == "" {
		if r, isRequest := source.(*http.Request); isRequest {
			source = map[string]interface{}{"headers": r.Header, "remote_addr": r.RemoteAddr}
		}
		fmt.Printf("cannot extract client address address_source_type: %s, address_source: %v", sourceType, source)
	}

	return clientIP, clientPort
}

// 获取毫秒时间戳
func GetMillisecond() int64 {
	return time.Now().UnixNano() / 1e6
}

// 获取微妙时间戳
func GetMicosecond() int64 {
	return time.Now().UnixNano() / 1e3
}

// 结构体转换为map[string]interface{}
func StrcutToMap(src interface{}, dst interface{}) error {
	b, err := jsoniter.Marshal(src)
	if err != nil {
		return err
	}
	decoder := jsoniter.NewDecoder(strings.NewReader(string(b)))
	decoder.UseNumber()
	return decoder.Decode(dst)
}

func DeepCopy(src, dst interface{}) error {
	var buf bytes.Buffer
	if err := gob.NewEncoder(&buf).Encode(src); err != nil {
		return err
	}
	return gob.NewDecoder(bytes.NewBuffer(buf.Bytes())).Decode(dst)
}

// 获取ip范围
func GetCidrIpRange(cidr string) (string, string) {
	ip := strings.Split(cidr, "/")[0]
	ipSegs := strings.Split(ip, ".")
	maskLen, _ := strconv.Atoi(strings.Split(cidr, "/")[1])
	seg3MinIp, seg3MaxIp := GetIpSeg3Range(ipSegs, maskLen)
	seg4MinIp, seg4MaxIp := GetIpSeg4Range(ipSegs, maskLen)
	ipPrefix := ipSegs[0] + "." + ipSegs[1] + "."

	return ipPrefix + strconv.Itoa(seg3MinIp) + "." + strconv.Itoa(seg4MinIp),
		ipPrefix + strconv.Itoa(seg3MaxIp) + "." + strconv.Itoa(seg4MaxIp)
}

// 计算得到CIDR地址范围内可拥有的主机数量
func GetCidrHostNum(maskLen int) uint {
	cidrIpNum := uint(0)
	var i uint = uint(32 - maskLen - 1)
	for ; i >= 1; i-- {
		cidrIpNum += 1 << i
	}
	return cidrIpNum
}

// 获取Cidr的掩码
func GetCidrIpMask(maskLen int) string {
	// ^uint32(0)二进制为32个比特1，通过向左位移，得到CIDR掩码的二进制
	cidrMask := ^uint32(0) << uint(32-maskLen)
	//fmt.Println(fmt.Sprintf("%b \n", cidrMask))
	//计算CIDR掩码的四个片段，将想要得到的片段移动到内存最低8位后，将其强转为8位整型，从而得到
	cidrMaskSeg1 := uint8(cidrMask >> 24)
	cidrMaskSeg2 := uint8(cidrMask >> 16)
	cidrMaskSeg3 := uint8(cidrMask >> 8)
	cidrMaskSeg4 := uint8(cidrMask & uint32(255))

	return fmt.Sprint(cidrMaskSeg1) + "." + fmt.Sprint(cidrMaskSeg2) + "." + fmt.Sprint(cidrMaskSeg3) + "." + fmt.Sprint(cidrMaskSeg4)
}

// 得到第三段IP的区间（第一片段.第二片段.第三片段.第四片段）
func GetIpSeg3Range(ipSegs []string, maskLen int) (int, int) {
	if maskLen > 24 {
		segIp, _ := strconv.Atoi(ipSegs[2])
		return segIp, segIp
	}
	ipSeg, _ := strconv.Atoi(ipSegs[2])
	return GetIpSegRange(uint8(ipSeg), uint8(24-maskLen))
}

// 得到第四段IP的区间（第一片段.第二片段.第三片段.第四片段）
func GetIpSeg4Range(ipSegs []string, maskLen int) (int, int) {
	ipSeg, _ := strconv.Atoi(ipSegs[3])
	segMinIp, segMaxIp := GetIpSegRange(uint8(ipSeg), uint8(32-maskLen))
	return segMinIp + 1, segMaxIp
}

// 根据用户输入的基础IP地址和CIDR掩码计算一个IP片段的区间
func GetIpSegRange(userSegIp, offset uint8) (int, int) {
	var ipSegMax uint8 = 255
	netSegIp := ipSegMax << offset
	segMinIp := netSegIp & userSegIp
	segMaxIp := userSegIp&(255<<offset) | ^(255 << offset)
	return int(segMinIp), int(segMaxIp)
}

// IPString2Long 把ip字符串转为数值
func IPString2Long(ip string) (uint, error) {
	b := net.ParseIP(ip).To4()
	if b == nil {
		return 0, errors.New("invalid ipv4 format")
	}

	return uint(b[3]) | uint(b[2])<<8 | uint(b[1])<<16 | uint(b[0])<<24, nil
}

// Long2IPString 把数值转为ip字符串
func Long2IPString(i uint) (string, error) {
	if i > math.MaxUint32 {
		return "", errors.New("beyond the scope of ipv4")
	}

	ip := make(net.IP, net.IPv4len)
	ip[0] = byte(i >> 24)
	ip[1] = byte(i >> 16)
	ip[2] = byte(i >> 8)
	ip[3] = byte(i)

	return ip.String(), nil
}

// 获取反射对象
func GetReflectObj(obj interface{}) interface{} {
	if reflect.ValueOf(obj).Kind() == reflect.Ptr {
		tty := reflect.ValueOf(obj).Elem()
		s := tty.Interface()
		return s
	} else {
		tty := reflect.ValueOf(&obj).Elem()
		s := tty.Interface()
		return s
	}
}

// 获取反射对象切片
func GetReflectSlice(obj interface{}) interface{} {
	typeOfT := reflect.TypeOf(obj)
	sliceOfT := reflect.SliceOf(typeOfT)
	ptr := reflect.New(sliceOfT)
	ptr.Elem().Set(reflect.MakeSlice(sliceOfT, 0, 0))
	s2 := ptr.Interface()
	return s2
}

// CreatePointerFromStruct 根据反射值动态创建指向同一结构体的指针对象
func CreatePointerFromStruct(value interface{}) (interface{}, error) {
	val := reflect.ValueOf(value)
	if !val.IsValid() {
		return nil, fmt.Errorf("invalid value: nil")
	}

	typ := val.Type()
	if val.Kind() == reflect.Ptr {
		typ = typ.Elem()
	}

	// 检查是否为结构体
	if typ.Kind() != reflect.Struct {
		return nil, fmt.Errorf("input must be a struct or a pointer to struct")
	}

	// 创建一个新的结构体指针对象
	newObj := reflect.New(typ).Interface()
	return newObj, nil
}

// 飞书通知签名
func FeiShuGenSign(secret string, timestamp int64) (string, error) {
	//timestamp + key 做sha256, 再进行base64 encode
	stringToSign := fmt.Sprintf("%v", timestamp) + "\n" + secret
	var data []byte
	h := hmac.New(sha256.New, []byte(stringToSign))
	_, err := h.Write(data)
	if err != nil {
		return "", err
	}

	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))
	return signature, nil
}

func ParseUrlRawQuery(rawstr string, unescape bool) map[string]string {
	result := map[string]string{}
	list := strings.Split(rawstr, "&")
	for _, v := range list {
		vals := strings.Split(v, "=")
		var err error
		key := vals[0]
		if unescape {
			key, err = url.QueryUnescape(vals[0])
			if err != nil {
				key = vals[0]
			}
		}
		if len(vals) == 1 {
			result[key] = ""
		} else if len(vals) == 2 {
			result[key] = vals[1]
			if unescape {
				if tmpval, err := url.QueryUnescape(vals[1]); err == nil {
					result[key] = tmpval
				}
			}
		} else {
			tv := ""
			for i := 1; i < len(vals); i++ {
				tv += vals[i]
				if i < len(vals)-1 {
					tv += "="
				}
			}
			result[key] = tv
			if unescape {
				if tmpval, err := url.QueryUnescape(tv); err == nil {
					result[key] = tmpval
				}
			}
		}
	}
	return result
}

func LuaOr(v, ret interface{}) interface{} {
	if v == nil {
		return ret
	}
	return v
}

func MapToUrlValue(val map[string]interface{}) string {
	uval := ""
	index := 0
	for k, v := range val {
		vv := Interface2String(v)
		if index == len(val)-1 {
			uval = fmt.Sprintf("%s%s=%s", uval, k, vv)
		} else {
			uval = fmt.Sprintf("%s%s=%s&", uval, k, vv)
		}
		index++
	}
	return uval
}

// 解析basicauth
func ParseBasicAuth(src string) []map[string]interface{} {
	tmp := []map[string]interface{}{}
	arr1 := strings.Split(src, ",")
	for i := 0; i < len(arr1); i++ {
		arr2 := strings.Split(arr1[i], ":")
		if len(arr2) == 2 {
			tmp = append(tmp, map[string]interface{}{
				arr2[0]: arr2[1],
			})
		}
	}
	return tmp
}

// 获取正在运行的函数名
func RunFuncName() string {
	pc := make([]uintptr, 1)
	runtime.Callers(2, pc)
	f := runtime.FuncForPC(pc[0])
	return f.Name()
}

// 版本号转换
func Ver2i(strver string) int {
	verlist := strings.Split(strver, ".")
	if len(verlist) == 3 {
		return StrToInt(verlist[0])*256*256 + StrToInt(verlist[1])*256 + StrToInt(verlist[2])
	} else {
		return 0
	}
}

func I2ver(ver int) string {
	a3 := ver % 256
	a2 := (ver - a3) % 65536
	a1 := (ver - a3 - a2) / 65536
	a2 = a2 / 256
	return fmt.Sprintf("%d.%d.%d", a1, a2, a3)
}

func MapToURL(m map[string]interface{}) string {
	// 将键和值编码为 URL 编码格式
	encodedPairs := make([]string, 0, len(m))
	for key, value := range m {
		encodedKey := url.QueryEscape(key)
		encodedValue := url.QueryEscape(Interface2String(value))
		encodedPair := encodedKey + "=" + encodedValue
		encodedPairs = append(encodedPairs, encodedPair)
	}
	url := strings.Join(encodedPairs, "&")
	return url
}

// hasBit 检查一个整数的指定位是否为1
func HasBit(n, pos int64) bool {
	return n&(1<<pos) != 0
}

// isRunningInKubernetes 检查当前进程是否在 Kubernetes 环境中运行
func IsRunningInKubernetes() bool {
	// 检查 Kubernetes 常用的环境变量
	envVars := []string{
		"KUBERNETES_SERVICE_HOST",
		"KUBERNETES_SERVICE_PORT",
		"KUBERNETES_PORT",
	}

	for _, envVar := range envVars {
		if _, exists := os.LookupEnv(envVar); exists {
			return true
		}
	}

	// 检查 Kubernetes 的 service account 文件
	if _, err := os.Stat("/var/run/secrets/kubernetes.io/serviceaccount"); err == nil {
		return true
	}

	return false
}

func ConvertRuntimeEnv(logger nkruntime.Logger, envMap map[string]string, env []string) map[string]string {
	if envMap == nil {
		envMap = make(map[string]string)
	}
	for _, e := range env {
		if !strings.Contains(e, "=") {
			logger.Error("Invalid runtime environment value %s", e)
			continue
		}

		kv := strings.SplitN(e, "=", 2) // the value can contain the character "=" many times over.
		if len(kv) == 1 {
			envMap[kv[0]] = ""
		} else if len(kv) == 2 {
			envMap[kv[0]] = kv[1]
		}
	}
	return envMap
}

func BasicAuth(username, password string) string {
	auth := username + ":" + password
	return "Basic " + base64.StdEncoding.EncodeToString([]byte(auth))
}

func GetMD5(data string) string {
	md5 := md5.Sum([]byte(data))
	return hex.EncodeToString(md5[:])
}

// 16位md5
func Get16MD5(data string) string {
	return GetMD5(data)[8:24]
}

func GetByteMD5(data []byte) string {
	md5 := md5.Sum(data)
	return hex.EncodeToString(md5[:])
}

func GetRandomCode(length int) string {
	rand.Seed(time.Now().UnixNano())
	const emailCodeAlphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = emailCodeAlphabet[rand.Intn(len(emailCodeAlphabet))]
	}
	return string(b)
}

// 获取本机内网ip
func GetLocalIP() string {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		fmt.Println("get local ip error", err)
		return "127.0.0.1"
	}

	for _, addr := range addrs {
		// 判断是否是 IP 地址类型（排除 MAC、链路层）
		if ipNet, ok := addr.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			// 只获取 IPv4 地址
			if ip4 := ipNet.IP.To4(); ip4 != nil {
				return ip4.String()
			}
		}
	}
	return "127.0.0.1"
}

// 通用发送函数
func SendMail(smtpHost string, smtpPort string, username string, password string, to []string, subject string, htmlBody string) error {
	e := email.NewEmail()
	e.From = username
	e.To = to
	e.Subject = subject
	e.HTML = []byte(htmlBody)

	// 支持 SSL
	return e.SendWithTLS(smtpHost+":"+smtpPort,
		smtp.PlainAuth("", username, password, smtpHost),
		&tls.Config{InsecureSkipVerify: true, ServerName: smtpHost})
}

// 清理ctx中会话信息保留trace跟踪信息
func WithSession(ctx context.Context) context.Context {
	// 从旧 ctx 中取出 span（OpenTelemetry trace信息）
	span := trace.SpanFromContext(ctx)
	newCtx := context.Background()
	newCtxWithSpan := trace.ContextWithSpan(newCtx, span)
	return newCtxWithSpan
}
