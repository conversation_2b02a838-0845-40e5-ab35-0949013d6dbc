package common

import (
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/sony/gobreaker/v2"
)

type Breaker struct {
	breakerMap sync.Map
}

func NewBreaker() *Breaker {
	return &Breaker{
		breakerMap: sync.Map{},
	}
}

// getBreaker 返回针对 addr 的熔断器（如 "************:7369"）
func (b *Breaker) GetBreaker(addr string) *gobreaker.CircuitBreaker[any] {
	if cb, ok := b.breakerMap.Load(addr); ok {
		return cb.(*gobreaker.CircuitBreaker[any])
	}

	cb := gobreaker.NewCircuitBreaker[any](gobreaker.Settings{
		Name:        addr,
		Timeout:     5 * time.Second,  // 熔断时间（Open -> HalfOpen）
		Interval:    10 * time.Second, // 统计窗口
		MaxRequests: 10,               // Half-Open 状态允许通过的调用数
		ReadyToTrip: func(c gobreaker.Counts) bool {
			// 超过100次请求，失败率 > 50% 就熔断
			return c.Requests >= 100 && float64(c.TotalFailures)/float64(c.Requests) > 0.5
		},
		OnStateChange: func(name string, from, to gobreaker.State) {
			fmt.Printf("Breaker %s changed state: %s -> %s\n", name, from, to)
			// metrics.Inc("breaker_state", labels{name, to})
		},
	})
	b.breakerMap.Store(addr, cb)
	return cb
}

// CallWithBreaker 封装统一调用入口，T 为返回值类型
func CallWithBreaker[T any](breaker *Breaker, addr string, op func() (T, error)) (T, error) {
	var zero T // 返回默认值
	cb := breaker.GetBreaker(addr)
	result, err := cb.Execute(func() (any, error) {
		return op()
	})
	if err != nil {
		return zero, err
	}
	return result.(T), nil
}

// 判断是否是熔断器短路错误
func IsBreakerOpen(err error) bool {
	return errors.Is(err, gobreaker.ErrOpenState) || errors.Is(err, gobreaker.ErrTooManyRequests)
}
