#!/bin/bash

# 根据exampleserver生成其他业务模块

# 检查参数数量和是否以 "server" 结尾
if [ "$#" -ne 1 ] || [[ "$1" != *server ]]; then
    echo "错误: 必须提供一个参数且参数值必须以 '[xxx]server' 结尾，例如testserver！"
    echo "用法: $0 <参数名>"
    exit 1
fi

# 检查模块是否已经存在
if [ -d "$1" ]; then
    echo "模块 $1 已经存在！"
    exit 1
fi

# 提取 'server' 前面的部分
prefix="${1%server}"
if [ -z "$prefix" ]; then
    echo "错误: 参数的 'server' 前缀部分不能为空！"
    exit 1
fi

# 将前缀转换为小写
prefix_lowercase="${prefix,,}"

# 将前缀的首字母转换为大写
prefix_capitalized="${prefix^}"

# 全部转换为大写
prefix_uppercase="${prefix^^}"

cp ./exampleserver ./$1 -rf

# 替换config.go Example example
sed -i "s/Example/${prefix_capitalized}/g" ./$1/config.go
sed -i "s/example/${prefix_lowercase}/g" ./$1/config.go
sed -i "s/Example/${prefix_capitalized}/g" ./$1/main.go
sed -i "s/example/${prefix_lowercase}/g" ./$1/main.go
sed -i "s/EXAMPLE/${prefix_uppercase}/g" ./$1/main.go
sed -i "s/Example/${prefix_capitalized}/g" ./$1/controller.go
sed -i "s/EXAMPLE/${prefix_uppercase}/g" ./$1/const.go
sed -i "s/example/${prefix_lowercase}/g" ./$1/const.go
sed -i "s/example/${prefix_lowercase}/g" ./$1/build.sh

# 追加服务名
sed -i "/SERVER_NAME_EXAMPLE/a \\\tSERVER_NAME_${prefix_uppercase} = \"$1\"" ../models/const.go

# 创建数据目录
mkdir -p ../../data/test_env/$1
mkdir -p ../../data/prod_env/$1

echo "模块 $1 创建成功！"
