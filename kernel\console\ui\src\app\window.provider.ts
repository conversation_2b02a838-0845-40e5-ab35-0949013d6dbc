// Copyright 2020 The Nakama Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {FactoryProvider, InjectionToken} from '@angular/core';

export const WINDOW = new InjectionToken<Window>('window');
const windowProvider: FactoryProvider = {provide: WINDOW, useFactory: () => window};
export const WINDOW_PROVIDERS = [windowProvider];

declare global {
  interface Window { }
}
