#!/bin/bash
#
# Scripts in this directory are run during the build process.
# each script will be uploaded to /tmp on your build droplet, 
# given execute permissions and run.  The cleanup process will
# remove the scripts from your build system after they have run
# if you use the build_image task.
#
echo "HELLO WORLD"

echo "Starting Cockroach"
cockroach version
cockroach start --background --insecure --store=path='/root/cdb-store1/' --listen-addr=localhost


echo "Testing Nakama"
systemctl start nakama.service
systemctl status nakama.service

echo "Stopping all"
systemctl stop nakama.service
sleep 30
cockroach quit --insecure

echo "Complete"