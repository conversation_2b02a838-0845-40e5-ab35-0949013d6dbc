package main

import (
	"kernel/plugins/models"
)

type CustomConfig struct {
	Config    *MallServerConfig       `json:"mall_server_config,omitempty" yaml:"mall_server_config,omitempty"`
	AccessLog *models.AccessLogConfig `json:"access_log,omitempty" yaml:"access_log,omitempty"`
	Logger    *models.LoggerConfig    `json:"logger,omitempty" yaml:"logger,omitempty"`
}

type MallServerConfig struct {
	NodeConfig        *models.NodeConfig `json:"node_config,omitempty" yaml:"node_config,omitempty"`
	WatchSvcGroupList []string           `json:"watch_svc_group_list,omitempty" yaml:"watch_svc_group_list,omitempty"`
	NacosConfig       *NacosConfig       `json:"visual_nacos,omitempty" yaml:"visual_nacos,omitempty"` // 可视化配置的 Nacos 配置
	VisualCfgs        []string           `json:"visual_cfgs,omitempty" yaml:"visual_cfgs,omitempty"`   // 需要加载的可视化模块列表
}

type NacosConfig struct {
	Urls        []string `yaml:"urls,omitempty" json:"urls,omitempty"`
	NamespaceId string   `yaml:"namespace_id,omitempty" json:"namespace_id,omitempty"`
	Username    string   `yaml:"username,omitempty" json:"username,omitempty"`
	Password    string   `yaml:"password,omitempty" json:"password,omitempty"`
	TimeoutMs   int      `yaml:"timeout_ms,omitempty" json:"timeout_ms,omitempty"`
	AccessKey   string   `yaml:"access_key,omitempty" json:"access_key,omitempty"`
	SecretKey   string   `yaml:"secret_key,omitempty" json:"secret_key,omitempty"`
}
