package bo

import (
	"bytes"
	"encoding/json"
	"fmt"
	"kernel/plugins/common"
	"kernel/plugins/models"
	"reflect"
	"time"
)

// CREATE TABLE IF NOT EXISTS recharge_order (
//     FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,

//     -- 内部订单号（先建订单时生成）
//     order_id UUID NOT NULL PRIMARY KEY,               -- 订单号
//     user_id UUID NOT NULL,                            -- 玩家ID

//     -- 渠道订单号（渠道方返回）
//     channel_order_id VARCHAR(128),                    -- 渠道订单号，支付成功后填
//     channel VARCHAR(32) NOT NULL,                     -- 渠道标识，如 google, apple, huawei 等
//     product_id VARCHAR(64) NOT NULL,                  -- 商品ID，例如 diamond_500
//     amount NUMERIC(10, 2) NOT NULL,                   -- 订单金额
//     currency VARCHAR(8) NOT NULL,                     -- 货币代码，ISO 4217，如 USD, CNY

//     status SMALLINT NOT NULL DEFAULT 0,               -- 订单状态：0待支付,1已支付待发货,2已发货,3支付失败,4过期未支付

//     extra JSONB DEFAULT NULL,                         -- 额外信息，比如购买IP、设备号、渠道原始返回数据等

//     created_at TIMESTAMP NOT NULL DEFAULT NOW(),      -- 创建时间
//     updated_at TIMESTAMP NOT NULL DEFAULT NOW(),      -- 更新时间（触发器更新）

//     CONSTRAINT uniq_channel_order UNIQUE (channel, channel_order_id)
// );

type RechargeOrder struct {
	OrderId        string    `json:"order_id,omitempty"`         // 订单号
	UserId         string    `json:"user_id,omitempty"`          // 玩家ID
	ChannelOrderId string    `json:"channel_order_id,omitempty"` // 渠道订单号，支付成功后填
	Channel        string    `json:"channel,omitempty"`          // 渠道标识，如 google, apple, huawei 等
	ProductId      string    `json:"product_id,omitempty"`       // 商品ID，例如 diamond_500
	Amount         float64   `json:"amount,omitempty"`           // 订单金额
	Currency       string    `json:"currency,omitempty"`         // 货币代码，ISO 4217，如 USD, CNY
	Status         int32     `json:"status,omitempty"`           // 订单状态：0待支付,1已支付待发货,2已发货,3支付失败,4过期未支付
	Extra          string    `json:"extra,omitempty"`            // 额外信息，比如购买IP、设备号、渠道原始返回数据等
	CreatedAt      time.Time `json:"created_at,omitempty"`       // 创建时间
	UpdatedAt      time.Time `json:"updated_at,omitempty"`       // 更新时间
}

func (v *RechargeOrder) GetTable() string {
	return "recharge_order"
}

func (v *RechargeOrder) GetKeyName() string {
	return "order_id"
}

func (v *RechargeOrder) GetUniqueKeys() []string {
	return []string{}
}

func (v *RechargeOrder) GetSecondKeyName() string {
	return ""
}

func (v *RechargeOrder) GetRowResultType() models.RowResultType {
	return models.ROWRESULT_TYPE_ONLYONE
}

func (v *RechargeOrder) GetQueryArgs() string {
	return "*"
}

func (v *RechargeOrder) GetFlushType() models.FlushType {
	return models.FLUSH_TYPE_SYNC
}

func (v *RechargeOrder) GetCacheLevel() models.CacheLevel {
	return models.CACHE_LEVEL_IMPORT
}

func (v *RechargeOrder) GetVersionName() string {
	return ""
}

func (v *RechargeOrder) Marshal() ([]byte, error) {
	return json.Marshal(v)
}

func (v *RechargeOrder) MarshalToMap(filter map[string]struct{}) map[string]interface{} {
	dst := make(map[string]interface{})
	err := common.StrcutToMap(v, &dst)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	for k := range filter {
		delete(dst, k)
	}
	return dst
}

func (v *RechargeOrder) Unmarshal(buf []byte) error {
	d := json.NewDecoder(bytes.NewReader(buf))
	d.UseNumber()
	return d.Decode(v)
}

func (v *RechargeOrder) Clear() {
	p := reflect.ValueOf(v).Elem()
	p.Set(reflect.Zero(p.Type()))
}
