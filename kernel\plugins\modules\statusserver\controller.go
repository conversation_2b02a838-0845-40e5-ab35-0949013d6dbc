package main

import (
	"context"
	"database/sql"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/models"
	"strings"

	json "github.com/json-iterator/go"
)

type Controller struct {
	server *StatusServerModule
}

func NewController(s *StatusServerModule) *Controller {
	return &Controller{
		server: s,
	}
}

// @Summary 根据uid查询用户在线信息
// @Description 根据uid查询在线信息
// @Accept json
// @Produce json
// @Param payload body string true "查询用户状态请求,多个uid用逗号分隔，例如: 1,2,3"
// @Success 200 {object} map[string]models.UserOnlineInfo "查询用户状态响应"
// @Router /v2/rpc/statusserver.querybyuid [post]
func (c *Controller) QueryByUid(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userIds := strings.Split(payload, ",")
	statuses := make(map[string]*models.UserOnlineInfo)
	for _, userId := range userIds {
		status := c.server.statusBuckets.GetByUid(userId)
		if status != nil {
			statuses[userId] = status
		}
	}
	return json.MarshalToString(statuses)
}

// @Summary 根据uid查询用户是否在线
// @Description 根据uid查询是否在线
// @Accept json
// @Produce json
// @Param payload body string true "查询用户状态请求,多个uid用逗号分隔，例如: 1,2,3"
// @Success 200 {object} map[string]bool "查询用户状态响应"
// @Router /v2/rpc/statusserver.queryonlinebyuid [post]
func (c *Controller) QueryOnlineByUid(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userIds := strings.Split(payload, ",")
	statuses := make(map[string]bool)
	for _, userId := range userIds {
		status := c.server.statusBuckets.GetByUid(userId)
		if status != nil {
			statuses[userId] = true
		} else {
			statuses[userId] = false
		}
	}
	return json.MarshalToString(statuses)
}

// @Summary 根据uin查询用户在线信息
// @Description 根据uin查询用户在线信息
// @Accept json
// @Produce json
// @Param payload body string true "查询用户状态请求,多个uin用逗号分隔，例如: 1,2,3"
// @Success 200 {object} map[string]models.UserOnlineInfo "查询用户状态响应"
// @Router /v2/rpc/statusserver.querybyuin [post]
func (c *Controller) QueryByUin(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uins := strings.Split(payload, ",")
	statuses := make(map[string]*models.UserOnlineInfo)
	for _, uin := range uins {
		status := c.server.statusBuckets.GetByUin(common.Interface2Int64(uin))
		if status != nil {
			statuses[uin] = status
		}
	}
	return json.MarshalToString(statuses)
}

// @Summary 根据uin查询用户是否在线
// @Description 根据uin查询是否在线
// @Accept json
// @Produce json
// @Param payload body string true "多个uin用逗号分隔，例如: 1,2,3"
// @Success 200 {object} map[string]bool "查询用户状态响应"
// @Router /v2/rpc/statusserver.queryonlinebyuin [post]
func (c *Controller) QueryOnlineByUin(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uins := strings.Split(payload, ",")
	statuses := make(map[string]bool)
	for _, uin := range uins {
		status := c.server.statusBuckets.GetByUin(common.Interface2Int64(uin))
		if status != nil {
			statuses[uin] = true
		} else {
			statuses[uin] = false
		}
	}
	return json.MarshalToString(statuses)
}
