package models

type NodeConfig struct {
	NodeId   string            `json:"node_id,omitempty" yaml:"node_id,omitempty"`     // 节点id
	SvcGroup string            `json:"svc_group,omitempty" yaml:"svc_group,omitempty"` // 服务组 服务名@@组名
	Host     string            `json:"host,omitempty" yaml:"host,omitempty"`           // 访问地址 ip:port
	NetHost  string            `json:"nethost,omitempty" yaml:"nethost,omitempty"`     // 公网地址访问 ip:port
	Region   string            `json:"region,omitempty" yaml:"region,omitempty"`       // 节点所在区域
	AuthKey  string            `json:"auth_key,omitempty" yaml:"auth_key,omitempty"`   // 访问密钥(http key)
	Weight   int               `json:"weight,omitempty" yaml:"weight,omitempty"`       // 权重
	HostName string            `json:"hostname,omitempty" yaml:"hostname,omitempty"`   // 主机名称
	Scheme   string            `json:"scheme,omitempty" yaml:"scheme,omitempty"`       // 访问协议 http or https, default http
	Extend   map[string]string `json:"extend,omitempty" yaml:"extend,omitempty"`       // 扩展字段
	MaxConns int               `json:"max_conns,omitempty" yaml:"max_conns,omitempty"` // 最大连接数
	ConnNum  int               `json:"conn_num,omitempty" yaml:"conn_num,omitempty"`   // 连接数
}

type Route struct {
	ProxyType   string `json:"type,omitempty" yaml:"type,omitempty"`               // 代理类型，svc,hosts
	ProxyData   string `json:"data,omitempty" yaml:"data,omitempty"`               // 数据，当代理类型为svc时，这里是服务组名(服务名@@组名)，当代理类型为hosts，多host,分割
	LBType      string `json:"lbtype,omitempty" yaml:"lbtype,omitempty"`           // 负载均衡算法：rr(轮循)，hash(一致性hash(优先请求头中X-Custom-Hash-ID,再根据ip))，rand(随机)
	Scheme      string `json:"scheme,omitempty" yaml:"scheme,omitempty"`           // 协议类型
	Method      string `json:"method,omitempty" yaml:"method,omitempty"`           // 方法 GET or POST
	RoutePath   string `json:"routepath,omitempty" yaml:"routepath,omitempty"`     // 路由路径
	AuthKey     string `json:"authkey,omitempty" yaml:"authkey,omitempty"`         // 认证秘钥(socket.server_key)
	Transport   int    `json:"transport,omitempty" yaml:"transport,omitempty"`     // 访问传输网络 0:tcp(默认),1:udp(使用quic)
	Prefix      bool   `json:"prefix,omitempty" yaml:"prefix,omitempty"`           // 是否前缀匹配
	MatchSuffix string `json:"matchsuffix,omitempty" yaml:"matchsuffix,omitempty"` // 匹配后缀
	Unwrap      bool   `json:"unwrap,omitempty" yaml:"unwrap,omitempty"`           // 是否解包
	//HealthCheck   bool   `json:"type,omitempty"`          // 健康检查
}

// 用户在线信息
type UserOnlineInfo struct {
	UserId     string `json:"user_id,omitempty" yaml:"user_id,omitempty"`         // 用户id
	Uin        int64  `json:"uin,omitempty" yaml:"uin,omitempty"`                 // 用户uin
	SessionId  string `json:"session_id,omitempty" yaml:"session_id,omitempty"`   // 会话id
	NodeId     string `json:"node_id,omitempty" yaml:"node_id,omitempty"`         // 网关节点id
	NodeAddr   string `json:"node_addr,omitempty" yaml:"node_addr,omitempty"`     // 网关节点地址
	CltVersion int    `json:"cltversion,omitempty" yaml:"cltversion,omitempty"`   // 客户端版本
	OnlineTime int64  `json:"online_time,omitempty" yaml:"online_time,omitempty"` // 在线时间
	ApiID      int    `json:"apiid,omitempty" yaml:"apiid,omitempty"`             // api id
	ClientIP   string `json:"client_ip,omitempty" yaml:"client_ip,omitempty"`     // 客户端ip
}

type AccessLogConfig struct {
	Dir                  string          `yaml:"dir,omitempty" json:"dir,omitempty"`
	IsDisable            bool            `yaml:"is_disable,omitempty" json:"is_disable,omitempty"`                           // 禁止输出访问日志
	WriteToLogFile       bool            `yaml:"write_to_log_file,omitempty" json:"write_to_log_file,omitempty"`             // 是否写访问日志到文件
	RecordRespBody       bool            `yaml:"record_resp_body,omitempty" json:"record_resp_body,omitempty"`               // 访问日志记录响应数据
	DisableRecordReqBody bool            `yaml:"disable_record_req_body,omitempty" json:"disable_record_req_body,omitempty"` // 禁用访问日志记录请求数据
	RpcIds               map[string]bool `yaml:"rpc_ids,omitempty" json:"rpc_ids,omitempty"`                                 // 指定那个路由记录访问日志，*，或者不填代表所有
	MaxBackups           int             `yaml:"max_backups,omitempty" json:"max_backups,omitempty"`
}

type LoggerConfig struct {
	Level      string `yaml:"level,omitempty" json:"level,omitempty"` // 日志级别 'debug', 'info', 'warn', 'error'
	Stdout     bool   `yaml:"stdout,omitempty" json:"stdout,omitempty"`
	File       string `yaml:"file,omitempty" json:"file,omitempty"`
	Rotation   bool   `yaml:"rotation,omitempty" json:"rotation,omitempty"`
	MaxSize    int    `yaml:"max_size,omitempty" json:"max_size,omitempty"`
	MaxAge     int    `yaml:"max_age,omitempty" json:"max_age,omitempty"`
	MaxBackups int    `yaml:"max_backups,omitempty" json:"max_backups,omitempty"`
	LocalTime  bool   `yaml:"local_time,omitempty" json:"local_time,omitempty"`
	Compress   bool   `yaml:"compress,omitempty" json:"compress,omitempty"`
	Format     string `yaml:"format,omitempty" json:"format,omitempty"`
}
