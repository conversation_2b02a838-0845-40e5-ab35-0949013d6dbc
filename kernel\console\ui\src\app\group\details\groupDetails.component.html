<ngb-alert [dismissible]="false" type="danger" class="mb-3" *ngIf="error">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">An error occurred: {{error}}</h6>
</ngb-alert>

<ngb-alert [dismissible]="false" type="success" class="mb-3" *ngIf="updated">
  <img src="/static/svg/green-tick.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">Group was modified successfully.</h6>
</ngb-alert>

<form [formGroup]="groupForm" (ngSubmit)="updateGroup()" class="add-border">
  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="group_id">Group ID</label>
      </div>
      <input type="text" id="group_id" [value]="group.id" class="form-control-plaintext form-control-sm my-2"
             placeholder="Group ID" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="creator_id">Creator ID</label>
      </div>
      <a [routerLink]="['/accounts', group.creator_id]" style="width: 100%">
      <input type="text" id="creator_id"  [value]="group.creator_id" class="form-control-plaintext form-control-sm my-2"
             placeholder="Creator ID" disabled readonly>
      </a>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="name">Name</label>
      </div>
      <input type="text" id="name" class="form-control form-control-sm my-2" placeholder="Name" required
             formControlName="name" [ngClass]="{'is-invalid': f.name.dirty && f.name.errors}">
      <div class="invalid-tooltip" [hidden]="f.name.disabled || f.name.valid || f.name.pristine">Name is required</div>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="description">Description</label>
      </div>
      <input type="text" id="description" class="form-control form-control-sm my-2" placeholder="Description"
             formControlName="description">
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline align-self-center">
      <div class="col-3 pl-0">
        <label class="d-inline" for="avatar_url">Avatar URL</label>
      </div>
      <input type="text" id="avatar_url" class="form-control form-control-sm my-2" placeholder="Avatar URL"
             formControlName="avatar_url">
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="lang_tag">Language</label>
      </div>
      <input type="text" id="lang_tag" class="form-control form-control-sm my-2" placeholder="Language"
             formControlName="lang_tag">
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="creator_id">Members</label>
      </div>
      <input type="text" id="edge_count" [value]="group.edge_count" class="form-control-plaintext form-control-sm my-2"
             placeholder="Members" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline align-self-center">
      <div class="col-3 pl-0">
        <label class="d-inline" for="max_count">Max Count</label>
      </div>
      <input type="text" id="max_count" class="form-control form-control-sm my-2" placeholder="Max Count"
             formControlName="max_count">
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline align-self-center">
      <div class="col-3 pl-0">
        <label class="d-inline" for="max_count">Open</label>
      </div>
      <input type="checkbox" id="open" class="my-2" formControlName="open">
    </div>
  </div>


  <div class="row mb-3 add-border-single-row-bottom">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="create_time">Create Time</label>
      </div>
      <input type="text" id="create_time" [value]="group.create_time"
             class="form-control-plaintext form-control-sm my-2" placeholder="Create Time" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="update_time">Update Time</label>
      </div>
      <input type="text" id="update_time" [value]="group.update_time"
             class="form-control-plaintext form-control-sm my-2" placeholder="Update Time" disabled readonly>
    </div>
  </div>

  <div class="card p-2 mb-3 jsoneditor" style="height: 400px">
    <div #editor style="height: 400px"></div>
  </div>
  <button type="submit" class="btn btn-primary" [disabled]="updating" *ngIf="updateAllowed()">Save</button>
</form>
