{"name": "nakama-console-ui", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production && ng build --configuration=production-nt", "postbuild": "sh postbuild.sh", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^15.2.2", "@angular/cdk": "^14.2.7", "@angular/common": "^15.2.2", "@angular/compiler": "^15.2.2", "@angular/core": "^15.2.2", "@angular/forms": "^15.2.2", "@angular/localize": "^15.2.2", "@angular/platform-browser": "^15.2.2", "@angular/platform-browser-dynamic": "^15.2.2", "@angular/router": "^15.2.2", "@ng-bootstrap/ng-bootstrap": "^14.0.0", "@ng-select/ng-select": "^10.0.0", "@popperjs/core": "^2.11.8", "@scarf/scarf": "^1.1.1", "@swimlane/ngx-charts": "^16.0.0", "ace-builds": "^1.4.12", "ajv": "^8.12.0", "angularx-qrcode": "^15.0.1", "file-saver": "^2.0.5", "js-yaml": "^4.1.0", "ngx-file-drop": "^13.0.0", "ngx-segment-analytics": "^16.1.0", "rxjs": "~6.6.3", "tslib": "^2.0.1", "vanilla-jsoneditor": "^0.15.1", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^15.2.2", "@angular/cli": "^15.2.2", "@angular/compiler-cli": "^15.2.2", "@angular/language-service": "^15.2.2", "@types/node": "^14.6.1", "codelyzer": "^6.0.0", "protractor": "~7.0.0", "ts-node": "^9.0.0", "tslint": "^6.1.3", "typescript": "~4.9.5"}, "optionalDependencies": {"fsevents": "*"}, "scarfSettings": {"allowTopLevel": true}}