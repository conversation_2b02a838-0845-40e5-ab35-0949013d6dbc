<ngb-alert [dismissible]="false" type="danger" class="mb-3" *ngIf="error">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">An error occurred: {{error}}</h6>
</ngb-alert>

<div class="add-border mb-3">
  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="id">ID</label>
      </div>
      <input type="text" id="id" placeholder="ID" [value]="leaderboard.id" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="authoritative">Authoritative</label>
      </div>
      <input type="text" id="authoritative" placeholder="Authoritative" [value]="leaderboard.authoritative" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="sortorder">Sort Order</label>
      </div>
      <input type="text" id="sortorder" placeholder="Sort Order" [value]="orderString[leaderboard.sort_order]" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="operator">Operator</label>
      </div>
      <input type="text" id="operator" placeholder="Operator" [value]="operatorString[leaderboard.operator]" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="reset">Reset Schedule</label>
      </div>
      <input type="text" id="reset" placeholder="Not Set" [value]="leaderboard.reset_schedule" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="create_time">Create Time</label>
      </div>
      <input type="text" id="create_time" placeholder="Create Time" [value]="leaderboard.create_time" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
  </div>

  <div class="row add-border-single-row-bottom">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="reset">Prev Reset</label>
      </div>
      <input type="text" id="prev_reset" placeholder="Not Set" [value]="leaderboard.prev_reset" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="create_time">Next Reset</label>
      </div>
      <input type="text" id="next_reset" placeholder="Create Time" [value]="leaderboard.next_reset" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
  </div>
</div>

<div class="mt-4" *ngIf="leaderboard.tournament">
  <h6>Tournament</h6>
  <hr class="mb-4"/>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="title">Title</label>
      </div>
      <input type="text" id="title" placeholder="Title" [value]="leaderboard.title" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="description">Description</label>
      </div>
      <input type="text" id="description" placeholder="Description" [value]="leaderboard.description" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="category">Category</label>
      </div>
      <input type="text" id="category" placeholder="Not Set" [value]="leaderboard.category" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="max_num_score">Max Num Score</label>
      </div>
      <input type="text" id="max_num_score" placeholder="Not Set" [value]="leaderboard.max_num_score" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="size">Size</label>
      </div>
      <input type="text" id="size" placeholder="Not Set" [value]="leaderboard.size" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="max_size">Max Size</label>
      </div>
      <input type="text" id="max_size" placeholder="Not Set" [value]="leaderboard.max_size" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="duration">Duration</label>
      </div>
      <input type="text" id="duration" placeholder="Not Set" [value]="leaderboard.duration" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="join_required">Join Required</label>
      </div>
      <input type="text" id="join_required" placeholder="Not Set" [value]="leaderboard.join_required" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="start_time">Start Time</label>
      </div>
      <input type="text" id="start_time" placeholder="Not Set" [value]="leaderboard.start_time" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="end_time">End Time</label>
      </div>
      <input type="text" id="end_time" placeholder="Not Set" [value]="leaderboard.end_time" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="start_active">Start Active</label>
      </div>
      <input type="text" id="start_active" placeholder="Not Set" [value]="leaderboard.start_active" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="end_active">End Active</label>
      </div>
      <input type="text" id="end_active" placeholder="Not Set" [value]="leaderboard.end_active" class="form-control-plaintext form-control-sm my-2" disabled readonly>
    </div>
  </div>
</div>

<div class="card p-2 mb-3 jsoneditor" style="height: 400px">
  <div #editor style="height: 400px"></div>
</div>
