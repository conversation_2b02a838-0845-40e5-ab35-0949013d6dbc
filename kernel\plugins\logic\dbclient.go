package logic

import (
	"context"
	"errors"
	"fmt"
	"hash/fnv"
	"kernel/plugins/common"
	"kernel/plugins/models"
	"strings"
	"sync"
	"time"

	"github.com/gofrs/uuid/v5"
	json "github.com/json-iterator/go"
)

type DbAgentGlobalDataStruct struct {
	i         ServerModule
	agentPool *sync.Pool
}

func NewDbAgentGlobalDataStruct(i ServerModule) *DbAgentGlobalDataStruct {
	agent := &DbAgentGlobalDataStruct{
		i: i,
	}
	agent.agentPool = &sync.Pool{New: func() interface{} {
		return NewDbAgentObj(agent)
	}}
	return agent
}

func (g *DbAgentGlobalDataStruct) Create(ctx context.Context, model models.DBModel, options ...models.DBReqOption) *DbAgentObj {
	o := g.agentPool.Get().(*DbAgentObj)
	o.ctx = ctx
	req := o.req
	models.LoadDBReqOption(req, options...)
	req.Table = model.GetTable()
	req.RowsSecondName = model.GetSecondKeyName()
	req.QueryArgSql = model.GetQueryArgs()
	req.FlushType = model.GetFlushType()
	req.CacheLevel = model.GetCacheLevel()
	req.RowResultType = model.GetRowResultType()
	req.UniqueKeys = model.GetUniqueKeys()
	req.VersionName = model.GetVersionName()

	if o.reqTimeout == time.Duration(0) {
		o.reqTimeout = models.RPC_TIMEOUT_DEFAULT
	}
	if o.lockTimeout == time.Duration(0) {
		o.lockTimeout = models.RPC_TIMEOUT_DEFAULT
	}
	if o.req.TTL == 0 {
		o.req.TTL = 3600 // 默认1小时
	}
	if o.group == "" {
		o.group = models.NACOS_DEFAULT_GROUP // 使用默认组
	}
	return o
}

func (g *DbAgentGlobalDataStruct) Release(o *DbAgentObj) {
	o.UnlockAll()
	o.resp = nil
	o.err = nil
	o.ctxLockId = 0
	o.ctxLockOpt = 0
	o.dbagentHost = ""
	o.reqTimeout = 0
	o.lockTimeout = 0
	o.group = ""
	for k := range o.ckeys {
		delete(o.ckeys, k)
	}
	o.ctx = context.Background()
	o.req.Reset()
	g.agentPool.Put(o)
}

type dbAgentObjOptionFunc func(opts *DbAgentObj)

func loadDbAgentClientOption(lc *DbAgentObj, options ...dbAgentObjOptionFunc) *DbAgentObj {
	for _, option := range options {
		option(lc)
	}
	return lc
}

type DbAgentObj struct {
	ctx         context.Context
	req         *models.DBAgentReq
	resp        *models.DBAgentResp // 外部附加
	ctxLockId   uint64
	ctxLockOpt  models.LockOpt
	ckeys       map[string]lockKey
	dbagentHost string // 自定义数据库访问host
	reqTimeout  time.Duration
	lockTimeout time.Duration
	err         error
	group       string // 服务组

	g *DbAgentGlobalDataStruct
}

type lockKey struct {
	lockopt models.LockOpt
	dbhost  string
}

func NewDbAgentObj(g *DbAgentGlobalDataStruct, options ...dbAgentObjOptionFunc) *DbAgentObj {
	c := &DbAgentObj{
		g:     g,
		ckeys: make(map[string]lockKey),
		req:   models.NewDBAgentReq(),
		ctx:   context.Background(),
	}
	loadDbAgentClientOption(c, options...)
	return c
}

func (o *DbAgentObj) Error() error {
	return o.err
}

func (o *DbAgentObj) Result() (*models.DBAgentResp, error) {
	return o.resp, o.err
}

func (o *DbAgentObj) As(value interface{}) (err error) {
	if o.err != nil {
		return o.err
	}
	if o.resp == nil {
		return errors.New("resp is nil")
	}
	return o.resp.DataAs(value)
}

func WithReq(param *models.DBAgentReq) dbAgentObjOptionFunc {
	return func(opts *DbAgentObj) {
		opts.req = param
	}
}
func WithDBAgentHost(param string) dbAgentObjOptionFunc {
	return func(opts *DbAgentObj) {
		opts.dbagentHost = param
	}
}
func WithTimeout(param time.Duration) dbAgentObjOptionFunc {
	return func(opts *DbAgentObj) {
		opts.reqTimeout = param
	}
}
func WithLockId(param uint64) dbAgentObjOptionFunc {
	return func(opts *DbAgentObj) {
		opts.ctxLockId = param
	}
}
func WithLockOpt(param models.LockOpt) dbAgentObjOptionFunc {
	return func(opts *DbAgentObj) {
		opts.ctxLockOpt = param
	}
}
func WithAutoLock() dbAgentObjOptionFunc {
	fnvhash := fnv.New64a()
	fnvhash.Write([]byte(uuid.Must(uuid.NewV4()).String()))
	return func(opts *DbAgentObj) {
		opts.ctxLockId = fnvhash.Sum64()
		opts.ctxLockOpt = models.LOCKOPT_LOCK_WRITE
	}
}
func WithAutoRLock() dbAgentObjOptionFunc {
	fnvhash := fnv.New64a()
	fnvhash.Write([]byte(uuid.Must(uuid.NewV4()).String()))
	return func(opts *DbAgentObj) {
		opts.ctxLockId = fnvhash.Sum64()
		opts.ctxLockOpt = models.LOCKOPT_LOCK_READ
	}
}
func WithGroup(param string) dbAgentObjOptionFunc {
	return func(opts *DbAgentObj) {
		opts.group = param
	}
}

func (o *DbAgentObj) WithReqOption(options ...models.DBReqOption) *DbAgentObj {
	for _, option := range options {
		option(o.req)
	}
	return o
}

func (o *DbAgentObj) WithOption(options ...dbAgentObjOptionFunc) *DbAgentObj {
	for _, option := range options {
		option(o)
	}
	return o
}

func (o *DbAgentObj) setLockerToReq(lockkey string, dbhost string) {
	if o.ctxLockId > 0 {
		o.req.LockId = o.ctxLockId
		o.req.LockOpt = o.ctxLockOpt
		o.req.LockTimeout = int64(o.lockTimeout / time.Millisecond)
		o.req.LockKey = lockkey
		if o.req.LockKey == "" {
			o.req.LockKey = fmt.Sprintf("%d", o.ctxLockId)
		}
		o.ckeys[o.req.LockKey] = lockKey{
			lockopt: o.ctxLockOpt,
			dbhost:  dbhost,
		}
	}
}

func (o *DbAgentObj) UnlockAll() {
	if o.ctxLockId == 0 {
		return
	}
	send := o.g.i.GetSend()
	common := o.g.i.GetCommon()
	for ckey, lockkey := range o.ckeys {
		if lockkey.lockopt == models.LOCKOPT_LOCK_READ {
			o.req.LockOpt = models.LOCKOPT_UNLOCK_READ
		}
		if lockkey.lockopt == models.LOCKOPT_LOCK_WRITE {
			o.req.LockOpt = models.LOCKOPT_UNLOCK_WRITE
		}
		resp, err := send.RpcToNodeAddrObj(o.ctx, lockkey.dbhost, models.SERVER_NAME_DBAGENT, o.group, models.RPCID_DBAGENTSERVER_UNLOCK, o.req, map[string]string{}, o.reqTimeout)
		if err != nil {
			common.Logger.Error("unlock %s error %v", ckey, err)
			continue
		}
		result := &models.DBAgentResp{}
		if err = json.Unmarshal(resp, result); err != nil {
			common.Logger.Error("unlock %s error %v", ckey, err)
			continue
		}
		if result.Code != models.OK {
			common.Logger.Error("unlock %s error %v", ckey, err)
			continue
		}
		delete(o.ckeys, ckey)
	}
}

func (o *DbAgentObj) Unlock(ckey string) bool {
	if o.ctxLockId == 0 {
		return true
	}
	lockkey, ok := o.ckeys[ckey]
	if !ok {
		return true
	}
	if lockkey.lockopt == models.LOCKOPT_LOCK_READ {
		o.req.LockOpt = models.LOCKOPT_UNLOCK_READ
	}
	if lockkey.lockopt == models.LOCKOPT_LOCK_WRITE {
		o.req.LockOpt = models.LOCKOPT_UNLOCK_WRITE
	}
	send := o.g.i.GetSend()
	logger := o.g.i.GetLogger()
	resp, err := send.RpcToNodeAddrObj(o.ctx, lockkey.dbhost, models.SERVER_NAME_DBAGENT, o.group, models.RPCID_DBAGENTSERVER_UNLOCK, o.req, map[string]string{}, o.reqTimeout)
	if err != nil {
		logger.Error("unlock %s error %v", ckey, err)
		return false
	}
	result := &models.DBAgentResp{}
	if err = json.Unmarshal(resp, result); err != nil {
		return false
	}
	if result.Code != models.OK {
		return false
	}
	delete(o.ckeys, ckey)
	return true
}

// 设置req.Values设置返回(单行结果：指定字段名，多行结果：指定行SecondKey对应的值)
func (o *DbAgentObj) Query(ctx context.Context, fields ...string) *DbAgentObj {
	o.err = nil
	if o.req == nil || len(o.req.Keys) == 0 {
		o.err = errors.New("req or key is nil")
		return o
	}
	var (
		resp []byte
		err  error
	)
	if len(fields) > 0 {
		// 将fields附加到req.Values
		for _, field := range fields {
			o.req.Values[field] = struct{}{}
		}
	}
	send := o.g.i.GetSend()
	logger := o.g.i.GetLogger()
	if o.dbagentHost == "" {
		vals := []string{o.req.Table}
		for _, v := range o.req.Keys {
			vals = append(vals, fmt.Sprintf("%v", v))
		}
		lockkey := strings.Join(vals, ":")

		hashkey := ""
		if o.req.Table == "users" && len(o.req.Keys) == 1 && (o.req.Keys["id"] != nil || o.req.Keys["uin"] != nil) {
			// 取最后一位作为hashkey
			if o.req.Keys["id"] != nil {
				tmpvalue := common.Interface2String(o.req.Keys["id"])
				hashkey = tmpvalue[len(tmpvalue)-1:]
			} else {
				tmpvalue := common.Interface2String(o.req.Keys["uin"])
				hashkey = tmpvalue[len(tmpvalue)-1:]
			}
		} else {
			hashkey = lockkey
		}
		var nodeconfig *models.NodeConfig
		nodeconfig, err = o.g.i.GetCommon().WatchNodeList.AllocServiceConfig(models.SERVER_NAME_DBAGENT, o.group, hashkey)
		if err != nil {
			o.err = err
			return o
		}
		o.setLockerToReq(lockkey, nodeconfig.Host)
		resp, err = send.RpcToNodeAddrObj(ctx, nodeconfig.Host, models.SERVER_NAME_DBAGENT, o.group, models.RPCID_DBAGENTSERVER_QUERY, o.req, map[string]string{}, o.reqTimeout)
	} else {
		o.setLockerToReq("", o.dbagentHost)
		resp, err = send.RpcToNodeAddrObj(ctx, o.dbagentHost, models.SERVER_NAME_DBAGENT, o.group, models.RPCID_DBAGENTSERVER_QUERY, o.req, map[string]string{}, o.reqTimeout)
	}
	//common.Logger.Debug("query resp %s", string(resp))
	if err != nil {
		logger.Debug("query %v", err)
		o.err = err
		return o
	}
	result := &models.DBAgentResp{}
	if err = result.Unmarshal(resp); err != nil {
		logger.Debug("query %v", err)
		o.err = err
		return o
	}
	if result.Code != models.OK {
		logger.Debug("query %v", result.Msg)
		o.err = fmt.Errorf("errcode:%d %s", result.Code, result.Msg)
		return o
	}
	o.resp = result
	for k := range o.req.Values {
		delete(o.req.Values, k)
	}
	return o
}

func (o *DbAgentObj) Insert(ctx context.Context) *DbAgentObj {
	o.err = nil
	if o.req == nil || len(o.req.Keys) == 0 {
		o.err = errors.New("req or key is nil")
		return o
	}
	if o.req.RowResultType == models.ROWRESULT_TYPE_MULTI {
		// 多行结构，检查二级键名称是否存在
		if o.req.RowsSecondName == "" {
			o.err = errors.New("rowssecondname is nil")
			return o
		}
		if _, ok := o.req.Values[o.req.RowsSecondName]; !ok {
			o.err = errors.New("rowssecondname value is nil")
			return o
		}
	}
	// 将keys附加到values
	for k, v := range o.req.Keys {
		if _, ok := o.req.Values[k]; !ok {
			o.req.Values[k] = v
		}
	}

	var (
		resp []byte
		err  error
	)
	send := o.g.i.GetSend()
	logger := o.g.i.GetLogger()
	if o.dbagentHost == "" {
		vals := []string{o.req.Table}
		for _, v := range o.req.Keys {
			vals = append(vals, fmt.Sprintf("%v", v))
		}
		lockkey := strings.Join(vals, ":")

		hashkey := ""
		if o.req.Table == "users" && len(o.req.Keys) == 1 && (o.req.Keys["id"] != nil || o.req.Keys["uin"] != nil) {
			// 取最后一位作为hashkey
			if o.req.Keys["id"] != nil {
				tmpvalue := common.Interface2String(o.req.Keys["id"])
				hashkey = tmpvalue[len(tmpvalue)-1:]
			} else {
				tmpvalue := common.Interface2String(o.req.Keys["uin"])
				hashkey = tmpvalue[len(tmpvalue)-1:]
			}
		} else {
			hashkey = lockkey
		}
		var nodeconfig *models.NodeConfig
		nodeconfig, err = o.g.i.GetCommon().WatchNodeList.AllocServiceConfig(models.SERVER_NAME_DBAGENT, o.group, hashkey)
		if err != nil {
			o.err = err
			return o
		}
		o.setLockerToReq(lockkey, nodeconfig.Host)
		resp, err = send.RpcToNodeAddrObj(ctx, nodeconfig.Host, models.SERVER_NAME_DBAGENT, o.group, models.RPCID_DBAGENTSERVER_INSERT, o.req, map[string]string{}, o.reqTimeout)
	} else {
		o.setLockerToReq("", o.dbagentHost)
		resp, err = send.RpcToNodeAddrObj(ctx, o.dbagentHost, models.SERVER_NAME_DBAGENT, o.group, models.RPCID_DBAGENTSERVER_INSERT, o.req, map[string]string{}, o.reqTimeout)
	}
	//common.Logger.Debug("insert resp %s", string(resp))
	if err != nil {
		logger.Debug("insert %v", err)
		o.err = err
		return o
	}
	result := &models.DBAgentResp{}
	if err = result.Unmarshal(resp); err != nil {
		logger.Debug("insert %v", err)
		o.err = err
		return o
	}
	if result.Code != models.OK {
		logger.Debug("insert %v", result.Msg)
		o.err = fmt.Errorf("errcode:%d %s", result.Code, result.Msg)
		return o
	}
	o.resp = result
	for k := range o.req.Values {
		delete(o.req.Values, k)
	}
	return o
}

func (o *DbAgentObj) Update(ctx context.Context) *DbAgentObj {
	o.err = nil
	if o.req == nil || len(o.req.Keys) == 0 {
		o.err = errors.New("req or key is nil")
		return o
	}
	if o.req.RowResultType == models.ROWRESULT_TYPE_MULTI {
		// 多行结构，检查二级键名称是否存在
		if o.req.RowsSecondName == "" {
			o.err = errors.New("rowssecondname is nil")
			return o
		}
		if _, ok := o.req.Values[o.req.RowsSecondName]; !ok {
			o.err = errors.New("rowssecondname value is nil")
			return o
		}
	}
	// 将keys附加到values
	for k, v := range o.req.Keys {
		if _, ok := o.req.Values[k]; !ok {
			o.req.Values[k] = v
		}
	}

	var (
		resp []byte
		err  error
	)
	send := o.g.i.GetSend()
	logger := o.g.i.GetLogger()
	if o.dbagentHost == "" {
		vals := []string{o.req.Table}
		for _, v := range o.req.Keys {
			vals = append(vals, fmt.Sprintf("%v", v))
		}
		lockkey := strings.Join(vals, ":")

		hashkey := ""
		if o.req.Table == "users" && len(o.req.Keys) == 1 && (o.req.Keys["id"] != nil || o.req.Keys["uin"] != nil) {
			// 取最后一位作为hashkey
			if o.req.Keys["id"] != nil {
				tmpvalue := common.Interface2String(o.req.Keys["id"])
				hashkey = tmpvalue[len(tmpvalue)-1:]
			} else {
				tmpvalue := common.Interface2String(o.req.Keys["uin"])
				hashkey = tmpvalue[len(tmpvalue)-1:]
			}
		} else {
			hashkey = lockkey
		}
		var nodeconfig *models.NodeConfig
		nodeconfig, err = o.g.i.GetCommon().WatchNodeList.AllocServiceConfig(models.SERVER_NAME_DBAGENT, o.group, hashkey)
		if err != nil {
			o.err = err
			return o
		}
		o.setLockerToReq(lockkey, nodeconfig.Host)
		resp, err = send.RpcToNodeAddrObj(ctx, nodeconfig.Host, models.SERVER_NAME_DBAGENT, o.group, models.RPCID_DBAGENTSERVER_UPDATE, o.req, map[string]string{}, o.reqTimeout)
	} else {
		o.setLockerToReq("", o.dbagentHost)
		resp, err = send.RpcToNodeAddrObj(ctx, o.dbagentHost, models.SERVER_NAME_DBAGENT, o.group, models.RPCID_DBAGENTSERVER_UPDATE, o.req, map[string]string{}, o.reqTimeout)
	}
	//common.Logger.Debug("update resp %s", string(resp))
	if err != nil {
		logger.Debug("update %v", err)
		o.err = err
		return o
	}
	result := &models.DBAgentResp{}
	if err = result.Unmarshal(resp); err != nil {
		logger.Debug("update %v", err)
		o.err = err
		return o
	}
	if result.Code != models.OK {
		logger.Debug("update %v", result.Msg)
		o.err = fmt.Errorf("errcode:%d %s", result.Code, result.Msg)
		return o
	}
	o.resp = result
	for k := range o.req.Values {
		delete(o.req.Values, k)
	}
	return o
}

func (o *DbAgentObj) Delete(ctx context.Context) *DbAgentObj {
	o.err = nil
	if o.req == nil || len(o.req.Keys) == 0 {
		o.err = errors.New("req or key is nil")
		return o
	}
	if o.req.RowResultType == models.ROWRESULT_TYPE_MULTI {
		// 多行结构，检查二级键名称是否存在
		if o.req.RowsSecondName == "" {
			o.err = errors.New("rowssecondname is nil")
			return o
		}
		if _, ok := o.req.Values[o.req.RowsSecondName]; !ok {
			o.err = errors.New("rowssecondname value is nil")
			return o
		}
	}
	// 将keys附加到values
	for k, v := range o.req.Keys {
		if _, ok := o.req.Values[k]; !ok {
			o.req.Values[k] = v
		}
	}

	var (
		resp []byte
		err  error
	)
	send := o.g.i.GetSend()
	if o.dbagentHost == "" {
		vals := []string{o.req.Table}
		for _, v := range o.req.Keys {
			vals = append(vals, fmt.Sprintf("%v", v))
		}
		lockkey := strings.Join(vals, ":")

		hashkey := ""
		if o.req.Table == "users" && len(o.req.Keys) == 1 && (o.req.Keys["id"] != nil || o.req.Keys["uin"] != nil) {
			// 取最后一位作为hashkey
			if o.req.Keys["id"] != nil {
				tmpvalue := common.Interface2String(o.req.Keys["id"])
				hashkey = tmpvalue[len(tmpvalue)-1:]
			} else {
				tmpvalue := common.Interface2String(o.req.Keys["uin"])
				hashkey = tmpvalue[len(tmpvalue)-1:]
			}
		} else {
			hashkey = lockkey
		}
		var nodeconfig *models.NodeConfig
		nodeconfig, err = o.g.i.GetCommon().WatchNodeList.AllocServiceConfig(models.SERVER_NAME_DBAGENT, o.group, hashkey)
		if err != nil {
			o.err = err
			return o
		}
		o.setLockerToReq(lockkey, nodeconfig.Host)
		resp, err = send.RpcToNodeAddrObj(ctx, nodeconfig.Host, models.SERVER_NAME_DBAGENT, o.group, models.RPCID_DBAGENTSERVER_DELETE, o.req, map[string]string{}, o.reqTimeout)
	} else {
		o.setLockerToReq("", o.dbagentHost)
		resp, err = send.RpcToNodeAddrObj(ctx, o.dbagentHost, models.SERVER_NAME_DBAGENT, o.group, models.RPCID_DBAGENTSERVER_DELETE, o.req, map[string]string{}, o.reqTimeout)
	}
	//common.Logger.Debug("delete resp %s", string(resp))
	if err != nil {
		o.err = err
		return o
	}
	result := &models.DBAgentResp{}
	if err = result.Unmarshal(resp); err != nil {
		o.err = err
		return o
	}
	if result.Code != models.OK {
		o.err = fmt.Errorf("errcode:%d %s", result.Code, result.Msg)
		return o
	}
	o.resp = result
	for k := range o.req.Values {
		delete(o.req.Values, k)
	}
	return o
}

func (o *DbAgentObj) RawExec(ctx context.Context, query string, args ...any) *DbAgentObj {
	o.err = nil
	if o.req == nil || query == "" {
		o.err = errors.New("req or query is nil")
		return o
	}
	var (
		resp []byte
		err  error
	)

	// 根据query, args生产sql语句
	sql := query
	if len(args) > 0 {
		sql = fmt.Sprintf(query, args...)
	}
	o.WithReqOption(models.WithQueryArgSql(sql))
	send := o.g.i.GetSend()
	if o.dbagentHost == "" {
		resp, err = send.RpcObj(ctx, models.SERVER_NAME_DBAGENT, o.group, models.RPCID_DBAGENTSERVER_RAWEXEC, o.req, map[string]string{}, o.reqTimeout)
	} else {
		resp, err = send.RpcToNodeAddrObj(ctx, o.dbagentHost, models.SERVER_NAME_DBAGENT, o.group, models.RPCID_DBAGENTSERVER_RAWEXEC, o.req, map[string]string{}, o.reqTimeout)
	}
	if err != nil {
		o.err = err
		return o
	}
	result := &models.DBAgentResp{}
	if err = result.Unmarshal(resp); err != nil {
		o.err = err
		return o
	}
	if result.Code != models.OK {
		o.err = fmt.Errorf("errcode:%d %s", result.Code, result.Msg)
		return o
	}
	o.resp = result
	for k := range o.req.Values {
		delete(o.req.Values, k)
	}
	return o
}

func (o *DbAgentObj) RawQuery(ctx context.Context, query string, args ...any) *DbAgentObj {
	o.err = nil
	if o.req == nil || query == "" {
		o.err = errors.New("req or query is nil")
		return o
	}
	if !o.req.NotOptCache && len(o.req.Keys) == 0 {
		o.err = errors.New("keys is nil")
		return o
	}
	var (
		resp []byte
		err  error
	)

	// 根据query, args生产sql语句
	sql := query
	if len(args) > 0 {
		sql = fmt.Sprintf(query, args...)
	}
	o.WithReqOption(models.WithQueryArgSql(sql))
	send := o.g.i.GetSend()
	if o.dbagentHost == "" {
		vals := []string{o.req.Table}
		for _, v := range o.req.Keys {
			vals = append(vals, fmt.Sprintf("%v", v))
		}
		lockkey := strings.Join(vals, ":")

		hashkey := ""
		if o.req.Table == "users" && len(o.req.Keys) == 1 && (o.req.Keys["id"] != nil || o.req.Keys["uin"] != nil) {
			// 取最后一位作为hashkey
			if o.req.Keys["id"] != nil {
				tmpvalue := common.Interface2String(o.req.Keys["id"])
				hashkey = tmpvalue[len(tmpvalue)-1:]
			} else {
				tmpvalue := common.Interface2String(o.req.Keys["uin"])
				hashkey = tmpvalue[len(tmpvalue)-1:]
			}
		} else {
			hashkey = lockkey
		}
		if hashkey != "" {
			resp, err = send.RpcHashObj(ctx, hashkey, models.SERVER_NAME_DBAGENT, o.group, models.RPCID_DBAGENTSERVER_RAWQUERY, o.req, map[string]string{}, o.reqTimeout)
		} else {
			resp, err = send.RpcObj(ctx, models.SERVER_NAME_DBAGENT, o.group, models.RPCID_DBAGENTSERVER_RAWQUERY, o.req, map[string]string{}, o.reqTimeout)
		}
	} else {
		resp, err = send.RpcToNodeAddrObj(ctx, o.dbagentHost, models.SERVER_NAME_DBAGENT, o.group, models.RPCID_DBAGENTSERVER_RAWQUERY, o.req, map[string]string{}, o.reqTimeout)
	}
	if err != nil {
		o.err = err
		return o
	}
	result := &models.DBAgentResp{}
	if err = result.Unmarshal(resp); err != nil {
		o.err = err
		return o
	}
	if result.Code != models.OK {
		o.err = fmt.Errorf("errcode:%d %s", result.Code, result.Msg)
		return o
	}
	o.resp = result
	for k := range o.req.Values {
		delete(o.req.Values, k)
	}
	return o
}
