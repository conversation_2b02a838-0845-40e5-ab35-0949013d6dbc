// Copyright 2018 The Nakama Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

//*
// The Nakama server RPC protocol for games and apps.

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.28.3
// source: apigrpc.proto

package apigrpc

import (
	context "context"
	api "kernel/kernel-common/api"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Nakama_AddFriends_FullMethodName                        = "/nakama.api.Nakama/AddFriends"
	Nakama_AddGroupUsers_FullMethodName                     = "/nakama.api.Nakama/AddGroupUsers"
	Nakama_SessionRefresh_FullMethodName                    = "/nakama.api.Nakama/SessionRefresh"
	Nakama_SessionLogout_FullMethodName                     = "/nakama.api.Nakama/SessionLogout"
	Nakama_AuthenticateApple_FullMethodName                 = "/nakama.api.Nakama/AuthenticateApple"
	Nakama_AuthenticateCustom_FullMethodName                = "/nakama.api.Nakama/AuthenticateCustom"
	Nakama_AuthenticateDevice_FullMethodName                = "/nakama.api.Nakama/AuthenticateDevice"
	Nakama_AuthenticateEmail_FullMethodName                 = "/nakama.api.Nakama/AuthenticateEmail"
	Nakama_AuthenticateFacebook_FullMethodName              = "/nakama.api.Nakama/AuthenticateFacebook"
	Nakama_AuthenticateFacebookInstantGame_FullMethodName   = "/nakama.api.Nakama/AuthenticateFacebookInstantGame"
	Nakama_AuthenticateGameCenter_FullMethodName            = "/nakama.api.Nakama/AuthenticateGameCenter"
	Nakama_AuthenticateGoogle_FullMethodName                = "/nakama.api.Nakama/AuthenticateGoogle"
	Nakama_AuthenticateSteam_FullMethodName                 = "/nakama.api.Nakama/AuthenticateSteam"
	Nakama_BanGroupUsers_FullMethodName                     = "/nakama.api.Nakama/BanGroupUsers"
	Nakama_BlockFriends_FullMethodName                      = "/nakama.api.Nakama/BlockFriends"
	Nakama_CreateGroup_FullMethodName                       = "/nakama.api.Nakama/CreateGroup"
	Nakama_DeleteAccount_FullMethodName                     = "/nakama.api.Nakama/DeleteAccount"
	Nakama_DeleteFriends_FullMethodName                     = "/nakama.api.Nakama/DeleteFriends"
	Nakama_DeleteGroup_FullMethodName                       = "/nakama.api.Nakama/DeleteGroup"
	Nakama_DeleteLeaderboardRecord_FullMethodName           = "/nakama.api.Nakama/DeleteLeaderboardRecord"
	Nakama_DeleteNotifications_FullMethodName               = "/nakama.api.Nakama/DeleteNotifications"
	Nakama_DeleteTournamentRecord_FullMethodName            = "/nakama.api.Nakama/DeleteTournamentRecord"
	Nakama_DeleteStorageObjects_FullMethodName              = "/nakama.api.Nakama/DeleteStorageObjects"
	Nakama_Event_FullMethodName                             = "/nakama.api.Nakama/Event"
	Nakama_GetAccount_FullMethodName                        = "/nakama.api.Nakama/GetAccount"
	Nakama_GetUsers_FullMethodName                          = "/nakama.api.Nakama/GetUsers"
	Nakama_GetSubscription_FullMethodName                   = "/nakama.api.Nakama/GetSubscription"
	Nakama_GetMatchmakerStats_FullMethodName                = "/nakama.api.Nakama/GetMatchmakerStats"
	Nakama_Healthcheck_FullMethodName                       = "/nakama.api.Nakama/Healthcheck"
	Nakama_ImportFacebookFriends_FullMethodName             = "/nakama.api.Nakama/ImportFacebookFriends"
	Nakama_ImportSteamFriends_FullMethodName                = "/nakama.api.Nakama/ImportSteamFriends"
	Nakama_JoinGroup_FullMethodName                         = "/nakama.api.Nakama/JoinGroup"
	Nakama_JoinTournament_FullMethodName                    = "/nakama.api.Nakama/JoinTournament"
	Nakama_KickGroupUsers_FullMethodName                    = "/nakama.api.Nakama/KickGroupUsers"
	Nakama_LeaveGroup_FullMethodName                        = "/nakama.api.Nakama/LeaveGroup"
	Nakama_LinkApple_FullMethodName                         = "/nakama.api.Nakama/LinkApple"
	Nakama_LinkCustom_FullMethodName                        = "/nakama.api.Nakama/LinkCustom"
	Nakama_LinkDevice_FullMethodName                        = "/nakama.api.Nakama/LinkDevice"
	Nakama_LinkEmail_FullMethodName                         = "/nakama.api.Nakama/LinkEmail"
	Nakama_LinkFacebook_FullMethodName                      = "/nakama.api.Nakama/LinkFacebook"
	Nakama_LinkFacebookInstantGame_FullMethodName           = "/nakama.api.Nakama/LinkFacebookInstantGame"
	Nakama_LinkGameCenter_FullMethodName                    = "/nakama.api.Nakama/LinkGameCenter"
	Nakama_LinkGoogle_FullMethodName                        = "/nakama.api.Nakama/LinkGoogle"
	Nakama_LinkSteam_FullMethodName                         = "/nakama.api.Nakama/LinkSteam"
	Nakama_ListChannelMessages_FullMethodName               = "/nakama.api.Nakama/ListChannelMessages"
	Nakama_ListFriends_FullMethodName                       = "/nakama.api.Nakama/ListFriends"
	Nakama_ListFriendsOfFriends_FullMethodName              = "/nakama.api.Nakama/ListFriendsOfFriends"
	Nakama_ListGroups_FullMethodName                        = "/nakama.api.Nakama/ListGroups"
	Nakama_ListGroupUsers_FullMethodName                    = "/nakama.api.Nakama/ListGroupUsers"
	Nakama_ListLeaderboardRecords_FullMethodName            = "/nakama.api.Nakama/ListLeaderboardRecords"
	Nakama_ListLeaderboardRecordsAroundOwner_FullMethodName = "/nakama.api.Nakama/ListLeaderboardRecordsAroundOwner"
	Nakama_ListMatches_FullMethodName                       = "/nakama.api.Nakama/ListMatches"
	Nakama_ListNotifications_FullMethodName                 = "/nakama.api.Nakama/ListNotifications"
	Nakama_ListStorageObjects_FullMethodName                = "/nakama.api.Nakama/ListStorageObjects"
	Nakama_ListSubscriptions_FullMethodName                 = "/nakama.api.Nakama/ListSubscriptions"
	Nakama_ListTournaments_FullMethodName                   = "/nakama.api.Nakama/ListTournaments"
	Nakama_ListTournamentRecords_FullMethodName             = "/nakama.api.Nakama/ListTournamentRecords"
	Nakama_ListTournamentRecordsAroundOwner_FullMethodName  = "/nakama.api.Nakama/ListTournamentRecordsAroundOwner"
	Nakama_ListUserGroups_FullMethodName                    = "/nakama.api.Nakama/ListUserGroups"
	Nakama_PromoteGroupUsers_FullMethodName                 = "/nakama.api.Nakama/PromoteGroupUsers"
	Nakama_DemoteGroupUsers_FullMethodName                  = "/nakama.api.Nakama/DemoteGroupUsers"
	Nakama_ReadStorageObjects_FullMethodName                = "/nakama.api.Nakama/ReadStorageObjects"
	Nakama_RpcFunc_FullMethodName                           = "/nakama.api.Nakama/RpcFunc"
	Nakama_UnlinkApple_FullMethodName                       = "/nakama.api.Nakama/UnlinkApple"
	Nakama_UnlinkCustom_FullMethodName                      = "/nakama.api.Nakama/UnlinkCustom"
	Nakama_UnlinkDevice_FullMethodName                      = "/nakama.api.Nakama/UnlinkDevice"
	Nakama_UnlinkEmail_FullMethodName                       = "/nakama.api.Nakama/UnlinkEmail"
	Nakama_UnlinkFacebook_FullMethodName                    = "/nakama.api.Nakama/UnlinkFacebook"
	Nakama_UnlinkFacebookInstantGame_FullMethodName         = "/nakama.api.Nakama/UnlinkFacebookInstantGame"
	Nakama_UnlinkGameCenter_FullMethodName                  = "/nakama.api.Nakama/UnlinkGameCenter"
	Nakama_UnlinkGoogle_FullMethodName                      = "/nakama.api.Nakama/UnlinkGoogle"
	Nakama_UnlinkSteam_FullMethodName                       = "/nakama.api.Nakama/UnlinkSteam"
	Nakama_UpdateAccount_FullMethodName                     = "/nakama.api.Nakama/UpdateAccount"
	Nakama_UpdateGroup_FullMethodName                       = "/nakama.api.Nakama/UpdateGroup"
	Nakama_ValidatePurchaseApple_FullMethodName             = "/nakama.api.Nakama/ValidatePurchaseApple"
	Nakama_ValidateSubscriptionApple_FullMethodName         = "/nakama.api.Nakama/ValidateSubscriptionApple"
	Nakama_ValidatePurchaseGoogle_FullMethodName            = "/nakama.api.Nakama/ValidatePurchaseGoogle"
	Nakama_ValidateSubscriptionGoogle_FullMethodName        = "/nakama.api.Nakama/ValidateSubscriptionGoogle"
	Nakama_ValidatePurchaseHuawei_FullMethodName            = "/nakama.api.Nakama/ValidatePurchaseHuawei"
	Nakama_ValidatePurchaseFacebookInstant_FullMethodName   = "/nakama.api.Nakama/ValidatePurchaseFacebookInstant"
	Nakama_WriteLeaderboardRecord_FullMethodName            = "/nakama.api.Nakama/WriteLeaderboardRecord"
	Nakama_WriteStorageObjects_FullMethodName               = "/nakama.api.Nakama/WriteStorageObjects"
	Nakama_WriteTournamentRecord_FullMethodName             = "/nakama.api.Nakama/WriteTournamentRecord"
)

// NakamaClient is the client API for Nakama service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NakamaClient interface {
	// Add friends by ID or username to a user's account.
	AddFriends(ctx context.Context, in *api.AddFriendsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Add users to a group.
	AddGroupUsers(ctx context.Context, in *api.AddGroupUsersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Refresh a user's session using a refresh token retrieved from a previous authentication request.
	SessionRefresh(ctx context.Context, in *api.SessionRefreshRequest, opts ...grpc.CallOption) (*api.Session, error)
	// Log out a session, invalidate a refresh token, or log out all sessions/refresh tokens for a user.
	SessionLogout(ctx context.Context, in *api.SessionLogoutRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Authenticate a user with an Apple ID against the server.
	AuthenticateApple(ctx context.Context, in *api.AuthenticateAppleRequest, opts ...grpc.CallOption) (*api.Session, error)
	// Authenticate a user with a custom id against the server.
	AuthenticateCustom(ctx context.Context, in *api.AuthenticateCustomRequest, opts ...grpc.CallOption) (*api.Session, error)
	// Authenticate a user with a device id against the server.
	AuthenticateDevice(ctx context.Context, in *api.AuthenticateDeviceRequest, opts ...grpc.CallOption) (*api.Session, error)
	// Authenticate a user with an email+password against the server.
	AuthenticateEmail(ctx context.Context, in *api.AuthenticateEmailRequest, opts ...grpc.CallOption) (*api.Session, error)
	// Authenticate a user with a Facebook OAuth token against the server.
	AuthenticateFacebook(ctx context.Context, in *api.AuthenticateFacebookRequest, opts ...grpc.CallOption) (*api.Session, error)
	// Authenticate a user with a Facebook Instant Game token against the server.
	AuthenticateFacebookInstantGame(ctx context.Context, in *api.AuthenticateFacebookInstantGameRequest, opts ...grpc.CallOption) (*api.Session, error)
	// Authenticate a user with Apple's GameCenter against the server.
	AuthenticateGameCenter(ctx context.Context, in *api.AuthenticateGameCenterRequest, opts ...grpc.CallOption) (*api.Session, error)
	// Authenticate a user with Google against the server.
	AuthenticateGoogle(ctx context.Context, in *api.AuthenticateGoogleRequest, opts ...grpc.CallOption) (*api.Session, error)
	// Authenticate a user with Steam against the server.
	AuthenticateSteam(ctx context.Context, in *api.AuthenticateSteamRequest, opts ...grpc.CallOption) (*api.Session, error)
	// Ban a set of users from a group.
	BanGroupUsers(ctx context.Context, in *api.BanGroupUsersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Block one or more users by ID or username.
	BlockFriends(ctx context.Context, in *api.BlockFriendsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Create a new group with the current user as the owner.
	CreateGroup(ctx context.Context, in *api.CreateGroupRequest, opts ...grpc.CallOption) (*api.Group, error)
	// Delete the current user's account.
	DeleteAccount(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete one or more users by ID or username.
	DeleteFriends(ctx context.Context, in *api.DeleteFriendsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete a group by ID.
	DeleteGroup(ctx context.Context, in *api.DeleteGroupRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete a leaderboard record.
	DeleteLeaderboardRecord(ctx context.Context, in *api.DeleteLeaderboardRecordRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete one or more notifications for the current user.
	DeleteNotifications(ctx context.Context, in *api.DeleteNotificationsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete a tournament record.
	DeleteTournamentRecord(ctx context.Context, in *api.DeleteTournamentRecordRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete one or more objects by ID or username.
	DeleteStorageObjects(ctx context.Context, in *api.DeleteStorageObjectsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Submit an event for processing in the server's registered runtime custom events handler.
	Event(ctx context.Context, in *api.Event, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Fetch the current user's account.
	GetAccount(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*api.Account, error)
	// Fetch zero or more users by ID and/or username.
	GetUsers(ctx context.Context, in *api.GetUsersRequest, opts ...grpc.CallOption) (*api.Users, error)
	// Get subscription by product id.
	GetSubscription(ctx context.Context, in *api.GetSubscriptionRequest, opts ...grpc.CallOption) (*api.ValidatedSubscription, error)
	// Get matchmaker stats.
	GetMatchmakerStats(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*api.MatchmakerStats, error)
	// A healthcheck which load balancers can use to check the service.
	Healthcheck(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Import Facebook friends and add them to a user's account.
	ImportFacebookFriends(ctx context.Context, in *api.ImportFacebookFriendsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Import Steam friends and add them to a user's account.
	ImportSteamFriends(ctx context.Context, in *api.ImportSteamFriendsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Immediately join an open group, or request to join a closed one.
	JoinGroup(ctx context.Context, in *api.JoinGroupRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Attempt to join an open and running tournament.
	JoinTournament(ctx context.Context, in *api.JoinTournamentRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Kick a set of users from a group.
	KickGroupUsers(ctx context.Context, in *api.KickGroupUsersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Leave a group the user is a member of.
	LeaveGroup(ctx context.Context, in *api.LeaveGroupRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Add an Apple ID to the social profiles on the current user's account.
	LinkApple(ctx context.Context, in *api.AccountApple, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Add a custom ID to the social profiles on the current user's account.
	LinkCustom(ctx context.Context, in *api.AccountCustom, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Add a device ID to the social profiles on the current user's account.
	LinkDevice(ctx context.Context, in *api.AccountDevice, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Add an email+password to the social profiles on the current user's account.
	LinkEmail(ctx context.Context, in *api.AccountEmail, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Add Facebook to the social profiles on the current user's account.
	LinkFacebook(ctx context.Context, in *api.LinkFacebookRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Add Facebook Instant Game to the social profiles on the current user's account.
	LinkFacebookInstantGame(ctx context.Context, in *api.AccountFacebookInstantGame, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Add Apple's GameCenter to the social profiles on the current user's account.
	LinkGameCenter(ctx context.Context, in *api.AccountGameCenter, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Add Google to the social profiles on the current user's account.
	LinkGoogle(ctx context.Context, in *api.AccountGoogle, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Add Steam to the social profiles on the current user's account.
	LinkSteam(ctx context.Context, in *api.LinkSteamRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// List a channel's message history.
	ListChannelMessages(ctx context.Context, in *api.ListChannelMessagesRequest, opts ...grpc.CallOption) (*api.ChannelMessageList, error)
	// List all friends for the current user.
	ListFriends(ctx context.Context, in *api.ListFriendsRequest, opts ...grpc.CallOption) (*api.FriendList, error)
	// List friends of friends for the current user.
	ListFriendsOfFriends(ctx context.Context, in *api.ListFriendsOfFriendsRequest, opts ...grpc.CallOption) (*api.FriendsOfFriendsList, error)
	// List groups based on given filters.
	ListGroups(ctx context.Context, in *api.ListGroupsRequest, opts ...grpc.CallOption) (*api.GroupList, error)
	// List all users that are part of a group.
	ListGroupUsers(ctx context.Context, in *api.ListGroupUsersRequest, opts ...grpc.CallOption) (*api.GroupUserList, error)
	// List leaderboard records.
	ListLeaderboardRecords(ctx context.Context, in *api.ListLeaderboardRecordsRequest, opts ...grpc.CallOption) (*api.LeaderboardRecordList, error)
	// List leaderboard records that belong to a user.
	ListLeaderboardRecordsAroundOwner(ctx context.Context, in *api.ListLeaderboardRecordsAroundOwnerRequest, opts ...grpc.CallOption) (*api.LeaderboardRecordList, error)
	// Fetch list of running matches.
	ListMatches(ctx context.Context, in *api.ListMatchesRequest, opts ...grpc.CallOption) (*api.MatchList, error)
	// Fetch list of notifications.
	ListNotifications(ctx context.Context, in *api.ListNotificationsRequest, opts ...grpc.CallOption) (*api.NotificationList, error)
	// List publicly readable storage objects in a given collection.
	ListStorageObjects(ctx context.Context, in *api.ListStorageObjectsRequest, opts ...grpc.CallOption) (*api.StorageObjectList, error)
	// List user's subscriptions.
	ListSubscriptions(ctx context.Context, in *api.ListSubscriptionsRequest, opts ...grpc.CallOption) (*api.SubscriptionList, error)
	// List current or upcoming tournaments.
	ListTournaments(ctx context.Context, in *api.ListTournamentsRequest, opts ...grpc.CallOption) (*api.TournamentList, error)
	// List tournament records.
	ListTournamentRecords(ctx context.Context, in *api.ListTournamentRecordsRequest, opts ...grpc.CallOption) (*api.TournamentRecordList, error)
	// List tournament records for a given owner.
	ListTournamentRecordsAroundOwner(ctx context.Context, in *api.ListTournamentRecordsAroundOwnerRequest, opts ...grpc.CallOption) (*api.TournamentRecordList, error)
	// List groups the current user belongs to.
	ListUserGroups(ctx context.Context, in *api.ListUserGroupsRequest, opts ...grpc.CallOption) (*api.UserGroupList, error)
	// Promote a set of users in a group to the next role up.
	PromoteGroupUsers(ctx context.Context, in *api.PromoteGroupUsersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Demote a set of users in a group to the next role down.
	DemoteGroupUsers(ctx context.Context, in *api.DemoteGroupUsersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Get storage objects.
	ReadStorageObjects(ctx context.Context, in *api.ReadStorageObjectsRequest, opts ...grpc.CallOption) (*api.StorageObjects, error)
	// Execute a Lua function on the server.
	RpcFunc(ctx context.Context, in *api.Rpc, opts ...grpc.CallOption) (*api.Rpc, error)
	// Remove the Apple ID from the social profiles on the current user's account.
	UnlinkApple(ctx context.Context, in *api.AccountApple, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Remove the custom ID from the social profiles on the current user's account.
	UnlinkCustom(ctx context.Context, in *api.AccountCustom, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Remove the device ID from the social profiles on the current user's account.
	UnlinkDevice(ctx context.Context, in *api.AccountDevice, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Remove the email+password from the social profiles on the current user's account.
	UnlinkEmail(ctx context.Context, in *api.AccountEmail, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Remove Facebook from the social profiles on the current user's account.
	UnlinkFacebook(ctx context.Context, in *api.AccountFacebook, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Remove Facebook Instant Game profile from the social profiles on the current user's account.
	UnlinkFacebookInstantGame(ctx context.Context, in *api.AccountFacebookInstantGame, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Remove Apple's GameCenter from the social profiles on the current user's account.
	UnlinkGameCenter(ctx context.Context, in *api.AccountGameCenter, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Remove Google from the social profiles on the current user's account.
	UnlinkGoogle(ctx context.Context, in *api.AccountGoogle, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Remove Steam from the social profiles on the current user's account.
	UnlinkSteam(ctx context.Context, in *api.AccountSteam, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Update fields in the current user's account.
	UpdateAccount(ctx context.Context, in *api.UpdateAccountRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Update fields in a given group.
	UpdateGroup(ctx context.Context, in *api.UpdateGroupRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Validate Apple IAP Receipt
	ValidatePurchaseApple(ctx context.Context, in *api.ValidatePurchaseAppleRequest, opts ...grpc.CallOption) (*api.ValidatePurchaseResponse, error)
	// Validate Apple Subscription Receipt
	ValidateSubscriptionApple(ctx context.Context, in *api.ValidateSubscriptionAppleRequest, opts ...grpc.CallOption) (*api.ValidateSubscriptionResponse, error)
	// Validate Google IAP Receipt
	ValidatePurchaseGoogle(ctx context.Context, in *api.ValidatePurchaseGoogleRequest, opts ...grpc.CallOption) (*api.ValidatePurchaseResponse, error)
	// Validate Google Subscription Receipt
	ValidateSubscriptionGoogle(ctx context.Context, in *api.ValidateSubscriptionGoogleRequest, opts ...grpc.CallOption) (*api.ValidateSubscriptionResponse, error)
	// Validate Huawei IAP Receipt
	ValidatePurchaseHuawei(ctx context.Context, in *api.ValidatePurchaseHuaweiRequest, opts ...grpc.CallOption) (*api.ValidatePurchaseResponse, error)
	// Validate FB Instant IAP Receipt
	ValidatePurchaseFacebookInstant(ctx context.Context, in *api.ValidatePurchaseFacebookInstantRequest, opts ...grpc.CallOption) (*api.ValidatePurchaseResponse, error)
	// Write a record to a leaderboard.
	WriteLeaderboardRecord(ctx context.Context, in *api.WriteLeaderboardRecordRequest, opts ...grpc.CallOption) (*api.LeaderboardRecord, error)
	// Write objects into the storage engine.
	WriteStorageObjects(ctx context.Context, in *api.WriteStorageObjectsRequest, opts ...grpc.CallOption) (*api.StorageObjectAcks, error)
	// Write a record to a tournament.
	WriteTournamentRecord(ctx context.Context, in *api.WriteTournamentRecordRequest, opts ...grpc.CallOption) (*api.LeaderboardRecord, error)
}

type nakamaClient struct {
	cc grpc.ClientConnInterface
}

func NewNakamaClient(cc grpc.ClientConnInterface) NakamaClient {
	return &nakamaClient{cc}
}

func (c *nakamaClient) AddFriends(ctx context.Context, in *api.AddFriendsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_AddFriends_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) AddGroupUsers(ctx context.Context, in *api.AddGroupUsersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_AddGroupUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) SessionRefresh(ctx context.Context, in *api.SessionRefreshRequest, opts ...grpc.CallOption) (*api.Session, error) {
	out := new(api.Session)
	err := c.cc.Invoke(ctx, Nakama_SessionRefresh_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) SessionLogout(ctx context.Context, in *api.SessionLogoutRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_SessionLogout_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) AuthenticateApple(ctx context.Context, in *api.AuthenticateAppleRequest, opts ...grpc.CallOption) (*api.Session, error) {
	out := new(api.Session)
	err := c.cc.Invoke(ctx, Nakama_AuthenticateApple_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) AuthenticateCustom(ctx context.Context, in *api.AuthenticateCustomRequest, opts ...grpc.CallOption) (*api.Session, error) {
	out := new(api.Session)
	err := c.cc.Invoke(ctx, Nakama_AuthenticateCustom_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) AuthenticateDevice(ctx context.Context, in *api.AuthenticateDeviceRequest, opts ...grpc.CallOption) (*api.Session, error) {
	out := new(api.Session)
	err := c.cc.Invoke(ctx, Nakama_AuthenticateDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) AuthenticateEmail(ctx context.Context, in *api.AuthenticateEmailRequest, opts ...grpc.CallOption) (*api.Session, error) {
	out := new(api.Session)
	err := c.cc.Invoke(ctx, Nakama_AuthenticateEmail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) AuthenticateFacebook(ctx context.Context, in *api.AuthenticateFacebookRequest, opts ...grpc.CallOption) (*api.Session, error) {
	out := new(api.Session)
	err := c.cc.Invoke(ctx, Nakama_AuthenticateFacebook_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) AuthenticateFacebookInstantGame(ctx context.Context, in *api.AuthenticateFacebookInstantGameRequest, opts ...grpc.CallOption) (*api.Session, error) {
	out := new(api.Session)
	err := c.cc.Invoke(ctx, Nakama_AuthenticateFacebookInstantGame_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) AuthenticateGameCenter(ctx context.Context, in *api.AuthenticateGameCenterRequest, opts ...grpc.CallOption) (*api.Session, error) {
	out := new(api.Session)
	err := c.cc.Invoke(ctx, Nakama_AuthenticateGameCenter_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) AuthenticateGoogle(ctx context.Context, in *api.AuthenticateGoogleRequest, opts ...grpc.CallOption) (*api.Session, error) {
	out := new(api.Session)
	err := c.cc.Invoke(ctx, Nakama_AuthenticateGoogle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) AuthenticateSteam(ctx context.Context, in *api.AuthenticateSteamRequest, opts ...grpc.CallOption) (*api.Session, error) {
	out := new(api.Session)
	err := c.cc.Invoke(ctx, Nakama_AuthenticateSteam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) BanGroupUsers(ctx context.Context, in *api.BanGroupUsersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_BanGroupUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) BlockFriends(ctx context.Context, in *api.BlockFriendsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_BlockFriends_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) CreateGroup(ctx context.Context, in *api.CreateGroupRequest, opts ...grpc.CallOption) (*api.Group, error) {
	out := new(api.Group)
	err := c.cc.Invoke(ctx, Nakama_CreateGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) DeleteAccount(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_DeleteAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) DeleteFriends(ctx context.Context, in *api.DeleteFriendsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_DeleteFriends_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) DeleteGroup(ctx context.Context, in *api.DeleteGroupRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_DeleteGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) DeleteLeaderboardRecord(ctx context.Context, in *api.DeleteLeaderboardRecordRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_DeleteLeaderboardRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) DeleteNotifications(ctx context.Context, in *api.DeleteNotificationsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_DeleteNotifications_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) DeleteTournamentRecord(ctx context.Context, in *api.DeleteTournamentRecordRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_DeleteTournamentRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) DeleteStorageObjects(ctx context.Context, in *api.DeleteStorageObjectsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_DeleteStorageObjects_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) Event(ctx context.Context, in *api.Event, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_Event_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) GetAccount(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*api.Account, error) {
	out := new(api.Account)
	err := c.cc.Invoke(ctx, Nakama_GetAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) GetUsers(ctx context.Context, in *api.GetUsersRequest, opts ...grpc.CallOption) (*api.Users, error) {
	out := new(api.Users)
	err := c.cc.Invoke(ctx, Nakama_GetUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) GetSubscription(ctx context.Context, in *api.GetSubscriptionRequest, opts ...grpc.CallOption) (*api.ValidatedSubscription, error) {
	out := new(api.ValidatedSubscription)
	err := c.cc.Invoke(ctx, Nakama_GetSubscription_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) GetMatchmakerStats(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*api.MatchmakerStats, error) {
	out := new(api.MatchmakerStats)
	err := c.cc.Invoke(ctx, Nakama_GetMatchmakerStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) Healthcheck(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_Healthcheck_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ImportFacebookFriends(ctx context.Context, in *api.ImportFacebookFriendsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_ImportFacebookFriends_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ImportSteamFriends(ctx context.Context, in *api.ImportSteamFriendsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_ImportSteamFriends_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) JoinGroup(ctx context.Context, in *api.JoinGroupRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_JoinGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) JoinTournament(ctx context.Context, in *api.JoinTournamentRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_JoinTournament_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) KickGroupUsers(ctx context.Context, in *api.KickGroupUsersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_KickGroupUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) LeaveGroup(ctx context.Context, in *api.LeaveGroupRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_LeaveGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) LinkApple(ctx context.Context, in *api.AccountApple, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_LinkApple_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) LinkCustom(ctx context.Context, in *api.AccountCustom, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_LinkCustom_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) LinkDevice(ctx context.Context, in *api.AccountDevice, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_LinkDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) LinkEmail(ctx context.Context, in *api.AccountEmail, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_LinkEmail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) LinkFacebook(ctx context.Context, in *api.LinkFacebookRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_LinkFacebook_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) LinkFacebookInstantGame(ctx context.Context, in *api.AccountFacebookInstantGame, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_LinkFacebookInstantGame_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) LinkGameCenter(ctx context.Context, in *api.AccountGameCenter, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_LinkGameCenter_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) LinkGoogle(ctx context.Context, in *api.AccountGoogle, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_LinkGoogle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) LinkSteam(ctx context.Context, in *api.LinkSteamRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_LinkSteam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ListChannelMessages(ctx context.Context, in *api.ListChannelMessagesRequest, opts ...grpc.CallOption) (*api.ChannelMessageList, error) {
	out := new(api.ChannelMessageList)
	err := c.cc.Invoke(ctx, Nakama_ListChannelMessages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ListFriends(ctx context.Context, in *api.ListFriendsRequest, opts ...grpc.CallOption) (*api.FriendList, error) {
	out := new(api.FriendList)
	err := c.cc.Invoke(ctx, Nakama_ListFriends_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ListFriendsOfFriends(ctx context.Context, in *api.ListFriendsOfFriendsRequest, opts ...grpc.CallOption) (*api.FriendsOfFriendsList, error) {
	out := new(api.FriendsOfFriendsList)
	err := c.cc.Invoke(ctx, Nakama_ListFriendsOfFriends_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ListGroups(ctx context.Context, in *api.ListGroupsRequest, opts ...grpc.CallOption) (*api.GroupList, error) {
	out := new(api.GroupList)
	err := c.cc.Invoke(ctx, Nakama_ListGroups_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ListGroupUsers(ctx context.Context, in *api.ListGroupUsersRequest, opts ...grpc.CallOption) (*api.GroupUserList, error) {
	out := new(api.GroupUserList)
	err := c.cc.Invoke(ctx, Nakama_ListGroupUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ListLeaderboardRecords(ctx context.Context, in *api.ListLeaderboardRecordsRequest, opts ...grpc.CallOption) (*api.LeaderboardRecordList, error) {
	out := new(api.LeaderboardRecordList)
	err := c.cc.Invoke(ctx, Nakama_ListLeaderboardRecords_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ListLeaderboardRecordsAroundOwner(ctx context.Context, in *api.ListLeaderboardRecordsAroundOwnerRequest, opts ...grpc.CallOption) (*api.LeaderboardRecordList, error) {
	out := new(api.LeaderboardRecordList)
	err := c.cc.Invoke(ctx, Nakama_ListLeaderboardRecordsAroundOwner_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ListMatches(ctx context.Context, in *api.ListMatchesRequest, opts ...grpc.CallOption) (*api.MatchList, error) {
	out := new(api.MatchList)
	err := c.cc.Invoke(ctx, Nakama_ListMatches_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ListNotifications(ctx context.Context, in *api.ListNotificationsRequest, opts ...grpc.CallOption) (*api.NotificationList, error) {
	out := new(api.NotificationList)
	err := c.cc.Invoke(ctx, Nakama_ListNotifications_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ListStorageObjects(ctx context.Context, in *api.ListStorageObjectsRequest, opts ...grpc.CallOption) (*api.StorageObjectList, error) {
	out := new(api.StorageObjectList)
	err := c.cc.Invoke(ctx, Nakama_ListStorageObjects_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ListSubscriptions(ctx context.Context, in *api.ListSubscriptionsRequest, opts ...grpc.CallOption) (*api.SubscriptionList, error) {
	out := new(api.SubscriptionList)
	err := c.cc.Invoke(ctx, Nakama_ListSubscriptions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ListTournaments(ctx context.Context, in *api.ListTournamentsRequest, opts ...grpc.CallOption) (*api.TournamentList, error) {
	out := new(api.TournamentList)
	err := c.cc.Invoke(ctx, Nakama_ListTournaments_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ListTournamentRecords(ctx context.Context, in *api.ListTournamentRecordsRequest, opts ...grpc.CallOption) (*api.TournamentRecordList, error) {
	out := new(api.TournamentRecordList)
	err := c.cc.Invoke(ctx, Nakama_ListTournamentRecords_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ListTournamentRecordsAroundOwner(ctx context.Context, in *api.ListTournamentRecordsAroundOwnerRequest, opts ...grpc.CallOption) (*api.TournamentRecordList, error) {
	out := new(api.TournamentRecordList)
	err := c.cc.Invoke(ctx, Nakama_ListTournamentRecordsAroundOwner_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ListUserGroups(ctx context.Context, in *api.ListUserGroupsRequest, opts ...grpc.CallOption) (*api.UserGroupList, error) {
	out := new(api.UserGroupList)
	err := c.cc.Invoke(ctx, Nakama_ListUserGroups_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) PromoteGroupUsers(ctx context.Context, in *api.PromoteGroupUsersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_PromoteGroupUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) DemoteGroupUsers(ctx context.Context, in *api.DemoteGroupUsersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_DemoteGroupUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ReadStorageObjects(ctx context.Context, in *api.ReadStorageObjectsRequest, opts ...grpc.CallOption) (*api.StorageObjects, error) {
	out := new(api.StorageObjects)
	err := c.cc.Invoke(ctx, Nakama_ReadStorageObjects_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) RpcFunc(ctx context.Context, in *api.Rpc, opts ...grpc.CallOption) (*api.Rpc, error) {
	out := new(api.Rpc)
	err := c.cc.Invoke(ctx, Nakama_RpcFunc_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) UnlinkApple(ctx context.Context, in *api.AccountApple, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_UnlinkApple_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) UnlinkCustom(ctx context.Context, in *api.AccountCustom, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_UnlinkCustom_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) UnlinkDevice(ctx context.Context, in *api.AccountDevice, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_UnlinkDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) UnlinkEmail(ctx context.Context, in *api.AccountEmail, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_UnlinkEmail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) UnlinkFacebook(ctx context.Context, in *api.AccountFacebook, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_UnlinkFacebook_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) UnlinkFacebookInstantGame(ctx context.Context, in *api.AccountFacebookInstantGame, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_UnlinkFacebookInstantGame_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) UnlinkGameCenter(ctx context.Context, in *api.AccountGameCenter, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_UnlinkGameCenter_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) UnlinkGoogle(ctx context.Context, in *api.AccountGoogle, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_UnlinkGoogle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) UnlinkSteam(ctx context.Context, in *api.AccountSteam, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_UnlinkSteam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) UpdateAccount(ctx context.Context, in *api.UpdateAccountRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_UpdateAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) UpdateGroup(ctx context.Context, in *api.UpdateGroupRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Nakama_UpdateGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ValidatePurchaseApple(ctx context.Context, in *api.ValidatePurchaseAppleRequest, opts ...grpc.CallOption) (*api.ValidatePurchaseResponse, error) {
	out := new(api.ValidatePurchaseResponse)
	err := c.cc.Invoke(ctx, Nakama_ValidatePurchaseApple_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ValidateSubscriptionApple(ctx context.Context, in *api.ValidateSubscriptionAppleRequest, opts ...grpc.CallOption) (*api.ValidateSubscriptionResponse, error) {
	out := new(api.ValidateSubscriptionResponse)
	err := c.cc.Invoke(ctx, Nakama_ValidateSubscriptionApple_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ValidatePurchaseGoogle(ctx context.Context, in *api.ValidatePurchaseGoogleRequest, opts ...grpc.CallOption) (*api.ValidatePurchaseResponse, error) {
	out := new(api.ValidatePurchaseResponse)
	err := c.cc.Invoke(ctx, Nakama_ValidatePurchaseGoogle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ValidateSubscriptionGoogle(ctx context.Context, in *api.ValidateSubscriptionGoogleRequest, opts ...grpc.CallOption) (*api.ValidateSubscriptionResponse, error) {
	out := new(api.ValidateSubscriptionResponse)
	err := c.cc.Invoke(ctx, Nakama_ValidateSubscriptionGoogle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ValidatePurchaseHuawei(ctx context.Context, in *api.ValidatePurchaseHuaweiRequest, opts ...grpc.CallOption) (*api.ValidatePurchaseResponse, error) {
	out := new(api.ValidatePurchaseResponse)
	err := c.cc.Invoke(ctx, Nakama_ValidatePurchaseHuawei_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) ValidatePurchaseFacebookInstant(ctx context.Context, in *api.ValidatePurchaseFacebookInstantRequest, opts ...grpc.CallOption) (*api.ValidatePurchaseResponse, error) {
	out := new(api.ValidatePurchaseResponse)
	err := c.cc.Invoke(ctx, Nakama_ValidatePurchaseFacebookInstant_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) WriteLeaderboardRecord(ctx context.Context, in *api.WriteLeaderboardRecordRequest, opts ...grpc.CallOption) (*api.LeaderboardRecord, error) {
	out := new(api.LeaderboardRecord)
	err := c.cc.Invoke(ctx, Nakama_WriteLeaderboardRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) WriteStorageObjects(ctx context.Context, in *api.WriteStorageObjectsRequest, opts ...grpc.CallOption) (*api.StorageObjectAcks, error) {
	out := new(api.StorageObjectAcks)
	err := c.cc.Invoke(ctx, Nakama_WriteStorageObjects_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nakamaClient) WriteTournamentRecord(ctx context.Context, in *api.WriteTournamentRecordRequest, opts ...grpc.CallOption) (*api.LeaderboardRecord, error) {
	out := new(api.LeaderboardRecord)
	err := c.cc.Invoke(ctx, Nakama_WriteTournamentRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NakamaServer is the server API for Nakama service.
// All implementations must embed UnimplementedNakamaServer
// for forward compatibility
type NakamaServer interface {
	// Add friends by ID or username to a user's account.
	AddFriends(context.Context, *api.AddFriendsRequest) (*emptypb.Empty, error)
	// Add users to a group.
	AddGroupUsers(context.Context, *api.AddGroupUsersRequest) (*emptypb.Empty, error)
	// Refresh a user's session using a refresh token retrieved from a previous authentication request.
	SessionRefresh(context.Context, *api.SessionRefreshRequest) (*api.Session, error)
	// Log out a session, invalidate a refresh token, or log out all sessions/refresh tokens for a user.
	SessionLogout(context.Context, *api.SessionLogoutRequest) (*emptypb.Empty, error)
	// Authenticate a user with an Apple ID against the server.
	AuthenticateApple(context.Context, *api.AuthenticateAppleRequest) (*api.Session, error)
	// Authenticate a user with a custom id against the server.
	AuthenticateCustom(context.Context, *api.AuthenticateCustomRequest) (*api.Session, error)
	// Authenticate a user with a device id against the server.
	AuthenticateDevice(context.Context, *api.AuthenticateDeviceRequest) (*api.Session, error)
	// Authenticate a user with an email+password against the server.
	AuthenticateEmail(context.Context, *api.AuthenticateEmailRequest) (*api.Session, error)
	// Authenticate a user with a Facebook OAuth token against the server.
	AuthenticateFacebook(context.Context, *api.AuthenticateFacebookRequest) (*api.Session, error)
	// Authenticate a user with a Facebook Instant Game token against the server.
	AuthenticateFacebookInstantGame(context.Context, *api.AuthenticateFacebookInstantGameRequest) (*api.Session, error)
	// Authenticate a user with Apple's GameCenter against the server.
	AuthenticateGameCenter(context.Context, *api.AuthenticateGameCenterRequest) (*api.Session, error)
	// Authenticate a user with Google against the server.
	AuthenticateGoogle(context.Context, *api.AuthenticateGoogleRequest) (*api.Session, error)
	// Authenticate a user with Steam against the server.
	AuthenticateSteam(context.Context, *api.AuthenticateSteamRequest) (*api.Session, error)
	// Ban a set of users from a group.
	BanGroupUsers(context.Context, *api.BanGroupUsersRequest) (*emptypb.Empty, error)
	// Block one or more users by ID or username.
	BlockFriends(context.Context, *api.BlockFriendsRequest) (*emptypb.Empty, error)
	// Create a new group with the current user as the owner.
	CreateGroup(context.Context, *api.CreateGroupRequest) (*api.Group, error)
	// Delete the current user's account.
	DeleteAccount(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// Delete one or more users by ID or username.
	DeleteFriends(context.Context, *api.DeleteFriendsRequest) (*emptypb.Empty, error)
	// Delete a group by ID.
	DeleteGroup(context.Context, *api.DeleteGroupRequest) (*emptypb.Empty, error)
	// Delete a leaderboard record.
	DeleteLeaderboardRecord(context.Context, *api.DeleteLeaderboardRecordRequest) (*emptypb.Empty, error)
	// Delete one or more notifications for the current user.
	DeleteNotifications(context.Context, *api.DeleteNotificationsRequest) (*emptypb.Empty, error)
	// Delete a tournament record.
	DeleteTournamentRecord(context.Context, *api.DeleteTournamentRecordRequest) (*emptypb.Empty, error)
	// Delete one or more objects by ID or username.
	DeleteStorageObjects(context.Context, *api.DeleteStorageObjectsRequest) (*emptypb.Empty, error)
	// Submit an event for processing in the server's registered runtime custom events handler.
	Event(context.Context, *api.Event) (*emptypb.Empty, error)
	// Fetch the current user's account.
	GetAccount(context.Context, *emptypb.Empty) (*api.Account, error)
	// Fetch zero or more users by ID and/or username.
	GetUsers(context.Context, *api.GetUsersRequest) (*api.Users, error)
	// Get subscription by product id.
	GetSubscription(context.Context, *api.GetSubscriptionRequest) (*api.ValidatedSubscription, error)
	// Get matchmaker stats.
	GetMatchmakerStats(context.Context, *emptypb.Empty) (*api.MatchmakerStats, error)
	// A healthcheck which load balancers can use to check the service.
	Healthcheck(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// Import Facebook friends and add them to a user's account.
	ImportFacebookFriends(context.Context, *api.ImportFacebookFriendsRequest) (*emptypb.Empty, error)
	// Import Steam friends and add them to a user's account.
	ImportSteamFriends(context.Context, *api.ImportSteamFriendsRequest) (*emptypb.Empty, error)
	// Immediately join an open group, or request to join a closed one.
	JoinGroup(context.Context, *api.JoinGroupRequest) (*emptypb.Empty, error)
	// Attempt to join an open and running tournament.
	JoinTournament(context.Context, *api.JoinTournamentRequest) (*emptypb.Empty, error)
	// Kick a set of users from a group.
	KickGroupUsers(context.Context, *api.KickGroupUsersRequest) (*emptypb.Empty, error)
	// Leave a group the user is a member of.
	LeaveGroup(context.Context, *api.LeaveGroupRequest) (*emptypb.Empty, error)
	// Add an Apple ID to the social profiles on the current user's account.
	LinkApple(context.Context, *api.AccountApple) (*emptypb.Empty, error)
	// Add a custom ID to the social profiles on the current user's account.
	LinkCustom(context.Context, *api.AccountCustom) (*emptypb.Empty, error)
	// Add a device ID to the social profiles on the current user's account.
	LinkDevice(context.Context, *api.AccountDevice) (*emptypb.Empty, error)
	// Add an email+password to the social profiles on the current user's account.
	LinkEmail(context.Context, *api.AccountEmail) (*emptypb.Empty, error)
	// Add Facebook to the social profiles on the current user's account.
	LinkFacebook(context.Context, *api.LinkFacebookRequest) (*emptypb.Empty, error)
	// Add Facebook Instant Game to the social profiles on the current user's account.
	LinkFacebookInstantGame(context.Context, *api.AccountFacebookInstantGame) (*emptypb.Empty, error)
	// Add Apple's GameCenter to the social profiles on the current user's account.
	LinkGameCenter(context.Context, *api.AccountGameCenter) (*emptypb.Empty, error)
	// Add Google to the social profiles on the current user's account.
	LinkGoogle(context.Context, *api.AccountGoogle) (*emptypb.Empty, error)
	// Add Steam to the social profiles on the current user's account.
	LinkSteam(context.Context, *api.LinkSteamRequest) (*emptypb.Empty, error)
	// List a channel's message history.
	ListChannelMessages(context.Context, *api.ListChannelMessagesRequest) (*api.ChannelMessageList, error)
	// List all friends for the current user.
	ListFriends(context.Context, *api.ListFriendsRequest) (*api.FriendList, error)
	// List friends of friends for the current user.
	ListFriendsOfFriends(context.Context, *api.ListFriendsOfFriendsRequest) (*api.FriendsOfFriendsList, error)
	// List groups based on given filters.
	ListGroups(context.Context, *api.ListGroupsRequest) (*api.GroupList, error)
	// List all users that are part of a group.
	ListGroupUsers(context.Context, *api.ListGroupUsersRequest) (*api.GroupUserList, error)
	// List leaderboard records.
	ListLeaderboardRecords(context.Context, *api.ListLeaderboardRecordsRequest) (*api.LeaderboardRecordList, error)
	// List leaderboard records that belong to a user.
	ListLeaderboardRecordsAroundOwner(context.Context, *api.ListLeaderboardRecordsAroundOwnerRequest) (*api.LeaderboardRecordList, error)
	// Fetch list of running matches.
	ListMatches(context.Context, *api.ListMatchesRequest) (*api.MatchList, error)
	// Fetch list of notifications.
	ListNotifications(context.Context, *api.ListNotificationsRequest) (*api.NotificationList, error)
	// List publicly readable storage objects in a given collection.
	ListStorageObjects(context.Context, *api.ListStorageObjectsRequest) (*api.StorageObjectList, error)
	// List user's subscriptions.
	ListSubscriptions(context.Context, *api.ListSubscriptionsRequest) (*api.SubscriptionList, error)
	// List current or upcoming tournaments.
	ListTournaments(context.Context, *api.ListTournamentsRequest) (*api.TournamentList, error)
	// List tournament records.
	ListTournamentRecords(context.Context, *api.ListTournamentRecordsRequest) (*api.TournamentRecordList, error)
	// List tournament records for a given owner.
	ListTournamentRecordsAroundOwner(context.Context, *api.ListTournamentRecordsAroundOwnerRequest) (*api.TournamentRecordList, error)
	// List groups the current user belongs to.
	ListUserGroups(context.Context, *api.ListUserGroupsRequest) (*api.UserGroupList, error)
	// Promote a set of users in a group to the next role up.
	PromoteGroupUsers(context.Context, *api.PromoteGroupUsersRequest) (*emptypb.Empty, error)
	// Demote a set of users in a group to the next role down.
	DemoteGroupUsers(context.Context, *api.DemoteGroupUsersRequest) (*emptypb.Empty, error)
	// Get storage objects.
	ReadStorageObjects(context.Context, *api.ReadStorageObjectsRequest) (*api.StorageObjects, error)
	// Execute a Lua function on the server.
	RpcFunc(context.Context, *api.Rpc) (*api.Rpc, error)
	// Remove the Apple ID from the social profiles on the current user's account.
	UnlinkApple(context.Context, *api.AccountApple) (*emptypb.Empty, error)
	// Remove the custom ID from the social profiles on the current user's account.
	UnlinkCustom(context.Context, *api.AccountCustom) (*emptypb.Empty, error)
	// Remove the device ID from the social profiles on the current user's account.
	UnlinkDevice(context.Context, *api.AccountDevice) (*emptypb.Empty, error)
	// Remove the email+password from the social profiles on the current user's account.
	UnlinkEmail(context.Context, *api.AccountEmail) (*emptypb.Empty, error)
	// Remove Facebook from the social profiles on the current user's account.
	UnlinkFacebook(context.Context, *api.AccountFacebook) (*emptypb.Empty, error)
	// Remove Facebook Instant Game profile from the social profiles on the current user's account.
	UnlinkFacebookInstantGame(context.Context, *api.AccountFacebookInstantGame) (*emptypb.Empty, error)
	// Remove Apple's GameCenter from the social profiles on the current user's account.
	UnlinkGameCenter(context.Context, *api.AccountGameCenter) (*emptypb.Empty, error)
	// Remove Google from the social profiles on the current user's account.
	UnlinkGoogle(context.Context, *api.AccountGoogle) (*emptypb.Empty, error)
	// Remove Steam from the social profiles on the current user's account.
	UnlinkSteam(context.Context, *api.AccountSteam) (*emptypb.Empty, error)
	// Update fields in the current user's account.
	UpdateAccount(context.Context, *api.UpdateAccountRequest) (*emptypb.Empty, error)
	// Update fields in a given group.
	UpdateGroup(context.Context, *api.UpdateGroupRequest) (*emptypb.Empty, error)
	// Validate Apple IAP Receipt
	ValidatePurchaseApple(context.Context, *api.ValidatePurchaseAppleRequest) (*api.ValidatePurchaseResponse, error)
	// Validate Apple Subscription Receipt
	ValidateSubscriptionApple(context.Context, *api.ValidateSubscriptionAppleRequest) (*api.ValidateSubscriptionResponse, error)
	// Validate Google IAP Receipt
	ValidatePurchaseGoogle(context.Context, *api.ValidatePurchaseGoogleRequest) (*api.ValidatePurchaseResponse, error)
	// Validate Google Subscription Receipt
	ValidateSubscriptionGoogle(context.Context, *api.ValidateSubscriptionGoogleRequest) (*api.ValidateSubscriptionResponse, error)
	// Validate Huawei IAP Receipt
	ValidatePurchaseHuawei(context.Context, *api.ValidatePurchaseHuaweiRequest) (*api.ValidatePurchaseResponse, error)
	// Validate FB Instant IAP Receipt
	ValidatePurchaseFacebookInstant(context.Context, *api.ValidatePurchaseFacebookInstantRequest) (*api.ValidatePurchaseResponse, error)
	// Write a record to a leaderboard.
	WriteLeaderboardRecord(context.Context, *api.WriteLeaderboardRecordRequest) (*api.LeaderboardRecord, error)
	// Write objects into the storage engine.
	WriteStorageObjects(context.Context, *api.WriteStorageObjectsRequest) (*api.StorageObjectAcks, error)
	// Write a record to a tournament.
	WriteTournamentRecord(context.Context, *api.WriteTournamentRecordRequest) (*api.LeaderboardRecord, error)
	mustEmbedUnimplementedNakamaServer()
}

// UnimplementedNakamaServer must be embedded to have forward compatible implementations.
type UnimplementedNakamaServer struct {
}

func (UnimplementedNakamaServer) AddFriends(context.Context, *api.AddFriendsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddFriends not implemented")
}
func (UnimplementedNakamaServer) AddGroupUsers(context.Context, *api.AddGroupUsersRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddGroupUsers not implemented")
}
func (UnimplementedNakamaServer) SessionRefresh(context.Context, *api.SessionRefreshRequest) (*api.Session, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SessionRefresh not implemented")
}
func (UnimplementedNakamaServer) SessionLogout(context.Context, *api.SessionLogoutRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SessionLogout not implemented")
}
func (UnimplementedNakamaServer) AuthenticateApple(context.Context, *api.AuthenticateAppleRequest) (*api.Session, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthenticateApple not implemented")
}
func (UnimplementedNakamaServer) AuthenticateCustom(context.Context, *api.AuthenticateCustomRequest) (*api.Session, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthenticateCustom not implemented")
}
func (UnimplementedNakamaServer) AuthenticateDevice(context.Context, *api.AuthenticateDeviceRequest) (*api.Session, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthenticateDevice not implemented")
}
func (UnimplementedNakamaServer) AuthenticateEmail(context.Context, *api.AuthenticateEmailRequest) (*api.Session, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthenticateEmail not implemented")
}
func (UnimplementedNakamaServer) AuthenticateFacebook(context.Context, *api.AuthenticateFacebookRequest) (*api.Session, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthenticateFacebook not implemented")
}
func (UnimplementedNakamaServer) AuthenticateFacebookInstantGame(context.Context, *api.AuthenticateFacebookInstantGameRequest) (*api.Session, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthenticateFacebookInstantGame not implemented")
}
func (UnimplementedNakamaServer) AuthenticateGameCenter(context.Context, *api.AuthenticateGameCenterRequest) (*api.Session, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthenticateGameCenter not implemented")
}
func (UnimplementedNakamaServer) AuthenticateGoogle(context.Context, *api.AuthenticateGoogleRequest) (*api.Session, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthenticateGoogle not implemented")
}
func (UnimplementedNakamaServer) AuthenticateSteam(context.Context, *api.AuthenticateSteamRequest) (*api.Session, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthenticateSteam not implemented")
}
func (UnimplementedNakamaServer) BanGroupUsers(context.Context, *api.BanGroupUsersRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BanGroupUsers not implemented")
}
func (UnimplementedNakamaServer) BlockFriends(context.Context, *api.BlockFriendsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BlockFriends not implemented")
}
func (UnimplementedNakamaServer) CreateGroup(context.Context, *api.CreateGroupRequest) (*api.Group, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGroup not implemented")
}
func (UnimplementedNakamaServer) DeleteAccount(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAccount not implemented")
}
func (UnimplementedNakamaServer) DeleteFriends(context.Context, *api.DeleteFriendsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteFriends not implemented")
}
func (UnimplementedNakamaServer) DeleteGroup(context.Context, *api.DeleteGroupRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGroup not implemented")
}
func (UnimplementedNakamaServer) DeleteLeaderboardRecord(context.Context, *api.DeleteLeaderboardRecordRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLeaderboardRecord not implemented")
}
func (UnimplementedNakamaServer) DeleteNotifications(context.Context, *api.DeleteNotificationsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNotifications not implemented")
}
func (UnimplementedNakamaServer) DeleteTournamentRecord(context.Context, *api.DeleteTournamentRecordRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTournamentRecord not implemented")
}
func (UnimplementedNakamaServer) DeleteStorageObjects(context.Context, *api.DeleteStorageObjectsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteStorageObjects not implemented")
}
func (UnimplementedNakamaServer) Event(context.Context, *api.Event) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Event not implemented")
}
func (UnimplementedNakamaServer) GetAccount(context.Context, *emptypb.Empty) (*api.Account, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccount not implemented")
}
func (UnimplementedNakamaServer) GetUsers(context.Context, *api.GetUsersRequest) (*api.Users, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUsers not implemented")
}
func (UnimplementedNakamaServer) GetSubscription(context.Context, *api.GetSubscriptionRequest) (*api.ValidatedSubscription, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSubscription not implemented")
}
func (UnimplementedNakamaServer) GetMatchmakerStats(context.Context, *emptypb.Empty) (*api.MatchmakerStats, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMatchmakerStats not implemented")
}
func (UnimplementedNakamaServer) Healthcheck(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Healthcheck not implemented")
}
func (UnimplementedNakamaServer) ImportFacebookFriends(context.Context, *api.ImportFacebookFriendsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportFacebookFriends not implemented")
}
func (UnimplementedNakamaServer) ImportSteamFriends(context.Context, *api.ImportSteamFriendsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportSteamFriends not implemented")
}
func (UnimplementedNakamaServer) JoinGroup(context.Context, *api.JoinGroupRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JoinGroup not implemented")
}
func (UnimplementedNakamaServer) JoinTournament(context.Context, *api.JoinTournamentRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JoinTournament not implemented")
}
func (UnimplementedNakamaServer) KickGroupUsers(context.Context, *api.KickGroupUsersRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KickGroupUsers not implemented")
}
func (UnimplementedNakamaServer) LeaveGroup(context.Context, *api.LeaveGroupRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LeaveGroup not implemented")
}
func (UnimplementedNakamaServer) LinkApple(context.Context, *api.AccountApple) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkApple not implemented")
}
func (UnimplementedNakamaServer) LinkCustom(context.Context, *api.AccountCustom) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkCustom not implemented")
}
func (UnimplementedNakamaServer) LinkDevice(context.Context, *api.AccountDevice) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkDevice not implemented")
}
func (UnimplementedNakamaServer) LinkEmail(context.Context, *api.AccountEmail) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkEmail not implemented")
}
func (UnimplementedNakamaServer) LinkFacebook(context.Context, *api.LinkFacebookRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkFacebook not implemented")
}
func (UnimplementedNakamaServer) LinkFacebookInstantGame(context.Context, *api.AccountFacebookInstantGame) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkFacebookInstantGame not implemented")
}
func (UnimplementedNakamaServer) LinkGameCenter(context.Context, *api.AccountGameCenter) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkGameCenter not implemented")
}
func (UnimplementedNakamaServer) LinkGoogle(context.Context, *api.AccountGoogle) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkGoogle not implemented")
}
func (UnimplementedNakamaServer) LinkSteam(context.Context, *api.LinkSteamRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkSteam not implemented")
}
func (UnimplementedNakamaServer) ListChannelMessages(context.Context, *api.ListChannelMessagesRequest) (*api.ChannelMessageList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListChannelMessages not implemented")
}
func (UnimplementedNakamaServer) ListFriends(context.Context, *api.ListFriendsRequest) (*api.FriendList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListFriends not implemented")
}
func (UnimplementedNakamaServer) ListFriendsOfFriends(context.Context, *api.ListFriendsOfFriendsRequest) (*api.FriendsOfFriendsList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListFriendsOfFriends not implemented")
}
func (UnimplementedNakamaServer) ListGroups(context.Context, *api.ListGroupsRequest) (*api.GroupList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGroups not implemented")
}
func (UnimplementedNakamaServer) ListGroupUsers(context.Context, *api.ListGroupUsersRequest) (*api.GroupUserList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGroupUsers not implemented")
}
func (UnimplementedNakamaServer) ListLeaderboardRecords(context.Context, *api.ListLeaderboardRecordsRequest) (*api.LeaderboardRecordList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLeaderboardRecords not implemented")
}
func (UnimplementedNakamaServer) ListLeaderboardRecordsAroundOwner(context.Context, *api.ListLeaderboardRecordsAroundOwnerRequest) (*api.LeaderboardRecordList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLeaderboardRecordsAroundOwner not implemented")
}
func (UnimplementedNakamaServer) ListMatches(context.Context, *api.ListMatchesRequest) (*api.MatchList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMatches not implemented")
}
func (UnimplementedNakamaServer) ListNotifications(context.Context, *api.ListNotificationsRequest) (*api.NotificationList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNotifications not implemented")
}
func (UnimplementedNakamaServer) ListStorageObjects(context.Context, *api.ListStorageObjectsRequest) (*api.StorageObjectList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStorageObjects not implemented")
}
func (UnimplementedNakamaServer) ListSubscriptions(context.Context, *api.ListSubscriptionsRequest) (*api.SubscriptionList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSubscriptions not implemented")
}
func (UnimplementedNakamaServer) ListTournaments(context.Context, *api.ListTournamentsRequest) (*api.TournamentList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTournaments not implemented")
}
func (UnimplementedNakamaServer) ListTournamentRecords(context.Context, *api.ListTournamentRecordsRequest) (*api.TournamentRecordList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTournamentRecords not implemented")
}
func (UnimplementedNakamaServer) ListTournamentRecordsAroundOwner(context.Context, *api.ListTournamentRecordsAroundOwnerRequest) (*api.TournamentRecordList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTournamentRecordsAroundOwner not implemented")
}
func (UnimplementedNakamaServer) ListUserGroups(context.Context, *api.ListUserGroupsRequest) (*api.UserGroupList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserGroups not implemented")
}
func (UnimplementedNakamaServer) PromoteGroupUsers(context.Context, *api.PromoteGroupUsersRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PromoteGroupUsers not implemented")
}
func (UnimplementedNakamaServer) DemoteGroupUsers(context.Context, *api.DemoteGroupUsersRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DemoteGroupUsers not implemented")
}
func (UnimplementedNakamaServer) ReadStorageObjects(context.Context, *api.ReadStorageObjectsRequest) (*api.StorageObjects, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadStorageObjects not implemented")
}
func (UnimplementedNakamaServer) RpcFunc(context.Context, *api.Rpc) (*api.Rpc, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RpcFunc not implemented")
}
func (UnimplementedNakamaServer) UnlinkApple(context.Context, *api.AccountApple) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkApple not implemented")
}
func (UnimplementedNakamaServer) UnlinkCustom(context.Context, *api.AccountCustom) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkCustom not implemented")
}
func (UnimplementedNakamaServer) UnlinkDevice(context.Context, *api.AccountDevice) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkDevice not implemented")
}
func (UnimplementedNakamaServer) UnlinkEmail(context.Context, *api.AccountEmail) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkEmail not implemented")
}
func (UnimplementedNakamaServer) UnlinkFacebook(context.Context, *api.AccountFacebook) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkFacebook not implemented")
}
func (UnimplementedNakamaServer) UnlinkFacebookInstantGame(context.Context, *api.AccountFacebookInstantGame) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkFacebookInstantGame not implemented")
}
func (UnimplementedNakamaServer) UnlinkGameCenter(context.Context, *api.AccountGameCenter) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkGameCenter not implemented")
}
func (UnimplementedNakamaServer) UnlinkGoogle(context.Context, *api.AccountGoogle) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkGoogle not implemented")
}
func (UnimplementedNakamaServer) UnlinkSteam(context.Context, *api.AccountSteam) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkSteam not implemented")
}
func (UnimplementedNakamaServer) UpdateAccount(context.Context, *api.UpdateAccountRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAccount not implemented")
}
func (UnimplementedNakamaServer) UpdateGroup(context.Context, *api.UpdateGroupRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGroup not implemented")
}
func (UnimplementedNakamaServer) ValidatePurchaseApple(context.Context, *api.ValidatePurchaseAppleRequest) (*api.ValidatePurchaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidatePurchaseApple not implemented")
}
func (UnimplementedNakamaServer) ValidateSubscriptionApple(context.Context, *api.ValidateSubscriptionAppleRequest) (*api.ValidateSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateSubscriptionApple not implemented")
}
func (UnimplementedNakamaServer) ValidatePurchaseGoogle(context.Context, *api.ValidatePurchaseGoogleRequest) (*api.ValidatePurchaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidatePurchaseGoogle not implemented")
}
func (UnimplementedNakamaServer) ValidateSubscriptionGoogle(context.Context, *api.ValidateSubscriptionGoogleRequest) (*api.ValidateSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateSubscriptionGoogle not implemented")
}
func (UnimplementedNakamaServer) ValidatePurchaseHuawei(context.Context, *api.ValidatePurchaseHuaweiRequest) (*api.ValidatePurchaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidatePurchaseHuawei not implemented")
}
func (UnimplementedNakamaServer) ValidatePurchaseFacebookInstant(context.Context, *api.ValidatePurchaseFacebookInstantRequest) (*api.ValidatePurchaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidatePurchaseFacebookInstant not implemented")
}
func (UnimplementedNakamaServer) WriteLeaderboardRecord(context.Context, *api.WriteLeaderboardRecordRequest) (*api.LeaderboardRecord, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WriteLeaderboardRecord not implemented")
}
func (UnimplementedNakamaServer) WriteStorageObjects(context.Context, *api.WriteStorageObjectsRequest) (*api.StorageObjectAcks, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WriteStorageObjects not implemented")
}
func (UnimplementedNakamaServer) WriteTournamentRecord(context.Context, *api.WriteTournamentRecordRequest) (*api.LeaderboardRecord, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WriteTournamentRecord not implemented")
}
func (UnimplementedNakamaServer) mustEmbedUnimplementedNakamaServer() {}

// UnsafeNakamaServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NakamaServer will
// result in compilation errors.
type UnsafeNakamaServer interface {
	mustEmbedUnimplementedNakamaServer()
}

func RegisterNakamaServer(s grpc.ServiceRegistrar, srv NakamaServer) {
	s.RegisterService(&Nakama_ServiceDesc, srv)
}

func _Nakama_AddFriends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AddFriendsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).AddFriends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_AddFriends_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).AddFriends(ctx, req.(*api.AddFriendsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_AddGroupUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AddGroupUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).AddGroupUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_AddGroupUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).AddGroupUsers(ctx, req.(*api.AddGroupUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_SessionRefresh_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.SessionRefreshRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).SessionRefresh(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_SessionRefresh_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).SessionRefresh(ctx, req.(*api.SessionRefreshRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_SessionLogout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.SessionLogoutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).SessionLogout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_SessionLogout_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).SessionLogout(ctx, req.(*api.SessionLogoutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_AuthenticateApple_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AuthenticateAppleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).AuthenticateApple(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_AuthenticateApple_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).AuthenticateApple(ctx, req.(*api.AuthenticateAppleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_AuthenticateCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AuthenticateCustomRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).AuthenticateCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_AuthenticateCustom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).AuthenticateCustom(ctx, req.(*api.AuthenticateCustomRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_AuthenticateDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AuthenticateDeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).AuthenticateDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_AuthenticateDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).AuthenticateDevice(ctx, req.(*api.AuthenticateDeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_AuthenticateEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AuthenticateEmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).AuthenticateEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_AuthenticateEmail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).AuthenticateEmail(ctx, req.(*api.AuthenticateEmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_AuthenticateFacebook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AuthenticateFacebookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).AuthenticateFacebook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_AuthenticateFacebook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).AuthenticateFacebook(ctx, req.(*api.AuthenticateFacebookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_AuthenticateFacebookInstantGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AuthenticateFacebookInstantGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).AuthenticateFacebookInstantGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_AuthenticateFacebookInstantGame_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).AuthenticateFacebookInstantGame(ctx, req.(*api.AuthenticateFacebookInstantGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_AuthenticateGameCenter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AuthenticateGameCenterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).AuthenticateGameCenter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_AuthenticateGameCenter_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).AuthenticateGameCenter(ctx, req.(*api.AuthenticateGameCenterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_AuthenticateGoogle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AuthenticateGoogleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).AuthenticateGoogle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_AuthenticateGoogle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).AuthenticateGoogle(ctx, req.(*api.AuthenticateGoogleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_AuthenticateSteam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AuthenticateSteamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).AuthenticateSteam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_AuthenticateSteam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).AuthenticateSteam(ctx, req.(*api.AuthenticateSteamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_BanGroupUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.BanGroupUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).BanGroupUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_BanGroupUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).BanGroupUsers(ctx, req.(*api.BanGroupUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_BlockFriends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.BlockFriendsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).BlockFriends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_BlockFriends_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).BlockFriends(ctx, req.(*api.BlockFriendsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_CreateGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.CreateGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).CreateGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_CreateGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).CreateGroup(ctx, req.(*api.CreateGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_DeleteAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).DeleteAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_DeleteAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).DeleteAccount(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_DeleteFriends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.DeleteFriendsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).DeleteFriends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_DeleteFriends_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).DeleteFriends(ctx, req.(*api.DeleteFriendsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_DeleteGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.DeleteGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).DeleteGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_DeleteGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).DeleteGroup(ctx, req.(*api.DeleteGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_DeleteLeaderboardRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.DeleteLeaderboardRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).DeleteLeaderboardRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_DeleteLeaderboardRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).DeleteLeaderboardRecord(ctx, req.(*api.DeleteLeaderboardRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_DeleteNotifications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.DeleteNotificationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).DeleteNotifications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_DeleteNotifications_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).DeleteNotifications(ctx, req.(*api.DeleteNotificationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_DeleteTournamentRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.DeleteTournamentRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).DeleteTournamentRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_DeleteTournamentRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).DeleteTournamentRecord(ctx, req.(*api.DeleteTournamentRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_DeleteStorageObjects_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.DeleteStorageObjectsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).DeleteStorageObjects(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_DeleteStorageObjects_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).DeleteStorageObjects(ctx, req.(*api.DeleteStorageObjectsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_Event_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.Event)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).Event(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_Event_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).Event(ctx, req.(*api.Event))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_GetAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).GetAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_GetAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).GetAccount(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_GetUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.GetUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).GetUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_GetUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).GetUsers(ctx, req.(*api.GetUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_GetSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.GetSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).GetSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_GetSubscription_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).GetSubscription(ctx, req.(*api.GetSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_GetMatchmakerStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).GetMatchmakerStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_GetMatchmakerStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).GetMatchmakerStats(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_Healthcheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).Healthcheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_Healthcheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).Healthcheck(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ImportFacebookFriends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ImportFacebookFriendsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ImportFacebookFriends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ImportFacebookFriends_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ImportFacebookFriends(ctx, req.(*api.ImportFacebookFriendsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ImportSteamFriends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ImportSteamFriendsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ImportSteamFriends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ImportSteamFriends_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ImportSteamFriends(ctx, req.(*api.ImportSteamFriendsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_JoinGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.JoinGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).JoinGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_JoinGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).JoinGroup(ctx, req.(*api.JoinGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_JoinTournament_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.JoinTournamentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).JoinTournament(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_JoinTournament_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).JoinTournament(ctx, req.(*api.JoinTournamentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_KickGroupUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.KickGroupUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).KickGroupUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_KickGroupUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).KickGroupUsers(ctx, req.(*api.KickGroupUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_LeaveGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.LeaveGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).LeaveGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_LeaveGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).LeaveGroup(ctx, req.(*api.LeaveGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_LinkApple_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AccountApple)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).LinkApple(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_LinkApple_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).LinkApple(ctx, req.(*api.AccountApple))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_LinkCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AccountCustom)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).LinkCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_LinkCustom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).LinkCustom(ctx, req.(*api.AccountCustom))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_LinkDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AccountDevice)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).LinkDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_LinkDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).LinkDevice(ctx, req.(*api.AccountDevice))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_LinkEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AccountEmail)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).LinkEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_LinkEmail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).LinkEmail(ctx, req.(*api.AccountEmail))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_LinkFacebook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.LinkFacebookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).LinkFacebook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_LinkFacebook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).LinkFacebook(ctx, req.(*api.LinkFacebookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_LinkFacebookInstantGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AccountFacebookInstantGame)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).LinkFacebookInstantGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_LinkFacebookInstantGame_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).LinkFacebookInstantGame(ctx, req.(*api.AccountFacebookInstantGame))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_LinkGameCenter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AccountGameCenter)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).LinkGameCenter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_LinkGameCenter_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).LinkGameCenter(ctx, req.(*api.AccountGameCenter))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_LinkGoogle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AccountGoogle)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).LinkGoogle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_LinkGoogle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).LinkGoogle(ctx, req.(*api.AccountGoogle))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_LinkSteam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.LinkSteamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).LinkSteam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_LinkSteam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).LinkSteam(ctx, req.(*api.LinkSteamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ListChannelMessages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ListChannelMessagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ListChannelMessages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ListChannelMessages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ListChannelMessages(ctx, req.(*api.ListChannelMessagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ListFriends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ListFriendsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ListFriends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ListFriends_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ListFriends(ctx, req.(*api.ListFriendsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ListFriendsOfFriends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ListFriendsOfFriendsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ListFriendsOfFriends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ListFriendsOfFriends_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ListFriendsOfFriends(ctx, req.(*api.ListFriendsOfFriendsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ListGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ListGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ListGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ListGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ListGroups(ctx, req.(*api.ListGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ListGroupUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ListGroupUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ListGroupUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ListGroupUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ListGroupUsers(ctx, req.(*api.ListGroupUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ListLeaderboardRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ListLeaderboardRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ListLeaderboardRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ListLeaderboardRecords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ListLeaderboardRecords(ctx, req.(*api.ListLeaderboardRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ListLeaderboardRecordsAroundOwner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ListLeaderboardRecordsAroundOwnerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ListLeaderboardRecordsAroundOwner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ListLeaderboardRecordsAroundOwner_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ListLeaderboardRecordsAroundOwner(ctx, req.(*api.ListLeaderboardRecordsAroundOwnerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ListMatches_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ListMatchesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ListMatches(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ListMatches_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ListMatches(ctx, req.(*api.ListMatchesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ListNotifications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ListNotificationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ListNotifications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ListNotifications_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ListNotifications(ctx, req.(*api.ListNotificationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ListStorageObjects_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ListStorageObjectsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ListStorageObjects(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ListStorageObjects_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ListStorageObjects(ctx, req.(*api.ListStorageObjectsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ListSubscriptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ListSubscriptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ListSubscriptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ListSubscriptions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ListSubscriptions(ctx, req.(*api.ListSubscriptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ListTournaments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ListTournamentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ListTournaments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ListTournaments_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ListTournaments(ctx, req.(*api.ListTournamentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ListTournamentRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ListTournamentRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ListTournamentRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ListTournamentRecords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ListTournamentRecords(ctx, req.(*api.ListTournamentRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ListTournamentRecordsAroundOwner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ListTournamentRecordsAroundOwnerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ListTournamentRecordsAroundOwner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ListTournamentRecordsAroundOwner_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ListTournamentRecordsAroundOwner(ctx, req.(*api.ListTournamentRecordsAroundOwnerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ListUserGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ListUserGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ListUserGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ListUserGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ListUserGroups(ctx, req.(*api.ListUserGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_PromoteGroupUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.PromoteGroupUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).PromoteGroupUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_PromoteGroupUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).PromoteGroupUsers(ctx, req.(*api.PromoteGroupUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_DemoteGroupUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.DemoteGroupUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).DemoteGroupUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_DemoteGroupUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).DemoteGroupUsers(ctx, req.(*api.DemoteGroupUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ReadStorageObjects_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ReadStorageObjectsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ReadStorageObjects(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ReadStorageObjects_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ReadStorageObjects(ctx, req.(*api.ReadStorageObjectsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_RpcFunc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.Rpc)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).RpcFunc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_RpcFunc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).RpcFunc(ctx, req.(*api.Rpc))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_UnlinkApple_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AccountApple)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).UnlinkApple(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_UnlinkApple_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).UnlinkApple(ctx, req.(*api.AccountApple))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_UnlinkCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AccountCustom)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).UnlinkCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_UnlinkCustom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).UnlinkCustom(ctx, req.(*api.AccountCustom))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_UnlinkDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AccountDevice)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).UnlinkDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_UnlinkDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).UnlinkDevice(ctx, req.(*api.AccountDevice))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_UnlinkEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AccountEmail)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).UnlinkEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_UnlinkEmail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).UnlinkEmail(ctx, req.(*api.AccountEmail))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_UnlinkFacebook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AccountFacebook)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).UnlinkFacebook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_UnlinkFacebook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).UnlinkFacebook(ctx, req.(*api.AccountFacebook))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_UnlinkFacebookInstantGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AccountFacebookInstantGame)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).UnlinkFacebookInstantGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_UnlinkFacebookInstantGame_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).UnlinkFacebookInstantGame(ctx, req.(*api.AccountFacebookInstantGame))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_UnlinkGameCenter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AccountGameCenter)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).UnlinkGameCenter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_UnlinkGameCenter_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).UnlinkGameCenter(ctx, req.(*api.AccountGameCenter))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_UnlinkGoogle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AccountGoogle)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).UnlinkGoogle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_UnlinkGoogle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).UnlinkGoogle(ctx, req.(*api.AccountGoogle))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_UnlinkSteam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.AccountSteam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).UnlinkSteam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_UnlinkSteam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).UnlinkSteam(ctx, req.(*api.AccountSteam))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_UpdateAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.UpdateAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).UpdateAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_UpdateAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).UpdateAccount(ctx, req.(*api.UpdateAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_UpdateGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.UpdateGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).UpdateGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_UpdateGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).UpdateGroup(ctx, req.(*api.UpdateGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ValidatePurchaseApple_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ValidatePurchaseAppleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ValidatePurchaseApple(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ValidatePurchaseApple_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ValidatePurchaseApple(ctx, req.(*api.ValidatePurchaseAppleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ValidateSubscriptionApple_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ValidateSubscriptionAppleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ValidateSubscriptionApple(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ValidateSubscriptionApple_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ValidateSubscriptionApple(ctx, req.(*api.ValidateSubscriptionAppleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ValidatePurchaseGoogle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ValidatePurchaseGoogleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ValidatePurchaseGoogle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ValidatePurchaseGoogle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ValidatePurchaseGoogle(ctx, req.(*api.ValidatePurchaseGoogleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ValidateSubscriptionGoogle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ValidateSubscriptionGoogleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ValidateSubscriptionGoogle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ValidateSubscriptionGoogle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ValidateSubscriptionGoogle(ctx, req.(*api.ValidateSubscriptionGoogleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ValidatePurchaseHuawei_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ValidatePurchaseHuaweiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ValidatePurchaseHuawei(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ValidatePurchaseHuawei_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ValidatePurchaseHuawei(ctx, req.(*api.ValidatePurchaseHuaweiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_ValidatePurchaseFacebookInstant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.ValidatePurchaseFacebookInstantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).ValidatePurchaseFacebookInstant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_ValidatePurchaseFacebookInstant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).ValidatePurchaseFacebookInstant(ctx, req.(*api.ValidatePurchaseFacebookInstantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_WriteLeaderboardRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.WriteLeaderboardRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).WriteLeaderboardRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_WriteLeaderboardRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).WriteLeaderboardRecord(ctx, req.(*api.WriteLeaderboardRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_WriteStorageObjects_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.WriteStorageObjectsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).WriteStorageObjects(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_WriteStorageObjects_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).WriteStorageObjects(ctx, req.(*api.WriteStorageObjectsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nakama_WriteTournamentRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(api.WriteTournamentRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NakamaServer).WriteTournamentRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nakama_WriteTournamentRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NakamaServer).WriteTournamentRecord(ctx, req.(*api.WriteTournamentRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Nakama_ServiceDesc is the grpc.ServiceDesc for Nakama service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Nakama_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "nakama.api.Nakama",
	HandlerType: (*NakamaServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddFriends",
			Handler:    _Nakama_AddFriends_Handler,
		},
		{
			MethodName: "AddGroupUsers",
			Handler:    _Nakama_AddGroupUsers_Handler,
		},
		{
			MethodName: "SessionRefresh",
			Handler:    _Nakama_SessionRefresh_Handler,
		},
		{
			MethodName: "SessionLogout",
			Handler:    _Nakama_SessionLogout_Handler,
		},
		{
			MethodName: "AuthenticateApple",
			Handler:    _Nakama_AuthenticateApple_Handler,
		},
		{
			MethodName: "AuthenticateCustom",
			Handler:    _Nakama_AuthenticateCustom_Handler,
		},
		{
			MethodName: "AuthenticateDevice",
			Handler:    _Nakama_AuthenticateDevice_Handler,
		},
		{
			MethodName: "AuthenticateEmail",
			Handler:    _Nakama_AuthenticateEmail_Handler,
		},
		{
			MethodName: "AuthenticateFacebook",
			Handler:    _Nakama_AuthenticateFacebook_Handler,
		},
		{
			MethodName: "AuthenticateFacebookInstantGame",
			Handler:    _Nakama_AuthenticateFacebookInstantGame_Handler,
		},
		{
			MethodName: "AuthenticateGameCenter",
			Handler:    _Nakama_AuthenticateGameCenter_Handler,
		},
		{
			MethodName: "AuthenticateGoogle",
			Handler:    _Nakama_AuthenticateGoogle_Handler,
		},
		{
			MethodName: "AuthenticateSteam",
			Handler:    _Nakama_AuthenticateSteam_Handler,
		},
		{
			MethodName: "BanGroupUsers",
			Handler:    _Nakama_BanGroupUsers_Handler,
		},
		{
			MethodName: "BlockFriends",
			Handler:    _Nakama_BlockFriends_Handler,
		},
		{
			MethodName: "CreateGroup",
			Handler:    _Nakama_CreateGroup_Handler,
		},
		{
			MethodName: "DeleteAccount",
			Handler:    _Nakama_DeleteAccount_Handler,
		},
		{
			MethodName: "DeleteFriends",
			Handler:    _Nakama_DeleteFriends_Handler,
		},
		{
			MethodName: "DeleteGroup",
			Handler:    _Nakama_DeleteGroup_Handler,
		},
		{
			MethodName: "DeleteLeaderboardRecord",
			Handler:    _Nakama_DeleteLeaderboardRecord_Handler,
		},
		{
			MethodName: "DeleteNotifications",
			Handler:    _Nakama_DeleteNotifications_Handler,
		},
		{
			MethodName: "DeleteTournamentRecord",
			Handler:    _Nakama_DeleteTournamentRecord_Handler,
		},
		{
			MethodName: "DeleteStorageObjects",
			Handler:    _Nakama_DeleteStorageObjects_Handler,
		},
		{
			MethodName: "Event",
			Handler:    _Nakama_Event_Handler,
		},
		{
			MethodName: "GetAccount",
			Handler:    _Nakama_GetAccount_Handler,
		},
		{
			MethodName: "GetUsers",
			Handler:    _Nakama_GetUsers_Handler,
		},
		{
			MethodName: "GetSubscription",
			Handler:    _Nakama_GetSubscription_Handler,
		},
		{
			MethodName: "GetMatchmakerStats",
			Handler:    _Nakama_GetMatchmakerStats_Handler,
		},
		{
			MethodName: "Healthcheck",
			Handler:    _Nakama_Healthcheck_Handler,
		},
		{
			MethodName: "ImportFacebookFriends",
			Handler:    _Nakama_ImportFacebookFriends_Handler,
		},
		{
			MethodName: "ImportSteamFriends",
			Handler:    _Nakama_ImportSteamFriends_Handler,
		},
		{
			MethodName: "JoinGroup",
			Handler:    _Nakama_JoinGroup_Handler,
		},
		{
			MethodName: "JoinTournament",
			Handler:    _Nakama_JoinTournament_Handler,
		},
		{
			MethodName: "KickGroupUsers",
			Handler:    _Nakama_KickGroupUsers_Handler,
		},
		{
			MethodName: "LeaveGroup",
			Handler:    _Nakama_LeaveGroup_Handler,
		},
		{
			MethodName: "LinkApple",
			Handler:    _Nakama_LinkApple_Handler,
		},
		{
			MethodName: "LinkCustom",
			Handler:    _Nakama_LinkCustom_Handler,
		},
		{
			MethodName: "LinkDevice",
			Handler:    _Nakama_LinkDevice_Handler,
		},
		{
			MethodName: "LinkEmail",
			Handler:    _Nakama_LinkEmail_Handler,
		},
		{
			MethodName: "LinkFacebook",
			Handler:    _Nakama_LinkFacebook_Handler,
		},
		{
			MethodName: "LinkFacebookInstantGame",
			Handler:    _Nakama_LinkFacebookInstantGame_Handler,
		},
		{
			MethodName: "LinkGameCenter",
			Handler:    _Nakama_LinkGameCenter_Handler,
		},
		{
			MethodName: "LinkGoogle",
			Handler:    _Nakama_LinkGoogle_Handler,
		},
		{
			MethodName: "LinkSteam",
			Handler:    _Nakama_LinkSteam_Handler,
		},
		{
			MethodName: "ListChannelMessages",
			Handler:    _Nakama_ListChannelMessages_Handler,
		},
		{
			MethodName: "ListFriends",
			Handler:    _Nakama_ListFriends_Handler,
		},
		{
			MethodName: "ListFriendsOfFriends",
			Handler:    _Nakama_ListFriendsOfFriends_Handler,
		},
		{
			MethodName: "ListGroups",
			Handler:    _Nakama_ListGroups_Handler,
		},
		{
			MethodName: "ListGroupUsers",
			Handler:    _Nakama_ListGroupUsers_Handler,
		},
		{
			MethodName: "ListLeaderboardRecords",
			Handler:    _Nakama_ListLeaderboardRecords_Handler,
		},
		{
			MethodName: "ListLeaderboardRecordsAroundOwner",
			Handler:    _Nakama_ListLeaderboardRecordsAroundOwner_Handler,
		},
		{
			MethodName: "ListMatches",
			Handler:    _Nakama_ListMatches_Handler,
		},
		{
			MethodName: "ListNotifications",
			Handler:    _Nakama_ListNotifications_Handler,
		},
		{
			MethodName: "ListStorageObjects",
			Handler:    _Nakama_ListStorageObjects_Handler,
		},
		{
			MethodName: "ListSubscriptions",
			Handler:    _Nakama_ListSubscriptions_Handler,
		},
		{
			MethodName: "ListTournaments",
			Handler:    _Nakama_ListTournaments_Handler,
		},
		{
			MethodName: "ListTournamentRecords",
			Handler:    _Nakama_ListTournamentRecords_Handler,
		},
		{
			MethodName: "ListTournamentRecordsAroundOwner",
			Handler:    _Nakama_ListTournamentRecordsAroundOwner_Handler,
		},
		{
			MethodName: "ListUserGroups",
			Handler:    _Nakama_ListUserGroups_Handler,
		},
		{
			MethodName: "PromoteGroupUsers",
			Handler:    _Nakama_PromoteGroupUsers_Handler,
		},
		{
			MethodName: "DemoteGroupUsers",
			Handler:    _Nakama_DemoteGroupUsers_Handler,
		},
		{
			MethodName: "ReadStorageObjects",
			Handler:    _Nakama_ReadStorageObjects_Handler,
		},
		{
			MethodName: "RpcFunc",
			Handler:    _Nakama_RpcFunc_Handler,
		},
		{
			MethodName: "UnlinkApple",
			Handler:    _Nakama_UnlinkApple_Handler,
		},
		{
			MethodName: "UnlinkCustom",
			Handler:    _Nakama_UnlinkCustom_Handler,
		},
		{
			MethodName: "UnlinkDevice",
			Handler:    _Nakama_UnlinkDevice_Handler,
		},
		{
			MethodName: "UnlinkEmail",
			Handler:    _Nakama_UnlinkEmail_Handler,
		},
		{
			MethodName: "UnlinkFacebook",
			Handler:    _Nakama_UnlinkFacebook_Handler,
		},
		{
			MethodName: "UnlinkFacebookInstantGame",
			Handler:    _Nakama_UnlinkFacebookInstantGame_Handler,
		},
		{
			MethodName: "UnlinkGameCenter",
			Handler:    _Nakama_UnlinkGameCenter_Handler,
		},
		{
			MethodName: "UnlinkGoogle",
			Handler:    _Nakama_UnlinkGoogle_Handler,
		},
		{
			MethodName: "UnlinkSteam",
			Handler:    _Nakama_UnlinkSteam_Handler,
		},
		{
			MethodName: "UpdateAccount",
			Handler:    _Nakama_UpdateAccount_Handler,
		},
		{
			MethodName: "UpdateGroup",
			Handler:    _Nakama_UpdateGroup_Handler,
		},
		{
			MethodName: "ValidatePurchaseApple",
			Handler:    _Nakama_ValidatePurchaseApple_Handler,
		},
		{
			MethodName: "ValidateSubscriptionApple",
			Handler:    _Nakama_ValidateSubscriptionApple_Handler,
		},
		{
			MethodName: "ValidatePurchaseGoogle",
			Handler:    _Nakama_ValidatePurchaseGoogle_Handler,
		},
		{
			MethodName: "ValidateSubscriptionGoogle",
			Handler:    _Nakama_ValidateSubscriptionGoogle_Handler,
		},
		{
			MethodName: "ValidatePurchaseHuawei",
			Handler:    _Nakama_ValidatePurchaseHuawei_Handler,
		},
		{
			MethodName: "ValidatePurchaseFacebookInstant",
			Handler:    _Nakama_ValidatePurchaseFacebookInstant_Handler,
		},
		{
			MethodName: "WriteLeaderboardRecord",
			Handler:    _Nakama_WriteLeaderboardRecord_Handler,
		},
		{
			MethodName: "WriteStorageObjects",
			Handler:    _Nakama_WriteStorageObjects_Handler,
		},
		{
			MethodName: "WriteTournamentRecord",
			Handler:    _Nakama_WriteTournamentRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "apigrpc.proto",
}
