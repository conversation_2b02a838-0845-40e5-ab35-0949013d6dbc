package models

import (
	"fmt"
	"time"
)

const (
	NACOS_DEFAULT_GROUP            = "omo"
	NACOS_CUSTOM_DATA_ID_GROUP_KEY = "NACOS_CUSTOM_DATA_ID_GROUP" // data_id@@nacosgroup
	LOCAL_CUSTOM_CONFIG_PATH_KEY   = "LOCAL_CUSTOM_CONFIG_PATH"
)

var OFFLINE_ERROR = fmt.Errorf("offline")

// 反向代理类型
const (
	PROXYSVC   string = "svc"
	PROXYHOSTS string = "hosts"
)

// 反向代理负载均衡算法
const (
	PROXYLBRR       string = "rr"       // 轮循
	PROXYLBWRR      string = "wrr"      // 加权轮循
	PROXYLBHASH     string = "hash"     // 一致性hash
	PROXYLBRAND     string = "rand"     // 随机
	PROXYLBLEASSNUM string = "leastnum" // 最小连接数,暂不支持
)

// 消息类型
const (
	MSGTYPE_ONE       int32 = 1
	MSGTYPE_MULTICAST int32 = 2 // 多播
	MSGTYPE_BOARDCAST int32 = 2 // 多播
)

// 自定义http头
const X_Custom_Direct_ServiceID string = "X-Custom-Direct-Serviceid"
const X_Custom_Direct_HOST string = "X-Custom-Direct-Host"
const X_Custom_Hash_ID string = "X-Custom-Hash-Id"
const X_Custom_ProxyType string = "X-Custom-Proxytype"
const X_Custom_ReqTime string = "X-Custom-Reqtime"
const X_Custom_MsgID string = "X-Custom-Msgid"
const X_Msg_Level string = "X-Msglevel"
const X_Push_To_MQ string = "X-Push-To-Mq"

const RPC_TIMEOUT_DEFAULT = time.Second * 3

type ErrorCode int64 // 与grpc的codes.Code区分开从20开始

const (
	OK             ErrorCode = 0
	TIMEOUT        ErrorCode = 21 // 超时
	FAILED         ErrorCode = 22 // 执行失败
	BUSY           ErrorCode = 23
	EXCEPTION      ErrorCode = 24
	PARAM_ERR      ErrorCode = 25
	LOCK_ERR       ErrorCode = 26
	SESSION_ERR    ErrorCode = 27
	ACCESS_DENIED  ErrorCode = 28
	AUTH_CODE      ErrorCode = 29 // 告诉客户端需要验证码
	AUTH_ERR       ErrorCode = 30 // 验证错误
	REPEAT_REG     ErrorCode = 31 // 重复注册
	DATA_EXIST     ErrorCode = 32 // 数据已存在
	DATA_NOT_EXIST ErrorCode = 33 // 数据不存在
	AGG_AUTH_ERR   ErrorCode = 34 // 聚合验证错误
)

// 回写数据库策略
type FlushType int32

const (
	FLUSH_TYPE_SYNC  FlushType = 0 // 立即同步回写，默认值
	FLUSH_TYPE_ASYNC FlushType = 1 // 立即异步回写
	FLUSH_TYPE_TIMER FlushType = 2 // 定时回写
	FLUSH_TYPE_DEL   FlushType = 3 // 删除回写
)

// 缓存重要性
type CacheLevel int32

const (
	CACHE_LEVEL_NORMAL CacheLevel = 1 // 正常
	CACHE_LEVEL_IMPORT CacheLevel = 2 // 重要
)

// 锁操作
type LockOpt int

const (
	LOCKOPT_NONE         LockOpt = 0  // 无锁
	LOCKOPT_LOCK_READ    LockOpt = 1  // 读锁
	LOCKOPT_LOCK_WRITE   LockOpt = 2  // 写锁
	LOCKOPT_UNLOCK_READ  LockOpt = 11 // 解读锁
	LOCKOPT_UNLOCK_WRITE LockOpt = 12 // 解写锁
)

// 服务名定义
const (
	SERVER_NAME_EXAMPLE = "exampleserver" // 示例服务
	SERVER_NAME_MALL    = "mallserver"
	SERVER_NAME_IM      = "imserver"      // im服务
	SERVER_NAME_CRON    = "cronserver"    // 定时任务服
	SERVER_NAME_STATUS  = "statusserver"  // 状态服
	SERVER_NAME_SESSION = "sessionserver" // 会话服
	SERVER_NAME_USER    = "userserver"    // 用户服
	SERVER_NAME_DBAGENT = "dbagentserver" // 数据库代理服务
	SERVER_NAME_GATE    = "gateserver"    // 网关服务
	SERVER_NAME_LOGIC   = "logicserver"   // 逻辑服
	SERVER_NAME_LOGIN   = "loginserver"   // 登录服
	SERVER_NAME_QUEUE   = "queueserver"   // 排队服
	SERVER_NAME_FRIEND  = "friendserver"  // 好友服
)

// 结果集类型
type RowResultType string

const (
	ROWRESULT_TYPE_ONLYONE RowResultType = "onlyone" // 单行
	ROWRESULT_TYPE_MULTI   RowResultType = "multi"   // 多行
)

// 玩家身份
type PlayerIdentityType int32

const (
	PLAYER_IDENTITY_TYPE_NORMAL PlayerIdentityType = 0 // 正常玩家
	PLAYER_IDENTITY_TYPE_TEST   PlayerIdentityType = 1 // 测试玩家
	PLAYER_IDENTITY_TYPE_GUEST  PlayerIdentityType = 2 // 游客
	PLAYER_IDENTITY_TYPE_ROBOT  PlayerIdentityType = 3 // 机器人
	PLAYER_IDENTITY_TYPE_ADMIN  PlayerIdentityType = 4 // 管理员
)
