package common

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/gofrs/uuid/v5"
	"github.com/redis/go-redis/v9"
)

type RedisLockType int

const (
	ReadLock RedisLockType = iota
	WriteLock
)

var (
	ErrLockNotObtained = errors.New("lock not obtained")
	ErrInvalidLockType = errors.New("invalid lock type")
)

// RedisRWLocker 管理 redis 读写锁及对象池
type RedisRWLocker struct {
	rdb  redis.Cmdable
	pool sync.Pool
}

func NewRedisRWLocker(rdb redis.Cmdable) *RedisRWLocker {
	l := &RedisRWLocker{rdb: rdb}
	l.pool.New = func() interface{} {
		return &RedisRWLock{locker: l}
	}
	return l
}

// RedisRWLock 表示一个持有锁的实例
type RedisRWLock struct {
	Resource string
	LockID   string
	TTL      time.Duration
	Type     RedisLockType

	locker *RedisRWLocker
}

// Lock 获取锁，支持读写锁类型
func (l *RedisRWLocker) Lock(ctx context.Context, resource string, lockID string, ttl time.Duration, lockType RedisLockType) (*RedisRWLock, error) {
	lock, err := l.lockInternal(ctx, resource, lockID, ttl, lockType)
	if err != nil {
		return nil, err
	}
	return lock, nil
}

func (l *RedisRWLocker) lockInternal(ctx context.Context, resource, lockID string, ttl time.Duration, lockType RedisLockType) (*RedisRWLock, error) {
	obj := l.pool.Get()
	lock := obj.(*RedisRWLock)

	// 复用前清理并赋值
	lock.Resource = resource
	lock.LockID = lockID
	lock.TTL = ttl
	lock.Type = lockType

	var err error
	switch lockType {
	case WriteLock:
		err = l.acquireWriteLock(ctx, lock)
	case ReadLock:
		err = l.acquireReadLock(ctx, lock)
	default:
		err = ErrInvalidLockType
	}

	if err != nil {
		// 清理字段，防止数据残留
		lock.Resource = ""
		lock.LockID = ""
		lock.TTL = 0
		lock.Type = 0
		l.pool.Put(lock)
		return nil, err
	}
	return lock, nil
}

func (l *RedisRWLocker) acquireWriteLock(ctx context.Context, lock *RedisRWLock) error {
	readKey := fmt.Sprintf("rwlock:%s:read", lock.Resource)
	writeKey := fmt.Sprintf("rwlock:%s:write", lock.Resource)

	lua := `
		redis.call("ZREMRANGEBYSCORE", KEYS[1], "-inf", ARGV[3])
		if (redis.call("ZCARD", KEYS[1]) == 0) and (redis.call("EXISTS", KEYS[2]) == 0) then
			return redis.call("SET", KEYS[2], ARGV[1], "PX", ARGV[2], "NX") and 1 or 0
		else
			return 0
		end`

	expireAt := time.Now().Add(lock.TTL).UnixMilli()
	res, err := l.rdb.Eval(ctx, lua, []string{readKey, writeKey}, lock.LockID, lock.TTL.Milliseconds(), expireAt).Int()
	if err != nil {
		return err
	}
	if res == 0 {
		return ErrLockNotObtained
	}
	return nil
}

func (l *RedisRWLocker) acquireReadLock(ctx context.Context, lock *RedisRWLock) error {
	readKey := fmt.Sprintf("rwlock:%s:read", lock.Resource)
	writeKey := fmt.Sprintf("rwlock:%s:write", lock.Resource)

	lua := `
		redis.call("ZREMRANGEBYSCORE", KEYS[1], "-inf", ARGV[2])
		if redis.call("EXISTS", KEYS[2]) == 0 then
			redis.call("ZADD", KEYS[1], ARGV[2], ARGV[1])
			return 1
		else
			return 0
		end`

	expireAt := time.Now().Add(lock.TTL).UnixMilli()
	res, err := l.rdb.Eval(ctx, lua, []string{readKey, writeKey}, lock.LockID, expireAt).Int()
	if err != nil {
		return err
	}
	if res == 0 {
		return ErrLockNotObtained
	}
	return nil
}

// Unlock 解锁，释放锁并放回对象池
func (l *RedisRWLock) Unlock(ctx context.Context) error {
	if l.locker == nil || l.Resource == "" || l.LockID == "" {
		return errors.New("lock already released or not initialized")
	}

	readKey := fmt.Sprintf("rwlock:%s:read", l.Resource)
	writeKey := fmt.Sprintf("rwlock:%s:write", l.Resource)

	var err error
	switch l.Type {
	case WriteLock:
		lua := `
			if redis.call("GET", KEYS[1]) == ARGV[1] then
				return redis.call("DEL", KEYS[1])
			else
				return 0
			end`
		_, err = l.locker.rdb.Eval(ctx, lua, []string{writeKey}, l.LockID).Result()

	case ReadLock:
		lua := `return redis.call("ZREM", KEYS[1], ARGV[1])`
		_, err = l.locker.rdb.Eval(ctx, lua, []string{readKey}, l.LockID).Result()

	default:
		return ErrInvalidLockType
	}

	// 清理字段，防止数据残留
	l.Resource = ""
	l.LockID = ""
	l.TTL = 0
	l.Type = 0

	l.locker.pool.Put(l)

	return err
}

func (l *RedisRWLocker) TryLock(ctx context.Context, resource string, ttl time.Duration, lockType RedisLockType) (*RedisRWLock, error) {
	lockID := uuid.Must(uuid.NewV4()).String()
	backoff := 100 * time.Millisecond
	maxWait := 2 * time.Second
	return l.TryLockWithRetry(ctx, resource, lockID, ttl, lockType, maxWait, backoff)
}

func (l *RedisRWLocker) TryReadLock(ctx context.Context, resource string, ttl time.Duration) (*RedisRWLock, error) {
	return l.TryLock(ctx, resource, ttl, ReadLock)
}

func (l *RedisRWLocker) TryWriteLock(ctx context.Context, resource string, ttl time.Duration) (*RedisRWLock, error) {
	return l.TryLock(ctx, resource, ttl, WriteLock)
}

// TryLockWithRetry 尝试加锁，失败后按 backoff 重试，
// 最多等待 maxWait 时间，超过返回 ErrLockNotObtained。
func (l *RedisRWLocker) TryLockWithRetry(
	ctx context.Context,
	resource string,
	lockID string,
	ttl time.Duration,
	lockType RedisLockType,
	maxWait time.Duration,
	backoff time.Duration,
) (*RedisRWLock, error) {
	deadline := time.Now().Add(maxWait)

	for {
		lock, err := l.Lock(ctx, resource, lockID, ttl, lockType)
		if err == nil {
			return lock, nil
		}

		if !errors.Is(err, ErrLockNotObtained) {
			// 非抢锁失败的其他错误直接返回
			return nil, err
		}

		if time.Now().After(deadline) {
			return nil, ErrLockNotObtained
		}

		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(backoff):
		}
	}
}
