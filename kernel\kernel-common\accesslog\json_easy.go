// Code generated by easyj<PERSON> but it's highly edited. Please manually add fields as Log is grow,
// in-short: all decode methods removed, rename of the methods, add a new line breaker,
// remove the easyjson import requirement.

package accesslog

import (
	"github.com/mailru/easyjson/jwriter"
)

func (f *JSON) writeEasyJSON(in *Log) error {
	out := &jwriter.Writer{NoEscapeHTML: !f.EscapeHTML}

	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"timestamp\":"
		if first {
			first = false
			out.RawString(prefix[1:])
		} else {
			out.RawString(prefix)
		}

		if f.HumanTime {
			t := in.Now.Format(in.TimeFormat)
			out.String(t)
		} else {
			out.Int64(in.Timestamp)
		}
	}
	{
		const prefix string = ",\"latency\":"
		out.RawString(prefix)
		out.Int64(int64(in.Latency))
	}
	{
		const prefix string = ",\"code\":"
		out.RawString(prefix)
		out.Int(int(in.Code))
	}
	{
		const prefix string = ",\"method\":"
		out.RawString(prefix)
		out.String(in.Method)
	}
	{
		const prefix string = ",\"path\":"
		out.RawString(prefix)
		out.String(in.Path)
	}
	if in.IP != "" {
		const prefix string = ",\"ip\":"
		out.RawString(prefix)
		out.String(in.IP)
	}
	if in.Query != nil {
		const prefix string = ",\"query\":"
		out.RawString(prefix)
		{
			out.RawByte('[')
			out.String(in.Query.Encode())
			out.RawByte(']')
		}
	}
	if in.PathParams != nil {
		const prefix string = ",\"params\":"
		out.RawString(prefix)
		{
			out.RawByte('[')
			out.String(in.PathParams.Encode())
			out.RawByte(']')
		}
	}
	if in.Fields != nil {
		const prefix string = ",\"fields\":"
		out.RawString(prefix)
		{
			out.RawByte('[')
			out.String(in.Fields.Encode())
			out.RawByte(']')
		}
	}
	if in.Logger.RequestBody {
		const prefix string = ",\"request\":"
		out.RawString(prefix)
		out.String(string(in.Request))
	}
	if in.Logger.ResponseBody {

		const prefix string = ",\"response\":"
		out.RawString(prefix)
		out.String(string(in.Response))

	}
	if in.BytesReceived != 0 {
		const prefix string = ",\"bytes_received\":"
		out.RawString(prefix)
		out.Int(int(in.BytesReceived))
	}
	if in.BytesSent != 0 {
		const prefix string = ",\"bytes_sent\":"
		out.RawString(prefix)
		out.Int(int(in.BytesSent))
	}
	out.RawByte('}')
	out.RawByte(newLine)

	if out.Error != nil {
		return out.Error
	}
	f.ac.Write(out.Buffer.BuildBytes())
	return nil
}
