package graceful

import (
	"fmt"
	"log"
	"net"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"
)

const (
	workerStopSignal   = syscall.SIGTERM
	workerReloadSignal = syscall.SIGHUP
)

type worker struct {
	opt       *option
	stopCh    chan struct{}
	listeners []net.Listener
	services  []*service
}

func (w *worker) run() error {
	err := w.initListeners()
	if err != nil {
		return err
	}

	runResultChan := make(chan struct{})
	isreload := false
	oldWorkerPid, err := strconv.Atoi(os.Getenv(EnvOldWorkerPid))
	if err != nil {
		return err
	}
	if oldWorkerPid > 0 {
		isreload = true
	}
	go w.startServers(runResultChan, isreload)
	<-runResultChan

	if oldWorkerPid > 1 {
		time.Sleep(time.Millisecond * 1)
		//err = syscall.Kill(oldWorkerPid, workerStopSignal)
		p, err := os.FindProcess(oldWorkerPid)
		if err != nil {
			return err
		}
		err = p.Signal(workerReloadSignal)
		if err != nil {
			log.Printf("[warning]kill old worker error: %v", err)
		}
	}

	go w.watchMaster()
	w.waitSignal()
	time.Sleep(time.Millisecond * 10)
	return nil
}

func (w *worker) initListeners() error {
	for i := 0; i < len(w.services); i++ {
		f := os.NewFile(uintptr(3+i), "")
		l, err := net.FileListener(f)
		if err != nil {
			return err
		}
		w.listeners = append(w.listeners, newListener(l.(*net.TCPListener), w.opt.enableConnectionLimit, w.opt.maxConnectionNumber))
	}
	return nil
}

func (w *worker) startServers(c chan struct{}, isreload bool) {
	for i, l := range w.listeners {
		err := w.services[i].startFunc(l, c, isreload)
		if err != nil {
			log.Printf("[warning]worker <%d> start service error: %v, service is %s", syscall.Getpid(), err, w.services[i].addr)
		}
	}
}

func (w *worker) watchMaster() {
	for {
		// if parent id change to 1, it means parent is dead
		if os.Getppid() == 1 {
			log.Printf("[warning] master dead, stop worker <%d>\n", syscall.Getpid())
			w.stop(false)
			break
		}
		time.Sleep(w.opt.watchInterval)
	}
	w.stopCh <- struct{}{}
}

func (w *worker) stop(isreload bool) {
	for _, s := range w.services {
		err := s.shutdownFunc(isreload)
		if err != nil {
			fmt.Printf("[warning]shutdown server error: %s", err)
		}
	}
}

func (w *worker) waitSignal() {
	ch := make(chan os.Signal, 1)
	signal.Notify(ch, workerStopSignal, workerReloadSignal)
	select {
	case sig := <-ch:
		log.Printf("[info]worker <%d> got signal: %v\n", syscall.Getpid(), sig)
		if sig == workerStopSignal {
			w.stop(false)
		} else if sig == workerReloadSignal {
			w.stop(true)
		}
	case <-w.stopCh:
		log.Printf("[info]stop worker: %d", syscall.Getpid())
		w.stop(false)
	}
}
