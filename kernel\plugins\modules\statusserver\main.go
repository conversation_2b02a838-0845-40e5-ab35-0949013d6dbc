package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"os"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap/zapcore"
)

type StatusServerModule struct {
	name                 string
	logger               runtime.Logger
	db                   *sql.DB
	nk                   runtime.NakamaModule
	config               runtime.Config // nakama 配置
	CustomConfig         *CustomConfig  // 自定义配置
	isRegisterSelf       bool
	controller           *Controller
	statusBuckets        *StatusBucketList
	statusConsumerCancel context.CancelFunc

	common  *logic.CommonGlobalDataStruct
	online  *logic.OnlineGlobalDataStruct
	send    *logic.SendGlobalDataStruct
	dbagent *logic.DbAgentGlobalDataStruct
	notify  *logic.NotifyGlobalDataStruct
}

var StatusServerData *StatusServerModule

func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
	StatusServerData = new(StatusServerModule)
	StatusServerData.name = models.SERVER_NAME_STATUS
	StatusServerData.logger = logger
	StatusServerData.db = db
	StatusServerData.nk = nk
	StatusServerData.common = logic.NewCommonGlobalDataStruct()
	StatusServerData.send = logic.NewSendGlobalDataStruct(StatusServerData)
	StatusServerData.dbagent = logic.NewDbAgentGlobalDataStruct(StatusServerData)
	StatusServerData.online = logic.NewOnlineGlobalDataStruct(StatusServerData)
	StatusServerData.notify = logic.NewNotifyGlobalDataStruct(StatusServerData)
	config, err := initializer.GetConfig()
	if err != nil {
		return err
	}
	StatusServerData.config = config
	if err := StatusServerData.common.Init(StatusServerData, StatusServerData.CustomConfig, StatusServerData.customConfigEvent); err != nil {
		logger.Error("init common logic error %v", err)
		return err
	}

	initializer.RegisterShutdown(StatusServerData.Shutdown)

	// 自定义路由注册
	StatusServerData.controller = NewController(StatusServerData)
	initializer.RegisterRpc(models.RPCID_STATUSSERVER_QUERYBYUID, StatusServerData.controller.QueryByUid)
	initializer.RegisterRpc(models.RPCID_STATUSSERVER_QUERYBYUIN, StatusServerData.controller.QueryByUin)
	initializer.RegisterRpc(models.RPCID_STATUSSERVER_QUERYONLINEBYUID, StatusServerData.controller.QueryOnlineByUid)
	initializer.RegisterRpc(models.RPCID_STATUSSERVER_QUERYONLINEBYUIN, StatusServerData.controller.QueryOnlineByUin)

	// 初始化状态桶
	StatusServerData.statusBuckets = NewStatusBucketList(1000)

	// 从redis队列消费数据
	go StatusServerData.consumeStatusData()

	return nil
}

func (s *StatusServerModule) GetCommon() *logic.CommonGlobalDataStruct {
	return s.common
}
func (s *StatusServerModule) GetOnline() *logic.OnlineGlobalDataStruct {
	return s.online
}
func (s *StatusServerModule) GetSend() *logic.SendGlobalDataStruct {
	return s.send
}
func (s *StatusServerModule) GetDbAgent() *logic.DbAgentGlobalDataStruct {
	return s.dbagent
}
func (s *StatusServerModule) GetNotify() *logic.NotifyGlobalDataStruct {
	return s.notify
}
func (s *StatusServerModule) GetStream() *logic.StreamGlobalDataStruct {
	return nil
}
func (s *StatusServerModule) GetChannel() *logic.ChannelGlobalDataStruct {
	return nil
}
func (s *StatusServerModule) GetLogger() runtime.Logger {
	return s.logger
}
func (s *StatusServerModule) GetConfig() runtime.Config {
	return s.config
}
func (s *StatusServerModule) GetName() string {
	return s.name
}
func (s *StatusServerModule) GetDb() *sql.DB {
	return s.db
}
func (s *StatusServerModule) GetNk() runtime.NakamaModule {
	return s.nk
}

// 配置读取回调
func (s *StatusServerModule) customConfigEvent(dataId string, newobj any) {
	tmpobj, ok := newobj.(*CustomConfig)
	if !ok {
		return
	}
	if tmpobj.Config == nil {
		tmpobj.Config = &StatusServerConfig{}
	}
	defaultNodeCfg := false
	if tmpobj.Config.NodeConfig == nil {
		// 使用默认节点配置
		scheme := "http"
		if s.config.GetSocket().GetUseTLS() {
			scheme = "https"
		}
		tmpobj.Config.NodeConfig = &models.NodeConfig{
			NodeId:   s.config.GetName(),
			SvcGroup: fmt.Sprintf("%s@@%s", s.name, models.NACOS_DEFAULT_GROUP),
			Host:     fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			NetHost:  fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			AuthKey:  s.config.GetRuntime().GetHTTPKey(),
			Weight:   10,
			Scheme:   scheme,
		}
		defaultNodeCfg = true
	}
	tmp, _ := json.Marshal(tmpobj)
	s.logger.Debug("customConfigEvent defaultNodeCfg: %v, customCfg: %s", defaultNodeCfg, string(tmp))
	s.common.SetNodeConfig(tmpobj.Config.NodeConfig)
	s.common.WatchNodeList.InitWatchSvr(tmpobj.Config.WatchSvcGroupList, tmpobj.Config.NodeConfig.SvcGroup)
	s.CustomConfig = tmpobj

	// 注册服务
	selfcfg := map[string]interface{}{}
	if err := common.StrcutToMap(tmpobj.Config.NodeConfig, &selfcfg); err == nil {
		if !s.isRegisterSelf {
			go s.common.RegisterAndUpdateSelfToNacos(false, selfcfg)
			s.isRegisterSelf = true
		} else {
			go s.common.RegisterAndUpdateSelfToNacos(true, selfcfg)
		}
	} else {
		s.logger.Error("register self, config error %v", err)
	}

	s.common.UpdateAccessLogConfig(tmpobj.AccessLog)
	loglevel, err := zapcore.ParseLevel(tmpobj.Logger.Level)
	if err != nil {
		s.logger.Error("parse log level error %v", err)
	} else {
		s.nk.SetLoggerLevel(loglevel)
	}
}

func (s *StatusServerModule) Shutdown(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, reload bool) {
	logger.Info("%s shutdown", s.name)
	if !reload {
		s.common.UnRegisterSelfToNacos()
	}
	if s.statusConsumerCancel != nil {
		s.statusConsumerCancel()
	}
}

// 从redis消费玩家状态数据，广播模式消费
func (s *StatusServerModule) consumeStatusData() {
	for i := 0; i < 100; i++ {
		if s.CustomConfig != nil {
			break
		}
		time.Sleep(100 * time.Millisecond)
	}
	if s.CustomConfig == nil {
		s.logger.Error("cannot start status server: config not loaded")
		os.Exit(1)
		return
	}

	rdb := s.common.Redis
	consumerGroup := s.CustomConfig.Config.NodeConfig.NodeId
	ctx, cancel := context.WithCancel(context.Background())
	s.statusConsumerCancel = cancel

	// 创建消费者组，如果已存在则忽略错误
	err := rdb.XGroupCreateMkStream(context.Background(), logic.PLAYERS_EVENT_STREAM, consumerGroup, "0").Err()
	if err != nil && !strings.Contains(err.Error(), "BUSYGROUP") {
		s.logger.Error("create consumer group failed: %v", err)
	}

	// 首先从头开始读取所有历史消息，恢复状态
	s.restoreStatusFromHistory()

	// 启动多个消费者协程处理新消息
	for i := 0; i < 4; i++ {
		go func(workerID int) {
			consumerName := fmt.Sprintf("%s-worker-%d", consumerGroup, workerID)

			for {
				select {
				case <-ctx.Done():
					return // 优雅退出
				default:
				}

				streams, err := rdb.XReadGroup(ctx, &redis.XReadGroupArgs{
					Group:    consumerGroup,
					Consumer: consumerName,
					Streams:  []string{logic.PLAYERS_EVENT_STREAM, ">"},
					Count:    100, // 每个工作线程处理较少的消息
					Block:    100 * time.Millisecond,
				}).Result()

				if (err != nil && err == redis.Nil) || len(streams) == 0 {
					continue
				}

				if err != nil {
					s.logger.Error("worker %d XReadGroup error: %v", workerID, err)
					continue
				}

				var ackIDs []string
				for _, str := range streams {
					for _, msg := range str.Messages {
						s.processStatusMessage(msg.Values, workerID)
						ackIDs = append(ackIDs, msg.ID)
					}
				}

				if len(ackIDs) > 0 {
					err := rdb.XAck(ctx, logic.PLAYERS_EVENT_STREAM, consumerGroup, ackIDs...).Err()
					if err != nil {
						s.logger.Error("worker %d XAck error: %v", workerID, err)
					}
				}
			}
		}(i)
	}

	// 注册服务
	selfcfg := map[string]interface{}{}
	if err := common.StrcutToMap(s.CustomConfig.Config.NodeConfig, &selfcfg); err == nil {
		s.common.RegisterAndUpdateSelfToNacos(false, selfcfg)
		s.isRegisterSelf = true
	} else {
		s.logger.Error("register self, config error %v", err)
	}

	// 重试未确认的消息
	s.retryPendingMessages(ctx, consumerGroup)
}

// 从历史消息中恢复状态
func (s *StatusServerModule) restoreStatusFromHistory() {
	rdb := s.common.Redis
	ctx := context.Background()

	s.logger.Info("start restore status from history")

	// 从流的开始位置读取所有历史消息
	var lastID string = "0"
	batchSize := 5000
	failCount := 0
	startTime := time.Now()
	totalProcessed := 0

	for {
		messages, err := rdb.XRead(ctx, &redis.XReadArgs{
			Streams: []string{logic.PLAYERS_EVENT_STREAM, lastID},
			Count:   int64(batchSize),
			Block:   100 * time.Millisecond,
		}).Result()

		if (err != nil && err == redis.Nil) || len(messages) == 0 {
			s.logger.Info("no more history message, status restore done")
			break
		}

		if err != nil {
			s.logger.Error("read history message error: %v", err)
			failCount++
			if failCount > 10 {
				s.logger.Error("read history message failed too many times, status restore done")
				break
			}
			time.Sleep(1 * time.Second)
			continue
		}

		msgCount := len(messages[0].Messages)
		s.logger.Debug("process history message batch, count: %d", msgCount)
		totalProcessed += msgCount

		// 处理这批消息
		for _, msg := range messages[0].Messages {
			s.processStatusMessage(msg.Values, -1) // -1 表示这是恢复过程
			lastID = msg.ID
		}

		// 如果消息数量小于批次大小，说明已经读完了
		if msgCount < batchSize {
			break
		}
	}

	s.logger.Info("status data restore done, cost time: %dms, total processed: %d", time.Since(startTime).Milliseconds(), totalProcessed)
}

// 重试未确认的消息
func (s *StatusServerModule) retryPendingMessages(ctx context.Context, consumerGroup string) {
	rdb := s.common.Redis
	nodeId := s.CustomConfig.Config.NodeConfig.NodeId
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	const (
		idleThreshold    = 30 * time.Second
		maxRetryAttempts = 5
		claimBatchSize   = 100
	)

	for {
		select {
		case <-ctx.Done():
			return // 优雅退出
		case <-ticker.C:
			// 获取 pending 消息列表
			pendingList, err := rdb.XPendingExt(ctx, &redis.XPendingExtArgs{
				Stream: logic.PLAYERS_EVENT_STREAM,
				Group:  consumerGroup,
				Idle:   idleThreshold,
				Start:  "-",
				End:    "+",
				Count:  claimBatchSize,
			}).Result()

			if err != nil && err != redis.Nil {
				s.logger.Error("retryPendingMessages XPendingExt error: %v", err)
				continue
			}
			if len(pendingList) == 0 {
				continue
			}

			s.logger.Debug("found %d pending messages need to retry", len(pendingList))

			// 过滤出需要重试的消息
			var retryIDs []string
			var forceAckIDs []string
			for _, entry := range pendingList {
				if entry.RetryCount >= maxRetryAttempts {
					forceAckIDs = append(forceAckIDs, entry.ID)
				} else {
					retryIDs = append(retryIDs, entry.ID)
				}
			}

			// 执行 XCLAIM 批量拉回
			if len(retryIDs) > 0 {
				msgs, err := rdb.XClaim(ctx, &redis.XClaimArgs{
					Stream:   logic.PLAYERS_EVENT_STREAM,
					Group:    consumerGroup,
					Consumer: fmt.Sprintf("retry-consumer-%s", nodeId),
					MinIdle:  idleThreshold,
					Messages: retryIDs,
				}).Result()

				if err != nil && err != redis.Nil {
					s.logger.Error("retryPendingMessages XClaim error: %v", err)
				}

				// 处理成功拉取的消息
				var ackIDs []string
				claimedSet := make(map[string]struct{})
				for _, msg := range msgs {
					s.processStatusMessage(msg.Values, -2) // -2 表示重试流程
					ackIDs = append(ackIDs, msg.ID)
					claimedSet[msg.ID] = struct{}{}
				}

				// 未 claim 到的消息，强制 ack 掉
				for _, id := range retryIDs {
					if _, ok := claimedSet[id]; !ok {
						forceAckIDs = append(forceAckIDs, id)
					}
				}

				if len(ackIDs) > 0 {
					err := rdb.XAck(ctx, logic.PLAYERS_EVENT_STREAM, consumerGroup, ackIDs...).Err()
					if err != nil {
						s.logger.Error("retryPendingMessages XAck error: %v", err)
					}
				}
			}

			// 强制 ack（比如死信、重复 claim 失败等）
			if len(forceAckIDs) > 0 {
				s.logger.Warn("force ACK %d messages (retry limit reached or not claimable)", len(forceAckIDs))
				err := rdb.XAck(ctx, logic.PLAYERS_EVENT_STREAM, consumerGroup, forceAckIDs...).Err()
				if err != nil {
					s.logger.Error("retryPendingMessages force ACK error: %v", err)
				}
			}
		}
	}
}

// 处理状态消息
func (s *StatusServerModule) processStatusMessage(values map[string]interface{}, workerID int) {
	if values == nil {
		s.logger.Error("worker %d receive empty message", workerID)
		return
	}

	event, ok := values["event"].(string)
	if !ok {
		s.logger.Error("worker %d invalid event type", workerID)
		return
	}

	switch event {
	case "online":
		if dataStr, ok := values["data"].(string); ok {
			onlineInfo := &models.UserOnlineInfo{}
			if err := json.Unmarshal([]byte(dataStr), onlineInfo); err != nil {
				s.logger.Error("worker %d parse online data failed: %v", workerID, err)
				return
			}

			// 验证必要字段
			if onlineInfo.UserId == "" || onlineInfo.Uin == 0 {
				s.logger.Error("worker %d online data missing required fields: %+v", workerID, onlineInfo)
				return
			}

			s.statusBuckets.Add(onlineInfo)
		} else {
			s.logger.Error("worker %d online event missing data field", workerID)
		}
	case "offline":
		uid := common.Interface2String(values["user_id"])
		uin := common.Interface2Int64(values["uin"])

		if uid == "" || uin == 0 {
			s.logger.Error("worker %d offline event missing required fields: user_id=%s, uin=%d", workerID, uid, uin)
			return
		}

		s.statusBuckets.Remove(uid, uin)
	default:
		s.logger.Error("worker %d unknown event type: %v", workerID, event)
	}
}
