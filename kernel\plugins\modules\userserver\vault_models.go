package main

import "kernel/plugins/models"

// 物品列表包含 { slot_id, item_id, count, bind_status, expire_timestamp? }
type VaultItem struct {
	SlotId     int64 `json:"slot_id,omitempty"`
	ItemId     int64 `json:"item_id,omitempty"`
	Count      int64 `json:"count,omitempty"`
	BindStatus int64 `json:"bind_status,omitempty"`
	ExpireTime int64 `json:"expire_time,omitempty"`
}

// 仓库列表返回
type RespVaultsList struct {
	Code    int64  `json:"code"`
	Message string `json:"message,omitempty"`
	Data    string `json:"data,omitempty"` // pb string
}

// 存入仓库请求
type ReqVaultsDeposit struct {
	Items []*VaultItem `json:"items,omitempty"`
}

// 存入仓库返回
type RespVaultsDeposit struct {
	Code  models.ErrorCode `json:"code,omitempty"`
	Msg   string           `json:"message,omitempty"`
	Items []*VaultItem     `json:"items,omitempty"`
}

// 提取仓库请求
type ReqVaultsExtract struct {
	Items []*VaultItem `json:"items,omitempty"`
}

// 提取仓库返回
type RespVaultsExtract struct {
	Code  models.ErrorCode `json:"code,omitempty"`
	Msg   string           `json:"message,omitempty"`
	Items []*VaultItem     `json:"items,omitempty"`
}

// 仓库扩容
type ReqVaultsExpand struct {
	Count int64 `json:"count,omitempty"`
}

// 仓库扩容返回
type RespVaultsExpand struct {
	Code     models.ErrorCode `json:"code,omitempty"`
	Msg      string           `json:"message,omitempty"`
	Capacity int64            `json:"capacity,omitempty"`
}

// 删除仓库请求
type ReqVaultsDelete struct {
	Items []*VaultItem `json:"items,omitempty"`
}

// 删除仓库返回
type RespVaultsDelete struct {
	Code models.ErrorCode `json:"code,omitempty"`
	Msg  string           `json:"message,omitempty"`
}
