package main

type Mount struct {
	MountId    int64 `json:"mount_id,omitempty"`
	ExpireTime int64 `json:"expire_time,omitempty"`
}

type RespMountList struct {
	Code    int64  `json:"code"`
	Message string `json:"message,omitempty"`
	Data    string `json:"data,omitempty"` // pb string
}

type ReqMountOpt struct {
	UserId string `json:"user_id,omitempty"`
	Mount  *Mount `json:"mount,omitempty"`
}

type RespMountQuery struct {
	Code    int64  `json:"code"`
	Message string `json:"message,omitempty"`
	Data    *Mount `json:"data,omitempty"`
}

type RespMountUpdate struct {
	Code    int64  `json:"code"`
	Message string `json:"message,omitempty"`
	Data    *Mount `json:"data,omitempty"`
}
