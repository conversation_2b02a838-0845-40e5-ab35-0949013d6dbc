#!/bin/bash

services=()
hname=`hostname`
cd `dirname $0`
svrtypes=(gateserver logicserver dbagentserver sessionserver statusserver)

#获取所有本地配置文件
filelist=`find ./data/ -maxdepth 1 -name "*.yaml"`
for file in $filelist; do
    services[${#services[@]}]=$(basename $file .yaml)
done

if [ $# -lt 1 ];
then
    echo services_cfglist: ${services[*]}
	echo './exec.sh [cmd start|stop|startall|stopall|startmore|stopmore|restart|restartmore|restartall|reload|reloadmore|reloadall|notrunstart|getpid|forceall] [cfg name|cfg type]'
	echo './exec.sh start gateserver101      说明: 配置文件不用携带.yaml 的后缀'
	exit
fi

chmod 0777 ./kernel
ulimit -n 65535
sysctl -w net.core.rmem_max=2500000 >/dev/null 2>&1
GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn
GOTRACEBACK=crash

if [ $1 == start ] ;then
    if [ $# -lt 2 ];
    then
        echo './exec.sh [cmd start|stop|startall|stopall|startmore|stopmore|restart|restartmore|restartall|reload|reloadmore|reloadall|notrunstart|getpid|forceall] [cfg name|cfg type]'
        echo './exec.sh start gateserver101      说明: 配置文件不用携带.yaml 的后缀'
        exit
    fi
	nohup ./kernel -config ./data/$2.yaml >> log/$2.log &
elif [ $1 == stop ] ;then
	#ps -ef|grep $2.json|grep -v grep |awk '{print $2}'|xargs kill -15
	pid=`ps -ef|grep $2.yaml|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'`
    if [ "$pid" != "" ] ;then
        echo "stop $2 $pid"
        ps -ef|grep $2.yaml|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'|xargs kill -2
    fi
    # 查询是否存在
    pid=`ps -ef|grep $2.yaml|grep kernel|grep -v grep |awk '{print $2}'`
    findnum=0
    while [ "$pid" != "" ]
    do
        sleep 0.05  #50ms
        pid=`ps -ef|grep $2.yaml|grep kernel|grep -v grep |awk '{print $2}'`
        findnum=$(($findnum+1))
        if [ $findnum -ge 200 ];
        then
            ps -ef|grep $2.yaml|grep kernel|grep -v grep |awk '{print $2}'|xargs kill -9
        fi
    done
elif [ $1 == startall ] ;then
	for(( i=0;i<${#services[@]};i++)) do
        nohup ./kernel -config ./data/${services[i]}.yaml >> log/${services[i]}.log &
    done;
elif [ $1 == stopall ] ;then
	for(( i=0;i<${#services[@]};i++)) do
        pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'`
        if [ "$pid" != "" ] ;then
            echo "stop ${services[i]} $pid"
            ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'|xargs kill -2
        fi
	done;
	
	# 检查所有服务是否已停止，如果超时则强制终止
	for(( i=0;i<${#services[@]};i++)) do
		# 查询是否存在
		pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v grep |awk '{print $2}'`
		findnum=0
		while [ "$pid" != "" ]
		do
			sleep 0.05  #50ms
			pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v grep |awk '{print $2}'`
			findnum=$(($findnum+1))
			if [ $findnum -ge 200 ];
			then
				ps -ef|grep ${services[i]}|grep kernel|grep -v grep |awk '{print $2}'|xargs kill -9
			fi
		done
	done;
elif [ $1 == startmore ] ;then
    isfind=no
    for(( i=0;i<${#svrtypes[@]};i++)) do
        if [[ "${svrtypes[i]}" == $2 ]] ;then
            isfind=yes
            break
        fi
    done;
    if [[ "$isfind" != "yes" ]] ;then
        exit
    fi
	for(( i=0;i<${#services[@]};i++)) do
	    if [[ ${services[i]} == $2* ]] ;then
            nohup ./kernel -config ./data/${services[i]}.yaml >> log/${services[i]}.log &
        fi
    done;
elif [ $1 == stopmore ] ;then
    isfind=no
    for(( i=0;i<${#svrtypes[@]};i++)) do
        if [[ "${svrtypes[i]}" == $2 ]] ;then
            isfind=yes
            break
        fi
    done;
    if [[ "$isfind" != "yes" ]] ;then
        exit
    fi
    for(( i=0;i<${#services[@]};i++)) do
	    if [[ ${services[i]} == $2* ]] ;then
            pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'`
            if [ "$pid" != "" ] ;then
                echo "stop ${services[i]} $pid"
                ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'|xargs kill -2
            fi
        fi
    done;
	for(( i=0;i<${#services[@]};i++)) do
	    if [[ ${services[i]} == $2* ]] ;then
	        # 查询是否存在
            pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v grep |awk '{print $2}'`
            findnum=0
            while [ "$pid" != "" ]
            do
                sleep 0.05  #50ms
                pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v grep |awk '{print $2}'`
                findnum=$(($findnum+1))
                if [ $findnum -ge 200 ];
                then
                    ps -ef|grep ${services[i]}|grep kernel|grep -v grep |awk '{print $2}'|xargs kill -9
                fi
            done
        fi
    done;
elif [ $1 == restart ] ;then
    if [ $# -lt 2 ];
    then
        echo './exec.sh [cmd start|stop|startall|stopall|startmore|stopmore|restart|restartmore|restartall|notrunstart|getpid|forceall] [cfg name|cfg type]'
        echo './exec.sh stop central701      说明: 配置文件不用携带.yaml 的后缀'
        exit
    fi

    # 查询是否存在
	pid=`ps -ef|grep $2.yaml|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'`
	if [ "$pid" != "" ] ;then
	    echo "stop $2 $pid"
	    #stop
	    ps -ef|grep $2.yaml|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'|xargs kill -2
	fi

	findnum=0
	while [ "$pid" != "" ]
	do
        sleep 0.05  #50ms
        pid=`ps -ef|grep $2.yaml|grep kernel|grep -v grep |awk '{print $2}'`
        findnum=$(($findnum+1))
        if [ $findnum -ge 200 ];
        then
            ps -ef|grep $2.yaml|grep kernel|grep -v grep |awk '{print $2}'|xargs kill -9
        fi
    done
	#start
	nohup ./kernel -config ./data/$2.yaml >> log/$2.log &
elif [ $1 == restartmore ] ;then
    isfind=no
    for(( i=0;i<${#svrtypes[@]};i++)) do
        if [[ "${svrtypes[i]}" == $2 ]] ;then
            isfind=yes
            break
        fi
    done;
    if [[ "$isfind" != "yes" ]] ;then
        exit
    fi
    for(( i=0;i<${#services[@]};i++)) do
	    if [[ ${services[i]} == $2* ]] ;then
            pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'`
            if [ "$pid" != "" ] ;then
                echo "stop ${services[i]} $pid"
                ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'|xargs kill -2
            fi
        fi
    done;
	for(( i=0;i<${#services[@]};i++)) do
	    if [[ ${services[i]} == $2* ]] ;then
	        # 查询是否存在
            pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v grep |awk '{print $2}'`
            findnum=0
            while [ "$pid" != "" ]
            do
                sleep 0.05  #50ms
                pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v grep |awk '{print $2}'`
                findnum=$(($findnum+1))
                if [ $findnum -ge 200 ];
                then
                    ps -ef|grep ${services[i]}|grep kernel|grep -v grep |awk '{print $2}'|xargs kill -9
                fi
            done
        fi
    done;
    for(( i=0;i<${#services[@]};i++)) do
	    if [[ ${services[i]} == $2* ]] ;then
            nohup ./kernel -config ./data/${services[i]}.yaml >> log/${services[i]}.log &
        fi
    done;
elif [ $1 == restartall ] ;then    
    # 逐个停止并启动服务
    for(( i=0;i<${#services[@]};i++)) do
        pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'`
        if [ "$pid" != "" ] ;then
            echo "stop ${services[i]} $pid"
            ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'|xargs kill -2
            
            # 等待服务停止
            findnum=0
            while [ "$pid" != "" ]
            do
                sleep 0.05  #50ms
                pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v grep |awk '{print $2}'`
                findnum=$(($findnum+1))
                if [ $findnum -ge 200 ];
                then
                    ps -ef|grep ${services[i]}|grep kernel|grep -v grep |awk '{print $2}'|xargs kill -9
                    break
                fi
            done
            
            # 立即启动该服务
            echo "start ${services[i]}"
            nohup ./kernel -config ./data/${services[i]}.yaml >> log/${services[i]}.log &
        else
            # 如果服务未运行，直接启动
            echo "start ${services[i]}"
            nohup ./kernel -config ./data/${services[i]}.yaml >> log/${services[i]}.log &
        fi
    done;
elif [ $1 == reload ] ;then
if [ $# -lt 2 ];
    then
        echo './exec.sh [cmd start|stop|startall|stopall|startmore|stopmore|restart|restartmore|restartall|reload|reloadmore|reloadall|notrunstart|getpid|forceall] [cfg name|cfg type]'
        echo './exec.sh stop central701      说明: 配置文件不用携带.yaml 的后缀'
        exit
    fi

    pid=`ps -ef|grep $2|grep kernel|grep worker|grep -v grep|awk '{print $2}'`
    if [ "$pid" != "" ] ;then
	    pid=`ps -ef|grep $2|grep kernel|grep -v worker|grep -v grep|awk '{print $2}'`
        if [ "$pid" != "" ] ;then
            echo "reload $2 $pid"
            ps -ef|grep $2|grep kernel|grep -v worker|grep -v grep|awk '{print $2}'|xargs kill -1
        else
            echo "nothing to do"
        fi
    else
        # 查询是否存在
        pid=`ps -ef|grep $2|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'`
        if [ "$pid" != "" ] ;then
            echo "restart-stop $2 $pid"
            #stop
            ps -ef|grep $2|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'|xargs kill -2
        fi

        findnum=0
        while [ "$pid" != "" ]
        do
            sleep 0.05  #50ms
            pid=`ps -ef|grep $2|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'`
            findnum=$(($findnum+1))
            if [ $findnum -ge 200 ];
            then
                ps -ef|grep $2|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'|xargs kill -9
            fi
        done

        nohup ./kernel -config ./data/$2.yaml >> log/$2.log &
    fi
elif [ $1 == reloadmore ] ;then
    if [ $# -lt 2 ];
    then
        echo './exec.sh [cmd start|stop|startall|stopall|startmore|stopmore|restart|restartmore|restartall|reload|reloadmore|reloadall|notrunstart|getpid|forceall] [cfg name|cfg type]'
        echo './exec.sh stop central701      说明: 配置文件不用携带.yaml 的后缀'
        exit
    fi

    isfind=no
    for(( i=0;i<${#svrtypes[@]};i++)) do
        if [[ "${svrtypes[i]}" == $2 ]] ;then
            isfind=yes
            break
        fi
    done;
    if [[ "$isfind" != "yes" ]] ;then
        exit
    fi
    for(( i=0;i<${#services[@]};i++)) do
	    if [[ ${services[i]} == $2* ]] ;then
            pid=`ps -ef|grep ${services[i]}|grep kernel|grep worker|grep -v grep|awk '{print $2}'`
            if [ "$pid" != "" ] ;then
                pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep|awk '{print $2}'`
                if [ "$pid" != "" ] ;then
                    echo "reload ${services[i]} $pid"
                    ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep|awk '{print $2}'|xargs kill -1
                else
                    echo "nothing to do"
                fi
            else
                # 查询是否存在
                pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'`
                if [ "$pid" != "" ] ;then
                    echo "restart-stop ${services[i]} $pid"
                    #stop
                    ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'|xargs kill -2
                fi

                findnum=0
                while [ "$pid" != "" ]
                do
                    sleep 0.05  #50ms
                    pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'`
                    findnum=$(($findnum+1))
                    if [ $findnum -ge 200 ];
                    then
                        ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'|xargs kill -9
                    fi
                done

                nohup ./kernel -config ./data/${services[i]}.yaml >> log/${services[i]}.log &
            fi
        fi
    done;

elif [ $1 == reloadall ] ;then
    for(( i=0;i<${#services[@]};i++)) do
        pid=`ps -ef|grep ${services[i]}|grep kernel|grep worker|grep -v grep|awk '{print $2}'`
        if [ "$pid" != "" ] ;then
            pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep|awk '{print $2}'`
            if [ "$pid" != "" ] ;then
                echo "reload ${services[i]} $pid"
                ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep|awk '{print $2}'|xargs kill -1
            else
                echo "nothing to do"
            fi
        else
            # 查询是否存在
            pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'`
            if [ "$pid" != "" ] ;then
                echo "restart-stop ${services[i]} $pid"
                #stop
                ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'|xargs kill -2
            fi

            findnum=0
            while [ "$pid" != "" ]
            do
                sleep 0.05  #50ms
                pid=`ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'`
                findnum=$(($findnum+1))
                if [ $findnum -ge 200 ];
                then
                    ps -ef|grep ${services[i]}|grep kernel|grep -v worker|grep -v grep |awk '{print $2}'|xargs kill -9
                fi
            done

            nohup ./kernel -config ./data/${services[i]}.yaml >> log/${services[i]}.log &
        fi
    done;
elif [ $1 == notrunstart ] ;then
    for(( i=0;i<${#services[@]};i++)) do
        # 查询是否存在
        pid=`ps -ef|grep ${services[i]}.yaml|grep kernel|grep -v grep |awk '{print $2}'`
        if [ "$pid" == "" ] ;then
            nohup ./kernel -config ./data/${services[i]}.yaml >> log/${services[i]}.log &
        fi
    done;
elif [ $1 == getpid ] ;then
	echo $2.yaml `ps -ef|grep $2.yaml|grep -v grep |awk '{print $2}'`
elif [ $1 == forceall ] ;then
    ps -ef|grep kernel|grep -v grep |awk '{print $2}'|xargs kill -9
else
	echo 'unknow cmd'
fi		
