package server

import (
	"io"
	"kernel/kernel-common/accesslog"
	"os"
	"path/filepath"
	"time"

	"kernel/kernel-common/lumberjack"
)

var accessLog *accesslog.AccessLog
var lumberJackLogger *lumberjack.Logger

// 生成日志文件名
func generateLogFileName(logpath, filename string, istime bool) string {
	if logpath == "" {
		logpath = "."
	}
	if istime {
		currentTime := time.Now()
		return filepath.Join(logpath, filename+"_"+currentTime.Format("20060102")+".log")
	}
	return filepath.Join(logpath, filename+".log")
}

func InitAccessLog(config Config) *accesslog.AccessLog {
	cfg := config.GetAccessLog()

	var writer io.Writer
	if cfg.WriteToLogFile { // 写入文件
		lumberJackLogger = &lumberjack.Logger{
			Filename:   generateLogFileName(cfg.Dir, "access", true),
			MaxSize:    256,
			MaxBackups: cfg.MaxBackups,
			MaxAge:     7,
			Compress:   false,
			FileNameAuto: func() string {
				return generateLogFileName(cfg.Dir, "access", true)
			},
		}
		writer = lumberJackLogger
	} else {
		writer = os.Stdout // 输出到控制台
	}

	accessLog = accesslog.New(writer)
	accessLog.RequestBody = !cfg.DisableRecordReqBody
	accessLog.ResponseBody = cfg.RecordRespBody
	return accessLog
}
