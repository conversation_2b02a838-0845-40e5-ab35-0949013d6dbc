package main

import (
	"context"
	"database/sql"
	"kernel/kernel-common/runtime"
	"kernel/plugins/models"

	json "github.com/json-iterator/go"
)

type Controller struct {
	server *SessionServerModule
}

func NewController(s *SessionServerModule) *Controller {
	return &Controller{
		server: s,
	}
}

// @Summary 用户上线
// @Description 用户上线
// @Tags sessionserver
// @Accept json
// @Produce json
// @Param payload body models.UserOnlineInfo true "用户上线信息"
// @Success 200 {object} models.CommonResp "响应结果"
// @Router /v2/rpc/sessionserver.session.online [post]
func (c *Controller) SessionOnline(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var req models.UserOnlineInfo
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		logger.Error("unmarshal error %v", err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}
	if !c.server.online.UserOnlineEvent(ctx, &req) {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "online failed", Data: nil})
	}
	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "ok", Data: nil})
}

// @Summary 用户下线
// @Description 用户下线
// @Tags sessionserver
// @Accept json
// @Produce json
// @Param payload body models.UserOnlineInfo true "用户下线信息"
// @Success 200 {object} models.CommonResp "响应结果"
// @Router /v2/rpc/sessionserver.session.offline [post]
func (c *Controller) SessionOffline(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	user_id := c.server.common.GetUserID(ctx)
	session_id := c.server.common.GetSessionID(ctx)
	uin := c.server.common.GetUin(ctx)
	if user_id == "" || session_id == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "uid or session_id is empty", Data: nil})
	}
	if !c.server.online.UserOfflineEvent(ctx, user_id, session_id, uin) {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "offline failed", Data: nil})
	}
	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "ok", Data: nil})
}

// @Summary 批量下线
// @Description 批量下线,网关关闭的时候调用
// @Tags sessionserver
// @Accept json
// @Produce json
// @Success 200 {object} models.CommonResp "响应结果"
// @Router /v2/rpc/sessionserver.session.batchoffline [post]
func (c *Controller) SessionBatchOffline(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	sessions := make([]*runtime.SessionInfo, 0)
	err := json.Unmarshal([]byte(payload), &sessions)
	if err != nil {
		logger.Error("Failed to unmarshal payload: %v", err)
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: err.Error(), Data: nil})
	}

	if !c.server.online.UserOfflineEventBatch(sessions) {
		return json.MarshalToString(&models.CommonResp{Code: models.EXCEPTION, Msg: "offline failed", Data: nil})
	}
	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "ok", Data: nil})
}
