name: statusserver501
config: null
shutdown_grace_sec: 30
data_dir: /data/kernel/data/statusserver
logger:
  level: debug
  stdout: true
  file: ''
  rotation: false
  max_size: 100
  max_age: 0
  max_backups: 0
  local_time: false
  compress: false
  format: json
metrics:
  reporting_freq_sec: 60
  namespace: ''
  prometheus_port: 0
  prefix: kernel
  custom_prefix: custom
session:
  encryption_key: 1b916331c6a1d5fa9c7a755144497408
  token_expiry_sec: 7200
  refresh_encryption_key: 7820dff96816635ebaecdc06747025e3
  refresh_token_expiry_sec: 9000
  single_socket: false
  single_match: false
  single_party: false
  single_session: false
socket:
  server_key: 2f72d524378403bb6f16f7310ac53238
  port: 7360
  address: ''
  protocol: tcp
  max_message_size_bytes: 4096
  max_request_size_bytes: 262144
  read_buffer_size_bytes: 4096
  write_buffer_size_bytes: 4096
  read_timeout_ms: 10000
  write_timeout_ms: 10000
  idle_timeout_ms: 60000
  write_wait_ms: 5000
  pong_wait_ms: 25000
  ping_period_ms: 15000
  ping_backoff_threshold: 20
  outgoing_queue_size: 64
  ssl_certificate: ''
  ssl_private_key: ''
  response_headers: null
database:
  address:
    - 'root_pg:<EMAIL>:5432/account'
  conn_max_lifetime_ms: 3600000
  max_open_conns: 100
  max_idle_conns: 100
  dns_scan_interval_sec: 60
social:
  steam:
    publisher_key: 'F0B0259F7BD8F1B23CC1C17F7CACD954'
    app_id: 3420320
  facebook_instant_game:
    app_secret: REDACTED
  facebook_limited_login:
    app_id: ''
  apple:
    bundle_id: ''
runtime:
  env: ["LOCAL_CUSTOM_CONFIG_PATH=/data/kernel/data/statusserver501.yaml"]
  path: /data/kernel/data/statusserver
  http_key: 2b69e5b1765ceeb131f544b96a670045
  min_count: 0
  lua_min_count: 16
  max_count: 0
  lua_max_count: 48
  js_min_count: 16
  js_max_count: 32
  call_stack_size: 0
  lua_call_stack_size: 128
  registry_size: 0
  lua_registry_size: 512
  event_queue_size: 65536
  event_queue_workers: 8
  read_only_globals: true
  lua_read_only_globals: true
  js_read_only_globals: true
  lua_api_stacktrace: false
  js_entrypoint: ''
match:
  input_queue_size: 128
  call_queue_size: 128
  signal_queue_size: 10
  join_attempt_queue_size: 128
  deferred_queue_size: 128
  join_marker_deadline_ms: 15000
  max_empty_sec: 0
  label_update_interval_ms: 1000
tracker:
  event_queue_size: 1024
console:
  port: 7361
  address: ''
  max_message_size_bytes: 4194304
  read_timeout_ms: 10000
  write_timeout_ms: 60000
  idle_timeout_ms: 300000
  token_expiry_sec: 86400
  signing_key: a359e714cec5f6c90faf301bbfce9ff7
  mfa:
    storage_encryption_key: 9bdd369d0814a210f481fdb272ce2c1c
    admin_account_enabled: false
leaderboard:
  blacklist_rank_cache: []
  callback_queue_size: 65536
  callback_queue_workers: 8
  rank_cache_workers: 1
matchmaker:
  max_tickets: 3
  interval_sec: 15
  max_intervals: 2
  rev_precision: false
  rev_threshold: 1
nacos:
  urls:
    - 'http://mse-dfd431f12-nacos-ans.mse.aliyuncs.com:8848/nacos'
  namespace_id: '1adc8295-e4a7-4d2c-ada6-0e68034335fe'
  access_key: 'LTAI5tAZHpRZ7xptaLRieD1k'
  secret_key: '******************************'
  timeout_ms: 5000
redis:
  addrs:
    - 'r-rj9880hzgc62lr33yl.redis.rds-aliyun-america.rds.aliyuncs.com:6379'
  username: ''
  password: 'epNmJGHM83SzbXwr'
  dial_timeout_ms: 5000
  read_timeout_ms: 5000
  write_timeout_ms: 5000
  pool_size: 100
  min_idle_conns: 20
  pool_timeout_ms: 5000
  idle_timeout_ms: 30000
access_log:
  is_disable: false
  write_to_log_file: false
  dir: ""
  record_resp_body: true
  disable_record_req_body: false
  max_backups: 0 # 0表示不限制
  rpc_ids:
    "*": true
    "*rtapi.envelope_ping": false
status_server_config:
  node_config:
    node_id: 'statusserver501'
    svc_group: 'statusserver@@omo'
    host: '***********:7360'
    nethost: '***********:7360'
    auth_key: '2b69e5b1765ceeb131f544b96a670045'
    weight: 10
    scheme: http