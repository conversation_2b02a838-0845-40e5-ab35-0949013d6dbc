package main

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"kernel/kernel-common/rtapi"
	"kernel/kernel-common/runtime"
	"strings"
	"sync"
	"time"

	"kernel/plugins/common"
	"kernel/plugins/models"

	"github.com/gofrs/uuid/v5"
	json "github.com/json-iterator/go"
	"github.com/redis/go-redis/v9"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

type PartyController struct {
	server    *ImServerModule
	pool      sync.Pool
	lock      *common.RedisRWLocker
	partyList *PartyList
}

func NewPartyController(s *ImServerModule) *PartyController {
	m := &PartyController{server: s, lock: common.NewRedisRWLocker(s.GetCommon().Redis)}
	m.partyList = NewPartyList(m)
	m.pool.New = func() any {
		return &PartyHandler{
			controller:   m,
			Presences:    make(map[string]*PartyPresenceItem),
			InvitedMap:   make(map[string]int64),
			JoinRequests: make([]*models.Presence, 0),
		}
	}
	go m.Loop()
	return m
}

func (c *PartyController) PartyRt(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	in := &rtapi.Envelope{}
	err := protojson.Unmarshal([]byte(payload), in)
	if err != nil {
		return "", err
	}

	var out *rtapi.Envelope
	switch in.Message.(type) {
	case *rtapi.Envelope_PartyCreate:
		out, err = c.partyCreate(ctx, logger, in)
	case *rtapi.Envelope_PartyJoin:
		out, err = c.partyJoin(ctx, logger, in)
	case *rtapi.Envelope_PartyLeave:
		out, err = c.partyLeave(ctx, logger, in)
	case *rtapi.Envelope_PartyPromote:
		out, err = c.partyPromote(ctx, logger, in)
	case *rtapi.Envelope_PartyAccept:
		out, err = c.partyAccept(ctx, logger, in)
	case *rtapi.Envelope_PartyRemove:
		out, err = c.partyRemove(ctx, logger, in)
	case *rtapi.Envelope_PartyClose:
		out, err = c.partyClose(ctx, logger, in)
	case *rtapi.Envelope_PartyJoinRequestList:
		out, err = c.partyJoinRequestList(ctx, logger, in)
	case *rtapi.Envelope_PartyMatchmakerAdd:
		out, err = c.partyMatchmakerAdd(ctx, logger, in)
	case *rtapi.Envelope_PartyMatchmakerRemove:
		out, err = c.partyMatchmakerRemove(ctx, logger, in)
	case *rtapi.Envelope_PartyDataSend:
		out, err = c.partyDataSend(ctx, logger, in)
	default:
		return "", errors.New("unknown message type")
	}
	if err != nil {
		return "", err
	}

	resp, err := protojson.Marshal(out)
	return string(resp), err
}

// @Summary 获取用户组队列表
// @Description 获取用户组队列表
// @Tags Party
// @Accept json
// @Produce json
// @Param page_size query int true "每页数量"
// @Param page_num query int true "页码"
// @Param request body PartyListReq true "请求参数"
// @Success 200 {object} PartyListResp "返回结果"
// @Router /v2/rpc/imserver.party.list [post]
func (c *PartyController) PartyList(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uid := c.server.common.GetStrUin(ctx)
	if uid == "" {
		return json.MarshalToString(&PartyListResp{Code: int(models.FAILED), Message: "session error", Data: nil})
	}

	var req PartyListReq
	err := json.Unmarshal([]byte(payload), &req)
	if err != nil {
		return json.MarshalToString(&PartyListResp{Code: int(models.FAILED), Message: "param is invalid", Data: nil})
	}

	pagenum := req.PageNum
	if pagenum < 0 {
		pagenum = 0
	}
	pagesize := req.PageSize
	if pagesize <= 0 {
		pagesize = 20
	}

	partyList, err := c.partyList.GetPartyListDetail(pagesize, pagenum)
	if err != nil {
		return json.MarshalToString(&PartyListResp{Code: int(models.FAILED), Message: err.Error(), Data: nil})
	}

	resp := &PartyListResp{
		Code:    int(models.OK),
		Message: "ok",
		Data:    make([]*PartyDetailData, 0, len(partyList)),
	}

	for _, party := range partyList {
		// 过滤掉已经满员的队伍
		if party == nil || len(party.Presences) >= int(party.MaxSize) {
			continue
		}
		memberList := make([]*models.Presence, 0, len(party.Presences))
		for _, presence := range party.Presences {
			memberList = append(memberList, presence.Presence)
		}
		resp.Data = append(resp.Data, &PartyDetailData{
			PartyId:          party.PartyID,
			PartyLeader:      party.Leader,
			PartyMemberCount: len(party.Presences),
			PartyMemberList:  memberList,
			PartyDesc:        party.PartyDesc,
			PartyLabels:      party.PartyLabels,
		})
	}

	// 释放partyHandler
	for _, party := range partyList {
		party.Release()
	}

	return json.MarshalToString(resp)
}

// @Summary 查询指定玩家所在队伍
// @Description 查询指定玩家所在队伍
// @Tags Party
// @Accept json
// @Produce json
// @Param request body PartyQueryReq true "请求参数"
// @Success 200 {object} PartyQueryResp "返回结果"
// @Router /v2/rpc/imserver.party.query [post]
func (c *PartyController) PartyQuery(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uin := c.server.common.GetStrUin(ctx)
	if uin == "" {
		return json.MarshalToString(&PartyQueryResp{Code: int(models.FAILED), Message: "session error", Data: nil})
	}

	var req PartyQueryReq
	err := json.Unmarshal([]byte(payload), &req)
	if err != nil {
		return json.MarshalToString(&PartyQueryResp{Code: int(models.FAILED), Message: "param is invalid", Data: nil})
	}

	queryUin := req.Uin
	uid := uuid.FromStringOrNil(queryUin)
	if uid != uuid.Nil { // 传过来是uid
		// 根据uid查询uin
		results, err := c.server.common.QueryUserFileds(ctx, req.Uin, "uin")
		if err != nil {
			return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error(), Data: nil})
		}
		if tmpuin, ok := results["uin"]; ok {
			queryUin = common.Interface2String(tmpuin)
		}
	}

	partyId, err := c.GetUserPartyId(queryUin)
	if err != nil {
		return json.MarshalToString(&PartyQueryResp{Code: int(models.FAILED), Message: err.Error(), Data: nil})
	}

	party_data := &PartyDetailData{
		PartyId: partyId,
	}

	if partyId != "" {
		locker, err := c.lock.TryReadLock(ctx, partyId, 3*time.Second)
		if err != nil {
			logger.Error("error in party detail %s", err.Error())
			return json.MarshalToString(&PartyDetailResp{Code: int(models.FAILED), Message: err.Error(), Data: nil})
		}
		ph, err := LoadPartyHandler(c, partyId)
		if err != nil {
			logger.Error("error in party detail %s", err.Error())
			return json.MarshalToString(&PartyDetailResp{Code: int(models.FAILED), Message: err.Error(), Data: nil})
		}
		defer func() {
			locker.Unlock(context.Background())
			ph.Release()
		}()

		party_data.PartyLeader = ph.Leader
		party_data.PartyMemberCount = len(ph.Presences)
	}

	return json.MarshalToString(&PartyQueryResp{Code: int(models.OK), Message: "success", Data: party_data})
}

// @Summary 获取用户组队详情
// @Description 获取用户组队详情
// @Tags Party
// @Accept json
// @Produce json
// @Param request body PartyDetailReq true "请求参数"
// @Success 200 {object} PartyDetailResp "返回结果"
// @Router /v2/rpc/imserver.party.detail [post]
func (c *PartyController) PartyDetail(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uin := c.server.common.GetStrUin(ctx)
	if uin == "" {
		return json.MarshalToString(&PartyDetailResp{Code: int(models.FAILED), Message: "session error", Data: nil})
	}

	var req PartyDetailReq
	err := json.Unmarshal([]byte(payload), &req)
	if err != nil {
		return json.MarshalToString(&PartyDetailResp{Code: int(models.FAILED), Message: "param is invalid", Data: nil})
	}

	partyId := req.PartyId
	if partyId == "" {
		// 查找自己是否在某个party中
		partyId, err = c.GetUserPartyId(uin)
		if err != nil {
			return json.MarshalToString(&PartyDetailResp{Code: int(models.FAILED), Message: err.Error(), Data: nil})
		}
		if partyId == "" {
			return json.MarshalToString(&PartyDetailResp{Code: int(models.DATA_NOT_EXIST), Message: "not in any party", Data: nil})
		}
	}

	locker, err := c.lock.TryReadLock(ctx, partyId, 3*time.Second)
	if err != nil {
		logger.Error("error in party detail %s", err.Error())
		return json.MarshalToString(&PartyDetailResp{Code: int(models.FAILED), Message: err.Error(), Data: nil})
	}
	ph, err := LoadPartyHandler(c, partyId)
	if err != nil {
		logger.Error("error in party detail %s", err.Error())
		return json.MarshalToString(&PartyDetailResp{Code: int(models.FAILED), Message: err.Error(), Data: nil})
	}
	defer func() {
		locker.Unlock(context.Background())
		ph.Release()
	}()

	memberList := make([]*models.Presence, 0, len(ph.Presences))
	for _, presence := range ph.Presences {
		memberList = append(memberList, presence.Presence)
	}
	party_data := &PartyDetailData{
		PartyId:          ph.PartyID,
		PartyLeader:      ph.Leader,
		PartyMemberCount: len(ph.Presences),
		PartyMemberList:  memberList,
		PartyDesc:        ph.PartyDesc,
		PartyLabels:      ph.PartyLabels,
	}

	return json.MarshalToString(&PartyDetailResp{Code: int(models.OK), Message: "ok", Data: party_data})
}

// @Summary 修改组队信息
// @Description 修改组队信息
// @Tags Party
// @Accept json
// @Produce json
// @Param request body PartyUpdateReq true "请求参数"
// @Success 200 {object} PartyUpdateResp "返回结果"
// @Router /v2/rpc/imserver.party.update [post]
func (c *PartyController) PartyUpdate(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uin := c.server.common.GetStrUin(ctx)
	if uin == "" {
		return json.MarshalToString(&PartyUpdateResp{Code: int(models.FAILED), Message: "session error"})
	}

	var req PartyUpdateReq
	err := json.Unmarshal([]byte(payload), &req)
	if err != nil {
		return json.MarshalToString(&PartyUpdateResp{Code: int(models.FAILED), Message: "param is invalid"})
	}

	// 查找自己是否在某个party中
	partyId, err := c.GetUserPartyId(uin)
	if err != nil {
		return json.MarshalToString(&PartyUpdateResp{Code: int(models.FAILED), Message: err.Error()})
	}
	if partyId == "" {
		return json.MarshalToString(&PartyUpdateResp{Code: int(models.DATA_NOT_EXIST), Message: "not in any party"})
	}

	locker, err := c.lock.TryWriteLock(ctx, partyId, 3*time.Second)
	if err != nil {
		logger.Error("error in party detail %s", err.Error())
		return json.MarshalToString(&PartyUpdateResp{Code: int(models.FAILED), Message: err.Error()})
	}
	ph, err := LoadPartyHandler(c, partyId)
	if err != nil {
		logger.Error("error in party detail %s", err.Error())
		return json.MarshalToString(&PartyUpdateResp{Code: int(models.FAILED), Message: err.Error()})
	}
	defer func() {
		locker.Unlock(context.Background())
		ph.Release()
	}()

	errMsg := ph.Update(ctx, uin, req.PartyDesc, req.PartyLabels, req.Open)
	if !models.IsErrOK(errMsg) {
		return json.MarshalToString(&PartyUpdateResp{Code: int(errMsg.Code), Message: errMsg.Message})
	}
	return json.MarshalToString(&PartyUpdateResp{Code: int(models.OK), Message: "ok"})
}

// @Summary 邀请加入队伍
// @Description 邀请加入队伍
// @Tags Party
// @Accept json
// @Produce json
// @Param request body PartyInviteReq true "请求参数"
// @Success 200 {object} models.CommonResp "返回结果"
// @Router /v2/rpc/imserver.party.invite [post]
func (c *PartyController) PartyInvite(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uin := c.server.common.GetStrUin(ctx)
	if uin == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "session error", Data: nil})
	}

	var req PartyInviteReq
	err := json.Unmarshal([]byte(payload), &req)
	if err != nil {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "param is invalid", Data: nil})
	}

	// 查找自己是否在某个party中
	partyId, err := c.GetUserPartyId(uin)
	if err != nil {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error(), Data: nil})
	}
	if partyId == "" {
		return json.MarshalToString(&models.CommonResp{Code: models.DATA_NOT_EXIST, Msg: "not in any party", Data: nil})
	}

	locker, err := c.lock.TryWriteLock(ctx, partyId, 3*time.Second)
	if err != nil {
		logger.Error("error in party invite %s", err.Error())
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error(), Data: nil})
	}
	ph, err := LoadPartyHandler(c, partyId)
	if err != nil {
		logger.Error("error in party invite %s", err.Error())
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error(), Data: nil})
	}
	defer func() {
		locker.Unlock(context.Background())
		ph.Release()
	}()

	inviteUin := req.Uin
	uid := uuid.FromStringOrNil(inviteUin)
	if uid != uuid.Nil { // 传过来是uid
		// 根据uid查询uin
		results, err := c.server.common.QueryUserFileds(ctx, req.Uin, "uin")
		if err != nil {
			return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error(), Data: nil})
		}
		if tmpuin, ok := results["uin"]; ok {
			inviteUin = common.Interface2String(tmpuin)
		}
	}

	errMsg := ph.Invite(ctx, uin, inviteUin)
	if !models.IsErrOK(errMsg) {
		return json.MarshalToString(&models.CommonResp{Code: models.ErrorCode(errMsg.Code), Msg: errMsg.Message, Data: nil})
	}

	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "ok", Data: nil})
}

func (c *PartyController) GetUserPartyId(uin string) (string, error) {
	// 检测是否已经在其他party中
	rediscli := c.server.GetCommon().Redis
	partyId, err := rediscli.Get(context.Background(), fmt.Sprintf("userparty:%s", uin)).Result()
	if err != nil {
		if err != redis.Nil {
			return "", err
		}
		return "", nil
	}
	return partyId, nil
}

func (c *PartyController) partyCreate(ctx context.Context, logger runtime.Logger, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	incoming := in.GetPartyCreate()

	uin, nick := c.server.common.GetStrUin(ctx), c.server.common.GetNick(ctx)
	if uin == "" {
		logger.Error("uin or session_id is empty")
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "session error",
		}}}, nil
	}
	if nick == "" {
		username := c.server.common.GetUsername(ctx)
		nick = username
	}

	// Validate party creation parameters.
	if incoming.MaxSize != 5 {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party max size, must be 5",
		}}}, nil
	}

	// 检测是否已经在其他party中
	partyId, err := c.GetUserPartyId(uin)
	if err != nil {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to create party",
		}}}, nil
	}
	if partyId != "" {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(ErrMsgPartyAlreadyInParty.Code),
			Message: "already in party",
		}}}, nil
	}

	presence := &models.Presence{
		UserID: uin,
		Meta: models.PresenceMeta{
			Username: nick,
		},
	}

	// Handle through the party registry.
	ph := NewPartyHandler(c, uin, incoming.Open, "", int(incoming.MaxSize))
	defer ph.Release()

	if errMsg := ph.Join(ctx, []*models.Presence{presence}); !models.IsErrOK(errMsg) {
		logger.Error("error in party create %s", errMsg.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(errMsg.Code),
			Message: errMsg.Message,
		}}}, nil
	}

	out := &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Party{Party: &rtapi.Party{
		PartyId: ph.PartyID,
		Open:    incoming.Open,
		MaxSize: incoming.MaxSize,
		Self: &rtapi.UserPresence{
			UserId:   presence.UserID,
			Username: presence.Meta.Username,
		},
		Leader: &rtapi.UserPresence{
			UserId:   presence.UserID,
			Username: presence.Meta.Username,
		},
	}}}

	return out, nil
}

func (c *PartyController) partyJoin(ctx context.Context, logger runtime.Logger, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	incoming := in.GetPartyJoin()

	uin, nick := c.server.common.GetStrUin(ctx), c.server.common.GetNick(ctx)
	if uin == "" {
		logger.Error("uin or session_id is empty")
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "session error",
		}}}, nil
	}
	if nick == "" {
		username := c.server.common.GetUsername(ctx)
		nick = username
	}

	// Validate the party ID.
	partyIDComponents := strings.SplitN(incoming.PartyId, ".", 2)
	if len(partyIDComponents) != 2 {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party ID",
		}}}, nil
	}
	if partyIDComponents[0] == "" {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party ID",
		}}}, nil
	}
	partyID := incoming.PartyId

	// 检测是否已经在其他party中
	partyId, err := c.GetUserPartyId(uin)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	if partyId != "" {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(ErrMsgPartyAlreadyInParty.Code),
			Message: "already in party",
		}}}, nil
	}

	locker, err := c.lock.TryWriteLock(ctx, partyID, 3*time.Second)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	ph, err := LoadPartyHandler(c, partyID)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	defer func() {
		locker.Unlock(context.Background())
		ph.Release()
	}()

	presence := &models.Presence{
		UserID: uin,
		Meta: models.PresenceMeta{
			Username: nick,
		},
	}

	// Handle through the party registry.
	autoJoin, errMsg := ph.JoinRequest(ctx, presence)
	if !models.IsErrOK(errMsg) {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(errMsg.Code),
			Message: errMsg.Message,
		}}}, nil
	}

	// If the party was open and the join was successful, track the new member immediately.
	if autoJoin {
		ph.Join(ctx, []*models.Presence{presence})
	}

	out := &rtapi.Envelope{Cid: in.Cid}
	return out, nil
}

func (c *PartyController) partyLeave(ctx context.Context, logger runtime.Logger, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	incoming := in.GetPartyLeave()

	uin, nick := c.server.common.GetStrUin(ctx), c.server.common.GetNick(ctx)
	if uin == "" {
		logger.Error("uin or session_id is empty")
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "session error",
		}}}, nil
	}
	if nick == "" {
		username := c.server.common.GetUsername(ctx)
		nick = username
	}

	// Validate the party ID.
	partyIDComponents := strings.SplitN(incoming.PartyId, ".", 2)
	if len(partyIDComponents) != 2 {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party ID",
		}}}, nil
	}
	if partyIDComponents[0] == "" {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party ID",
		}}}, nil
	}
	partyID := incoming.PartyId

	locker, err := c.lock.TryWriteLock(ctx, partyID, 3*time.Second)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	ph, err := LoadPartyHandler(c, partyID)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	defer func() {
		locker.Unlock(context.Background())
		ph.Release()
	}()

	presence := &models.Presence{
		UserID: uin,
		Meta: models.PresenceMeta{
			Username: nick,
		},
	}

	// Handle through the party registry.
	ph.Leave(ctx, []*models.Presence{presence})

	out := &rtapi.Envelope{Cid: in.Cid}
	return out, nil
}

// 提升为领导
func (c *PartyController) partyPromote(ctx context.Context, logger runtime.Logger, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	incoming := in.GetPartyPromote()

	uin := c.server.common.GetStrUin(ctx)
	if uin == "" {
		logger.Error("uin or session_id is empty")
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "session error",
		}}}, nil
	}

	// Validate presence info.
	if incoming.Presence == nil || incoming.Presence.UserId == "" || incoming.Presence.Username == "" {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid presence",
		}}}, nil
	}

	// Validate the party ID.
	partyIDComponents := strings.SplitN(incoming.PartyId, ".", 2)
	if len(partyIDComponents) != 2 {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party ID",
		}}}, nil
	}
	if partyIDComponents[0] == "" {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party ID",
		}}}, nil
	}
	partyID := incoming.PartyId

	locker, err := c.lock.TryWriteLock(ctx, partyID, 3*time.Second)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	ph, err := LoadPartyHandler(c, partyID)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	defer func() {
		locker.Unlock(context.Background())
		ph.Release()
	}()

	// Handle through the party registry.
	errMsg := ph.Promote(ctx, uin, incoming.Presence)
	if !models.IsErrOK(errMsg) {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(errMsg.Code),
			Message: errMsg.Message,
		}}}, nil
	}

	out := &rtapi.Envelope{Cid: in.Cid}
	return out, nil
}

func (c *PartyController) partyAccept(ctx context.Context, logger runtime.Logger, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	incoming := in.GetPartyAccept()

	uin := c.server.common.GetStrUin(ctx)
	if uin == "" {
		logger.Error("uin or session_id is empty")
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "session error",
		}}}, nil
	}

	// Validate presence info.
	if incoming.Presence == nil || incoming.Presence.UserId == "" || incoming.Presence.Username == "" {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid presence",
		}}}, nil
	}

	// Validate the party ID.
	partyIDComponents := strings.SplitN(incoming.PartyId, ".", 2)
	if len(partyIDComponents) != 2 {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party ID",
		}}}, nil
	}
	if partyIDComponents[0] == "" {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party ID",
		}}}, nil
	}
	partyID := incoming.PartyId

	locker, err := c.lock.TryWriteLock(ctx, partyID, 3*time.Second)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	ph, err := LoadPartyHandler(c, partyID)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	defer func() {
		locker.Unlock(context.Background())
		ph.Release()
	}()

	// Handle through the party registry.
	errMsg := ph.Accept(ctx, uin, incoming.Presence, true)
	if !models.IsErrOK(errMsg) {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(errMsg.Code),
			Message: errMsg.Message,
		}}}, nil
	}

	out := &rtapi.Envelope{Cid: in.Cid}
	return out, nil
}

func (c *PartyController) partyRemove(ctx context.Context, logger runtime.Logger, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	incoming := in.GetPartyRemove()

	uin := c.server.common.GetStrUin(ctx)
	if uin == "" {
		logger.Error("uin or session_id is empty")
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "session error",
		}}}, nil
	}

	// Validate presence info.
	if incoming.Presence == nil || incoming.Presence.UserId == "" {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid presence",
		}}}, nil
	}

	// Validate the party ID.
	partyIDComponents := strings.SplitN(incoming.PartyId, ".", 2)
	if len(partyIDComponents) != 2 {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party ID",
		}}}, nil
	}
	if partyIDComponents[0] == "" {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party ID",
		}}}, nil
	}
	partyID := incoming.PartyId

	locker, err := c.lock.TryWriteLock(ctx, partyID, 3*time.Second)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	ph, err := LoadPartyHandler(c, partyID)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	defer func() {
		locker.Unlock(context.Background())
		ph.Release()
	}()

	// Handle through the party registry.
	errMsg := ph.Remove(ctx, uin, incoming.Presence)
	if !models.IsErrOK(errMsg) {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(errMsg.Code),
			Message: errMsg.Message,
		}}}, nil
	}

	out := &rtapi.Envelope{Cid: in.Cid}
	return out, nil
}

func (c *PartyController) partyClose(ctx context.Context, logger runtime.Logger, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	incoming := in.GetPartyClose()

	uin := c.server.common.GetStrUin(ctx)
	if uin == "" {
		logger.Error("uin or session_id is empty")
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "session error",
		}}}, nil
	}

	// Validate the party ID.
	partyIDComponents := strings.SplitN(incoming.PartyId, ".", 2)
	if len(partyIDComponents) != 2 {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party ID",
		}}}, nil
	}
	if partyIDComponents[0] == "" {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party ID",
		}}}, nil
	}
	partyID := incoming.PartyId

	locker, err := c.lock.TryWriteLock(ctx, partyID, 3*time.Second)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	ph, err := LoadPartyHandler(c, partyID)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	defer func() {
		locker.Unlock(context.Background())
		ph.Release()
	}()

	// Handle through the party registry.
	errMsg := ph.Close(ctx, uin)
	if !models.IsErrOK(errMsg) {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(errMsg.Code),
			Message: errMsg.Message,
		}}}, nil
	}

	out := &rtapi.Envelope{Cid: in.Cid}
	return out, nil
}

func (c *PartyController) partyJoinRequestList(ctx context.Context, logger runtime.Logger, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	incoming := in.GetPartyJoinRequestList()

	uin := c.server.common.GetStrUin(ctx)
	if uin == "" {
		logger.Error("uin or session_id is empty")
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "session error",
		}}}, nil
	}

	// Validate the party ID.
	partyIDComponents := strings.SplitN(incoming.PartyId, ".", 2)
	if len(partyIDComponents) != 2 {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party ID",
		}}}, nil
	}
	if partyIDComponents[0] == "" {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party ID",
		}}}, nil
	}
	partyID := incoming.PartyId

	locker, err := c.lock.TryReadLock(ctx, partyID, 3*time.Second)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	ph, err := LoadPartyHandler(c, partyID)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	defer func() {
		locker.Unlock(context.Background())
		ph.Release()
	}()

	// Handle through the party registry.
	presences, errMsg := ph.JoinRequestList(ctx, uin)
	if !models.IsErrOK(errMsg) {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(errMsg.Code),
			Message: errMsg.Message,
		}}}, nil
	}

	userPresences := make([]*rtapi.UserPresence, len(presences))
	for i, presence := range presences {
		userPresences[i] = &rtapi.UserPresence{
			UserId:   presence.GetUserId(),
			Username: presence.GetUsername(),
			Status:   &wrapperspb.StringValue{Value: presence.GetStatus()},
		}
	}

	out := &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_PartyJoinRequest{PartyJoinRequest: &rtapi.PartyJoinRequest{
		PartyId:   incoming.PartyId,
		Presences: userPresences,
	}}}

	return out, nil
}

func (c *PartyController) partyMatchmakerAdd(ctx context.Context, logger runtime.Logger, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	return nil, nil
}

func (c *PartyController) partyMatchmakerRemove(ctx context.Context, logger runtime.Logger, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	return nil, nil
}

func (c *PartyController) partyDataSend(ctx context.Context, logger runtime.Logger, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	incoming := in.GetPartyDataSend()

	uin := c.server.common.GetStrUin(ctx)
	if uin == "" {
		logger.Error("uin or session_id is empty")
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "session error",
		}}}, nil
	}

	// Validate the party ID.
	partyIDComponents := strings.SplitN(incoming.PartyId, ".", 2)
	if len(partyIDComponents) != 2 {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party ID",
		}}}, nil
	}
	if partyIDComponents[0] == "" {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party ID",
		}}}, nil
	}
	partyID := incoming.PartyId
	if incoming.OpCode < 100 {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.PARAM_ERR),
			Message: "Invalid party op code",
		}}}, nil
	}

	locker, err := c.lock.TryReadLock(ctx, partyID, 3*time.Second)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	ph, err := LoadPartyHandler(c, partyID)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(models.FAILED),
			Message: "Failed to join party",
		}}}, nil
	}
	defer func() {
		locker.Unlock(context.Background())
		ph.Release()
	}()

	// Handle through the party registry.
	errMsg := ph.DataSend(ctx, uin, incoming.OpCode, incoming.Data)
	if !models.IsErrOK(errMsg) {
		return &rtapi.Envelope{Cid: in.Cid, Message: &rtapi.Envelope_Error{Error: &rtapi.Error{
			Code:    int32(errMsg.Code),
			Message: errMsg.Message,
		}}}, nil
	}

	out := &rtapi.Envelope{Cid: in.Cid}
	return out, nil
}

func (c *PartyController) partyUpdateStatus(partyID string, uin int64, online bool) error {
	logger := c.server.GetLogger()
	locker, err := c.lock.TryReadLock(context.Background(), partyID, 3*time.Second)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return err
	}
	ph, err := LoadPartyHandler(c, partyID)
	if err != nil {
		logger.Error("error in party join %s", err.Error())
		return err
	}
	defer func() {
		locker.Unlock(context.Background())
		ph.Release()
	}()

	err = ph.UpdateMemberOnlineStatus(uin, online)
	if err != nil {
		logger.Error("error in party update status %s", err.Error())
		return err
	}
	return nil
}

func (c *PartyController) Loop() {
	timer := time.NewTicker(1 * time.Minute)
	defer timer.Stop()

	for {
		select {
		case <-timer.C:
			// 只保留前200个
			rds := c.server.GetCommon().Redis
			if rds != nil {
				rds.ZRemRangeByRank(context.Background(), "partylist", 0, -201)
			}
		}
	}
}
