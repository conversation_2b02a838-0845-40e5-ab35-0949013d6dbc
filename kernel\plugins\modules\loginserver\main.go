package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"kernel/kernel-common/api"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"math/rand"
	"strconv"
	"sync"
	"time"

	"go.uber.org/zap/zapcore"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

type LoginServerModule struct {
	name            string
	logger          runtime.Logger
	db              *sql.DB
	nk              runtime.NakamaModule
	config          runtime.Config // nakama 配置
	CustomConfig    *CustomConfig  // 自定义配置
	isRegisterSelf  bool
	ProxyPassConfig sync.Map
	controller      *Controller

	common  *logic.CommonGlobalDataStruct
	online  *logic.OnlineGlobalDataStruct
	send    *logic.SendGlobalDataStruct
	dbagent *logic.DbAgentGlobalDataStruct
}

const LoginServerName = "loginserver"

var LoginServerData *LoginServerModule

func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
	LoginServerData = new(LoginServerModule)
	LoginServerData.name = LoginServerName
	LoginServerData.logger = logger
	LoginServerData.db = db
	LoginServerData.nk = nk
	LoginServerData.common = logic.NewCommonGlobalDataStruct()
	LoginServerData.online = logic.NewOnlineGlobalDataStruct(LoginServerData)
	LoginServerData.send = logic.NewSendGlobalDataStruct(LoginServerData)
	LoginServerData.dbagent = logic.NewDbAgentGlobalDataStruct(LoginServerData)
	config, err := initializer.GetConfig()
	if err != nil {
		return err
	}
	LoginServerData.config = config
	if err := LoginServerData.common.Init(LoginServerData, LoginServerData.CustomConfig, LoginServerData.customConfigEvent); err != nil {
		logger.Error("init common logic error %v", err)
		return err
	}

	initializer.RegisterShutdown(LoginServerData.Shutdown)
	initializer.RegisterBeforeAuthenticateEmail(LoginServerData.BeforeAuthenticateEmail)
	initializer.RegisterBeforeAuthenticateCustom(LoginServerData.BeforeAuthenticateCustom)
	initializer.RegisterBeforeAuthenticateDevice(LoginServerData.BeforeAuthenticateDevice)
	initializer.RegisterAfterAuthenticateEmail(LoginServerData.AfterAuthenticateEmail)
	initializer.RegisterAfterAuthenticateSteam(LoginServerData.AfterAuthenticateSteam)
	initializer.RegisterAfterAuthenticateCustom(LoginServerData.AfterAuthenticateCustom)
	initializer.RegisterAfterAuthenticateDevice(LoginServerData.AfterAuthenticateDevice)

	// 自定义路由注册
	LoginServerData.controller = NewController(LoginServerData)
	initializer.RegisterRpc(RPCID_CREATE_ACCOUNT_EMAIL, LoginServerData.controller.CreateAccountFromEmail)
	initializer.RegisterRpc(RPCID_QUERY_LOGIN_LOGS, LoginServerData.controller.GetLoginLogs)
	initializer.RegisterRpc(RPCID_AUTH_EMAIL, LoginServerData.controller.AuthenticateEmail)
	initializer.RegisterRpc(RPCID_AUTH_EMAIL_CODE, LoginServerData.controller.AuthenticateEmailCode)

	return nil
}

func (s *LoginServerModule) GetCommon() *logic.CommonGlobalDataStruct {
	return s.common
}
func (s *LoginServerModule) GetOnline() *logic.OnlineGlobalDataStruct {
	return s.online
}
func (s *LoginServerModule) GetSend() *logic.SendGlobalDataStruct {
	return s.send
}
func (s *LoginServerModule) GetDbAgent() *logic.DbAgentGlobalDataStruct {
	return s.dbagent
}
func (s *LoginServerModule) GetNotify() *logic.NotifyGlobalDataStruct {
	return nil
}
func (s *LoginServerModule) GetStream() *logic.StreamGlobalDataStruct {
	return nil
}
func (s *LoginServerModule) GetChannel() *logic.ChannelGlobalDataStruct {
	return nil
}
func (s *LoginServerModule) GetLogger() runtime.Logger {
	return s.logger
}
func (s *LoginServerModule) GetConfig() runtime.Config {
	return s.config
}
func (s *LoginServerModule) GetName() string {
	return s.name
}
func (s *LoginServerModule) GetDb() *sql.DB {
	return s.db
}
func (s *LoginServerModule) GetNk() runtime.NakamaModule {
	return s.nk
}

// 配置读取回调
func (s *LoginServerModule) customConfigEvent(dataId string, newobj any) {
	tmpobj, ok := newobj.(*CustomConfig)
	if !ok {
		return
	}
	if tmpobj.Config == nil {
		tmpobj.Config = &LoginServerConfig{}
	}
	defaultNodeCfg := false
	if tmpobj.Config.NodeConfig == nil {
		// 使用默认节点配置
		scheme := "http"
		if s.config.GetSocket().GetUseTLS() {
			scheme = "https"
		}
		tmpobj.Config.NodeConfig = &models.NodeConfig{
			NodeId:   s.config.GetName(),
			SvcGroup: fmt.Sprintf("%s@@%s", s.name, models.NACOS_DEFAULT_GROUP),
			Host:     fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			NetHost:  fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			AuthKey:  s.config.GetRuntime().GetHTTPKey(),
			Weight:   10,
			Scheme:   scheme,
		}
		defaultNodeCfg = true
	}
	tmp, _ := json.Marshal(tmpobj)
	s.logger.Debug("customConfigEvent defaultNodeCfg: %v, customCfg: %s", defaultNodeCfg, string(tmp))
	s.common.SetNodeConfig(tmpobj.Config.NodeConfig)
	s.common.WatchNodeList.InitWatchSvr(tmpobj.Config.WatchSvcGroupList, tmpobj.Config.NodeConfig.SvcGroup)
	s.CustomConfig = tmpobj

	// 注册服务
	selfcfg := map[string]interface{}{}
	if err := common.StrcutToMap(tmpobj.Config.NodeConfig, &selfcfg); err == nil {
		if !s.isRegisterSelf {
			go s.common.RegisterAndUpdateSelfToNacos(false, selfcfg)
			s.isRegisterSelf = true
		} else {
			go s.common.RegisterAndUpdateSelfToNacos(true, selfcfg)
		}
	} else {
		s.logger.Error("register self, config error %v", err)
	}

	s.common.UpdateAccessLogConfig(tmpobj.AccessLog)
	loglevel, err := zapcore.ParseLevel(tmpobj.Logger.Level)
	if err != nil {
		s.logger.Error("parse log level error %v", err)
	} else {
		s.nk.SetLoggerLevel(loglevel)
	}
}

func (s *LoginServerModule) Shutdown(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, reload bool) {
	logger.Info("%s shutdown", s.name)
	if !reload {
		s.common.UnRegisterSelfToNacos()
	}
}

func (s *LoginServerModule) BeforeAuthenticateEmail(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *api.AuthenticateEmailRequest) (*api.AuthenticateEmailRequest, error) {
	// 禁止主动通过邮箱创建账号
	//in.Create = wrapperspb.Bool(false)
	return in, nil
}

func (s *LoginServerModule) BeforeAuthenticateDevice(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *api.AuthenticateDeviceRequest) (*api.AuthenticateDeviceRequest, error) {
	nick := in.Username
	if nick != "" {
		nick = SanitizeUsername(nick) // 移除非法字符
	} else {
		nick = "Guest" + strconv.Itoa(rand.Intn(100000))
	}
	in.Create = wrapperspb.Bool(true)
	in.Username = nick
	return in, nil
}

// 自定义渠道登陆校验
func (s *LoginServerModule) BeforeAuthenticateCustom(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *api.AuthenticateCustomRequest) (*api.AuthenticateCustomRequest, error) {
	logger.Debug("before authenticate custom %v", in.String())
	if in.GetAccount() == nil {
		return nil, models.ErrMsgParamErr.Err()
	}
	account := in.GetAccount()
	if account.Id == "" || len(account.Vars) == 0 {
		return nil, models.ErrMsgParamErr.Err()
	}

	vars := account.GetVars()
	if vars["imei"] == "" || vars["udid"] == "" || vars["tenementId"] == "" {
		return nil, models.ErrMsgParamErr.Err()
	}

	params := map[string]interface{}{
		"req": map[string]string{
			"unifiedToken": account.Id,
		},
	}
	postdata, _ := json.Marshal(params)

	// MD5(timestamp=123&unifiedToken=456&秘钥)
	timestamp := time.Now().UnixMilli()
	signStr := fmt.Sprintf("timestamp=%d&unifiedToken=%s&%s", timestamp, account.Id, s.CustomConfig.Config.AggCfg.TenementKey)
	signmd5 := common.GetMD5(signStr)

	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": signmd5,
		"imei":          vars["imei"],
		"udid":          vars["udid"],
		"tenementId":    vars["tenementId"],
		"timestamp":     strconv.FormatInt(timestamp, 10),
	}

	_url := fmt.Sprintf("%s/json/authV2/authToken", s.CustomConfig.Config.AggCfg.Url)
	in.Account.Id = ""
	resp, err := common.HttpPost(ctx, _url, postdata, headers, time.Second*10)
	if err != nil {
		logger.Debug("agg auth http post error %v params: %s, url: %s, headers: %v, signStr: %s", err, string(postdata), _url, headers, signStr)
		return nil, models.ErrMsgFailed.Err()
	}

	var respData RespAggAuth
	if err := json.Unmarshal(resp, &respData); err != nil {
		logger.Error("agg authjson unmarshal error: %v", err)
		return nil, models.ErrMsgFailed.Err()
	}
	if respData.Ret != 0 {
		logger.Debug("agg auth error %v params: %s, url: %s, headers: %v, signStr: %s", respData.Ret, string(postdata), _url, headers, signStr)
		return nil, models.ErrMsgAggAuthErr.Err()
	}
	if respData.Data == nil {
		logger.Error("agg auth data is nil")
		return nil, models.ErrMsgAggAuthErr.Err()
	}
	if respData.Data.UserId == "" {
		logger.Error("agg auth user id is empty")
		return nil, models.ErrMsgAggAuthErr.Err()
	}
	logger.Debug("agg auth success %s", string(resp))

	nick := in.Username
	if nick != "" {
		nick = SanitizeUsername(nick) // 移除非法字符
	}
	in = &api.AuthenticateCustomRequest{
		Account: &api.AccountCustom{
			Id: respData.Data.UserId,
			Vars: map[string]string{
				"nick": nick,
			},
		},
		Create:   &wrapperspb.BoolValue{Value: true},
		Username: nick,
	}
	//common.StrcutToMap(respData.Data, &in.Account.Vars)

	return in, nil
}

func (s *LoginServerModule) AfterAuthenticateEmail(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, out *api.Session, in *api.AuthenticateEmailRequest) error {
	err := s.controller.AfterAuthenticate(ctx, logger, db, nk, out)
	logger.Debug("after authenticate email %v", out.String())
	return err
}

func (s *LoginServerModule) AfterAuthenticateSteam(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, out *api.Session, in *api.AuthenticateSteamRequest) error {
	err := s.controller.AfterAuthenticate(ctx, logger, db, nk, out)
	logger.Debug("after authenticate steam %v", out.String())
	return err
}

func (s *LoginServerModule) AfterAuthenticateCustom(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, out *api.Session, in *api.AuthenticateCustomRequest) error {
	err := s.controller.AfterAuthenticate(ctx, logger, db, nk, out)
	logger.Debug("after authenticate custom %v", out.String())
	return err
}

func (s *LoginServerModule) AfterAuthenticateDevice(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, out *api.Session, in *api.AuthenticateDeviceRequest) error {
	err := s.controller.AfterAuthenticate(ctx, logger, db, nk, out)
	logger.Debug("after authenticate device %v", out.String())
	return err
}
