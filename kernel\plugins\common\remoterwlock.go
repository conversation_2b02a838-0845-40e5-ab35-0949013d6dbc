// Package common provides a RemoteRWLock with timeout, ctxLockID, and safe concurrency.
package common

import (
	"context"
	"fmt"
	"kernel/plugins/common/timerwheel"
	"log"
	"sync"
	"sync/atomic"
	"time"
)

const (
	maxLockTime = 5 * time.Second // 默认最大锁超时时间
)

type RemoteRWLock struct {
	writerLockCh   chan struct{}     // 写锁信号量
	writerCtx      uint64            // 当前持有写锁的 ctxlockid
	writerTimer    *timerwheel.Timer // 自动释放写锁
	writerChMu     sync.Mutex
	writerReleased chan struct{} // 写锁释放信号

	readMu      sync.Mutex
	readHolders map[uint64]struct{}          // 当前持有读锁的 ctxlockid（不计数，同一ctx重复RLock不叠加）
	readZeroCh  chan struct{}                // 所有读锁释放后的信号
	readTimers  map[uint64]*timerwheel.Timer // 读锁超时清理定时器

	timerPool *timerwheel.TimeWheelPool
}

func NewRemoteRWLock(tp *timerwheel.TimeWheelPool) *RemoteRWLock {
	return &RemoteRWLock{
		writerLockCh:   make(chan struct{}, 1),
		writerReleased: nil,
		readHolders:    make(map[uint64]struct{}),
		readTimers:     make(map[uint64]*timerwheel.Timer),
		timerPool:      tp,
	}
}

func (rw *RemoteRWLock) Lock(ctx context.Context, ctxlockid uint64) bool {
	return rw.LockTimeout(ctx, ctxlockid, maxLockTime)
}

func (rw *RemoteRWLock) LockTimeout(ctx context.Context, ctxlockid uint64, timeout time.Duration) bool {
	if timeout < 0 {
		timeout = 0
	}
	if atomic.LoadUint64(&rw.writerCtx) == ctxlockid {
		return true
	}

	timer := rw.timerPool.Get().NewTimer(timeout)
	defer timer.Stop()

	select {
	case rw.writerLockCh <- struct{}{}:
		// 成功获取写锁
	case <-timer.C:
		return false
	case <-ctx.Done():
		return false
	}

	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		// 释放写锁信号量
		select {
		case <-rw.writerLockCh:
		default:
		}
		return false
	default:
	}

	// 修复竞态：原子地设置写锁状态和捕获读锁状态
	rw.readMu.Lock()
	rw.writerChMu.Lock()
	// 先设置writerReleased，再设置writerCtx，确保读者看到完整状态
	rw.writerReleased = make(chan struct{})
	atomic.StoreUint64(&rw.writerCtx, ctxlockid)
	rw.writerChMu.Unlock()

	// 在readMu保护下等待所有读锁释放
	for {
		if len(rw.readHolders) == 0 {
			// 没有读锁了，可以继续
			rw.readMu.Unlock()
			break
		}

		// 还有读锁，等待它们释放
		ch := rw.readZeroCh
		rw.readMu.Unlock()

		if ch != nil {
			select {
			case <-ch:
				// 重新获取锁继续检查
				rw.readMu.Lock()
				continue
			case <-timer.C:
				// 超时，需要清理写锁状态
				rw.cleanupWriterLock(ctxlockid)
				return false
			case <-ctx.Done():
				// 取消，需要清理写锁状态
				rw.cleanupWriterLock(ctxlockid)
				return false
			}
		} else {
			// readZeroCh为nil，说明没有读锁，重新检查
			rw.readMu.Lock()
			continue
		}
	}

	if rw.timerPool != nil && timeout > 0 {
		rw.writerTimer = rw.timerPool.Get().AfterFunc(timeout, func(param interface{}) {
			wCtx := param.(uint64)
			if atomic.LoadUint64(&rw.writerCtx) == wCtx {
				fmt.Println("Auto unlock writer", wCtx)
				rw.Unlock(wCtx)
			}
		}, ctxlockid)
	}

	return true
}

// cleanupWriterLock 清理写锁状态，用于超时和取消情况
func (rw *RemoteRWLock) cleanupWriterLock(ctxlockid uint64) {
	// 停止定时器
	if rw.writerTimer != nil {
		rw.writerTimer.Stop()
		rw.writerTimer = nil
	}

	// 清理写锁状态
	if atomic.LoadUint64(&rw.writerCtx) == ctxlockid {
		atomic.StoreUint64(&rw.writerCtx, 0)
	}

	// 释放信号量
	select {
	case <-rw.writerLockCh:
	default:
	}

	// 清理writerReleased
	rw.writerChMu.Lock()
	if rw.writerReleased != nil {
		close(rw.writerReleased)
		rw.writerReleased = nil
	}
	rw.writerChMu.Unlock()
}

func (rw *RemoteRWLock) Unlock(ctxlockid uint64) bool {
	if atomic.LoadUint64(&rw.writerCtx) != ctxlockid {
		// 兜底：尝试非阻塞释放一次信号量与关闭writerReleased，避免残留
		select {
		case <-rw.writerLockCh:
		default:
		}
		rw.writerChMu.Lock()
		if rw.writerReleased != nil {
			close(rw.writerReleased)
			rw.writerReleased = nil
		}
		rw.writerChMu.Unlock()
		return false
	}
	if rw.writerTimer != nil {
		rw.writerTimer.Stop()
		rw.writerTimer = nil
	}
	atomic.StoreUint64(&rw.writerCtx, 0)
	select {
	case <-rw.writerLockCh:
	default:
	}

	rw.writerChMu.Lock()
	if rw.writerReleased != nil {
		close(rw.writerReleased)
		rw.writerReleased = nil
	}
	rw.writerChMu.Unlock()
	return true
}

func (rw *RemoteRWLock) RLock(ctx context.Context, ctxlockid uint64) bool {
	return rw.RLockTimeout(ctx, ctxlockid, maxLockTime)
}

func (rw *RemoteRWLock) RLockTimeout(ctx context.Context, ctxlockid uint64, timeout time.Duration) bool {
	if timeout < 0 {
		timeout = 0
	}
	// 检查是否已经持有写锁，如果是，直接获取读锁
	if atomic.LoadUint64(&rw.writerCtx) == ctxlockid {
		// 修复：即使持有写锁，也要记录读锁（不计数，集合语义）
		rw.readMu.Lock()
		// 如果之前不存在，则可能是从0->1，需要创建readZeroCh
		_, existed := rw.readHolders[ctxlockid]
		if !existed && len(rw.readHolders) == 0 && rw.readZeroCh == nil {
			rw.readZeroCh = make(chan struct{})
		}
		rw.readHolders[ctxlockid] = struct{}{}
		// 写锁持有者获取读锁时也设置超时清理（若已存在旧timer则重置）
		if rw.timerPool != nil && timeout > 0 {
			if oldTimer, ok := rw.readTimers[ctxlockid]; ok {
				oldTimer.Stop()
				delete(rw.readTimers, ctxlockid)
			}
			rw.readTimers[ctxlockid] = rw.timerPool.Get().AfterFunc(timeout, func(param interface{}) {
				ctxID := param.(uint64)
				rw.cleanupReadLock(ctxID, timeout)
			}, ctxlockid)
		}
		rw.readMu.Unlock()
		return true
	}

	timer := rw.timerPool.Get().NewTimer(timeout)
	defer timer.Stop()

	// 修复竞态：确保等待写锁完全释放
	for {
		if atomic.LoadUint64(&rw.writerCtx) == 0 {
			break
		}
		rw.writerChMu.Lock()
		ch := rw.writerReleased
		rw.writerChMu.Unlock()
		if ch != nil {
			select {
			case <-ch:
				continue
			case <-timer.C:
				return false
			case <-ctx.Done():
				return false
			}
		} else {
			// 修复：当writerCtx!=0但writerReleased==nil时，说明写锁正在设置中
			// 需要等待一小段时间让写锁完成设置，而不是直接break
			select {
			case <-time.After(1 * time.Millisecond):
				continue
			case <-timer.C:
				return false
			case <-ctx.Done():
				return false
			}
		}
	}

	// 检查timer是否已超时
	select {
	case <-timer.C:
		return false
	default:
	}

	// 检查上下文是否已超时
	select {
	case <-ctx.Done():
		return false
	default:
	}

	rw.readMu.Lock()
	// 如果之前不存在，则可能是从0->1，需要创建readZeroCh
	_, existed := rw.readHolders[ctxlockid]
	if !existed && len(rw.readHolders) == 0 && rw.readZeroCh == nil {
		rw.readZeroCh = make(chan struct{})
	}
	rw.readHolders[ctxlockid] = struct{}{}
	// 设置读锁超时清理（若已存在旧timer则重置）
	if rw.timerPool != nil && timeout > 0 {
		if oldTimer, ok := rw.readTimers[ctxlockid]; ok {
			oldTimer.Stop()
			delete(rw.readTimers, ctxlockid)
		}
		rw.readTimers[ctxlockid] = rw.timerPool.Get().AfterFunc(timeout, func(param interface{}) {
			ctxID := param.(uint64)
			rw.cleanupReadLock(ctxID, timeout)
		}, ctxlockid)
	}
	rw.readMu.Unlock()
	return true
}

// cleanupReadLock 清理超时的读锁
func (rw *RemoteRWLock) cleanupReadLock(ctxlockid uint64, timeout time.Duration) {
	rw.readMu.Lock()
	defer rw.readMu.Unlock()

	if _, exists := rw.readHolders[ctxlockid]; exists {
		log.Printf("WARNING: Read lock for ctxlockid %d expired after %v, cleaning up",
			ctxlockid, timeout)
		delete(rw.readHolders, ctxlockid)
		if len(rw.readHolders) == 0 && rw.readZeroCh != nil {
			close(rw.readZeroCh)
			rw.readZeroCh = nil
		}
	}

	// 清理定时器
	if timer, exists := rw.readTimers[ctxlockid]; exists {
		timer.Stop()
		delete(rw.readTimers, ctxlockid)
	}
}

func (rw *RemoteRWLock) RUnlock(ctxlockid uint64) {
	rw.readMu.Lock()
	if _, exists := rw.readHolders[ctxlockid]; !exists {
		rw.readMu.Unlock()
		return // 未持有读锁，直接返回
	} else {
		// 直接删除该ctx的读持有（集合语义）
		delete(rw.readHolders, ctxlockid)
		// 清理定时器
		if timer, exists := rw.readTimers[ctxlockid]; exists {
			timer.Stop()
			delete(rw.readTimers, ctxlockid)
		}
		if len(rw.readHolders) == 0 && rw.readZeroCh != nil {
			close(rw.readZeroCh)
			rw.readZeroCh = nil
		}
	}
	rw.readMu.Unlock()
}

func (rw *RemoteRWLock) TryLock(ctxlockid uint64) bool {
	if atomic.LoadUint64(&rw.writerCtx) == ctxlockid {
		return true
	}

	select {
	case rw.writerLockCh <- struct{}{}:
		// 修复竞态：在readMu保护下原子地检查读锁数量和设置写锁状态
		rw.readMu.Lock()
		readCount := len(rw.readHolders)
		if readCount != 0 {
			// 有读锁，释放写锁信号量
			rw.readMu.Unlock()
			select {
			case <-rw.writerLockCh:
			default:
			}
			return false
		}
		// 没有读锁，设置写锁状态（统一顺序：先建 writerReleased，再设 writerCtx）
		rw.writerChMu.Lock()
		rw.writerReleased = make(chan struct{})
		rw.writerChMu.Unlock()
		atomic.StoreUint64(&rw.writerCtx, ctxlockid)
		rw.readMu.Unlock()
		return true
	default:
		return false
	}
}

func (rw *RemoteRWLock) GetWriterCtx() uint64 {
	return atomic.LoadUint64(&rw.writerCtx)
}

func (rw *RemoteRWLock) GetReadCount() int {
	rw.readMu.Lock()
	defer rw.readMu.Unlock()
	return len(rw.readHolders)
}

func (rw *RemoteRWLock) HasReadLock(ctxlockid uint64) bool {
	rw.readMu.Lock()
	defer rw.readMu.Unlock()
	_, ok := rw.readHolders[ctxlockid]
	return ok
}

func (rw *RemoteRWLock) IsLocked() bool {
	return atomic.LoadUint64(&rw.writerCtx) != 0
}
