package logic

import (
	"database/sql"
	"kernel/kernel-common/runtime"
)

// 服务模块接口
type ServerModule interface {
	GetCommon() *CommonGlobalDataStruct
	GetOnline() *OnlineGlobalDataStruct
	GetSend() *SendGlobalDataStruct
	GetDbAgent() *DbAgentGlobalDataStruct
	GetNotify() *NotifyGlobalDataStruct
	GetStream() *StreamGlobalDataStruct
	GetChannel() *ChannelGlobalDataStruct
	GetLogger() runtime.Logger
	GetConfig() runtime.Config
	GetName() string
	GetDb() *sql.DB
	GetNk() runtime.NakamaModule
}
