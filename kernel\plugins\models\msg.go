package models

import (
	"kernel/kernel-common/rtapi"
	"kernel/kernel-common/runtime"
)

type CommonReq struct {
	UserID    string      `json:"user_id,omitempty"`
	SessionID string      `json:"session_id,omitempty"`
	ClientIP  string      `json:"client_ip,omitempty"`
	Data      interface{} `json:"data,omitempty"`
}

// 公共响应
type CommonResp struct {
	Code ErrorCode   `json:"code"`
	Msg  string      `json:"message,omitempty"`
	Data interface{} `json:"data,omitempty"`
}

type CommonPush struct {
	SessionIDs []string             `json:"sessions,omitempty"`
	Data       *rtapi.Notifications `json:"data,omitempty" swaggertype:"object"`
}

type ClosePush struct {
	SessionIDs []string               `json:"sessions,omitempty"`
	Reason     runtime.PresenceReason `json:"reason,omitempty" swaggertype:"integer" enums:"0,1,2,3,4"`
	Data       *rtapi.Notifications   `json:"data,omitempty" swaggertype:"object"`
}

type CommonPushData struct {
	ID      string      `json:"id,omitempty"`
	Level   int32       `json:"level,omitempty"`
	Subject string      `json:"subject,omitempty"`
	Content interface{} `json:"content,omitempty"`
}

type NotificationMulticast struct {
	UserIDs    []string
	Subject    string
	Content    map[string]interface{}
	Code       int
	Sender     string
	Persistent bool
}

type MsgBusReqPub struct {
	AppId     int    `form:"appid" structs:"appid"  binding:"required"`        // 迷你号
	Timestamp int64  `form:"timestamp" structs:"timestamp" binding:"required"` // 时间戳
	Sign      string `form:"sign" structs:"sign" binding:"required"`           // 签名
	Topic     string `form:"topic" structs:"topic" binding:"required"`         // 消息的主题
	Event     int    `form:"event" structs:"event" binding:"required"`         // 事件类型
	Payload   string `form:"payload" structs:"payload" binding:"required"`     // 消息内容
}

// 游戏服路由消息
type GsRouterMsg struct {
	Identity string `json:"identity,omitempty"`
	Data     string `json:"data,omitempty"`
}
