package main

import (
	"bytes"
	"context"
	"database/sql"
	"errors"
	"fmt"
	"io"
	"kernel/kernel-common/api"
	"kernel/kernel-common/rtapi"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/golang-jwt/jwt/v4"
	json "github.com/json-iterator/go"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap/zapcore"
	"google.golang.org/protobuf/encoding/protojson"
)

type GateServerModule struct {
	name            string
	logger          runtime.Logger
	db              *sql.DB
	nk              runtime.NakamaModule
	initializer     runtime.Initializer
	config          runtime.Config // nakama 配置
	CustomConfig    *CustomConfig  // 自定义配置
	isRegisterSelf  bool
	ProxyPassConfig sync.Map
	controller      *Controller
	proxy           *httputil.ReverseProxy
	limiterm        *common.LimiterManager
	ipwm            *common.IPBlackWhiteListManager

	common *logic.CommonGlobalDataStruct
	online *logic.OnlineGlobalDataStruct
	send   *logic.SendGlobalDataStruct
}

var GateServerData *GateServerModule

func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
	GateServerData = new(GateServerModule)
	GateServerData.name = models.SERVER_NAME_GATE
	GateServerData.logger = logger
	GateServerData.db = db
	GateServerData.nk = nk
	GateServerData.initializer = initializer
	GateServerData.limiterm = common.NewLimiterManager()
	GateServerData.ipwm = common.NewIPBlackWhiteListManager()
	GateServerData.common = logic.NewCommonGlobalDataStruct()
	GateServerData.online = logic.NewOnlineGlobalDataStruct(GateServerData)
	GateServerData.send = logic.NewSendGlobalDataStruct(GateServerData)
	GateServerData.proxy = common.NewReverseProxy(GateServerData.targetLBFunc)
	config, err := initializer.GetConfig()
	if err != nil {
		return err
	}
	GateServerData.config = config
	if err := GateServerData.common.Init(GateServerData, GateServerData.CustomConfig, GateServerData.customConfigEvent); err != nil {
		logger.Error("init common logic error %v", err)
		return err
	}

	if err := initializer.RegisterEventSessionStart(GateServerData.eventSessionStart); err != nil {
		return err
	}
	if err := initializer.RegisterEventSessionEnd(GateServerData.eventSessionEnd); err != nil {
		return err
	}
	initializer.RegisterBeforeRt("*", GateServerData.AllBeforeRtFunction)
	initializer.RegisterRpc("*", GateServerData.AllRpcFunction)
	initializer.RegisterRt("*", GateServerData.AllRtFunction)
	initializer.RegisterShutdown(GateServerData.Shutdown)

	// 路由注册
	GateServerData.controller = NewController(GateServerData)
	initializer.RegisterRpc(models.RPCID_GATESERVER_SEND, GateServerData.controller.PostSend)
	initializer.RegisterRpc(models.RPCID_GATESERVER_MULTICAST, GateServerData.controller.PostMulticast)
	initializer.RegisterRpc(models.RPCID_GATESERVER_BOARDCAST, GateServerData.controller.PostBoardcast)
	initializer.RegisterRpc(models.RPCID_GATESERVER_CLOSE, GateServerData.controller.GetClose)

	initializer.RegisterRpc(models.RPCID_GATESERVER_STREAM_USER_LIST, GateServerData.controller.StreamUserList)
	initializer.RegisterRpc(models.RPCID_GATESERVER_STREAM_USER_GET, GateServerData.controller.StreamUserGet)
	initializer.RegisterRpc(models.RPCID_GATESERVER_STREAM_USER_JOIN, GateServerData.controller.StreamUserJoin)
	initializer.RegisterRpc(models.RPCID_GATESERVER_STREAM_USER_UPDATE, GateServerData.controller.StreamUserUpdate)
	initializer.RegisterRpc(models.RPCID_GATESERVER_STREAM_USER_LEAVE, GateServerData.controller.StreamUserLeave)
	initializer.RegisterRpc(models.RPCID_GATESERVER_STREAM_USER_KICK, GateServerData.controller.StreamUserKick)
	initializer.RegisterRpc(models.RPCID_GATESERVER_STREAM_COUNT, GateServerData.controller.StreamCount)
	initializer.RegisterRpc(models.RPCID_GATESERVER_STREAM_CLOSE, GateServerData.controller.StreamClose)
	initializer.RegisterRpc(models.RPCID_GATESERVER_STREAM_SEND, GateServerData.controller.StreamSend)
	initializer.RegisterRpc(models.RPCID_GATESERVER_STREAM_DIRECTSEND, GateServerData.controller.StreamSendDirect)
	initializer.RegisterRpc(models.RPCID_GATESERVER_STREAM_DIRECTSEND_BOARDCAST, GateServerData.controller.StreamSendDirectBoardcast)

	return nil
}

func (s *GateServerModule) GetCommon() *logic.CommonGlobalDataStruct {
	return s.common
}
func (s *GateServerModule) GetOnline() *logic.OnlineGlobalDataStruct {
	return s.online
}
func (s *GateServerModule) GetSend() *logic.SendGlobalDataStruct {
	return s.send
}
func (s *GateServerModule) GetDbAgent() *logic.DbAgentGlobalDataStruct {
	return nil
}
func (s *GateServerModule) GetNotify() *logic.NotifyGlobalDataStruct {
	return nil
}
func (s *GateServerModule) GetStream() *logic.StreamGlobalDataStruct {
	return nil
}
func (s *GateServerModule) GetChannel() *logic.ChannelGlobalDataStruct {
	return nil
}
func (s *GateServerModule) GetLogger() runtime.Logger {
	return s.logger
}
func (s *GateServerModule) GetConfig() runtime.Config {
	return s.config
}
func (s *GateServerModule) GetName() string {
	return s.name
}
func (s *GateServerModule) GetDb() *sql.DB {
	return s.db
}
func (s *GateServerModule) GetNk() runtime.NakamaModule {
	return s.nk
}

// 配置读取回调
func (s *GateServerModule) customConfigEvent(dataId string, newobj any) {
	tmpobj, ok := newobj.(*CustomConfig)
	if !ok {
		return
	}
	if tmpobj.Config == nil {
		tmpobj.Config = &GateServerConfig{}
	}
	defaultNodeCfg := false
	if tmpobj.Config.NodeConfig == nil {
		// 使用默认节点配置
		scheme := "http"
		if s.config.GetSocket().GetUseTLS() {
			scheme = "https"
		}
		tmpobj.Config.NodeConfig = &models.NodeConfig{
			NodeId:   s.config.GetName(),
			SvcGroup: fmt.Sprintf("%s@@%s", s.name, models.NACOS_DEFAULT_GROUP),
			Host:     fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			NetHost:  fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			AuthKey:  s.config.GetRuntime().GetHTTPKey(),
			Weight:   10,
			Scheme:   scheme,
		}
		defaultNodeCfg = true
	}
	tmp, _ := json.Marshal(tmpobj)
	s.logger.Debug("customConfigEvent defaultNodeCfg: %v, customCfg: %s", defaultNodeCfg, string(tmp))

	// 路由代理转发
	if len(tmpobj.Config.Routes) > 0 {
		for k, v := range tmpobj.Config.Routes {
			pathPattern := k
			if v.MatchSuffix != "" {
				pathPattern = pathPattern + v.MatchSuffix
			}
			//urlpath = fmt.Sprintf("%s/{rest:.*}", urlpath)
			err := s.initializer.RegisterHttp(v.Prefix, pathPattern, GateServerData.ProxyHttpHandler())
			if err != nil {
				s.logger.Error("register http %s error %v", pathPattern, err)
			}
		}
	}

	s.common.SetNodeConfig(tmpobj.Config.NodeConfig)
	s.common.WatchNodeList.InitWatchSvr(tmpobj.Config.WatchSvcGroupList, tmpobj.Config.NodeConfig.SvcGroup)
	s.CustomConfig = tmpobj

	// 注册服务
	selfcfg := map[string]interface{}{}
	if err := common.StrcutToMap(tmpobj.Config.NodeConfig, &selfcfg); err == nil {
		if !s.isRegisterSelf {
			go s.common.RegisterAndUpdateSelfToNacos(false, selfcfg)
			s.isRegisterSelf = true
		} else {
			go s.common.RegisterAndUpdateSelfToNacos(true, selfcfg)
		}
	} else {
		s.logger.Error("register self, config error %v", err)
	}

	for k, v := range tmpobj.Config.Routes {
		d := common.NewProxyPassData()
		d.Parse(v)
		s.ProxyPassConfig.Store(k, d)
	}

	s.common.UpdateAccessLogConfig(tmpobj.AccessLog)
	loglevel, err := zapcore.ParseLevel(tmpobj.Logger.Level)
	if err != nil {
		s.logger.Error("parse log level error %v", err)
	} else {
		s.nk.SetLoggerLevel(loglevel)
	}

	GateServerData.ipwm.InitParseSysBlackWhiteList(tmpobj.Config.IPWhiteList, tmpobj.Config.IPBlackList)
	GateServerData.limiterm.SetLimiter(tmpobj.Config.Limiters)
}

func (s *GateServerModule) getProxyPassPath(urlpath string) string {
	if s.CustomConfig == nil {
		s.logger.Error("get proxy pass, custom config is nil, please check config runtime.env: [\"LOCAL_CUSTOM_CONFIG_PATH=./config.yaml\"] or [\"NACOS_CUSTOM_DATA_ID_GROUP_KEY=custom_config\"]")
		return ""
	}

	// 从后往前匹配路径
	parts := strings.Split(urlpath, "/")
	for i := len(parts); i > 0; i-- {
		prefix := strings.Join(parts[:i], "/")
		if prefix == "" {
			prefix = "/"
		}

		// 尝试匹配带斜杠和不带斜杠的路径
		if cfg := s.CustomConfig.Config.Routes[prefix]; cfg != nil {
			return prefix
		}
		if cfg := s.CustomConfig.Config.Routes[prefix+"/"]; cfg != nil {
			return prefix + "/"
		}
	}

	return ""
}

func (s *GateServerModule) targetLBFunc(r *http.Request) *url.URL {
	if r == nil {
		return nil
	}
	matchurlpath := s.getProxyPassPath(r.URL.Path)
	if matchurlpath == "" {
		return nil
	}
	//s.logger.Debug("get proxy pass %s", matchurlpath)
	proxycfg, _ := s.ProxyPassConfig.Load(matchurlpath)
	if proxycfg != nil {
		clientip := common.GetClientIP(r)
		_cfg := proxycfg.(*common.ProxyPassData)
		//t, _ := json.Marshal(_cfg)
		//s.logger.Debug("get proxy pass %v", string(t))
		if _cfg.CfgData != nil && _cfg.CfgData.AuthKey != "" {
			r.SetBasicAuth(_cfg.CfgData.AuthKey, _cfg.CfgData.AuthKey)
		}
		if _cfg.CfgData != nil && _cfg.CfgData.Unwrap && r.URL.Query().Get("unwrap") == "" {
			q := r.URL.Query()
			q.Set("unwrap", "1")
			r.URL.RawQuery = q.Encode()
		}
		return s.common.GetProxyPassByReq(r, clientip, _cfg)
	}
	return nil
}

// curl -X POST 'http://127.0.0.1:7350/v2/account/authenticate/email' --header 'Content-Type: application/json' -H "Authorization: Basic ZGVmYXVsdGtleTo=" --data-raw '{"email": "<EMAIL>", "password": "********"}'
func (s *GateServerModule) ProxyHttpHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		//s.logger.Debug("ProxyHandler %v %v", r.Method, r.URL.Path)

		var err error
		var body []byte
		start := time.Now()
		requestCtx := r.Context()
		clientip := common.GetClientIP(r)
		fields := url.Values{}

		// 标准化路径
		urlpath := strings.ReplaceAll(r.URL.Path, "//", "/")
		if !strings.HasPrefix(urlpath, "/") {
			urlpath = "/" + urlpath
		}
		urlpath = strings.TrimRight(urlpath, "/")
		if urlpath == "" {
			urlpath = "/"
		}
		r.URL.Path = urlpath

		// /v2/rpc/userserver.query
		rpcid := ""
		if strings.HasPrefix(urlpath, "/v2/rpc/") { // 检查rpc请求
			rpcids := strings.Split(urlpath, "/")
			if len(rpcids) > 2 {
				rpcid = rpcids[2]
			}
		}

		if !s.config.GetAccessLog().GetIsDisable() {
			if strings.ToUpper(r.Method) == http.MethodPost {
				body, err = io.ReadAll(r.Body)
				if err != nil {
					s.logger.Error("read body error %v", err)
					w.WriteHeader(http.StatusBadRequest)
					return
				}
				r.Body = io.NopCloser(bytes.NewBuffer(body))
			}
		}

		wc := &common.ResponseWithRecorder{
			ResponseWriter: w,
			StatusCode:     http.StatusOK,
		}

		tr := otel.Tracer(s.config.GetName())
		requestCtx, span := tr.Start(r.Context(), urlpath)
		traceid := span.SpanContext().TraceID().String()

		defer func() {
			if span != nil {
				span.SetAttributes(attribute.String("rpc_id", rpcid))
				if wc.StatusCode != http.StatusOK {
					span.RecordError(errors.New(wc.Body.String()))
				}
				span.End()
			}
			if s.config.GetAccessLog().GetIsDisable() {
				return
			}
			query := r.URL.Query()
			fields.Set("traceid", traceid)
			s.nk.GetAccessLog().Print(&requestCtx, time.Since(start), "", wc.StatusCode, r.Method, r.URL.Path, clientip, string(body), wc.Body.String(), int(r.ContentLength), wc.Body.Len(), nil, &query, &fields)
		}()

		if tokenStr := r.Header.Get("Authorization"); tokenStr != "" {
			// 只解析，不验证签名
			if strings.HasPrefix(tokenStr, "Bearer ") {
				tokenStr = tokenStr[7:]
				jwtToken, _, err := jwt.NewParser().ParseUnverified(tokenStr, &common.SessionTokenClaims{})
				if err == nil && jwtToken != nil {
					claims, ok := jwtToken.Claims.(*common.SessionTokenClaims)
					if ok {
						fields.Set("uid", claims.UserId)
						fields.Set("uin", claims.Uin())
					}
				}
			}
		}

		// 检查白名单
		if GateServerData.ipwm != nil && GateServerData.ipwm.MyIPBlackWhiteListTotal > 0 {
			if code := GateServerData.ipwm.AddIPRequest(urlpath, clientip); code != http.StatusOK {
				wc.WriteHeader(code)
				wc.Write([]byte(http.StatusText(code)))
				return
			}
			if rpcid != "" {
				if code := GateServerData.ipwm.AddIPRequest(rpcid, clientip); code != http.StatusOK {
					wc.WriteHeader(code)
					wc.Write([]byte(http.StatusText(code)))
					return
				}
			}
		}

		// 限流
		if GateServerData.limiterm != nil && GateServerData.limiterm.GetLimiterTotal() > 0 {
			if l := GateServerData.limiterm.GetLimiter(urlpath); l != nil {
				if !l.Allow() {
					wc.WriteHeader(http.StatusTooManyRequests)
					wc.Write([]byte("Too Many Requests"))
					return
				}
			}
			if rpcid != "" {
				if l := GateServerData.limiterm.GetLimiter(rpcid); l != nil {
					if !l.Allow() {
						wc.WriteHeader(http.StatusTooManyRequests)
						wc.Write([]byte("Too Many Requests"))
						return
					}
				}
			}
		}

		if s.proxy != nil {
			s.proxy.ServeHTTP(wc, r)
		}
	}
}

func (s *GateServerModule) AllBeforeRtFunction(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	//logger.Info("allBeforeRtFunction %v %v %s", ctx, in, in.Cid)
	return in, nil
}

func (s *GateServerModule) AllRtFunction(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	//logger.Info("allRtFunction %v %v %s", ctx, in, in.Cid)
	messageName := fmt.Sprintf("%T", in.Message) // strings.ToLower(RTAPI_PREFIX) + RTID
	messageNameID := strings.ToLower(messageName)
	payload, err := protojson.Marshal(in)
	if err != nil {
		logger.Error("marshal envelope error: %s %v", messageNameID, err)
		return nil, err
	}
	var respdata []byte
	if strings.HasPrefix(messageNameID, "*rtapi.envelope_channel") {
		respdata, err = s.send.Rpc(ctx, models.SERVER_NAME_IM, models.NACOS_DEFAULT_GROUP, models.RPCID_IMSERVER_CHANNEL_RT, payload, nil, models.RPC_TIMEOUT_DEFAULT)
	} else if strings.HasPrefix(messageNameID, "*rtapi.envelope_party") {
		respdata, err = s.send.Rpc(ctx, models.SERVER_NAME_IM, models.NACOS_DEFAULT_GROUP, models.RPCID_IMSERVER_PARTY_RT, payload, nil, models.RPC_TIMEOUT_DEFAULT)
	} else {
		logger.Error("unknown message type: %s", messageNameID)
		return nil, fmt.Errorf("unknown message type: %s", messageNameID)
	}

	if err != nil {
		logger.Error("rpc error: %v", err)
		return nil, err
	}
	out := &rtapi.Envelope{}
	err = protojson.Unmarshal(respdata, out)
	if err != nil {
		logger.Error("unmarshal envelope error: %v", err)
		return nil, err
	}
	return out, nil
}

func (s *GateServerModule) AllRpcFunction(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	raw_rpc_id := ""
	headers := ctx.Value(runtime.RUNTIME_CTX_HEADERS)
	if tmpheaders, ok := headers.(map[string][]string); ok && len(tmpheaders) > 0 && len(tmpheaders[runtime.RUNTIME_CTX_HEADER_RPC_ID]) > 0 {
		raw_rpc_id = tmpheaders[runtime.RUNTIME_CTX_HEADER_RPC_ID][0]
	}
	clientip := ctx.Value(runtime.RUNTIME_CTX_CLIENT_IP).(string)
	//logger.Info("allRpcFunction %v", raw_rpc_id)

	// 如果是网关自己的rpc，又没有方法，防止死循环
	if strings.HasPrefix(raw_rpc_id, models.SERVER_NAME_GATE) {
		logger.Error("rpc function %s not found", raw_rpc_id)
		return "", errors.New("rpc function not found " + raw_rpc_id)
	}

	// 检查白名单
	if GateServerData.ipwm.MyIPBlackWhiteListTotal > 0 && raw_rpc_id != "" {
		if code := GateServerData.ipwm.AddIPRequest(raw_rpc_id, clientip); code != http.StatusOK {
			str, _ := json.MarshalToString(models.CommonResp{Code: models.ACCESS_DENIED, Msg: http.StatusText(code)})
			return str, nil
		}
	}

	// 限流
	if GateServerData.limiterm.GetLimiterTotal() > 0 && raw_rpc_id != "" {
		if l := GateServerData.limiterm.GetLimiter(raw_rpc_id); l != nil {
			if !l.Allow() {
				str, _ := json.MarshalToString(models.CommonResp{Code: models.BUSY, Msg: http.StatusText(http.StatusTooManyRequests)})
				return str, nil
			}
		}
	}

	// 解析rpc转发到哪个服务
	rpcids := strings.Split(raw_rpc_id, ".")
	if len(rpcids) < 2 {
		str, _ := json.MarshalToString(models.CommonResp{Code: models.PARAM_ERR, Msg: "rpc id error"})
		return str, nil
	}

	servicename := rpcids[0]
	respdata, err := s.send.Rpc(ctx, servicename, models.NACOS_DEFAULT_GROUP, raw_rpc_id, []byte(payload), nil, models.RPC_TIMEOUT_DEFAULT)

	return string(respdata), err
}

func (s *GateServerModule) eventSessionStart(ctx context.Context, logger runtime.Logger, evt *api.Event) {
	//logger.Info("session start %v %v", ctx, evt)
	user_id := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	session_id := ctx.Value(runtime.RUNTIME_CTX_SESSION_ID).(string)
	client_ip := ctx.Value(runtime.RUNTIME_CTX_CLIENT_IP).(string)

	tr := otel.Tracer(s.config.GetName())
	requestCtx, span := tr.Start(ctx, models.RPCID_SESSIONSERVER_SESSION_ONLINE)
	//traceid := span.SpanContext().TraceID().String()
	defer span.End()

	result, err := s.send.RpcObj(requestCtx, models.SERVER_NAME_SESSION, models.NACOS_DEFAULT_GROUP, models.RPCID_SESSIONSERVER_SESSION_ONLINE, &models.UserOnlineInfo{
		UserId:     user_id,
		Uin:        s.common.GetUin(requestCtx),
		SessionId:  session_id,
		NodeId:     s.CustomConfig.Config.NodeConfig.NodeId,
		NodeAddr:   s.CustomConfig.Config.NodeConfig.Host,
		ClientIP:   client_ip,
		OnlineTime: time.Now().UnixMilli(),
	}, nil, time.Second*10)
	if err != nil {
		logger.Error("online_resp error: %s %s %v", user_id, session_id, err)
		s.nk.SessionDisconnect(requestCtx, session_id, runtime.PresenceReasonDisconnect)
		return
	}

	result, err = s.send.RpcObj(requestCtx, models.SERVER_NAME_LOGIC, models.NACOS_DEFAULT_GROUP, models.RPCID_LOGICSERVER_LOGINFINISH, "", nil, time.Second*10)
	if err != nil {
		logger.Error("loginfinish_resp error: %s %s %v", user_id, session_id, err)
		s.nk.SessionDisconnect(requestCtx, session_id, runtime.PresenceReasonDisconnect)
		return
	}

	var resp models.CommonResp
	json.Unmarshal(result, &resp)
	if resp.Code != models.OK {
		logger.Error("online_resp failed: %s %s %v", user_id, session_id, resp.Msg)
		s.nk.SessionDisconnect(requestCtx, session_id, runtime.PresenceReasonDisconnect)
		return
	}
}

func (s *GateServerModule) eventSessionEnd(ctx context.Context, logger runtime.Logger, evt *api.Event) {
	//logger.Info("session end %v %v", ctx, evt)
	user_id := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	session_id := ctx.Value(runtime.RUNTIME_CTX_SESSION_ID).(string)

	tr := otel.Tracer(s.config.GetName())
	requestCtx, span := tr.Start(ctx, models.RPCID_SESSIONSERVER_SESSION_OFFLINE)
	//traceid := span.SpanContext().TraceID().String()
	defer span.End()

	_, err := s.send.RpcObj(requestCtx, models.SERVER_NAME_SESSION, models.NACOS_DEFAULT_GROUP, models.RPCID_SESSIONSERVER_SESSION_OFFLINE, nil, nil, time.Second*10)
	if err != nil {
		logger.Error("offline_resp error: %s %s %v", user_id, session_id, err)
	}
}

func (s *GateServerModule) Shutdown(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, reload bool) {
	logger.Info("%s shutdown", s.name)
	if !reload {
		s.common.UnRegisterSelfToNacos()
	}
	list := nk.SessionGetList()
	str, _ := json.Marshal(list)
	logger.Info("Shutdown session list %v", string(str))
	_, err := s.send.RpcObj(ctx, models.SERVER_NAME_SESSION, models.NACOS_DEFAULT_GROUP, models.RPCID_SESSIONSERVER_SESSION_BATCHOFFLINE, list, nil, time.Second*10)
	if err != nil {
		logger.Error("batch_offline_resp error: %v", err)
	}
	//s.online.UserOfflineEventBatch(list)
}
