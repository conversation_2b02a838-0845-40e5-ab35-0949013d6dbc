<h2 class="pb-4">User Management</h2>

<ngb-alert *ngIf="error" type="danger" [dismissible]="false">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">An error occurred: {{error}}</h6>
</ngb-alert>

<ngb-alert type="success" class="mb-3" [dismissible]="true" *ngIf="successMessage">
  <img src="/static/svg/green-tick.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">{{successMessage}}</h6>
</ngb-alert>

<table class="user-details mb-5 table table-bordered table-sm table-striped">
  <thead class="thead-light">
    <tr>
      <th style="width: 400px">Username</th>
      <th>Email</th>
      <th style="width: 300px">Role</th>
      <th>M<PERSON> Required</th>
      <th>MFA Setup</th>
      <th style="width: 90px">Action</th>
    </tr>
  </thead>
  <tbody>
  <tr *ngIf="users.length === 0">
    <td [colSpan]="5" class="text-muted">No additional users are setup. Create a new user below.</td>
  </tr>
  <tr *ngFor="let user of users">
    <td>{{user.username}}</td>
    <td>{{user.email}}</td>
    <td>
      <span [hidden]="user.role !== adminRole">Administrator</span>
      <span [hidden]="user.role !== developerRole">Developer</span>
      <span [hidden]="user.role !== maintainerRole">Maintainer</span>
      <span [hidden]="user.role !== readonlyRole">View Only</span>
    </td>
    <td>{{user.mfa_required}}</td>
    <td>{{user.mfa_enabled}}</td>
    <td style="width: 310px;">
      <div class="btn-group">
        <button [disabled]="user.mfa_required" type="button" class="btn btn-warning btn-outline-dark" (click)="requireUserMfa(user.username, true)">Enforce MFA</button>
        <button [disabled]="!user.mfa_enabled" type="button" class="btn btn-warning btn-outline-dark" (click)="resetUserMfa(user.username)">Reset MFA</button>
        <button type="button" class="btn btn-danger btn-outline-dark" (click)="deleteUser(user.username)">Delete</button>
      </div>
    </td>
  </tr>
  </tbody>
</table>

<h5 class="section-divider d-flex mb-4">Add new user</h5>

<ngb-alert [type]="'secondary'" class="mb-4 alert-permissions" [dismissible]="false">
  <h6 class="alert-title font-weight-bold">User roles</h6>
  <div class="d-flex flex-wrap flex-row">
    <div class="col col-md-3 p-4 flex-fill flex-grow-1">
      <div class="d-flex align-items-center mb-3">
        <img class="mr-2" src="/static/svg/role-admin.svg" alt="" width="30" height="">
        <h6 class="m-0 font-weight-bold">Administrator</h6>
      </div>
      <small>
        Owners have complete control over the server, its users and resources.
      </small>
    </div>

    <div class="col col-md-3 p-4 flex-fill flex-grow-1">
      <div class="d-flex align-items-center mb-3">
        <img class="mr-2" src="/static/svg/role-developer.svg" alt="" width="30" height="">
        <h6 class="m-0 font-weight-bold">Developer</h6>
      </div>
      <small>
        Like Administrators, Developers have complete control over the server, resources and data. However they cannot add, change or delete console users.
      </small>
    </div>

    <div class="col col-md-3 p-4 flex-fill flex-grow-1">
      <div class="d-flex align-items-center mb-3">
        <img class="mr-2" src="/static/svg/role-maintainer.svg" alt="" width="30" height="">
        <h6 class="m-0 font-weight-bold">Maintainer</h6>
      </div>
      <small>
        Maintainers have access to adding, changing and delete client resources such as accounts, storage and leaderboard records. They don't have access to the API Explorer or the console users.
      </small>
    </div>

    <div class="col col-md-3 p-4 flex-fill flex-grow-1">
      <div class="d-flex align-items-center mb-3">
        <img class="mr-2" src="/static/svg/role-viewonly.svg" alt="" width="30" height="">
        <h6 class="m-0 font-weight-bold">View Only</h6>
      </div>
      <small>
        View Only users can only view client resources but cannot make any changes to the data whatsoever.
      </small>
    </div>
  </div>
</ngb-alert>

<ngb-alert *ngIf="userCreateError" type="danger" [dismissible]="false">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">An error occurred:</h6>
  <p class="mb-0 pl-4">{{userCreateError}}</p>
</ngb-alert>

<div class="add-border rounded">
  <form [formGroup]="createUserForm" (ngSubmit)="addUser()">
    <div class="row no-gutters">
      <div class="col d-flex justify-content-between align-items-center">
        <div class="col-md-3">
          <label class="d-inline" for="email">Email</label>
        </div>
        <div class="col-md-9 ml-0 p-0">
          <input type="email" id="email" class="form-control" placeholder="<EMAIL>" required formControlName="email" [ngClass]="{'is-invalid': f.email.dirty && f.email.errors}">
          <div class="invalid-tooltip" [hidden]="f.email.disabled || f.email.valid || f.email.pristine">Email is required</div>
        </div>
      </div>
    </div>

    <div class="row no-gutters">
      <div class="col d-flex justify-content-between align-items-center">
        <div class="col-md-3">
          <label class="d-inline" for="username">Username</label>
        </div>
        <div class="col-md-9 ml-0 p-0">
          <input type="text" id="username" class="form-control" placeholder="Username" required formControlName="username" [ngClass]="{'is-invalid': f.username.dirty && f.username.errors}">
          <div class="invalid-tooltip" [hidden]="f.username.disabled || f.username.valid || f.username.pristine">Username is required</div>
        </div>
      </div>
    </div>

    <div class="row no-gutters">
      <div class="col d-flex justify-content-between align-items-center">
        <div class="col-md-3">
          <label class="d-inline" for="password">Password</label>
        </div>
        <div class="col-md-9 ml-0 p-0">
          <input type="password" id="password" class="form-control" placeholder="Password" required formControlName="password" [ngClass]="{'is-invalid': f.password.dirty && f.password.errors}">
          <div class="invalid-tooltip" [hidden]="f.password.disabled || f.password.valid || f.password.pristine">Password is required, must be 8 chars or longer and consist of at least a capital letter, a small letter and a number.</div>
        </div>
      </div>
    </div>

    <div class="row no-gutters">
      <div class="col d-flex justify-content-between align-items-center">
        <div class="col-md-3">
          <label class="d-inline">Role</label>
        </div>
        <div class="col-md-9 ml-0 p-0">
          <div class="btn-group">
            <button disabled class="btn btn-outline-dark disabled">
              <span [hidden]="f.role.value !== +adminRole">Administrator</span>
              <span [hidden]="f.role.value !== +developerRole">Developer</span>
              <span [hidden]="f.role.value !== +maintainerRole">Maintainer</span>
              <span [hidden]="f.role.value !== +readonlyRole">View Only</span>
            </button>
            <div class="btn-group" ngbDropdown role="group">
              <button type="button" class="btn btn-primary dropdown-toggle-split" ngbDropdownToggle></button>
              <div class="dropdown-menu" ngbDropdownMenu>
                <button type="button" ngbDropdownItem (click)="f.role.setValue(+adminRole)">Administrator</button>
                <button type="button" ngbDropdownItem (click)="f.role.setValue(+developerRole)">Developer</button>
                <button type="button" ngbDropdownItem (click)="f.role.setValue(+maintainerRole)">Maintainer</button>
                <button type="button" ngbDropdownItem (click)="f.role.setValue(+readonlyRole)">View Only</button>
              </div>
            </div>
          </div>
          <div class="invalid-tooltip" [hidden]="f.role.disabled || f.role.valid || f.role.pristine">Role is required</div>
        </div>
      </div>
    </div>

    <div class="row no-gutters">
      <div class="col d-flex align-items-center">
        <div class="col-md-3">
          <label class="d-inline">Require Multi-factor Authentication</label>
        </div>
        <div class="col-md-9 ml-0 p-0">
          <div class="custom-control custom-checkbox">
            <input type="checkbox" id="mfa" class="custom-control-input mr-2 my-2" formControlName="mfa"/>
            <label class="form-check-label custom-control-label" for="mfa"></label>
          </div>
        </div>
      </div>
    </div>

    <div class="row no-gutters add-border-single-row-bottom mb-4">
      <div class="col d-flex align-items-center">
        <div class="col-md-3"></div>
        <div class="col-md-9 ml-0 p-0">
          <div class="custom-control custom-checkbox">
            <input type="checkbox" id="newsletter" class="custom-control-input mr-2 my-2" formControlName="newsletter"/>
            <label class="form-check-label custom-control-label" for="newsletter">Subscribe to Heroic Labs' newsletters to receive latest updates to Nakama and other news.</label>
          </div>
        </div>
      </div>
    </div>

    <div class="">
      <button type="submit" [disabled]="createUserForm.invalid" class="btn btn-primary">Save</button>
    </div>
  </form>
</div>
