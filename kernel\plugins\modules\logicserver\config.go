package main

import (
	"kernel/plugins/models"
)

type CustomConfig struct {
	Config    *LogicServerConfig      `json:"logic_server_config,omitempty" yaml:"logic_server_config,omitempty"`
	AccessLog *models.AccessLogConfig `json:"access_log,omitempty" yaml:"access_log,omitempty"`
	Logger    *models.LoggerConfig    `json:"logger,omitempty" yaml:"logger,omitempty"`
}

type LogicServerConfig struct {
	NodeConfig        *models.NodeConfig `json:"node_config,omitempty" yaml:"node_config,omitempty"`
	WatchSvcGroupList []string           `json:"watch_svc_group_list,omitempty" yaml:"watch_svc_group_list,omitempty"`
	ReportCfg         *ReportConfig      `json:"report_cfg,omitempty" yaml:"report_cfg,omitempty"`
	ObsCfg            *ObsConfig         `json:"obs_cfg,omitempty" yaml:"obs_cfg,omitempty"`
}

type ReportConfig struct {
	Url    string `json:"url,omitempty" yaml:"url,omitempty"`
	AppID  string `json:"app_id,omitempty" yaml:"app_id,omitempty"`
	Secret string `json:"secret,omitempty" yaml:"secret,omitempty"`
}

type ObsConfig struct {
	ObsType   string `json:"obs_type,omitempty" yaml:"obs_type,omitempty"`     // obs类型(huawei,aliyun,tencent,s3_???)
	AccessKey string `json:"access_key,omitempty" yaml:"access_key,omitempty"` // 访问密钥
	SecretKey string `json:"secret_key,omitempty" yaml:"secret_key,omitempty"` // 密钥
	EndPoint  string `json:"end_point,omitempty" yaml:"end_point,omitempty"`   // 端点
	Bucket    string `json:"bucket,omitempty" yaml:"bucket,omitempty"`         // 桶
}
