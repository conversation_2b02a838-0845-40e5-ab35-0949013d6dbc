package main

import (
	"context"
	"database/sql"
	"fmt"
	"kernel/kernel-common/api"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"math/rand"
	"strconv"
	"strings"
	"sync"
	"time"

	commbo "kernel/plugins/bo"
	"kernel/plugins/models"
	"kernel/plugins/modules/loginserver/bo"

	"github.com/golang-jwt/jwt/v4"
	json "github.com/json-iterator/go"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type Controller struct {
	server *LoginServerModule
}

func NewController(s *LoginServerModule) *Controller {
	return &Controller{
		server: s,
	}
}

func SanitizeUsername(input string) string {
	// 将非法字符替换为空
	return InvalidCharsRegex.ReplaceAllString(input, "")
}

// @Summary 自定义验证邮箱
// @Description 自定义验证邮箱
// @Accept json
// @Produce json
// @Param account body AccountEmail true "账号信息"
// @Success 200 {object} RespAuthenticateEmail "创建结果"
// @Router /v2/rpc/loginserver.auth.email [post]
func (c *Controller) AuthenticateEmail(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var req ReqAuthenticateEmail
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&RespAuthenticateEmail{Code: models.PARAM_ERR, Msg: err.Error()})
	}
	if req.Account == nil || req.Account.Email == "" || req.Account.Password == "" {
		return json.MarshalToString(&RespAuthenticateEmail{Code: models.PARAM_ERR, Msg: "email or password is empty"})
	}

	emailCfg := c.server.CustomConfig.Config.EmailCfg
	if emailCfg == nil {
		logger.Error("email config is not set, please check the config email_cfg")
		return json.MarshalToString(&RespAuthenticateEmail{Code: models.EXCEPTION, Msg: "exception, please try again later"})
	}

	// 这里不创建
	uid, usn, created, err := nk.AuthenticateEmail(ctx, req.Account.Email, req.Account.Password, req.Username, false)
	if err != nil {
		if err == status.Error(codes.NotFound, "User account not found.") {
			// 发送验证码到邮箱
			code := common.GetRandomCode(6)
			err = c.server.common.Redis.Set(ctx, fmt.Sprintf("email_code:%s", req.Account.Email), code, time.Minute*5).Err()
			if err != nil {
				logger.Error("Failed to set email code to redis: %v", err)
				return json.MarshalToString(&RespAuthenticateEmail{Code: models.EXCEPTION, Msg: "exception, please try again later"})
			}

			subject := "Verification Code"
			htmlBody := fmt.Sprintf(`
				<p>Your verification code is: <strong style="font-size:18px;">%s</strong></p>
				<p>Valid for 5 minutes.</p>`, code)
			err = common.SendMail(emailCfg.SmtpHost, emailCfg.SmtpPort, emailCfg.FromEmail, emailCfg.FromPasswd, []string{req.Account.Email}, subject, htmlBody)
			if err != nil {
				logger.Error("Failed to send email: %v", err)
				return json.MarshalToString(&RespAuthenticateEmail{Code: models.EXCEPTION, Msg: "exception, please try again later"})
			}
			return json.MarshalToString(&RespAuthenticateEmail{Code: models.AUTH_CODE, Msg: "please look at your email for the verification code"})
		}
		return json.MarshalToString(&RespAuthenticateEmail{Code: models.FAILED, Msg: err.Error()})
	}

	token, refreshToken, _, err := nk.AuthenticateTokenGenerate(uid, usn, 0, map[string]string{})
	if err != nil {
		return json.MarshalToString(&RespAuthenticateEmail{Code: models.FAILED, Msg: err.Error()})
	}

	out := &api.Session{Token: token, RefreshToken: refreshToken, Created: created}
	err = c.AfterAuthenticate(ctx, logger, db, nk, out)
	if err != nil {
		return json.MarshalToString(&RespAuthenticateEmail{Code: models.FAILED, Msg: err.Error()})
	}

	return json.MarshalToString(&RespAuthenticateEmail{Code: models.OK, Msg: "ok", Created: out.Created, Token: out.Token, RefreshToken: out.RefreshToken})
}

// @Summary 验证邮箱验证码
// @Description 验证邮箱验证码
// @Accept json
// @Produce json
// @Param account body AccountEmail true "账号信息"
// @Success 200 {object} RespAuthenticateEmail "验证结果"
// @Router /v2/rpc/loginserver.auth.emailcode [post]
func (c *Controller) AuthenticateEmailCode(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var req ReqAuthenticateEmail
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&RespAuthenticateEmail{Code: models.PARAM_ERR, Msg: err.Error()})
	}
	if req.Account == nil || req.Account.Email == "" || req.Account.Password == "" || req.Code == "" {
		return json.MarshalToString(&RespAuthenticateEmail{Code: models.PARAM_ERR, Msg: "email or password or code is empty"})
	}

	// 验证验证码
	code, err := c.server.common.Redis.Get(ctx, fmt.Sprintf("email_code:%s", req.Account.Email)).Result()
	if err != nil {
		return json.MarshalToString(&RespAuthenticateEmail{Code: models.EXCEPTION, Msg: "exception, please try again later"})
	}
	if code != req.Code {
		return json.MarshalToString(&RespAuthenticateEmail{Code: models.AUTH_ERR, Msg: "email code is incorrect"})
	}

	uid, usn, created, err := nk.AuthenticateEmail(ctx, req.Account.Email, req.Account.Password, req.Username, true)
	if err != nil {
		return json.MarshalToString(&RespAuthenticateEmail{Code: models.FAILED, Msg: err.Error()})
	}

	token, refreshToken, _, err := nk.AuthenticateTokenGenerate(uid, usn, 0, map[string]string{})
	if err != nil {
		return json.MarshalToString(&RespAuthenticateEmail{Code: models.FAILED, Msg: err.Error()})
	}

	out := &api.Session{Token: token, RefreshToken: refreshToken, Created: created}
	err = c.AfterAuthenticate(ctx, logger, db, nk, out)
	if err != nil {
		return json.MarshalToString(&RespAuthenticateEmail{Code: models.FAILED, Msg: err.Error()})
	}

	return json.MarshalToString(&RespAuthenticateEmail{Code: models.OK, Msg: "ok", Created: out.Created, Token: out.Token, RefreshToken: out.RefreshToken})
}

// @Summary 根据邮箱注册账号
// @Description 根据邮箱注册账号，不需要验证邮箱，一般给运营后台调用
// @Accept json
// @Produce json
// @Param account body AccountEmail true "账号信息"
// @Success 200 {object} AccountEmailResp "创建结果"
// @Router /v2/rpc/loginserver.createaccount.email [post]
func (c *Controller) CreateAccountFromEmail(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var req AccountEmail
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&AccountEmailResp{Code: models.FAILED, Msg: err.Error()})
	}

	if req.Email == "" || req.Password == "" {
		return json.MarshalToString(&AccountEmailResp{Code: models.PARAM_ERR, Msg: "email or password is empty"})
	}

	uin := int32(0)
	if len(req.Vars) > 0 && req.Vars["uin"] != "" {
		// 查找uin是否存在
		tmpuin, err := strconv.ParseInt(req.Vars["uin"], 10, 32)
		if err != nil {
			return json.MarshalToString(&AccountEmailResp{Code: models.PARAM_ERR, Msg: "uin is not a number"})
		}
		uin = int32(tmpuin)
		user := commbo.User{}
		obj := c.server.dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{"uin": uin}), models.WithNotOptCache(true))
		defer c.server.dbagent.Release(obj)
		err = obj.Query(ctx).As(&user)
		if err != nil {
			if err != models.ErrDBResEmpty {
				return json.MarshalToString(&AccountEmailResp{Code: models.FAILED, Msg: err.Error()})
			}
		} else {
			return json.MarshalToString(&AccountEmailResp{Code: models.REPEAT_REG, Msg: "uin already exists"})
		}
	}

	nick := ""
	if len(req.Vars) > 0 && req.Vars["nick"] != "" {
		nick = req.Vars["nick"]
		nick = SanitizeUsername(nick) // 清理非法字符
	}
	if nick == "" {
		nick = "Guest" + strconv.Itoa(rand.Intn(100000))
	}

	uid, usn, created, err := nk.AuthenticateEmail(ctx, req.Email, req.Password, nick, true)
	if err != nil {
		if err == status.Error(codes.Unauthenticated, "Invalid credentials.") {
			return json.MarshalToString(&AccountEmailResp{Code: models.REPEAT_REG, Msg: "email already exists"})
		}
		if strings.Contains(err.Error(), "Invalid credentials") {
			return json.MarshalToString(&AccountEmailResp{Code: models.REPEAT_REG, Msg: "email already exists"})
		}
		return json.MarshalToString(&AccountEmailResp{Code: models.FAILED, Msg: err.Error()})
	}
	if !created {
		return json.MarshalToString(&AccountEmailResp{Code: models.REPEAT_REG, Msg: "email already exists"})
	}

	// 如果uin存在，则更新uin
	if uin > 0 {
		user := commbo.User{}
		obj := c.server.dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{user.GetKeyName(): uid})).WithOption(logic.WithAutoLock())
		defer c.server.dbagent.Release(obj)

		metadata := models.UserMetaData{}
		metadata.RoleId = uin
		metadata.RoleData = ""
		metadata.LastLoginTime = 0
		user.Metadata, _ = json.MarshalToString(metadata)
		user.Uin = metadata.RoleId
		user.UpdateTime = time.Now()
		obj.WithReqOption(models.WithValues(map[string]interface{}{"metadata": user.Metadata, "uin": user.Uin, "update_time": user.UpdateTime}))
		if resp, err := obj.Update(ctx).Result(); err != nil {
			logger.Warn("Failed to update user uin, uid:%s, uin:%d, err:%v", uid, uin, err)
		} else {
			if resp.RowsAffected > 0 {
				logger.Info("Update user uin success, uid:%s, uin:%d", uid, uin)
			} else {
				logger.Warn("Update user uin failed, uid:%s, uin:%d", uid, uin)
			}
		}
	}

	logger.Info("Create account from email success, uid:%s, usn:%s, created:%v, vars:%v", uid, usn, created, req.Vars)
	return json.MarshalToString(&AccountEmailResp{Code: models.OK, Msg: "ok"})
}

func (c *Controller) AfterAuthenticate(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, out *api.Session) error {
	// 解码现有的 JWT token
	parsedToken, err := jwt.ParseWithClaims(out.Token, &common.SessionTokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(c.server.config.GetSession().GetEncryptionKey()), nil
	})
	if err != nil {
		logger.Error("Failed to parse JWT token: %v", err)
		return fmt.Errorf("failed to parse JWT token")
	}

	// 获取 token 的声明部分
	claims, ok := parsedToken.Claims.(*common.SessionTokenClaims)
	if !ok {
		logger.Error("Failed to extract claims from JWT token")
		return fmt.Errorf("failed to extract claims from JWT token")
	}

	uid := claims.UserId
	usn := claims.Username
	vrs := claims.Vars

	// 根据uuid 查询 role_id(uin)
	user := commbo.User{}
	obj := c.server.dbagent.Create(ctx, &user, models.WithKeys(map[string]interface{}{user.GetKeyName(): uid})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx).As(&user)
	if err != nil {
		logger.Error("Failed to query user for user %s: %v", uid, err)
		return fmt.Errorf("failed to retrieve user for user %s", uid)
	}

	// var metadata string
	// query := `SELECT metadata FROM users WHERE id = $1`
	// err = db.QueryRowContext(ctx, query, uid).Scan(&metadata)
	// if err != nil {
	// 	// 如果查询失败，记录错误并返回
	// 	logger.Error("Failed to query role_id for user %s: %v", uid, err)
	// 	return fmt.Errorf("failed to retrieve role_id for user %s", uid)
	// }

	metadata := models.UserMetaData{}
	if user.Metadata == "" {

	} else {
		err = json.Unmarshal([]byte(user.Metadata), &metadata)
		if err != nil {
			logger.Error("Failed to unmarshal metadata for user_id %s: %v", uid, err)
			return fmt.Errorf("failed to unmarshal metadata for user_id %s: %v", uid, err)
		}
		logger.Debug("role_id: %d, uin: %d", metadata.RoleId, user.Uin)
		if metadata.RoleId == 0 {
			metadata.RoleId = user.Uin
			metadata.RoleData = ""
			metadata.LastLoginTime = 0
			user.Metadata, _ = json.MarshalToString(metadata)
			user.UpdateTime = time.Now()
			obj.WithReqOption(models.WithValues(map[string]interface{}{"metadata": user.Metadata, "update_time": user.UpdateTime}))
			if resp, err := obj.Update(ctx).Result(); err != nil {
				logger.Error("Failed to update user, for user_id %s: %v", uid, err)
			} else {
				if resp.RowsAffected <= 0 {
					logger.Error("Failed to update user, for user_id %s", uid)
				}
			}
		} else {
			// 更新昵称
			if len(vrs) > 0 && vrs["nick"] != "" && vrs["nick"] != user.DisplayName {
				user.DisplayName = vrs["nick"]
				user.UpdateTime = time.Now()
				obj.WithReqOption(models.WithValues(map[string]interface{}{"display_name": user.DisplayName, "update_time": user.UpdateTime}))
				if _, err := obj.Update(ctx).Result(); err != nil {
					logger.Error("Failed to update user, for user_id %s: %v", uid, err)
				}
			}
			if user.DisplayName == "" {
				user.DisplayName = "Guest" + strconv.Itoa(rand.Intn(100000))
				user.UpdateTime = time.Now()
				obj.WithReqOption(models.WithValues(map[string]interface{}{"display_name": user.DisplayName, "update_time": user.UpdateTime}))
				if _, err := obj.Update(ctx).Result(); err != nil {
					logger.Error("Failed to update user, for user_id %s: %v", uid, err)
				}
			}
		}
	}
	obj.UnlockAll()

	vars := map[string]string{
		"uin":  strconv.Itoa(int(metadata.RoleId)),
		"nick": user.DisplayName,
	}
	update_token, update_refresh_token, _, err := nk.AuthenticateTokenGenerate(uid, usn, 0, vars)
	if err != nil {
		logger.Error("Failed to generate token for user %s: %v", uid, err)
		return fmt.Errorf("failed to generate token for user %s", uid)
	}
	out.Token = update_token
	out.RefreshToken = update_refresh_token

	ip := c.server.common.GetClientIP(ctx)
	c.RecordLoginLog(ctx, logger, uid, user.Uin, ip, "", "{}")

	return nil
}

// 记录登录日志
func (c *Controller) RecordLoginLog(ctx context.Context, logger runtime.Logger, uid string, uin int32, ip string, device string, metadata string) {
	if metadata == "" {
		metadata = "{}"
	}
	obj := c.server.dbagent.Create(ctx, &bo.LoginLogs{}, models.WithNotOptCache(true))
	defer c.server.dbagent.Release(obj)

	strsql := fmt.Sprintf("INSERT INTO login_logs (user_id, uin, login_time, ip_address, device, metadata) VALUES ('%s', %d, '%s', '%s', '%s', '%s')", uid, uin, time.Now().Format("2006-01-02 15:04:05"), ip, device, metadata)
	err := obj.RawExec(ctx, strsql).Error()
	if err != nil {
		logger.Error("Failed to record login log for user %s: %v", uid, err)
	}
}

// @Summary 查询用户登录记录
// @Description 查询用户登录记录
// @Accept json
// @Produce json
// @Param account body ReqQueryLoginLogs true "查询条件"
// @Success 200 {object} RespQueryLoginLogs "查询结果"
// @Router /v2/rpc/loginserver.queryloginlogs [post]
func (c *Controller) GetLoginLogs(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var req ReqQueryLoginLogs
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&RespQueryLoginLogs{Code: models.EXCEPTION, Msg: err.Error()})
	}

	var total int32

	if req.Sort == "" || (req.Sort != "asc" && req.Sort != "desc") {
		req.Sort = "desc"
	}

	if req.PageNum < 1 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}
	offset := (req.PageNum - 1) * req.PageSize
	limit := req.PageSize

	// 创建一个数据库对象
	obj := c.server.dbagent.Create(ctx, &bo.LoginLogs{}, models.WithNotOptCache(true))
	defer c.server.dbagent.Release(obj)

	// 优化：同时查询用户列表和总数，减少一次数据库查询
	list := []*bo.LoginLogs{}
	var totalResults []struct {
		Total int32 `json:"total"`
	}

	sqlcond := ""
	conditions := []string{}
	// uin 和 user_id 只能有一个
	if req.UserId != "" {
		conditions = append(conditions, fmt.Sprintf("user_id = '%s'", req.UserId))
	} else if req.Uin != 0 {
		conditions = append(conditions, fmt.Sprintf("uin = %d", req.Uin))
	}
	if req.StartTime > 0 {
		conditions = append(conditions, fmt.Sprintf("login_time >= to_timestamp(%d)", req.StartTime))
	}
	if req.EndTime > 0 {
		conditions = append(conditions, fmt.Sprintf("login_time <= to_timestamp(%d)", req.EndTime))
	}
	if len(conditions) > 0 {
		sqlcond = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 并行执行两个查询
	var wg sync.WaitGroup
	var listErr, countErr error
	wg.Add(2)
	c.server.common.GroutinePool.Submit(func() {
		defer wg.Done()
		query := fmt.Sprintf("SELECT * FROM login_logs %s ORDER BY login_time %s LIMIT %d OFFSET %d", sqlcond, req.Sort, limit, offset)
		listErr = obj.RawQuery(ctx, query).As(&list)
	})
	c.server.common.GroutinePool.Submit(func() {
		defer wg.Done()
		query := fmt.Sprintf("SELECT COUNT(*) AS total FROM login_logs %s", sqlcond)
		countErr = obj.RawQuery(ctx, query).As(&totalResults)
	})
	wg.Wait()

	// 处理用户列表查询错误
	if listErr != nil {
		logger.Error("Failed to query login_logs: %v", listErr)
		return json.MarshalToString(&RespQueryLoginLogs{Code: models.EXCEPTION, Msg: listErr.Error()})
	}

	// 处理用户总数查询错误
	if countErr != nil {
		logger.Error("Failed to query login_logs count: %v", countErr)
		return json.MarshalToString(&RespQueryLoginLogs{Code: models.EXCEPTION, Msg: countErr.Error()})
	}

	// 处理用户列表数据
	logs := []*LoginLog{}
	for _, log := range list {
		apilog := &LoginLog{
			UserId:    log.UserId,
			Uin:       log.Uin,
			LoginTime: log.LoginTime,
			IpAddress: log.IpAddress,
			Device:    log.Device,
			Metadata:  log.Metadata,
		}
		logs = append(logs, apilog)
	}

	// 设置总数
	if len(totalResults) > 0 {
		total = totalResults[0].Total
	}
	return json.MarshalToString(&RespQueryLoginLogs{Code: models.OK, Msg: "ok", Total: total, Logs: logs})
}
