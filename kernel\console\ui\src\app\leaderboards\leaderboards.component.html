
<div class="row no-gutters">
  <div class="col d-flex justify-content-between no-gutters align-items-center">
    <div class="col-md-9">
      <h2 class="pb-1">Leaderboards</h2>
      <h6 class="pb-4">{{leaderboardsCount}} leaderboards found.</h6>
    </div>
    <div class="col-md-3 justify-content-end text-right">
        <div class="btn-group page-btns" role="group" aria-label="Search">
          <button type="button" class="btn btn-outline-secondary" (click)="search(0)" [disabled]="leaderboards.length === 0"><img src="/static/svg/page-first.svg" alt="" width="20" height=""></button>
          <button type="button" class="btn btn-outline-secondary" (click)="search(1)" [disabled]="nextCursor === ''"><img src="/static/svg/page-next.svg" alt="" width="20" height=""></button>
        </div>
    </div>
  </div>
</div>

<ngb-alert [dismissible]="false" type="danger" *ngIf="error">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">Error while processing request: {{error}}</h6>
</ngb-alert>

<table class="table table-sm table-bordered table-hover">
  <thead class="thead-light">
  <tr>
    <th>Leaderboard ID</th>
    <th style="width: 130px">Sort Order</th>
    <th style="width: 130px">Operator</th>
    <th style="width: 130px">Reset Schedule</th>
    <th style="width: 100px">Authoritative</th>
    <th style="width: 100px">Tournament</th>
    <th style="width: 90px" *ngIf="deleteAllowed()">Remove</th>
  </tr>
  </thead>
  <tbody>
    <tr *ngFor="let l of leaderboards; index as i">
      <td (click)="viewLeaderboardEntries(l);">{{l.id}}</td>
      <td (click)="viewLeaderboardEntries(l);">{{orderString[l.sort_order]}}</td>
      <td (click)="viewLeaderboardEntries(l);">{{operatorString[l.operator]}}</td>
      <td (click)="viewLeaderboardEntries(l);">{{l.reset_schedule === '' ? '-' : l.reset_schedule}}</td>
      <td (click)="viewLeaderboardEntries(l);" *ngIf="l.authoritative" class="text-center">Yes</td>
      <td (click)="viewLeaderboardEntries(l);" *ngIf="!l.authoritative" class="text-center">No</td>
      <td (click)="viewLeaderboardEntries(l);" *ngIf="l.tournament" class="text-center">Yes</td>
      <td (click)="viewLeaderboardEntries(l);" *ngIf="!l.tournament" class="text-center">No</td>
      <td *ngIf="deleteAllowed()" class="text-center"><button type="button" class="btn btn-sm btn-danger" (click)="deleteLeaderboard($event, i, l);">Delete</button></td>
    </tr>
    <tr *ngIf="leaderboards.length === 0"><td colspan="7" class="text-muted">No leaderboards were found.</td></tr>
  </tbody>
</table>
