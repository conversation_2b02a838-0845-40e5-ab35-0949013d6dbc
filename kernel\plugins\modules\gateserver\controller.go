package main

import (
	"bytes"
	"context"
	"database/sql"
	"kernel/kernel-common/rtapi"
	"kernel/kernel-common/runtime"
	"kernel/plugins/models"
	"time"

	json "github.com/json-iterator/go"
	protojson "google.golang.org/protobuf/encoding/protojson"
)

type Controller struct {
	server *GateServerModule
}

func NewController(s *GateServerModule) *Controller {
	return &Controller{
		server: s,
	}
}

// @Summary 发送数据
// @Description 发送数据
// @Tags gateserver
// @Accept json
// @Produce  json
// @Param payload body models.CommonPush true "数据内容"
// @Success 200 {object} models.CommonResp
// @Router /v2/rpc/gateserver.send [post]
func (c *Controller) PostSend(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	resp := models.CommonResp{Code: models.OK, Msg: "ok"}

	params := models.CommonPush{}
	decoder := json.NewDecoder(bytes.NewBuffer([]byte(payload)))
	decoder.UseNumber()
	if err := decoder.Decode(&params); err != nil {
		logger.Error(err.Error())
		return "", err
	}

	if len(params.SessionIDs) == 0 || params.Data == nil {
		resp.Code = models.PARAM_ERR
		resp.Msg = "param error"
		return json.MarshalToString(resp)
	}

	err := nk.SessionSend(ctx, params.SessionIDs, &rtapi.Envelope{
		Message: &rtapi.Envelope_Notifications{
			Notifications: params.Data,
		},
	})
	if err != nil {
		logger.Error(err.Error())
		resp.Code = models.FAILED
		resp.Msg = err.Error()
	}
	return json.MarshalToString(resp)
}

// @Summary 多播数据
// @Description 多播数据
// @Tags gateserver
// @Accept json
// @Produce  json
// @Param payload body models.CommonPush true "数据内容"
// @Success 200 {object} models.CommonResp
// @Router /v2/rpc/gateserver.multicast [post]
func (c *Controller) PostMulticast(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	resp := models.CommonResp{Code: models.OK, Msg: "ok"}

	params := models.CommonPush{}
	decoder := json.NewDecoder(bytes.NewBuffer([]byte(payload)))
	decoder.UseNumber()
	if err := decoder.Decode(&params); err != nil {
		logger.Error(err.Error())
		return "", err
	}

	if len(params.SessionIDs) == 0 || params.Data == nil {
		resp.Code = models.PARAM_ERR
		resp.Msg = "param error"
		return json.MarshalToString(resp)
	}

	err := nk.SessionSend(ctx, params.SessionIDs, &rtapi.Envelope{
		Message: &rtapi.Envelope_Notifications{
			Notifications: params.Data,
		},
	})
	if err != nil {
		logger.Error(err.Error())
		resp.Code = models.FAILED
		resp.Msg = err.Error()
	}
	return json.MarshalToString(resp)
}

// @Summary 广播数据
// @Description 广播数据
// @Tags gateserver
// @Accept json
// @Produce  json
// @Param payload body models.CommonPush true "数据内容"
// @Success 200 {object} models.CommonResp
// @Router /v2/rpc/gateserver.boardcast [post]
func (c *Controller) PostBoardcast(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	resp := models.CommonResp{Code: models.OK, Msg: "ok"}

	params := models.CommonPush{}
	decoder := json.NewDecoder(bytes.NewBuffer([]byte(payload)))
	decoder.UseNumber()
	if err := decoder.Decode(&params); err != nil {
		logger.Error(err.Error())
		return "", err
	}

	if params.Data == nil {
		resp.Code = models.PARAM_ERR
		resp.Msg = "param error"
		return json.MarshalToString(resp)
	}

	nk.SessionSendToAll(ctx, &rtapi.Envelope{
		Message: &rtapi.Envelope_Notifications{
			Notifications: params.Data,
		},
	})

	return json.MarshalToString(resp)
}

// @Summary 关闭连接
// @Description 关闭连接
// @Tags gateserver
// @Accept json
// @Produce  json
// @Param payload body models.ClosePush true "请求参数"
// @Success 200 {object} models.CommonResp
// @Router /v2/rpc/gateserver.close [post]
func (c *Controller) GetClose(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	resp := models.CommonResp{Code: models.OK, Msg: "ok"}
	params := &models.ClosePush{}
	if err := json.Unmarshal([]byte(payload), params); err != nil {
		logger.Error("GetClose unmarshal payload error: %v", err)
		return "", err
	}
	if params.Data != nil && len(params.Data.Notifications) > 0 {
		nk.SessionSend(ctx, params.SessionIDs, &rtapi.Envelope{
			Message: &rtapi.Envelope_Notifications{
				Notifications: params.Data,
			},
		})
		time.Sleep(time.Second * 1)
	}
	for _, sessionID := range params.SessionIDs {
		nk.SessionDisconnect(ctx, sessionID, params.Reason)
	}
	return json.MarshalToString(resp)
}

func (c *Controller) StreamUserList(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	resp := models.StreamUserListResp{Code: models.OK, Msg: "ok"}
	req := &models.StreamReq{}
	if err := json.Unmarshal([]byte(payload), req); err != nil {
		logger.Error("StreamUserList unmarshal payload error: %v", err)
		resp.Code = models.PARAM_ERR
		resp.Msg = "param error"
		return json.MarshalToString(resp)
	}

	presences, err := nk.StreamUserList(req.Mode, req.Subject, req.Subcontext, req.Label, req.IncludeHidden, req.IncludeNotHidden)
	if err != nil {
		logger.Error("StreamUserList error: %v", err)
		resp.Code = models.FAILED
		resp.Msg = err.Error()
		return json.MarshalToString(resp)
	}

	resp.Presences = make([]*models.Presence, 0, len(presences))
	for _, presence := range presences {
		resp.Presences = append(resp.Presences, &models.Presence{
			ID: models.PresenceID{
				Node:      presence.GetNodeId(),
				SessionID: presence.GetSessionId(),
			},
			UserID: presence.GetUserId(),
			Meta: models.PresenceMeta{
				Hidden:      presence.GetHidden(),
				Persistence: presence.GetPersistence(),
				Username:    presence.GetUsername(),
				Status:      presence.GetStatus(),
				Reason:      uint32(presence.GetReason()),
			},
		})
	}
	return json.MarshalToString(resp)
}

func (c *Controller) StreamUserGet(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	resp := models.StreamUserGetResp{Code: models.OK, Msg: "ok"}
	req := &models.StreamReq{}
	if err := json.Unmarshal([]byte(payload), req); err != nil {
		logger.Error("StreamUserGet unmarshal payload error: %v", err)
		resp.Code = models.PARAM_ERR
		resp.Msg = "param error"
		return json.MarshalToString(resp)
	}

	presence, err := nk.StreamUserGet(req.Mode, req.Subject, req.Subcontext, req.Label, req.UserId, req.SessionId)
	if err != nil {
		logger.Error("StreamUserGet error: %v", err)
		resp.Code = models.FAILED
		resp.Msg = err.Error()
		return json.MarshalToString(resp)
	}

	resp.Meta = &models.PresenceMeta{
		Hidden:      presence.GetHidden(),
		Persistence: presence.GetPersistence(),
		Username:    presence.GetUsername(),
		Status:      presence.GetStatus(),
		Reason:      uint32(presence.GetReason()),
	}
	return json.MarshalToString(resp)
}

func (c *Controller) StreamUserJoin(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	resp := models.CommonResp{Code: models.OK, Msg: "ok"}
	req := &models.StreamReq{}
	if err := json.Unmarshal([]byte(payload), req); err != nil {
		logger.Error("StreamUserJoin unmarshal payload error: %v", err)
		resp.Code = models.PARAM_ERR
		resp.Msg = "param error"
		return json.MarshalToString(resp)
	}

	ok, err := nk.StreamUserJoin(req.Mode, req.Subject, req.Subcontext, req.Label, req.UserId, req.SessionId, req.IncludeHidden, req.Persistence, req.Status)
	if err != nil {
		logger.Error("StreamUserJoin error: %v", err)
		resp.Code = models.FAILED
		resp.Msg = err.Error()
		return json.MarshalToString(resp)
	}
	if !ok {
		resp.Code = models.FAILED
		resp.Msg = "failed"
	}
	return json.MarshalToString(resp)
}

func (c *Controller) StreamUserUpdate(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	resp := models.CommonResp{Code: models.OK, Msg: "ok"}
	req := &models.StreamReq{}
	if err := json.Unmarshal([]byte(payload), req); err != nil {
		logger.Error("StreamUserUpdate unmarshal payload error: %v", err)
		resp.Code = models.PARAM_ERR
		resp.Msg = "param error"
		return json.MarshalToString(resp)
	}

	err := nk.StreamUserUpdate(req.Mode, req.Subject, req.Subcontext, req.Label, req.UserId, req.SessionId, req.IncludeHidden, req.Persistence, req.Status)
	if err != nil {
		logger.Error("StreamUserUpdate error: %v", err)
		resp.Code = models.FAILED
		resp.Msg = err.Error()
		return json.MarshalToString(resp)
	}
	return json.MarshalToString(resp)
}

func (c *Controller) StreamUserLeave(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	resp := models.CommonResp{Code: models.OK, Msg: "ok"}
	req := &models.StreamReq{}
	if err := json.Unmarshal([]byte(payload), req); err != nil {
		logger.Error("StreamUserLeave unmarshal payload error: %v", err)
		resp.Code = models.PARAM_ERR
		resp.Msg = "param error"
		return json.MarshalToString(resp)
	}

	err := nk.StreamUserLeave(req.Mode, req.Subject, req.Subcontext, req.Label, req.UserId, req.SessionId)
	if err != nil {
		logger.Error("StreamUserLeave error: %v", err)
		resp.Code = models.FAILED
		resp.Msg = err.Error()
		return json.MarshalToString(resp)
	}
	return json.MarshalToString(resp)
}

func (c *Controller) StreamUserKick(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	resp := models.CommonResp{Code: models.OK, Msg: "ok"}
	req := &models.StreamReq{}
	if err := json.Unmarshal([]byte(payload), req); err != nil {
		logger.Error("StreamUserKick unmarshal payload error: %v", err)
		resp.Code = models.PARAM_ERR
		resp.Msg = "param error"
		return json.MarshalToString(resp)
	}

	err := nk.StreamUserKick(req.Mode, req.Subject, req.Subcontext, req.Label, req.Presences[0])
	if err != nil {
		logger.Error("StreamUserKick error: %v", err)
		resp.Code = models.FAILED
		resp.Msg = err.Error()
		return json.MarshalToString(resp)
	}
	return json.MarshalToString(resp)
}

func (c *Controller) StreamCount(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	resp := models.StreamCountResp{Code: models.OK, Msg: "ok"}
	req := &models.StreamReq{}
	if err := json.Unmarshal([]byte(payload), req); err != nil {
		logger.Error("StreamCount unmarshal payload error: %v", err)
		resp.Code = models.PARAM_ERR
		resp.Msg = "param error"
		return json.MarshalToString(resp)
	}

	count, err := nk.StreamCount(req.Mode, req.Subject, req.Subcontext, req.Label)
	if err != nil {
		logger.Error("StreamCount error: %v", err)
		resp.Code = models.FAILED
		resp.Msg = err.Error()
		return json.MarshalToString(resp)
	}
	resp.Count = count
	return json.MarshalToString(resp)
}

func (c *Controller) StreamClose(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	resp := models.CommonResp{Code: models.OK, Msg: "ok"}
	req := &models.StreamReq{}
	if err := json.Unmarshal([]byte(payload), req); err != nil {
		logger.Error("StreamClose unmarshal payload error: %v", err)
		resp.Code = models.PARAM_ERR
		resp.Msg = "param error"
		return json.MarshalToString(resp)
	}

	err := nk.StreamClose(req.Mode, req.Subject, req.Subcontext, req.Label)
	if err != nil {
		logger.Error("StreamClose error: %v", err)
		resp.Code = models.FAILED
		resp.Msg = err.Error()
		return json.MarshalToString(resp)
	}
	return json.MarshalToString(resp)
}

func (c *Controller) StreamSend(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	resp := models.CommonResp{Code: models.OK, Msg: "ok"}
	req := &models.StreamReq{}
	if err := json.Unmarshal([]byte(payload), req); err != nil {
		logger.Error("StreamSend unmarshal payload error: %v", err)
		resp.Code = models.PARAM_ERR
		resp.Msg = "param error"
		return json.MarshalToString(resp)
	}

	// Convert presences
	runtimePresences := make([]runtime.Presence, len(req.Presences))
	for i, p := range req.Presences {
		runtimePresences[i] = p
	}

	err := nk.StreamSend(req.Mode, req.Subject, req.Subcontext, req.Label, req.Data, runtimePresences, req.Reliable)
	if err != nil {
		logger.Error("StreamSend error: %v", err)
		resp.Code = models.FAILED
		resp.Msg = err.Error()
		return json.MarshalToString(resp)
	}
	return json.MarshalToString(resp)
}

func (c *Controller) StreamSendDirect(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	resp := models.CommonResp{Code: models.OK, Msg: "ok"}
	req := &models.StreamReq{}
	if err := json.Unmarshal([]byte(payload), req); err != nil {
		logger.Error("StreamSendRawDirect unmarshal payload error: %v", err)
		resp.Code = models.PARAM_ERR
		resp.Msg = "param error"
		return json.MarshalToString(resp)
	}

	var msg rtapi.Envelope
	err := protojson.Unmarshal([]byte(req.Data), &msg)
	if err != nil {
		logger.Error("StreamSendRawDirect unmarshal data error: %v", err)
		resp.Code = models.FAILED
		resp.Msg = err.Error()
		return json.MarshalToString(resp)
	}

	sessionIDs := make([]string, 0, len(req.Presences))
	for _, p := range req.Presences {
		sessionIDs = append(sessionIDs, p.ID.SessionID)
	}

	err = nk.SessionSend(ctx, sessionIDs, &msg)
	if err != nil {
		logger.Error("StreamSendRawDirect error: %v", err)
		resp.Code = models.FAILED
		resp.Msg = err.Error()
		return json.MarshalToString(resp)
	}

	return json.MarshalToString(resp)
}

func (c *Controller) StreamSendDirectBoardcast(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	resp := models.CommonResp{Code: models.OK, Msg: "ok"}
	req := &models.StreamReq{}
	if err := json.Unmarshal([]byte(payload), req); err != nil {
		logger.Error("StreamSendRawDirect unmarshal payload error: %v", err)
		resp.Code = models.PARAM_ERR
		resp.Msg = "param error"
		return json.MarshalToString(resp)
	}

	var msg rtapi.Envelope
	err := protojson.Unmarshal([]byte(req.Data), &msg)
	if err != nil {
		logger.Error("StreamSendRawDirect unmarshal data error: %v", err)
		resp.Code = models.FAILED
		resp.Msg = err.Error()
		return json.MarshalToString(resp)
	}

	nk.SessionSendRawToAll(ctx, []byte(req.Data))
	return json.MarshalToString(resp)
}

//
//// @Summary 获取当前连接数
//// @Tags Gatesvr
//// @Accept json
//// @Produce  json
//// @Router /minigate/onlinecount [get]
//// @Success 200 {string} json ""
//func (c *Controller) GetOnlinecount(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) {
//	resp := models.CommonResp{Code: models.ERRCODE_OK, Msg: "ok"}
//	defer func() {
//		d, _ := json.Marshal(resp)
//		ctx.Write(d)
//	}()
//	resp.Data = service.SelfWsServer.SessionCount
//}
//
//// @Summary 获取指定连接信息
//// @Tags Gatesvr
//// @Accept json
//// @Produce  json
//// @Router /minigate/connectstatus [get]
//// @Success 200 {string} json ""
//func (c *Controller) GetConnectstatus(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) {
//	resp := models.CommonResp{Code: models.ERRCODE_OK, Msg: "ok"}
//	defer func() {
//		d, _ := json.Marshal(resp)
//		ctx.Write(d)
//	}()
//
//	connid := ctx.URLParamUint64("connid")
//	conn, ok := service.SelfWsServer.Conns.Load(connid)
//	if ok && conn != nil {
//		resp.Data = map[string]interface{}{
//			"pfuid":       conn.(*service.Session).Get("pfuid"),
//			"onlinetime":  conn.(*service.Session).Get("onlinetime"),
//			"connid":      conn.(*service.Session).Get("connid"),
//			"cltversion":  conn.(*service.Session).Get("cltversion"),
//			"apiid":       conn.(*service.Session).Get("apiid"),
//			"checkflag":   conn.(*service.Session).Get("checkflag"),
//			"checkresult": conn.(*service.Session).Get("checkresult"),
//			"reconnect":   conn.(*service.Session).Get("reconnect"),
//		}
//	}
//}
