package common

import (
	"errors"
	"fmt"
	"net/url"
	"strings"

	"github.com/nacos-group/nacos-sdk-go/v2/clients"
	"github.com/nacos-group/nacos-sdk-go/v2/clients/config_client"
	"github.com/nacos-group/nacos-sdk-go/v2/clients/naming_client"
	"github.com/nacos-group/nacos-sdk-go/v2/common/constant"
	"github.com/nacos-group/nacos-sdk-go/v2/model"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
)

// 配置改变事件回调函数
type NacosConfigEventFunc func(namespace, group, dataId, data string)

type NacosClient struct {
	ConfigClient   config_client.IConfigClient
	RegisterClient naming_client.INamingClient
}

// 初始化nacos配置中心和注册中心，通过用户名和密码
func InitNacos(user, passwd, envNamespaceId string, urls []string, timeoutMs uint64) (*NacosClient, error) {
	sc := []constant.ServerConfig{}
	for _, v := range urls {
		u, err := url.Parse(v)
		if err != nil {
			fmt.Println(err, v)
			continue
		}
		scheme := "http"
		if u.Scheme != "" {
			scheme = u.Scheme
		}
		path := "/nacos"
		if u.Path != "" {
			path = u.Path
		}
		addrs := strings.Split(u.Host, ":")
		if len(addrs) == 2 {
			sc = append(sc, constant.ServerConfig{
				Scheme:      scheme,
				IpAddr:      addrs[0],
				Port:        StrToUInt64(addrs[1]),
				ContextPath: path,
			})
		}
	}

	cc := constant.ClientConfig{
		NamespaceId:          envNamespaceId, //namespace id
		TimeoutMs:            timeoutMs,
		NotLoadCacheAtStart:  true,
		UpdateCacheWhenEmpty: false,
		Username:             user,
		Password:             passwd,
		LogLevel:             "warn",
		//LogDir:              "/tmp/nacos/log",
		//CacheDir:            "/tmp/nacos/cache",
		//RotateTime:          "1h",
		//MaxAge:              3,
		//LogLevel:            "info",
	}

	// a more graceful way to create config client
	cfgclient, err := clients.NewConfigClient(
		vo.NacosClientParam{
			ClientConfig:  &cc,
			ServerConfigs: sc,
		},
	)
	if err != nil {
		return nil, err
	}
	nc := &NacosClient{}
	nc.ConfigClient = cfgclient

	// a more graceful way to create config client
	regclient, err := clients.NewNamingClient(
		vo.NacosClientParam{
			ClientConfig:  &cc,
			ServerConfigs: sc,
		},
	)
	if err != nil {
		return nil, err
	}
	nc.RegisterClient = regclient

	return nc, nil
}

// 初始化nacos配置中心和注册中心，通过ak，sk
func InitNacosByAkSk(ak, sk, envNamespaceId string, urls []string, timeoutMs uint64) (*NacosClient, error) {
	sc := []constant.ServerConfig{}
	for _, v := range urls {
		u, err := url.Parse(v)
		if err != nil {
			fmt.Println(err, v)
			continue
		}
		scheme := "http"
		if u.Scheme != "" {
			scheme = u.Scheme
		}
		path := "/nacos"
		if u.Path != "" {
			path = u.Path
		}
		addrs := strings.Split(u.Host, ":")
		if len(addrs) == 2 {
			sc = append(sc, constant.ServerConfig{
				Scheme:      scheme,
				IpAddr:      addrs[0],
				Port:        StrToUInt64(addrs[1]),
				ContextPath: path,
			})
		}
	}

	cc := constant.ClientConfig{
		NamespaceId:          envNamespaceId, //namespace id
		TimeoutMs:            timeoutMs,
		NotLoadCacheAtStart:  true,
		UpdateCacheWhenEmpty: false,
		AccessKey:            ak,
		SecretKey:            sk,
		LogLevel:             "warn",
		//LogDir:              "/tmp/nacos/log",
		//CacheDir:            "/tmp/nacos/cache",
		//RotateTime:          "1h",
		//MaxAge:              3,
		//LogLevel:            "info",
	}

	// a more graceful way to create config client
	cfgclient, err := clients.NewConfigClient(
		vo.NacosClientParam{
			ClientConfig:  &cc,
			ServerConfigs: sc,
		},
	)
	if err != nil {
		return nil, err
	}
	nc := &NacosClient{}
	nc.ConfigClient = cfgclient

	// a more graceful way to create config client
	regclient, err := clients.NewNamingClient(
		vo.NacosClientParam{
			ClientConfig:  &cc,
			ServerConfigs: sc,
		},
	)
	if err != nil {
		return nil, err
	}
	nc.RegisterClient = regclient

	return nc, nil
}

// 获取和监听配置
func (nc *NacosClient) ListenConfig(dataId, group string, changeevent NacosConfigEventFunc) error {
	if nc.ConfigClient == nil {
		return errors.New("nacos config not init")
	}

	// get config
	content, err := nc.ConfigClient.GetConfig(vo.ConfigParam{
		DataId: dataId,
		Group:  group,
	})
	if err != nil {
		return err
	}
	if changeevent != nil {
		changeevent("", group, dataId, content)
	}

	//Listen config change,key=dataId+group+namespaceId.
	return nc.ConfigClient.ListenConfig(vo.ConfigParam{
		DataId:   dataId,
		Group:    group,
		OnChange: changeevent,
	})
}

// 获取配置
func (nc *NacosClient) GetConfig(dataId, group string) (string, error) {
	if nc.ConfigClient == nil {
		return "", errors.New("nacos config not init")
	}
	return nc.ConfigClient.GetConfig(vo.ConfigParam{
		DataId: dataId,
		Group:  group,
	})
}

func (nc *NacosClient) RegisterSelf(enable bool, ip string, port uint64, weight uint64, serviceName, group string, metacfg map[string]interface{}) (bool, error) {
	if nc.RegisterClient == nil {
		return false, errors.New("NacosRegisterClient not init")
	}
	meta := map[string]string{}
	for k, v := range metacfg {
		meta[k] = Interface2String(v)
	}
	return nc.RegisterClient.RegisterInstance(vo.RegisterInstanceParam{
		Ip:          ip,
		Port:        port,
		ServiceName: serviceName,
		Weight:      Interface2Float64(weight),
		Enable:      enable,
		Healthy:     true,
		Ephemeral:   true,
		Metadata:    meta,
		GroupName:   group,
	})
}

func (nc *NacosClient) UpdateRegisterSelf(enable bool, ip string, port uint64, weight uint64, serviceName, group string, metacfg map[string]interface{}) (bool, error) {
	if nc.RegisterClient == nil {
		return false, errors.New("NacosRegisterClient not init")
	}
	meta := map[string]string{}
	for k, v := range metacfg {
		meta[k] = Interface2String(v)
	}
	return nc.RegisterClient.UpdateInstance(vo.UpdateInstanceParam{
		Ip:          ip,
		Port:        port,
		ServiceName: serviceName,
		Weight:      Interface2Float64(weight),
		Enable:      enable,
		Healthy:     true,
		Ephemeral:   true,
		Metadata:    meta,
		GroupName:   group,
	})
}

// 取消注册自身到nacos
func (nc *NacosClient) UnRegisterSelf(ip string, port uint64, serviceName, group string) (bool, error) {
	if nc.RegisterClient == nil {
		return false, errors.New("NacosRegisterClient not init")
	}
	return nc.RegisterClient.DeregisterInstance(vo.DeregisterInstanceParam{
		Ip:          ip,
		Port:        port,
		ServiceName: serviceName,
		Ephemeral:   true,
		GroupName:   group,
	})
}

func (nc *NacosClient) SubscribeWatch(name, group string, callback func(services []model.Instance, err error)) error {
	if nc.RegisterClient == nil {
		return errors.New("NacosRegisterClient not init")
	}
	return nc.RegisterClient.Subscribe(&vo.SubscribeParam{
		ServiceName:       name,
		GroupName:         group,
		SubscribeCallback: callback,
	})
}

func (nc *NacosClient) UnSubscribeWatch(name, group string, callback func(services []model.Instance, err error)) error {
	if nc.RegisterClient == nil {
		return errors.New("NacosRegisterClient not init")
	}
	return nc.RegisterClient.Unsubscribe(&vo.SubscribeParam{
		ServiceName:       name,
		GroupName:         group,
		SubscribeCallback: callback,
	})
}

func (nc *NacosClient) GetOneHealthy(name, group string) (*model.Instance, error) {
	return nc.RegisterClient.SelectOneHealthyInstance(vo.SelectOneHealthInstanceParam{
		ServiceName: name,
		GroupName:   group,
	})
}

func (nc *NacosClient) GetAll(name, group string) ([]model.Instance, error) {
	return nc.RegisterClient.SelectAllInstances(vo.SelectAllInstancesParam{
		ServiceName: name,
		GroupName:   group,
	})
}
