package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap/zapcore"
)

type CronServerModule struct {
	name            string
	logger          runtime.Logger
	db              *sql.DB
	nk              runtime.NakamaModule
	config          runtime.Config // nakama 配置
	CustomConfig    *CustomConfig  // 自定义配置
	isRegisterSelf  bool
	ProxyPassConfig sync.Map
	controller      *Controller
	loginLogsCron   *cron.Cron
	dailyStatsCron  *cron.Cron

	common  *logic.CommonGlobalDataStruct
	online  *logic.OnlineGlobalDataStruct
	send    *logic.SendGlobalDataStruct
	dbagent *logic.DbAgentGlobalDataStruct
	notify  *logic.NotifyGlobalDataStruct
}

var CronServerData *CronServerModule

func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
	CronServerData = new(CronServerModule)
	CronServerData.name = models.SERVER_NAME_CRON
	CronServerData.logger = logger
	CronServerData.db = db
	CronServerData.nk = nk
	CronServerData.common = logic.NewCommonGlobalDataStruct()
	CronServerData.send = logic.NewSendGlobalDataStruct(CronServerData)
	CronServerData.dbagent = logic.NewDbAgentGlobalDataStruct(CronServerData)
	CronServerData.online = logic.NewOnlineGlobalDataStruct(CronServerData)
	CronServerData.notify = logic.NewNotifyGlobalDataStruct(CronServerData)
	config, err := initializer.GetConfig()
	if err != nil {
		return err
	}
	CronServerData.config = config
	if err := CronServerData.common.Init(CronServerData, CronServerData.CustomConfig, CronServerData.customConfigEvent); err != nil {
		logger.Error("init common logic error %v", err)
		return err
	}

	initializer.RegisterShutdown(CronServerData.Shutdown)

	// 自定义路由注册
	CronServerData.controller = NewController(CronServerData)

	// 立即执行一次
	go CronServerData.checkAndCreateLoginLogsPartitionTable()

	CronServerData.loginLogsCron = cron.New() // 每天零点执行
	CronServerData.loginLogsCron.AddFunc("0 0 * * *", CronServerData.checkAndCreateLoginLogsPartitionTable)
	CronServerData.loginLogsCron.Start()
	CronServerData.dailyStatsCron = cron.New() // 每天零点执行
	CronServerData.dailyStatsCron.AddFunc("0 0 * * *", CronServerData.runDailyStatsJob)
	CronServerData.dailyStatsCron.Start()

	return nil
}

func (s *CronServerModule) GetCommon() *logic.CommonGlobalDataStruct {
	return s.common
}
func (s *CronServerModule) GetOnline() *logic.OnlineGlobalDataStruct {
	return s.online
}
func (s *CronServerModule) GetSend() *logic.SendGlobalDataStruct {
	return s.send
}
func (s *CronServerModule) GetDbAgent() *logic.DbAgentGlobalDataStruct {
	return s.dbagent
}
func (s *CronServerModule) GetNotify() *logic.NotifyGlobalDataStruct {
	return s.notify
}
func (s *CronServerModule) GetStream() *logic.StreamGlobalDataStruct {
	return nil
}
func (s *CronServerModule) GetChannel() *logic.ChannelGlobalDataStruct {
	return nil
}
func (s *CronServerModule) GetLogger() runtime.Logger {
	return s.logger
}
func (s *CronServerModule) GetConfig() runtime.Config {
	return s.config
}
func (s *CronServerModule) GetName() string {
	return s.name
}
func (s *CronServerModule) GetDb() *sql.DB {
	return s.db
}
func (s *CronServerModule) GetNk() runtime.NakamaModule {
	return s.nk
}

// 配置读取回调
func (s *CronServerModule) customConfigEvent(dataId string, newobj any) {
	tmpobj, ok := newobj.(*CustomConfig)
	if !ok {
		return
	}
	if tmpobj.Config == nil {
		tmpobj.Config = &CronServerConfig{}
	}
	defaultNodeCfg := false
	if tmpobj.Config.NodeConfig == nil {
		// 使用默认节点配置
		scheme := "http"
		if s.config.GetSocket().GetUseTLS() {
			scheme = "https"
		}
		tmpobj.Config.NodeConfig = &models.NodeConfig{
			NodeId:   s.config.GetName(),
			SvcGroup: fmt.Sprintf("%s@@%s", s.name, models.NACOS_DEFAULT_GROUP),
			Host:     fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			NetHost:  fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			AuthKey:  s.config.GetRuntime().GetHTTPKey(),
			Weight:   10,
			Scheme:   scheme,
		}
		defaultNodeCfg = true
	}
	tmp, _ := json.Marshal(tmpobj)
	s.logger.Debug("customConfigEvent defaultNodeCfg: %v, customCfg: %s", defaultNodeCfg, string(tmp))
	s.common.SetNodeConfig(tmpobj.Config.NodeConfig)
	s.common.WatchNodeList.InitWatchSvr(tmpobj.Config.WatchSvcGroupList, tmpobj.Config.NodeConfig.SvcGroup)
	s.CustomConfig = tmpobj

	// 注册服务
	selfcfg := map[string]interface{}{}
	if err := common.StrcutToMap(tmpobj.Config.NodeConfig, &selfcfg); err == nil {
		if !s.isRegisterSelf {
			go s.common.RegisterAndUpdateSelfToNacos(false, selfcfg)
			s.isRegisterSelf = true
		} else {
			go s.common.RegisterAndUpdateSelfToNacos(true, selfcfg)
		}
	} else {
		s.logger.Error("register self, config error %v", err)
	}

	s.common.UpdateAccessLogConfig(tmpobj.AccessLog)
	loglevel, err := zapcore.ParseLevel(tmpobj.Logger.Level)
	if err != nil {
		s.logger.Error("parse log level error %v", err)
	} else {
		s.nk.SetLoggerLevel(loglevel)
	}
}

func (s *CronServerModule) Shutdown(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, reload bool) {
	logger.Info("%s shutdown", s.name)
	if !reload {
		s.common.UnRegisterSelfToNacos()
	}
}

// 定时检查创建login_logs分区表，按月创建, 当前+后面6个月
func (s *CronServerModule) checkAndCreateLoginLogsPartitionTable() {
	now := time.Now()
	base := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.UTC) // 本月第一天

	for i := 0; i <= 6; i++ { // 当前月 + 后6个月
		partitionStart := base.AddDate(0, i, 0)
		partitionEnd := partitionStart.AddDate(0, 1, 0)
		tableName := fmt.Sprintf("login_logs_%s", partitionStart.Format("200601"))

		// 检查分区表是否存在
		var exists bool
		checkSQL := `
			SELECT EXISTS (
				SELECT 1 FROM pg_class c
				JOIN pg_namespace n ON n.oid = c.relnamespace
				WHERE c.relname = $1 AND n.nspname = 'public'
			);
		`
		err := s.db.QueryRow(checkSQL, tableName).Scan(&exists)
		if err != nil {
			s.logger.Error("Failed to check partition table %s existence: %v", tableName, err)
			continue
		}

		if exists {
			s.logger.Info("Partition table %s already exists", tableName)
			continue
		}

		// 创建分区表
		createTableSQL := fmt.Sprintf(`
			CREATE TABLE IF NOT EXISTS %s PARTITION OF login_logs
			FOR VALUES FROM ('%s') TO ('%s');
		`, tableName, partitionStart.Format("2006-01-02"), partitionEnd.Format("2006-01-02"))

		_, err = s.db.Exec(createTableSQL)
		if err != nil {
			s.logger.Error("Failed to create partition table %s: %v", tableName, err)
			continue
		}
		s.logger.Info("Successfully created partition table %s", tableName)

		// 创建分区表索引
		createIndexSQL := fmt.Sprintf(`
			CREATE INDEX IF NOT EXISTS idx_%s_uin ON %s (uin);
			CREATE INDEX IF NOT EXISTS idx_%s_uin_time ON %s (uin, login_time DESC);
			CREATE INDEX IF NOT EXISTS idx_%s_user_id ON %s (user_id);
			CREATE INDEX IF NOT EXISTS idx_%s_login_time ON %s (login_time DESC);
		`, tableName, tableName,
			tableName, tableName,
			tableName, tableName,
			tableName, tableName)

		_, err = s.db.Exec(createIndexSQL)
		if err != nil {
			s.logger.Error("Failed to create indexes on partition table %s: %v", tableName, err)
		} else {
			s.logger.Info("Successfully created indexes on partition table %s", tableName)
		}
	}
}

// 每日统计,下面表数据(dau,mau,次留,7日留存,每日新增)，数据来源login_logs是个按月分区表，格式例如：login_logs_202501
// CREATE TABLE IF NOT EXISTS daily_stats (
//
//	stat_date DATE PRIMARY KEY,
//	dau INT DEFAULT 0,
//	next_day_retention FLOAT4 DEFAULT 0,
//	day7_retention FLOAT4 DEFAULT 0,
//	mau INT DEFAULT 0,
//	new_users INT DEFAULT 0,
//	week_retention FLOAT4 DEFAULT 0,
//	created_at TIMESTAMPTZ DEFAULT now()
//
// );
func (s *CronServerModule) runDailyStatsJob() {
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	dayStr := yesterday.Format("2006-01-02")
	monthStart := time.Date(yesterday.Year(), yesterday.Month(), 1, 0, 0, 0, 0, yesterday.Location())

	// 关键日期
	dayBeforeYesterday := yesterday.AddDate(0, 0, -1)                         // D-2，次留新增用户日期
	day7BeforeYesterday := yesterday.AddDate(0, 0, -7)                        // D-8，7日留存新增用户日期
	weekStartBeforeLast := getWeekStart(dayBeforeYesterday.AddDate(0, 0, -7)) // D-15（两周前周一）
	weekStartLast := getWeekStart(dayBeforeYesterday)                         // D-8（前一周周一）
	// getWeekStart 是辅助函数，计算某天对应的周一日期，下面会写示例实现

	// 分区表函数
	tbl := func(t time.Time) string {
		return fmt.Sprintf("login_logs_%s", t.Format("200601"))
	}

	sqlStmt := fmt.Sprintf(`
WITH
	-- 新增用户：前天新增
	new_users_dby AS (
		SELECT uin FROM users
		WHERE create_time >= DATE '%s' AND create_time < DATE '%s'
	),
	-- 新增用户：7天前新增
	new_users_7d AS (
		SELECT uin FROM users
		WHERE create_time >= DATE '%s' AND create_time < DATE '%s'
	),
	-- 新增用户：两周前那周新增（周一到周日）
	new_users_2w AS (
		SELECT uin FROM users
		WHERE create_time >= DATE '%s' AND create_time < DATE '%s'
	),
	-- 昨天登录且是前天新增的（次留）
	next_day_retained AS (
		SELECT DISTINCT uin FROM %s
		WHERE login_time >= DATE '%s' AND login_time < DATE '%s'
		AND uin IN (SELECT uin FROM new_users_dby)
	),
	-- 昨天登录且是7天前新增的（7日留存）
	day7_retained AS (
		SELECT DISTINCT uin FROM %s
		WHERE login_time >= DATE '%s' AND login_time < DATE '%s'
		AND uin IN (SELECT uin FROM new_users_7d)
	),
	-- 昨天登录且是两周前那周新增的（周留存）
	weekly_retained AS (
		SELECT DISTINCT uin FROM %s
		WHERE login_time >= DATE '%s' AND login_time < DATE '%s'
		AND uin IN (SELECT uin FROM new_users_2w)
	),
	-- dau 昨天活跃所有用户
	dau_users AS (
		SELECT DISTINCT uin FROM %s
		WHERE login_time >= DATE '%s' AND login_time < DATE '%s'
	),
	-- mau 当月活跃用户
	mau_users AS (
		SELECT DISTINCT uin FROM %s
		WHERE login_time >= DATE '%s' AND login_time < DATE '%s'
	),
	-- 新增用户数量（昨天新增）
	new_users AS (
		SELECT COUNT(*) AS count FROM users
		WHERE create_time >= DATE '%s' AND create_time < DATE '%s'
	)
INSERT INTO daily_stats (stat_date, dau, next_day_retention, day7_retention, weekly_retention, mau, new_users)
SELECT
	DATE '%s',
	(SELECT COUNT(*) FROM dau_users),
	ROUND(COALESCE((SELECT COUNT(*) FROM next_day_retained) * 100.0 / NULLIF((SELECT COUNT(*) FROM new_users_dby), 0), 0), 2),
	ROUND(COALESCE((SELECT COUNT(*) FROM day7_retained) * 100.0 / NULLIF((SELECT COUNT(*) FROM new_users_7d), 0), 0), 2),
	ROUND(COALESCE((SELECT COUNT(*) FROM weekly_retained) * 100.0 / NULLIF((SELECT COUNT(*) FROM new_users_2w), 0), 0), 2),
	(SELECT COUNT(*) FROM mau_users),
	(SELECT count FROM new_users)
ON CONFLICT (stat_date) DO UPDATE SET
	dau = EXCLUDED.dau,
	next_day_retention = EXCLUDED.next_day_retention,
	day7_retention = EXCLUDED.day7_retention,
	weekly_retention = EXCLUDED.weekly_retention,
	mau = EXCLUDED.mau,
	new_users = EXCLUDED.new_users;
`,
		// new_users_dby: 前天新增
		dayBeforeYesterday.Format("2006-01-02"), yesterday.Format("2006-01-02"),
		// new_users_7d: 7天前新增
		day7BeforeYesterday.Format("2006-01-02"), day7BeforeYesterday.AddDate(0, 0, 1).Format("2006-01-02"),
		// new_users_2w: 两周前那周新增（周一开始到周日结束）
		weekStartBeforeLast.Format("2006-01-02"), weekStartLast.Format("2006-01-02"),
		// next_day_retained 查询昨天登录的前天新增用户
		tbl(yesterday), yesterday.Format("2006-01-02"), now.Format("2006-01-02"),
		// day7_retained 查询昨天登录的7天前新增用户
		tbl(yesterday), yesterday.Format("2006-01-02"), now.Format("2006-01-02"),
		// weekly_retained 查询昨天登录的两周前那周新增用户
		tbl(yesterday), yesterday.Format("2006-01-02"), now.Format("2006-01-02"),
		// dau_users 昨天登录的所有用户
		tbl(yesterday), yesterday.Format("2006-01-02"), now.Format("2006-01-02"),
		// mau_users 本月活跃
		tbl(yesterday), monthStart.Format("2006-01-02"), now.Format("2006-01-02"),
		// new_users 昨天新增用户数量
		yesterday.Format("2006-01-02"), now.Format("2006-01-02"),
		// stat_date
		dayStr,
	)

	_, err := s.db.Exec(sqlStmt)
	if err != nil {
		s.logger.Error("Failed to run daily stats for %s: %v", dayStr, err)
	} else {
		s.logger.Info("Stats for %s updated successfully.", dayStr)
	}
}

// getWeekStart 返回某个时间对应周一的日期，时分秒归零
func getWeekStart(t time.Time) time.Time {
	weekday := int(t.Weekday())
	if weekday == 0 {
		weekday = 7
	}
	return time.Date(t.Year(), t.Month(), t.Day()-weekday+1, 0, 0, 0, 0, t.Location())
}
