package main

// rpcid
const (
	RPCID_LOGICSERVER_LOGINFINISH      string = "logicserver.loginfinish"      // 登录完成
	RPCID_LOGICSERVER_CREATEROLE       string = "logicserver.createrole"       // 创建角色
	RPCID_LOGICSERVER_GETFAVORITEROOMS string = "logicserver.getfavoriterooms" // 获取收藏房间列表
	RPCID_LOGICSERVER_ADDFAVORITEROOM  string = "logicserver.addfavoriteroom"  // 添加收藏房间
	RPCID_LOGICSERVER_DELFAVORITEROOMS string = "logicserver.delfavoriterooms" // 删除收藏房间
	RPCID_LOGICSERVER_GETHISTORYROOMS  string = "logicserver.gethistoryrooms"  // 获取历史房间列表
	RPCID_LOGICSERVER_ADDHISTORYROOM   string = "logicserver.addhistoryroom"   // 添加历史房间
	RPCID_LOGICSERVER_DELHISTORYROOMS  string = "logicserver.delhistoryrooms"  // 删除历史房间
	RPCID_LOGICSERVER_QUERYSTATUS      string = "logicserver.querystatus"      // 查询用户状态

	RPCID_LOGICSERVER_GETUPLOADURL   string = "logicserver.getuploadurl"   // 获取上传url
	RPCID_LOGICSERVER_REPORT         string = "logicserver.report"         // 举报
	RPCID_LOGICSERVER_QUERYREPORT    string = "logicserver.queryreport"    // 查询举报
	RPCID_LOGICSERVER_REPORTCALLBACK string = "logicserver.reportcallback" // 举报处理结果回调
)

// push subject
const (
	SUBJECT_LOGICSERVER_ROLEDATA    string = "logicserver.roledata"
	SUBJECT_LOGICSERVER_LOGINFAILED string = "logicserver.loginfailed"
)
