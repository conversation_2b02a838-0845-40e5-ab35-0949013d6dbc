// Copyright 2018 The Nakama Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

//*
// The RPC protocol for the developer console.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v5.28.3
// source: console.proto

package console

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	api "kernel/kernel-common/api"
	rtapi "kernel/kernel-common/rtapi"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserRole int32

const (
	UserRole_USER_ROLE_UNKNOWN    UserRole = 0
	UserRole_USER_ROLE_ADMIN      UserRole = 1 // All access
	UserRole_USER_ROLE_DEVELOPER  UserRole = 2 // Best for developers, also enables APIs and API explorer
	UserRole_USER_ROLE_MAINTAINER UserRole = 3 // Best for users who regularly update player information.
	UserRole_USER_ROLE_READONLY   UserRole = 4 // Read-only role for those only need to view data
)

// Enum value maps for UserRole.
var (
	UserRole_name = map[int32]string{
		0: "USER_ROLE_UNKNOWN",
		1: "USER_ROLE_ADMIN",
		2: "USER_ROLE_DEVELOPER",
		3: "USER_ROLE_MAINTAINER",
		4: "USER_ROLE_READONLY",
	}
	UserRole_value = map[string]int32{
		"USER_ROLE_UNKNOWN":    0,
		"USER_ROLE_ADMIN":      1,
		"USER_ROLE_DEVELOPER":  2,
		"USER_ROLE_MAINTAINER": 3,
		"USER_ROLE_READONLY":   4,
	}
)

func (x UserRole) Enum() *UserRole {
	p := new(UserRole)
	*p = x
	return p
}

func (x UserRole) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserRole) Descriptor() protoreflect.EnumDescriptor {
	return file_console_proto_enumTypes[0].Descriptor()
}

func (UserRole) Type() protoreflect.EnumType {
	return &file_console_proto_enumTypes[0]
}

func (x UserRole) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserRole.Descriptor instead.
func (UserRole) EnumDescriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{0}
}

type StatusHealth int32

const (
	StatusHealth_STATUS_HEALTH_OK            StatusHealth = 0
	StatusHealth_STATUS_HEALTH_ERROR         StatusHealth = 1
	StatusHealth_STATUS_HEALTH_CONNECTING    StatusHealth = 2
	StatusHealth_STATUS_HEALTH_DISCONNECTING StatusHealth = 3
)

// Enum value maps for StatusHealth.
var (
	StatusHealth_name = map[int32]string{
		0: "STATUS_HEALTH_OK",
		1: "STATUS_HEALTH_ERROR",
		2: "STATUS_HEALTH_CONNECTING",
		3: "STATUS_HEALTH_DISCONNECTING",
	}
	StatusHealth_value = map[string]int32{
		"STATUS_HEALTH_OK":            0,
		"STATUS_HEALTH_ERROR":         1,
		"STATUS_HEALTH_CONNECTING":    2,
		"STATUS_HEALTH_DISCONNECTING": 3,
	}
)

func (x StatusHealth) Enum() *StatusHealth {
	p := new(StatusHealth)
	*p = x
	return p
}

func (x StatusHealth) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatusHealth) Descriptor() protoreflect.EnumDescriptor {
	return file_console_proto_enumTypes[1].Descriptor()
}

func (StatusHealth) Type() protoreflect.EnumType {
	return &file_console_proto_enumTypes[1]
}

func (x StatusHealth) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StatusHealth.Descriptor instead.
func (StatusHealth) EnumDescriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{1}
}

type ListChannelMessagesRequest_Type int32

const (
	ListChannelMessagesRequest_UNKNOWN ListChannelMessagesRequest_Type = 0
	ListChannelMessagesRequest_ROOM    ListChannelMessagesRequest_Type = 2
	ListChannelMessagesRequest_GROUP   ListChannelMessagesRequest_Type = 3
	ListChannelMessagesRequest_DIRECT  ListChannelMessagesRequest_Type = 4
)

// Enum value maps for ListChannelMessagesRequest_Type.
var (
	ListChannelMessagesRequest_Type_name = map[int32]string{
		0: "UNKNOWN",
		2: "ROOM",
		3: "GROUP",
		4: "DIRECT",
	}
	ListChannelMessagesRequest_Type_value = map[string]int32{
		"UNKNOWN": 0,
		"ROOM":    2,
		"GROUP":   3,
		"DIRECT":  4,
	}
)

func (x ListChannelMessagesRequest_Type) Enum() *ListChannelMessagesRequest_Type {
	p := new(ListChannelMessagesRequest_Type)
	*p = x
	return p
}

func (x ListChannelMessagesRequest_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListChannelMessagesRequest_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_console_proto_enumTypes[2].Descriptor()
}

func (ListChannelMessagesRequest_Type) Type() protoreflect.EnumType {
	return &file_console_proto_enumTypes[2]
}

func (x ListChannelMessagesRequest_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListChannelMessagesRequest_Type.Descriptor instead.
func (ListChannelMessagesRequest_Type) EnumDescriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{37, 0}
}

// API Explorer List of Endpoints response message
type ApiEndpointDescriptor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Method       string `protobuf:"bytes,1,opt,name=method,proto3" json:"method,omitempty"`
	BodyTemplate string `protobuf:"bytes,2,opt,name=body_template,json=bodyTemplate,proto3" json:"body_template,omitempty"`
}

func (x *ApiEndpointDescriptor) Reset() {
	*x = ApiEndpointDescriptor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApiEndpointDescriptor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiEndpointDescriptor) ProtoMessage() {}

func (x *ApiEndpointDescriptor) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiEndpointDescriptor.ProtoReflect.Descriptor instead.
func (*ApiEndpointDescriptor) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{0}
}

func (x *ApiEndpointDescriptor) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *ApiEndpointDescriptor) GetBodyTemplate() string {
	if x != nil {
		return x.BodyTemplate
	}
	return ""
}

// Account information.
type Account struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The user's account details.
	Account *api.Account `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	// The UNIX time when the account was disabled.
	DisableTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=disable_time,json=disableTime,proto3" json:"disable_time,omitempty"`
}

func (x *Account) Reset() {
	*x = Account{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account) ProtoMessage() {}

func (x *Account) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account.ProtoReflect.Descriptor instead.
func (*Account) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{1}
}

func (x *Account) GetAccount() *api.Account {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *Account) GetDisableTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DisableTime
	}
	return nil
}

// Delete a user account.
type AccountDeleteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique identifier of the user account.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Record the user deletion - used for GDPR compliance.
	RecordDeletion *wrapperspb.BoolValue `protobuf:"bytes,2,opt,name=record_deletion,json=recordDeletion,proto3" json:"record_deletion,omitempty"`
}

func (x *AccountDeleteRequest) Reset() {
	*x = AccountDeleteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountDeleteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountDeleteRequest) ProtoMessage() {}

func (x *AccountDeleteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountDeleteRequest.ProtoReflect.Descriptor instead.
func (*AccountDeleteRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{2}
}

func (x *AccountDeleteRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AccountDeleteRequest) GetRecordDeletion() *wrapperspb.BoolValue {
	if x != nil {
		return x.RecordDeletion
	}
	return nil
}

// An export of all information stored for a user account.
type AccountExport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The user's account details.
	Account *api.Account `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	// The user's storage.
	Objects []*api.StorageObject `protobuf:"bytes,2,rep,name=objects,proto3" json:"objects,omitempty"`
	// The user's friends.
	Friends []*api.Friend `protobuf:"bytes,3,rep,name=friends,proto3" json:"friends,omitempty"`
	// The user's groups.
	Groups []*api.Group `protobuf:"bytes,4,rep,name=groups,proto3" json:"groups,omitempty"`
	// The user's chat messages.
	Messages []*api.ChannelMessage `protobuf:"bytes,5,rep,name=messages,proto3" json:"messages,omitempty"`
	// The user's leaderboard records.
	LeaderboardRecords []*api.LeaderboardRecord `protobuf:"bytes,6,rep,name=leaderboard_records,json=leaderboardRecords,proto3" json:"leaderboard_records,omitempty"`
	// The user's notifications.
	Notifications []*api.Notification `protobuf:"bytes,7,rep,name=notifications,proto3" json:"notifications,omitempty"`
	// The user's wallet ledger items.
	WalletLedgers []*WalletLedger `protobuf:"bytes,8,rep,name=wallet_ledgers,json=walletLedgers,proto3" json:"wallet_ledgers,omitempty"`
}

func (x *AccountExport) Reset() {
	*x = AccountExport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountExport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountExport) ProtoMessage() {}

func (x *AccountExport) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountExport.ProtoReflect.Descriptor instead.
func (*AccountExport) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{3}
}

func (x *AccountExport) GetAccount() *api.Account {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *AccountExport) GetObjects() []*api.StorageObject {
	if x != nil {
		return x.Objects
	}
	return nil
}

func (x *AccountExport) GetFriends() []*api.Friend {
	if x != nil {
		return x.Friends
	}
	return nil
}

func (x *AccountExport) GetGroups() []*api.Group {
	if x != nil {
		return x.Groups
	}
	return nil
}

func (x *AccountExport) GetMessages() []*api.ChannelMessage {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *AccountExport) GetLeaderboardRecords() []*api.LeaderboardRecord {
	if x != nil {
		return x.LeaderboardRecords
	}
	return nil
}

func (x *AccountExport) GetNotifications() []*api.Notification {
	if x != nil {
		return x.Notifications
	}
	return nil
}

func (x *AccountExport) GetWalletLedgers() []*WalletLedger {
	if x != nil {
		return x.WalletLedgers
	}
	return nil
}

// The identifier for a user account.
type AccountId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique identifier of the user account.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *AccountId) Reset() {
	*x = AccountId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountId) ProtoMessage() {}

func (x *AccountId) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountId.ProtoReflect.Descriptor instead.
func (*AccountId) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{4}
}

func (x *AccountId) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// A list of users.
type AccountList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of users.
	Users []*api.User `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	// Approximate total number of users.
	TotalCount int32 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	// Next cursor.
	NextCursor string `protobuf:"bytes,3,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
}

func (x *AccountList) Reset() {
	*x = AccountList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountList) ProtoMessage() {}

func (x *AccountList) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountList.ProtoReflect.Descriptor instead.
func (*AccountList) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{5}
}

func (x *AccountList) GetUsers() []*api.User {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *AccountList) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *AccountList) GetNextCursor() string {
	if x != nil {
		return x.NextCursor
	}
	return ""
}

// The identifier for a group.
type GroupId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique identifier of the group.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GroupId) Reset() {
	*x = GroupId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupId) ProtoMessage() {}

func (x *GroupId) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupId.ProtoReflect.Descriptor instead.
func (*GroupId) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{6}
}

func (x *GroupId) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// A list of groups.
type GroupList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of groups.
	Groups []*api.Group `protobuf:"bytes,1,rep,name=groups,proto3" json:"groups,omitempty"`
	// Approximate total number of groups.
	TotalCount int32 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	// Next cursor.
	NextCursor string `protobuf:"bytes,3,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
}

func (x *GroupList) Reset() {
	*x = GroupList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupList) ProtoMessage() {}

func (x *GroupList) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupList.ProtoReflect.Descriptor instead.
func (*GroupList) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{7}
}

func (x *GroupList) GetGroups() []*api.Group {
	if x != nil {
		return x.Groups
	}
	return nil
}

func (x *GroupList) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *GroupList) GetNextCursor() string {
	if x != nil {
		return x.NextCursor
	}
	return ""
}

// An export of all information stored for a group.
type GroupExport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The group details.
	Group *api.Group `protobuf:"bytes,1,opt,name=group,proto3" json:"group,omitempty"`
	// The group's list of members.
	Members []*api.GroupUserList_GroupUser `protobuf:"bytes,2,rep,name=members,proto3" json:"members,omitempty"`
}

func (x *GroupExport) Reset() {
	*x = GroupExport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupExport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupExport) ProtoMessage() {}

func (x *GroupExport) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupExport.ProtoReflect.Descriptor instead.
func (*GroupExport) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{8}
}

func (x *GroupExport) GetGroup() *api.Group {
	if x != nil {
		return x.Group
	}
	return nil
}

func (x *GroupExport) GetMembers() []*api.GroupUserList_GroupUser {
	if x != nil {
		return x.Members
	}
	return nil
}

// A list of realtime matches, with their node names.
type MatchList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Matches []*MatchList_Match `protobuf:"bytes,1,rep,name=matches,proto3" json:"matches,omitempty"`
}

func (x *MatchList) Reset() {
	*x = MatchList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchList) ProtoMessage() {}

func (x *MatchList) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchList.ProtoReflect.Descriptor instead.
func (*MatchList) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{9}
}

func (x *MatchList) GetMatches() []*MatchList_Match {
	if x != nil {
		return x.Matches
	}
	return nil
}

// Add a new console user
type AddUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The username of the user.
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// The password of the user.
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	// Email address of the user.
	Email string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	// Role of this user;
	Role UserRole `protobuf:"varint,4,opt,name=role,proto3,enum=nakama.console.UserRole" json:"role,omitempty"`
	// Require MFA
	MfaRequired bool `protobuf:"varint,5,opt,name=mfa_required,json=mfaRequired,proto3" json:"mfa_required,omitempty"`
	// Subscribe to newsletters
	NewsletterSubscription bool `protobuf:"varint,6,opt,name=newsletter_subscription,json=newsletterSubscription,proto3" json:"newsletter_subscription,omitempty"`
}

func (x *AddUserRequest) Reset() {
	*x = AddUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserRequest) ProtoMessage() {}

func (x *AddUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserRequest.ProtoReflect.Descriptor instead.
func (*AddUserRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{10}
}

func (x *AddUserRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *AddUserRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *AddUserRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *AddUserRequest) GetRole() UserRole {
	if x != nil {
		return x.Role
	}
	return UserRole_USER_ROLE_UNKNOWN
}

func (x *AddUserRequest) GetMfaRequired() bool {
	if x != nil {
		return x.MfaRequired
	}
	return false
}

func (x *AddUserRequest) GetNewsletterSubscription() bool {
	if x != nil {
		return x.NewsletterSubscription
	}
	return false
}

// Add/join users to a group.
type AddGroupUsersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Users to add/join.
	Ids string `protobuf:"bytes,1,opt,name=ids,proto3" json:"ids,omitempty"`
	// ID of the group to add them to.
	GroupId string `protobuf:"bytes,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	// Whether it is a join request.
	JoinRequest bool `protobuf:"varint,3,opt,name=join_request,json=joinRequest,proto3" json:"join_request,omitempty"`
}

func (x *AddGroupUsersRequest) Reset() {
	*x = AddGroupUsersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddGroupUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddGroupUsersRequest) ProtoMessage() {}

func (x *AddGroupUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddGroupUsersRequest.ProtoReflect.Descriptor instead.
func (*AddGroupUsersRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{11}
}

func (x *AddGroupUsersRequest) GetIds() string {
	if x != nil {
		return x.Ids
	}
	return ""
}

func (x *AddGroupUsersRequest) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *AddGroupUsersRequest) GetJoinRequest() bool {
	if x != nil {
		return x.JoinRequest
	}
	return false
}

// API Explorer List of Endpoints
type ApiEndpointList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Endpoints    []*ApiEndpointDescriptor `protobuf:"bytes,1,rep,name=endpoints,proto3" json:"endpoints,omitempty"`
	RpcEndpoints []*ApiEndpointDescriptor `protobuf:"bytes,2,rep,name=rpc_endpoints,json=rpcEndpoints,proto3" json:"rpc_endpoints,omitempty"`
}

func (x *ApiEndpointList) Reset() {
	*x = ApiEndpointList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApiEndpointList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiEndpointList) ProtoMessage() {}

func (x *ApiEndpointList) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiEndpointList.ProtoReflect.Descriptor instead.
func (*ApiEndpointList) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{12}
}

func (x *ApiEndpointList) GetEndpoints() []*ApiEndpointDescriptor {
	if x != nil {
		return x.Endpoints
	}
	return nil
}

func (x *ApiEndpointList) GetRpcEndpoints() []*ApiEndpointDescriptor {
	if x != nil {
		return x.RpcEndpoints
	}
	return nil
}

// Authenticate a console user with username and password.
type AuthenticateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The username of the user.
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// The password of the user.
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	// Multi-factor authentication code.
	Mfa string `protobuf:"bytes,3,opt,name=mfa,proto3" json:"mfa,omitempty"`
}

func (x *AuthenticateRequest) Reset() {
	*x = AuthenticateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthenticateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthenticateRequest) ProtoMessage() {}

func (x *AuthenticateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthenticateRequest.ProtoReflect.Descriptor instead.
func (*AuthenticateRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{13}
}

func (x *AuthenticateRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *AuthenticateRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *AuthenticateRequest) GetMfa() string {
	if x != nil {
		return x.Mfa
	}
	return ""
}

// Request to change MFA.
type AuthenticateMFASetupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// MFA token.
	Mfa string `protobuf:"bytes,1,opt,name=mfa,proto3" json:"mfa,omitempty"`
	// MFA code.
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *AuthenticateMFASetupRequest) Reset() {
	*x = AuthenticateMFASetupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthenticateMFASetupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthenticateMFASetupRequest) ProtoMessage() {}

func (x *AuthenticateMFASetupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthenticateMFASetupRequest.ProtoReflect.Descriptor instead.
func (*AuthenticateMFASetupRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{14}
}

func (x *AuthenticateMFASetupRequest) GetMfa() string {
	if x != nil {
		return x.Mfa
	}
	return ""
}

func (x *AuthenticateMFASetupRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

// Response to change MFA.
type AuthenticateMFASetupResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// An one-time code to configure the MFA mechanism
	RecoveryCodes []string `protobuf:"bytes,1,rep,name=recovery_codes,json=recoveryCodes,proto3" json:"recovery_codes,omitempty"`
}

func (x *AuthenticateMFASetupResponse) Reset() {
	*x = AuthenticateMFASetupResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthenticateMFASetupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthenticateMFASetupResponse) ProtoMessage() {}

func (x *AuthenticateMFASetupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthenticateMFASetupResponse.ProtoReflect.Descriptor instead.
func (*AuthenticateMFASetupResponse) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{15}
}

func (x *AuthenticateMFASetupResponse) GetRecoveryCodes() []string {
	if x != nil {
		return x.RecoveryCodes
	}
	return nil
}

// Log out a session and invalidate a session token.
type AuthenticateLogoutRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Session token to log out.
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *AuthenticateLogoutRequest) Reset() {
	*x = AuthenticateLogoutRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthenticateLogoutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthenticateLogoutRequest) ProtoMessage() {}

func (x *AuthenticateLogoutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthenticateLogoutRequest.ProtoReflect.Descriptor instead.
func (*AuthenticateLogoutRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{16}
}

func (x *AuthenticateLogoutRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// API Explorer request definition for CallApiEndpoint
type CallApiEndpointRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Method      string            `protobuf:"bytes,1,opt,name=method,proto3" json:"method,omitempty"`
	Body        string            `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	UserId      string            `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SessionVars map[string]string `protobuf:"bytes,4,rep,name=session_vars,json=sessionVars,proto3" json:"session_vars,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CallApiEndpointRequest) Reset() {
	*x = CallApiEndpointRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallApiEndpointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallApiEndpointRequest) ProtoMessage() {}

func (x *CallApiEndpointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallApiEndpointRequest.ProtoReflect.Descriptor instead.
func (*CallApiEndpointRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{17}
}

func (x *CallApiEndpointRequest) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *CallApiEndpointRequest) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *CallApiEndpointRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CallApiEndpointRequest) GetSessionVars() map[string]string {
	if x != nil {
		return x.SessionVars
	}
	return nil
}

// API Explorer response definition for CallApiEndpoint
type CallApiEndpointResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Body         string `protobuf:"bytes,1,opt,name=body,proto3" json:"body,omitempty"`
	ErrorMessage string `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
}

func (x *CallApiEndpointResponse) Reset() {
	*x = CallApiEndpointResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallApiEndpointResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallApiEndpointResponse) ProtoMessage() {}

func (x *CallApiEndpointResponse) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallApiEndpointResponse.ProtoReflect.Descriptor instead.
func (*CallApiEndpointResponse) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{18}
}

func (x *CallApiEndpointResponse) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *CallApiEndpointResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// The current server configuration and any associated warnings.
type Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// JSON-encoded active server configuration.
	Config string `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	// Any warnings about the current config.
	Warnings []*Config_Warning `protobuf:"bytes,2,rep,name=warnings,proto3" json:"warnings,omitempty"`
	// Server version
	ServerVersion string `protobuf:"bytes,3,opt,name=server_version,json=serverVersion,proto3" json:"server_version,omitempty"`
}

func (x *Config) Reset() {
	*x = Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{19}
}

func (x *Config) GetConfig() string {
	if x != nil {
		return x.Config
	}
	return ""
}

func (x *Config) GetWarnings() []*Config_Warning {
	if x != nil {
		return x.Warnings
	}
	return nil
}

func (x *Config) GetServerVersion() string {
	if x != nil {
		return x.ServerVersion
	}
	return ""
}

// A console user session.
type ConsoleSession struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A session token (JWT) for the console user.
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	// MFA code required to setup the MFA mechanism.
	MfaCode *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=mfa_code,json=mfaCode,proto3" json:"mfa_code,omitempty"`
}

func (x *ConsoleSession) Reset() {
	*x = ConsoleSession{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsoleSession) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsoleSession) ProtoMessage() {}

func (x *ConsoleSession) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsoleSession.ProtoReflect.Descriptor instead.
func (*ConsoleSession) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{20}
}

func (x *ConsoleSession) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *ConsoleSession) GetMfaCode() *wrapperspb.StringValue {
	if x != nil {
		return x.MfaCode
	}
	return nil
}

// Delete channel messages by timestamp or/and ids.
type DeleteChannelMessagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Timestamp before which messages will be deleted.
	Before *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=before,proto3" json:"before,omitempty"`
	// IDs of the messages to delete.
	Ids []string `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
}

func (x *DeleteChannelMessagesRequest) Reset() {
	*x = DeleteChannelMessagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteChannelMessagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteChannelMessagesRequest) ProtoMessage() {}

func (x *DeleteChannelMessagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteChannelMessagesRequest.ProtoReflect.Descriptor instead.
func (*DeleteChannelMessagesRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{21}
}

func (x *DeleteChannelMessagesRequest) GetBefore() *timestamppb.Timestamp {
	if x != nil {
		return x.Before
	}
	return nil
}

func (x *DeleteChannelMessagesRequest) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

// Delete friend relationship between two users.
type DeleteFriendRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The user do delete for.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// User ID of the friend to remove.
	FriendId string `protobuf:"bytes,2,opt,name=friend_id,json=friendId,proto3" json:"friend_id,omitempty"`
}

func (x *DeleteFriendRequest) Reset() {
	*x = DeleteFriendRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteFriendRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFriendRequest) ProtoMessage() {}

func (x *DeleteFriendRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFriendRequest.ProtoReflect.Descriptor instead.
func (*DeleteFriendRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{22}
}

func (x *DeleteFriendRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeleteFriendRequest) GetFriendId() string {
	if x != nil {
		return x.FriendId
	}
	return ""
}

// Delete a group.
type DeleteGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID of the group to delete.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteGroupRequest) Reset() {
	*x = DeleteGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGroupRequest) ProtoMessage() {}

func (x *DeleteGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGroupRequest.ProtoReflect.Descriptor instead.
func (*DeleteGroupRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{23}
}

func (x *DeleteGroupRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// Remove a user from a group.
type DeleteGroupUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User to remove.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// ID of the group to remove them from.
	GroupId string `protobuf:"bytes,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
}

func (x *DeleteGroupUserRequest) Reset() {
	*x = DeleteGroupUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteGroupUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGroupUserRequest) ProtoMessage() {}

func (x *DeleteGroupUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGroupUserRequest.ProtoReflect.Descriptor instead.
func (*DeleteGroupUserRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{24}
}

func (x *DeleteGroupUserRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeleteGroupUserRequest) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

// Promote/demote a user in a group.
type UpdateGroupUserStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User to change state.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// ID of the group.
	GroupId string `protobuf:"bytes,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
}

func (x *UpdateGroupUserStateRequest) Reset() {
	*x = UpdateGroupUserStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateGroupUserStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGroupUserStateRequest) ProtoMessage() {}

func (x *UpdateGroupUserStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGroupUserStateRequest.ProtoReflect.Descriptor instead.
func (*UpdateGroupUserStateRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateGroupUserStateRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateGroupUserStateRequest) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

// Reset a user's mfa setup.
type ResetUserMfaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User username.
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
}

func (x *ResetUserMfaRequest) Reset() {
	*x = ResetUserMfaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetUserMfaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetUserMfaRequest) ProtoMessage() {}

func (x *ResetUserMfaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetUserMfaRequest.ProtoReflect.Descriptor instead.
func (*ResetUserMfaRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{26}
}

func (x *ResetUserMfaRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

// Make a user's mfa required or not.
type RequireUserMfaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User username.
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// Required.
	Required bool `protobuf:"varint,2,opt,name=required,proto3" json:"required,omitempty"`
}

func (x *RequireUserMfaRequest) Reset() {
	*x = RequireUserMfaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequireUserMfaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequireUserMfaRequest) ProtoMessage() {}

func (x *RequireUserMfaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequireUserMfaRequest.ProtoReflect.Descriptor instead.
func (*RequireUserMfaRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{27}
}

func (x *RequireUserMfaRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *RequireUserMfaRequest) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

// Remove a leaderboard record
type DeleteLeaderboardRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Leaderboard ID.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Record owner.
	OwnerId string `protobuf:"bytes,2,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
}

func (x *DeleteLeaderboardRecordRequest) Reset() {
	*x = DeleteLeaderboardRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLeaderboardRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLeaderboardRecordRequest) ProtoMessage() {}

func (x *DeleteLeaderboardRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLeaderboardRecordRequest.ProtoReflect.Descriptor instead.
func (*DeleteLeaderboardRecordRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{28}
}

func (x *DeleteLeaderboardRecordRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeleteLeaderboardRecordRequest) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

// Delete notification
type DeleteNotificationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Notification ID.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteNotificationRequest) Reset() {
	*x = DeleteNotificationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNotificationRequest) ProtoMessage() {}

func (x *DeleteNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNotificationRequest.ProtoReflect.Descriptor instead.
func (*DeleteNotificationRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{29}
}

func (x *DeleteNotificationRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// Delete an individual storage object.
type DeleteStorageObjectRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Collection.
	Collection string `protobuf:"bytes,1,opt,name=collection,proto3" json:"collection,omitempty"`
	// Key.
	Key string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	// Owner user ID.
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// Version for OCC.
	Version string `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *DeleteStorageObjectRequest) Reset() {
	*x = DeleteStorageObjectRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteStorageObjectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStorageObjectRequest) ProtoMessage() {}

func (x *DeleteStorageObjectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStorageObjectRequest.ProtoReflect.Descriptor instead.
func (*DeleteStorageObjectRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{30}
}

func (x *DeleteStorageObjectRequest) GetCollection() string {
	if x != nil {
		return x.Collection
	}
	return ""
}

func (x *DeleteStorageObjectRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *DeleteStorageObjectRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DeleteStorageObjectRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// Delete a single wallet ledger item.
type DeleteWalletLedgerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User ID to remove wallet ledger item from.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// ID of the wallet ledger item to remove.
	WalletId string `protobuf:"bytes,2,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
}

func (x *DeleteWalletLedgerRequest) Reset() {
	*x = DeleteWalletLedgerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWalletLedgerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWalletLedgerRequest) ProtoMessage() {}

func (x *DeleteWalletLedgerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWalletLedgerRequest.ProtoReflect.Descriptor instead.
func (*DeleteWalletLedgerRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{31}
}

func (x *DeleteWalletLedgerRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeleteWalletLedgerRequest) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

// A leaderboard.
type Leaderboard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the leaderboard.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The title for the leaderboard.
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// The description of the leaderboard. May be blank.
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// The category of the leaderboard. e.g. "vip" could be category 1.
	Category uint32 `protobuf:"varint,4,opt,name=category,proto3" json:"category,omitempty"`
	// ASC or DESC sort mode of scores in the leaderboard.
	SortOrder uint32 `protobuf:"varint,5,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	// The current number of players in the leaderboard.
	Size uint32 `protobuf:"varint,6,opt,name=size,proto3" json:"size,omitempty"`
	// The maximum number of players for the leaderboard.
	MaxSize uint32 `protobuf:"varint,7,opt,name=max_size,json=maxSize,proto3" json:"max_size,omitempty"`
	// The maximum score updates allowed per player for the current leaderboard.
	MaxNumScore uint32 `protobuf:"varint,8,opt,name=max_num_score,json=maxNumScore,proto3" json:"max_num_score,omitempty"`
	// The operator of the leaderboard
	Operator uint32 `protobuf:"varint,9,opt,name=operator,proto3" json:"operator,omitempty"`
	// The UNIX time when the leaderboard stops being active until next reset. A computed value.
	EndActive uint32 `protobuf:"varint,10,opt,name=end_active,json=endActive,proto3" json:"end_active,omitempty"`
	// Reset cron expression.
	ResetSchedule string `protobuf:"bytes,11,opt,name=reset_schedule,json=resetSchedule,proto3" json:"reset_schedule,omitempty"`
	// Additional information stored as a JSON object.
	Metadata string `protobuf:"bytes,12,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// The UNIX time when the leaderboard was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The UNIX time when the leaderboard will start.
	StartTime *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// The UNIX time when the leaderboard will be stopped.
	EndTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// Duration of the tournament in seconds.
	Duration uint32 `protobuf:"varint,16,opt,name=duration,proto3" json:"duration,omitempty"`
	// The UNIX time when the leaderboard start being active. A computed value.
	StartActive uint32 `protobuf:"varint,17,opt,name=start_active,json=startActive,proto3" json:"start_active,omitempty"`
	// Join required.
	JoinRequired bool `protobuf:"varint,18,opt,name=join_required,json=joinRequired,proto3" json:"join_required,omitempty"`
	// Authoritative.
	Authoritative bool `protobuf:"varint,19,opt,name=authoritative,proto3" json:"authoritative,omitempty"`
	// Tournament.
	Tournament bool `protobuf:"varint,20,opt,name=tournament,proto3" json:"tournament,omitempty"`
	// The UNIX time when the tournament was last reset. A computed value.
	PrevReset uint32 `protobuf:"varint,21,opt,name=prev_reset,json=prevReset,proto3" json:"prev_reset,omitempty"`
	// The UNIX time when the tournament is next playable. A computed value.
	NextReset uint32 `protobuf:"varint,22,opt,name=next_reset,json=nextReset,proto3" json:"next_reset,omitempty"`
}

func (x *Leaderboard) Reset() {
	*x = Leaderboard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Leaderboard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Leaderboard) ProtoMessage() {}

func (x *Leaderboard) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Leaderboard.ProtoReflect.Descriptor instead.
func (*Leaderboard) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{32}
}

func (x *Leaderboard) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Leaderboard) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Leaderboard) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Leaderboard) GetCategory() uint32 {
	if x != nil {
		return x.Category
	}
	return 0
}

func (x *Leaderboard) GetSortOrder() uint32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *Leaderboard) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Leaderboard) GetMaxSize() uint32 {
	if x != nil {
		return x.MaxSize
	}
	return 0
}

func (x *Leaderboard) GetMaxNumScore() uint32 {
	if x != nil {
		return x.MaxNumScore
	}
	return 0
}

func (x *Leaderboard) GetOperator() uint32 {
	if x != nil {
		return x.Operator
	}
	return 0
}

func (x *Leaderboard) GetEndActive() uint32 {
	if x != nil {
		return x.EndActive
	}
	return 0
}

func (x *Leaderboard) GetResetSchedule() string {
	if x != nil {
		return x.ResetSchedule
	}
	return ""
}

func (x *Leaderboard) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *Leaderboard) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Leaderboard) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *Leaderboard) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *Leaderboard) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *Leaderboard) GetStartActive() uint32 {
	if x != nil {
		return x.StartActive
	}
	return 0
}

func (x *Leaderboard) GetJoinRequired() bool {
	if x != nil {
		return x.JoinRequired
	}
	return false
}

func (x *Leaderboard) GetAuthoritative() bool {
	if x != nil {
		return x.Authoritative
	}
	return false
}

func (x *Leaderboard) GetTournament() bool {
	if x != nil {
		return x.Tournament
	}
	return false
}

func (x *Leaderboard) GetPrevReset() uint32 {
	if x != nil {
		return x.PrevReset
	}
	return 0
}

func (x *Leaderboard) GetNextReset() uint32 {
	if x != nil {
		return x.NextReset
	}
	return 0
}

// List leaderboards.
type LeaderboardListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// An optional cursor to paginate from.
	Cursor string `protobuf:"bytes,1,opt,name=cursor,proto3" json:"cursor,omitempty"`
}

func (x *LeaderboardListRequest) Reset() {
	*x = LeaderboardListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LeaderboardListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaderboardListRequest) ProtoMessage() {}

func (x *LeaderboardListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaderboardListRequest.ProtoReflect.Descriptor instead.
func (*LeaderboardListRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{33}
}

func (x *LeaderboardListRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

// A list of leaderboards.
type LeaderboardList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of leaderboards returned.
	Leaderboards []*Leaderboard `protobuf:"bytes,1,rep,name=leaderboards,proto3" json:"leaderboards,omitempty"`
	// Total count of leaderboards and tournaments.
	Total int32 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	// A cursor, if any.
	Cursor string `protobuf:"bytes,3,opt,name=cursor,proto3" json:"cursor,omitempty"`
}

func (x *LeaderboardList) Reset() {
	*x = LeaderboardList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LeaderboardList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaderboardList) ProtoMessage() {}

func (x *LeaderboardList) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaderboardList.ProtoReflect.Descriptor instead.
func (*LeaderboardList) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{34}
}

func (x *LeaderboardList) GetLeaderboards() []*Leaderboard {
	if x != nil {
		return x.Leaderboards
	}
	return nil
}

func (x *LeaderboardList) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *LeaderboardList) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

// Get Leaderboard.
type LeaderboardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Leaderboard ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *LeaderboardRequest) Reset() {
	*x = LeaderboardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LeaderboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaderboardRequest) ProtoMessage() {}

func (x *LeaderboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaderboardRequest.ProtoReflect.Descriptor instead.
func (*LeaderboardRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{35}
}

func (x *LeaderboardRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// List (and optionally filter) users.
type ListAccountsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User ID or username filter.
	Filter string `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// Search only recorded deletes.
	Tombstones bool `protobuf:"varint,2,opt,name=tombstones,proto3" json:"tombstones,omitempty"`
	// Cursor to start from
	Cursor string `protobuf:"bytes,3,opt,name=cursor,proto3" json:"cursor,omitempty"`
}

func (x *ListAccountsRequest) Reset() {
	*x = ListAccountsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccountsRequest) ProtoMessage() {}

func (x *ListAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccountsRequest.ProtoReflect.Descriptor instead.
func (*ListAccountsRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{36}
}

func (x *ListAccountsRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListAccountsRequest) GetTombstones() bool {
	if x != nil {
		return x.Tombstones
	}
	return false
}

func (x *ListAccountsRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

// List selected channel messages.
type ListChannelMessagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Type of the chat channel
	Type ListChannelMessagesRequest_Type `protobuf:"varint,1,opt,name=type,proto3,enum=nakama.console.ListChannelMessagesRequest_Type" json:"type,omitempty"`
	// Label of the channel, if a standard chat room
	Label string `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	// Group ID of the channel, if a group chat
	GroupId string `protobuf:"bytes,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	// User IDs, if a direct chat
	UserIdOne string `protobuf:"bytes,4,opt,name=user_id_one,json=userIdOne,proto3" json:"user_id_one,omitempty"`
	UserIdTwo string `protobuf:"bytes,5,opt,name=user_id_two,json=userIdTwo,proto3" json:"user_id_two,omitempty"`
	// Cursor to start from
	Cursor string `protobuf:"bytes,6,opt,name=cursor,proto3" json:"cursor,omitempty"`
}

func (x *ListChannelMessagesRequest) Reset() {
	*x = ListChannelMessagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListChannelMessagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListChannelMessagesRequest) ProtoMessage() {}

func (x *ListChannelMessagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListChannelMessagesRequest.ProtoReflect.Descriptor instead.
func (*ListChannelMessagesRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{37}
}

func (x *ListChannelMessagesRequest) GetType() ListChannelMessagesRequest_Type {
	if x != nil {
		return x.Type
	}
	return ListChannelMessagesRequest_UNKNOWN
}

func (x *ListChannelMessagesRequest) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *ListChannelMessagesRequest) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *ListChannelMessagesRequest) GetUserIdOne() string {
	if x != nil {
		return x.UserIdOne
	}
	return ""
}

func (x *ListChannelMessagesRequest) GetUserIdTwo() string {
	if x != nil {
		return x.UserIdTwo
	}
	return ""
}

func (x *ListChannelMessagesRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

// List (and optionally filter) groups.
type ListGroupsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User ID or username filter.
	Filter string `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// Cursor to start from
	Cursor string `protobuf:"bytes,2,opt,name=cursor,proto3" json:"cursor,omitempty"`
}

func (x *ListGroupsRequest) Reset() {
	*x = ListGroupsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGroupsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGroupsRequest) ProtoMessage() {}

func (x *ListGroupsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGroupsRequest.ProtoReflect.Descriptor instead.
func (*ListGroupsRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{38}
}

func (x *ListGroupsRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListGroupsRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

// List realtime matches.
type ListMatchesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Limit the number of returned matches.
	Limit *wrapperspb.Int32Value `protobuf:"bytes,1,opt,name=limit,proto3" json:"limit,omitempty"`
	// Authoritative or relayed matches, or null for both.
	Authoritative *wrapperspb.BoolValue `protobuf:"bytes,2,opt,name=authoritative,proto3" json:"authoritative,omitempty"`
	// Label filter.
	Label *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=label,proto3" json:"label,omitempty"`
	// Minimum user count.
	MinSize *wrapperspb.Int32Value `protobuf:"bytes,4,opt,name=min_size,json=minSize,proto3" json:"min_size,omitempty"`
	// Maximum user count.
	MaxSize *wrapperspb.Int32Value `protobuf:"bytes,5,opt,name=max_size,json=maxSize,proto3" json:"max_size,omitempty"`
	// Match ID.
	MatchId string `protobuf:"bytes,6,opt,name=match_id,json=matchId,proto3" json:"match_id,omitempty"`
	// Arbitrary label query.
	Query *wrapperspb.StringValue `protobuf:"bytes,7,opt,name=query,proto3" json:"query,omitempty"`
	// Node name filter, optional.
	Node *wrapperspb.StringValue `protobuf:"bytes,8,opt,name=node,proto3" json:"node,omitempty"`
}

func (x *ListMatchesRequest) Reset() {
	*x = ListMatchesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMatchesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMatchesRequest) ProtoMessage() {}

func (x *ListMatchesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMatchesRequest.ProtoReflect.Descriptor instead.
func (*ListMatchesRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{39}
}

func (x *ListMatchesRequest) GetLimit() *wrapperspb.Int32Value {
	if x != nil {
		return x.Limit
	}
	return nil
}

func (x *ListMatchesRequest) GetAuthoritative() *wrapperspb.BoolValue {
	if x != nil {
		return x.Authoritative
	}
	return nil
}

func (x *ListMatchesRequest) GetLabel() *wrapperspb.StringValue {
	if x != nil {
		return x.Label
	}
	return nil
}

func (x *ListMatchesRequest) GetMinSize() *wrapperspb.Int32Value {
	if x != nil {
		return x.MinSize
	}
	return nil
}

func (x *ListMatchesRequest) GetMaxSize() *wrapperspb.Int32Value {
	if x != nil {
		return x.MaxSize
	}
	return nil
}

func (x *ListMatchesRequest) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

func (x *ListMatchesRequest) GetQuery() *wrapperspb.StringValue {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *ListMatchesRequest) GetNode() *wrapperspb.StringValue {
	if x != nil {
		return x.Node
	}
	return nil
}

// List notifications.
type ListNotificationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User ID to filter purchases for
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// The number of notifications to get.
	Limit uint32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	// A cursor to page through notifications.
	Cursor string `protobuf:"bytes,3,opt,name=cursor,proto3" json:"cursor,omitempty"`
}

func (x *ListNotificationsRequest) Reset() {
	*x = ListNotificationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListNotificationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNotificationsRequest) ProtoMessage() {}

func (x *ListNotificationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNotificationsRequest.ProtoReflect.Descriptor instead.
func (*ListNotificationsRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{40}
}

func (x *ListNotificationsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListNotificationsRequest) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListNotificationsRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

// List validated purchases.
type ListPurchasesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User ID to filter purchases for
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// Max number of results per page
	Limit uint32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	// Cursor to retrieve a page of records from
	Cursor string `protobuf:"bytes,3,opt,name=cursor,proto3" json:"cursor,omitempty"`
}

func (x *ListPurchasesRequest) Reset() {
	*x = ListPurchasesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPurchasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPurchasesRequest) ProtoMessage() {}

func (x *ListPurchasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPurchasesRequest.ProtoReflect.Descriptor instead.
func (*ListPurchasesRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{41}
}

func (x *ListPurchasesRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListPurchasesRequest) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListPurchasesRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

// List subscriptions.
type ListSubscriptionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// UserID to filter subscriptions for
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// Max number of results per page
	Limit uint32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	// Cursor to retrieve a page of records from
	Cursor string `protobuf:"bytes,3,opt,name=cursor,proto3" json:"cursor,omitempty"`
}

func (x *ListSubscriptionsRequest) Reset() {
	*x = ListSubscriptionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSubscriptionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSubscriptionsRequest) ProtoMessage() {}

func (x *ListSubscriptionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSubscriptionsRequest.ProtoReflect.Descriptor instead.
func (*ListSubscriptionsRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{42}
}

func (x *ListSubscriptionsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListSubscriptionsRequest) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListSubscriptionsRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

// List (and optionally filter) storage objects.
type ListStorageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User ID to filter objects for.
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// Key to filter objects for
	Key string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	// Collection name to filter objects for
	Collection string `protobuf:"bytes,3,opt,name=collection,proto3" json:"collection,omitempty"`
	// Cursor to retrieve a page of records from
	Cursor string `protobuf:"bytes,4,opt,name=cursor,proto3" json:"cursor,omitempty"`
}

func (x *ListStorageRequest) Reset() {
	*x = ListStorageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStorageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStorageRequest) ProtoMessage() {}

func (x *ListStorageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStorageRequest.ProtoReflect.Descriptor instead.
func (*ListStorageRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{43}
}

func (x *ListStorageRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListStorageRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ListStorageRequest) GetCollection() string {
	if x != nil {
		return x.Collection
	}
	return ""
}

func (x *ListStorageRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

// Match state
type MatchState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Presence list.
	Presences []*rtapi.UserPresence `protobuf:"bytes,1,rep,name=presences,proto3" json:"presences,omitempty"`
	// Current tick number.
	Tick int64 `protobuf:"varint,2,opt,name=tick,proto3" json:"tick,omitempty"`
	// State.
	State string `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *MatchState) Reset() {
	*x = MatchState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchState) ProtoMessage() {}

func (x *MatchState) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchState.ProtoReflect.Descriptor instead.
func (*MatchState) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{44}
}

func (x *MatchState) GetPresences() []*rtapi.UserPresence {
	if x != nil {
		return x.Presences
	}
	return nil
}

func (x *MatchState) GetTick() int64 {
	if x != nil {
		return x.Tick
	}
	return 0
}

func (x *MatchState) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

// Get current state of a running match
type MatchStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Match ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *MatchStateRequest) Reset() {
	*x = MatchStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchStateRequest) ProtoMessage() {}

func (x *MatchStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchStateRequest.ProtoReflect.Descriptor instead.
func (*MatchStateRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{45}
}

func (x *MatchStateRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type Notification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID of the Notification.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Subject of the notification.
	Subject string `protobuf:"bytes,2,opt,name=subject,proto3" json:"subject,omitempty"`
	// Content of the notification in JSON.
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	// Category code for this notification.
	Code int32 `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	// ID of the sender, if a user. Otherwise 'null'.
	SenderId string `protobuf:"bytes,5,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
	// The UNIX time (for gRPC clients) or ISO string (for REST clients) when the notification was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// True if this notification was persisted to the database.
	Persistent bool `protobuf:"varint,7,opt,name=persistent,proto3" json:"persistent,omitempty"`
	// User id.
	UserId string `protobuf:"bytes,8,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *Notification) Reset() {
	*x = Notification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Notification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Notification) ProtoMessage() {}

func (x *Notification) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Notification.ProtoReflect.Descriptor instead.
func (*Notification) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{46}
}

func (x *Notification) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Notification) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *Notification) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Notification) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Notification) GetSenderId() string {
	if x != nil {
		return x.SenderId
	}
	return ""
}

func (x *Notification) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Notification) GetPersistent() bool {
	if x != nil {
		return x.Persistent
	}
	return false
}

func (x *Notification) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type NotificationList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of notifications.
	Notifications []*Notification `protobuf:"bytes,1,rep,name=notifications,proto3" json:"notifications,omitempty"`
	// Next page cursor if any.
	NextCursor string `protobuf:"bytes,2,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
	// Previous page cursor if any.
	PrevCursor string `protobuf:"bytes,3,opt,name=prev_cursor,json=prevCursor,proto3" json:"prev_cursor,omitempty"`
}

func (x *NotificationList) Reset() {
	*x = NotificationList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotificationList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationList) ProtoMessage() {}

func (x *NotificationList) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationList.ProtoReflect.Descriptor instead.
func (*NotificationList) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{47}
}

func (x *NotificationList) GetNotifications() []*Notification {
	if x != nil {
		return x.Notifications
	}
	return nil
}

func (x *NotificationList) GetNextCursor() string {
	if x != nil {
		return x.NextCursor
	}
	return ""
}

func (x *NotificationList) GetPrevCursor() string {
	if x != nil {
		return x.PrevCursor
	}
	return ""
}

type DeleteChannelMessagesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Total number of messages deleted.
	Total int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *DeleteChannelMessagesResponse) Reset() {
	*x = DeleteChannelMessagesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteChannelMessagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteChannelMessagesResponse) ProtoMessage() {}

func (x *DeleteChannelMessagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteChannelMessagesResponse.ProtoReflect.Descriptor instead.
func (*DeleteChannelMessagesResponse) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{48}
}

func (x *DeleteChannelMessagesResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// List of storage objects.
type StorageList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of storage objects matching list/filter operation.
	Objects []*StorageListObject `protobuf:"bytes,1,rep,name=objects,proto3" json:"objects,omitempty"`
	// Approximate total number of storage objects.
	TotalCount int32 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	// Next page cursor if any
	NextCursor string `protobuf:"bytes,3,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
}

func (x *StorageList) Reset() {
	*x = StorageList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StorageList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StorageList) ProtoMessage() {}

func (x *StorageList) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StorageList.ProtoReflect.Descriptor instead.
func (*StorageList) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{49}
}

func (x *StorageList) GetObjects() []*StorageListObject {
	if x != nil {
		return x.Objects
	}
	return nil
}

func (x *StorageList) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *StorageList) GetNextCursor() string {
	if x != nil {
		return x.NextCursor
	}
	return ""
}

type StorageCollectionsList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Available collection names in the whole of the storage
	Collections []string `protobuf:"bytes,3,rep,name=collections,proto3" json:"collections,omitempty"`
}

func (x *StorageCollectionsList) Reset() {
	*x = StorageCollectionsList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StorageCollectionsList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StorageCollectionsList) ProtoMessage() {}

func (x *StorageCollectionsList) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StorageCollectionsList.ProtoReflect.Descriptor instead.
func (*StorageCollectionsList) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{50}
}

func (x *StorageCollectionsList) GetCollections() []string {
	if x != nil {
		return x.Collections
	}
	return nil
}

// Unlink a particular device ID from a user's account.
type UnlinkDeviceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User ID to unlink from.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Device ID to unlink.
	DeviceId string `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
}

func (x *UnlinkDeviceRequest) Reset() {
	*x = UnlinkDeviceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnlinkDeviceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnlinkDeviceRequest) ProtoMessage() {}

func (x *UnlinkDeviceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnlinkDeviceRequest.ProtoReflect.Descriptor instead.
func (*UnlinkDeviceRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{51}
}

func (x *UnlinkDeviceRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UnlinkDeviceRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

// Update user account information.
type UpdateAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User ID to update.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Username.
	Username *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	// Display name.
	DisplayName *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// Metadata.
	Metadata *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// Avatar URL.
	AvatarUrl *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	// Langtag.
	LangTag *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=lang_tag,json=langTag,proto3" json:"lang_tag,omitempty"`
	// Location.
	Location *wrapperspb.StringValue `protobuf:"bytes,7,opt,name=location,proto3" json:"location,omitempty"`
	// Timezone.
	Timezone *wrapperspb.StringValue `protobuf:"bytes,8,opt,name=timezone,proto3" json:"timezone,omitempty"`
	// Custom ID.
	CustomId *wrapperspb.StringValue `protobuf:"bytes,9,opt,name=custom_id,json=customId,proto3" json:"custom_id,omitempty"`
	// Email.
	Email *wrapperspb.StringValue `protobuf:"bytes,10,opt,name=email,proto3" json:"email,omitempty"`
	// Password.
	Password *wrapperspb.StringValue `protobuf:"bytes,11,opt,name=password,proto3" json:"password,omitempty"`
	// Device ID modifications.
	DeviceIds map[string]string `protobuf:"bytes,12,rep,name=device_ids,json=deviceIds,proto3" json:"device_ids,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Wallet.
	Wallet *wrapperspb.StringValue `protobuf:"bytes,13,opt,name=wallet,proto3" json:"wallet,omitempty"`
}

func (x *UpdateAccountRequest) Reset() {
	*x = UpdateAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAccountRequest) ProtoMessage() {}

func (x *UpdateAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAccountRequest.ProtoReflect.Descriptor instead.
func (*UpdateAccountRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{52}
}

func (x *UpdateAccountRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateAccountRequest) GetUsername() *wrapperspb.StringValue {
	if x != nil {
		return x.Username
	}
	return nil
}

func (x *UpdateAccountRequest) GetDisplayName() *wrapperspb.StringValue {
	if x != nil {
		return x.DisplayName
	}
	return nil
}

func (x *UpdateAccountRequest) GetMetadata() *wrapperspb.StringValue {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *UpdateAccountRequest) GetAvatarUrl() *wrapperspb.StringValue {
	if x != nil {
		return x.AvatarUrl
	}
	return nil
}

func (x *UpdateAccountRequest) GetLangTag() *wrapperspb.StringValue {
	if x != nil {
		return x.LangTag
	}
	return nil
}

func (x *UpdateAccountRequest) GetLocation() *wrapperspb.StringValue {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *UpdateAccountRequest) GetTimezone() *wrapperspb.StringValue {
	if x != nil {
		return x.Timezone
	}
	return nil
}

func (x *UpdateAccountRequest) GetCustomId() *wrapperspb.StringValue {
	if x != nil {
		return x.CustomId
	}
	return nil
}

func (x *UpdateAccountRequest) GetEmail() *wrapperspb.StringValue {
	if x != nil {
		return x.Email
	}
	return nil
}

func (x *UpdateAccountRequest) GetPassword() *wrapperspb.StringValue {
	if x != nil {
		return x.Password
	}
	return nil
}

func (x *UpdateAccountRequest) GetDeviceIds() map[string]string {
	if x != nil {
		return x.DeviceIds
	}
	return nil
}

func (x *UpdateAccountRequest) GetWallet() *wrapperspb.StringValue {
	if x != nil {
		return x.Wallet
	}
	return nil
}

// Update group information.
type UpdateGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Group ID to update.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Name.
	Name *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// Description.
	Description *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// Langtag.
	LangTag *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=lang_tag,json=langTag,proto3" json:"lang_tag,omitempty"`
	// Metadata.
	Metadata *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// Avatar URL.
	AvatarUrl *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	// Anyone can join open groups, otherwise only admins can accept members.
	Open *wrapperspb.BoolValue `protobuf:"bytes,7,opt,name=open,proto3" json:"open,omitempty"`
	// The maximum number of members allowed.
	MaxCount *wrapperspb.Int32Value `protobuf:"bytes,8,opt,name=max_count,json=maxCount,proto3" json:"max_count,omitempty"`
}

func (x *UpdateGroupRequest) Reset() {
	*x = UpdateGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGroupRequest) ProtoMessage() {}

func (x *UpdateGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGroupRequest.ProtoReflect.Descriptor instead.
func (*UpdateGroupRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{53}
}

func (x *UpdateGroupRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateGroupRequest) GetName() *wrapperspb.StringValue {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *UpdateGroupRequest) GetDescription() *wrapperspb.StringValue {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *UpdateGroupRequest) GetLangTag() *wrapperspb.StringValue {
	if x != nil {
		return x.LangTag
	}
	return nil
}

func (x *UpdateGroupRequest) GetMetadata() *wrapperspb.StringValue {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *UpdateGroupRequest) GetAvatarUrl() *wrapperspb.StringValue {
	if x != nil {
		return x.AvatarUrl
	}
	return nil
}

func (x *UpdateGroupRequest) GetOpen() *wrapperspb.BoolValue {
	if x != nil {
		return x.Open
	}
	return nil
}

func (x *UpdateGroupRequest) GetMaxCount() *wrapperspb.Int32Value {
	if x != nil {
		return x.MaxCount
	}
	return nil
}

// The identifier for a user account.
type Username struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique username of the user account.
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
}

func (x *Username) Reset() {
	*x = Username{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Username) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Username) ProtoMessage() {}

func (x *Username) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Username.ProtoReflect.Descriptor instead.
func (*Username) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{54}
}

func (x *Username) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

// A list of console users.
type UserList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of users.
	Users []*UserList_User `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *UserList) Reset() {
	*x = UserList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserList) ProtoMessage() {}

func (x *UserList) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserList.ProtoReflect.Descriptor instead.
func (*UserList) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{55}
}

func (x *UserList) GetUsers() []*UserList_User {
	if x != nil {
		return x.Users
	}
	return nil
}

// List of nodes and their stats.
type StatusList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of nodes and their stats.
	Nodes []*StatusList_Status `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"`
	// Timestamp
	Timestamp *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *StatusList) Reset() {
	*x = StatusList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusList) ProtoMessage() {}

func (x *StatusList) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusList.ProtoReflect.Descriptor instead.
func (*StatusList) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{56}
}

func (x *StatusList) GetNodes() []*StatusList_Status {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *StatusList) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

// Runtime information
type RuntimeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Lua registered RPC functions
	LuaRpcFunctions []string `protobuf:"bytes,1,rep,name=lua_rpc_functions,json=luaRpcFunctions,proto3" json:"lua_rpc_functions,omitempty"`
	// Go registered RPC functions
	GoRpcFunctions []string `protobuf:"bytes,2,rep,name=go_rpc_functions,json=goRpcFunctions,proto3" json:"go_rpc_functions,omitempty"`
	// JavaScript registered RPC functions
	JsRpcFunctions []string `protobuf:"bytes,3,rep,name=js_rpc_functions,json=jsRpcFunctions,proto3" json:"js_rpc_functions,omitempty"`
	// Go loaded modules
	GoModules []*RuntimeInfo_ModuleInfo `protobuf:"bytes,4,rep,name=go_modules,json=goModules,proto3" json:"go_modules,omitempty"`
	// Lua loaded modules
	LuaModules []*RuntimeInfo_ModuleInfo `protobuf:"bytes,5,rep,name=lua_modules,json=luaModules,proto3" json:"lua_modules,omitempty"`
	// JavaScript loaded modules
	JsModules []*RuntimeInfo_ModuleInfo `protobuf:"bytes,6,rep,name=js_modules,json=jsModules,proto3" json:"js_modules,omitempty"`
}

func (x *RuntimeInfo) Reset() {
	*x = RuntimeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuntimeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuntimeInfo) ProtoMessage() {}

func (x *RuntimeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuntimeInfo.ProtoReflect.Descriptor instead.
func (*RuntimeInfo) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{57}
}

func (x *RuntimeInfo) GetLuaRpcFunctions() []string {
	if x != nil {
		return x.LuaRpcFunctions
	}
	return nil
}

func (x *RuntimeInfo) GetGoRpcFunctions() []string {
	if x != nil {
		return x.GoRpcFunctions
	}
	return nil
}

func (x *RuntimeInfo) GetJsRpcFunctions() []string {
	if x != nil {
		return x.JsRpcFunctions
	}
	return nil
}

func (x *RuntimeInfo) GetGoModules() []*RuntimeInfo_ModuleInfo {
	if x != nil {
		return x.GoModules
	}
	return nil
}

func (x *RuntimeInfo) GetLuaModules() []*RuntimeInfo_ModuleInfo {
	if x != nil {
		return x.LuaModules
	}
	return nil
}

func (x *RuntimeInfo) GetJsModules() []*RuntimeInfo_ModuleInfo {
	if x != nil {
		return x.JsModules
	}
	return nil
}

// An individual update to a user's wallet.
type WalletLedger struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The identifier of this wallet change.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The user ID this wallet ledger item belongs to.
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// The changeset.
	Changeset string `protobuf:"bytes,3,opt,name=changeset,proto3" json:"changeset,omitempty"`
	// Any associated metadata.
	Metadata string `protobuf:"bytes,4,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// The UNIX time when the wallet ledger item was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The UNIX time when the wallet ledger item was updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *WalletLedger) Reset() {
	*x = WalletLedger{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WalletLedger) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WalletLedger) ProtoMessage() {}

func (x *WalletLedger) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WalletLedger.ProtoReflect.Descriptor instead.
func (*WalletLedger) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{58}
}

func (x *WalletLedger) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WalletLedger) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *WalletLedger) GetChangeset() string {
	if x != nil {
		return x.Changeset
	}
	return ""
}

func (x *WalletLedger) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *WalletLedger) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *WalletLedger) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// List of wallet ledger items for a particular user.
type WalletLedgerList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of wallet ledger items.
	Items []*WalletLedger `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	// The cursor to send when retrieving the next older page, if any.
	NextCursor string `protobuf:"bytes,2,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
	// The cursor to send when retrieving the previous page newer, if any.
	PrevCursor string `protobuf:"bytes,3,opt,name=prev_cursor,json=prevCursor,proto3" json:"prev_cursor,omitempty"`
}

func (x *WalletLedgerList) Reset() {
	*x = WalletLedgerList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WalletLedgerList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WalletLedgerList) ProtoMessage() {}

func (x *WalletLedgerList) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WalletLedgerList.ProtoReflect.Descriptor instead.
func (*WalletLedgerList) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{59}
}

func (x *WalletLedgerList) GetItems() []*WalletLedger {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *WalletLedgerList) GetNextCursor() string {
	if x != nil {
		return x.NextCursor
	}
	return ""
}

func (x *WalletLedgerList) GetPrevCursor() string {
	if x != nil {
		return x.PrevCursor
	}
	return ""
}

// Write a new storage object or update an existing one.
type WriteStorageObjectRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Collection.
	Collection string `protobuf:"bytes,1,opt,name=collection,proto3" json:"collection,omitempty"`
	// Key.
	Key string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	// Owner user ID.
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// Value.
	Value string `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
	// Version for OCC.
	Version string `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`
	// Read permission value.
	PermissionRead *wrapperspb.Int32Value `protobuf:"bytes,6,opt,name=permission_read,json=permissionRead,proto3" json:"permission_read,omitempty"`
	// Write permission value.
	PermissionWrite *wrapperspb.Int32Value `protobuf:"bytes,7,opt,name=permission_write,json=permissionWrite,proto3" json:"permission_write,omitempty"`
}

func (x *WriteStorageObjectRequest) Reset() {
	*x = WriteStorageObjectRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WriteStorageObjectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WriteStorageObjectRequest) ProtoMessage() {}

func (x *WriteStorageObjectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WriteStorageObjectRequest.ProtoReflect.Descriptor instead.
func (*WriteStorageObjectRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{60}
}

func (x *WriteStorageObjectRequest) GetCollection() string {
	if x != nil {
		return x.Collection
	}
	return ""
}

func (x *WriteStorageObjectRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *WriteStorageObjectRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *WriteStorageObjectRequest) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *WriteStorageObjectRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *WriteStorageObjectRequest) GetPermissionRead() *wrapperspb.Int32Value {
	if x != nil {
		return x.PermissionRead
	}
	return nil
}

func (x *WriteStorageObjectRequest) GetPermissionWrite() *wrapperspb.Int32Value {
	if x != nil {
		return x.PermissionWrite
	}
	return nil
}

type GetWalletLedgerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique identifier of the user account.
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// Max number of results per page
	Limit uint32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	// Cursor to retrieve a page of records from
	Cursor string `protobuf:"bytes,3,opt,name=cursor,proto3" json:"cursor,omitempty"`
}

func (x *GetWalletLedgerRequest) Reset() {
	*x = GetWalletLedgerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWalletLedgerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWalletLedgerRequest) ProtoMessage() {}

func (x *GetWalletLedgerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWalletLedgerRequest.ProtoReflect.Descriptor instead.
func (*GetWalletLedgerRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{61}
}

func (x *GetWalletLedgerRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetWalletLedgerRequest) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *GetWalletLedgerRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

type GetNotificationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Notification id.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetNotificationRequest) Reset() {
	*x = GetNotificationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationRequest) ProtoMessage() {}

func (x *GetNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationRequest.ProtoReflect.Descriptor instead.
func (*GetNotificationRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{62}
}

func (x *GetNotificationRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetPurchaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Purchase original transaction id.
	TransactionId string `protobuf:"bytes,1,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
}

func (x *GetPurchaseRequest) Reset() {
	*x = GetPurchaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPurchaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPurchaseRequest) ProtoMessage() {}

func (x *GetPurchaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPurchaseRequest.ProtoReflect.Descriptor instead.
func (*GetPurchaseRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{63}
}

func (x *GetPurchaseRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

type GetSubscriptionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription original transaction id.
	OriginalTransactionId string `protobuf:"bytes,1,opt,name=original_transaction_id,json=originalTransactionId,proto3" json:"original_transaction_id,omitempty"`
}

func (x *GetSubscriptionRequest) Reset() {
	*x = GetSubscriptionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubscriptionRequest) ProtoMessage() {}

func (x *GetSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*GetSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{64}
}

func (x *GetSubscriptionRequest) GetOriginalTransactionId() string {
	if x != nil {
		return x.OriginalTransactionId
	}
	return ""
}

// An object within the storage engine.
type StorageListObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The collection which stores the object.
	Collection string `protobuf:"bytes,1,opt,name=collection,proto3" json:"collection,omitempty"`
	// The key of the object within the collection.
	Key string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	// The user owner of the object.
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// The version hash of the object.
	Version string `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	// The read access permissions for the object.
	PermissionRead int32 `protobuf:"varint,5,opt,name=permission_read,json=permissionRead,proto3" json:"permission_read,omitempty"`
	// The write access permissions for the object.
	PermissionWrite int32 `protobuf:"varint,6,opt,name=permission_write,json=permissionWrite,proto3" json:"permission_write,omitempty"`
	// The UNIX time (for gRPC clients) or ISO string (for REST clients) when the object was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The UNIX time (for gRPC clients) or ISO string (for REST clients) when the object was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *StorageListObject) Reset() {
	*x = StorageListObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StorageListObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StorageListObject) ProtoMessage() {}

func (x *StorageListObject) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StorageListObject.ProtoReflect.Descriptor instead.
func (*StorageListObject) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{65}
}

func (x *StorageListObject) GetCollection() string {
	if x != nil {
		return x.Collection
	}
	return ""
}

func (x *StorageListObject) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *StorageListObject) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *StorageListObject) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *StorageListObject) GetPermissionRead() int32 {
	if x != nil {
		return x.PermissionRead
	}
	return 0
}

func (x *StorageListObject) GetPermissionWrite() int32 {
	if x != nil {
		return x.PermissionWrite
	}
	return 0
}

func (x *StorageListObject) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *StorageListObject) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

type MatchList_Match struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The API match
	ApiMatch *api.Match `protobuf:"bytes,1,opt,name=api_match,json=apiMatch,proto3" json:"api_match,omitempty"`
	// The node name
	Node string `protobuf:"bytes,2,opt,name=node,proto3" json:"node,omitempty"`
}

func (x *MatchList_Match) Reset() {
	*x = MatchList_Match{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchList_Match) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchList_Match) ProtoMessage() {}

func (x *MatchList_Match) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchList_Match.ProtoReflect.Descriptor instead.
func (*MatchList_Match) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{9, 0}
}

func (x *MatchList_Match) GetApiMatch() *api.Match {
	if x != nil {
		return x.ApiMatch
	}
	return nil
}

func (x *MatchList_Match) GetNode() string {
	if x != nil {
		return x.Node
	}
	return ""
}

// A warning for a configuration field.
type Config_Warning struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The config field this warning is for in a JSON pointer format.
	Field string `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	// Warning message text.
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *Config_Warning) Reset() {
	*x = Config_Warning{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Config_Warning) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config_Warning) ProtoMessage() {}

func (x *Config_Warning) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config_Warning.ProtoReflect.Descriptor instead.
func (*Config_Warning) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{19, 0}
}

func (x *Config_Warning) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *Config_Warning) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// A console user
type UserList_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Username of the user
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// Email of the user
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// Role of the user;
	Role UserRole `protobuf:"varint,3,opt,name=role,proto3,enum=nakama.console.UserRole" json:"role,omitempty"`
	// Whether the user is required to setup MFA.
	MfaRequired bool `protobuf:"varint,4,opt,name=mfa_required,json=mfaRequired,proto3" json:"mfa_required,omitempty"`
	// Whether the user has MFA enabled.
	MfaEnabled bool `protobuf:"varint,5,opt,name=mfa_enabled,json=mfaEnabled,proto3" json:"mfa_enabled,omitempty"`
}

func (x *UserList_User) Reset() {
	*x = UserList_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserList_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserList_User) ProtoMessage() {}

func (x *UserList_User) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserList_User.ProtoReflect.Descriptor instead.
func (*UserList_User) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{55, 0}
}

func (x *UserList_User) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UserList_User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserList_User) GetRole() UserRole {
	if x != nil {
		return x.Role
	}
	return UserRole_USER_ROLE_UNKNOWN
}

func (x *UserList_User) GetMfaRequired() bool {
	if x != nil {
		return x.MfaRequired
	}
	return false
}

func (x *UserList_User) GetMfaEnabled() bool {
	if x != nil {
		return x.MfaEnabled
	}
	return false
}

// The status of a Nakama node.
type StatusList_Status struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Node name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Health score.
	Health StatusHealth `protobuf:"varint,2,opt,name=health,proto3,enum=nakama.console.StatusHealth" json:"health,omitempty"`
	// Currently connected sessions.
	SessionCount int32 `protobuf:"varint,3,opt,name=session_count,json=sessionCount,proto3" json:"session_count,omitempty"`
	// Currently registered live presences.
	PresenceCount int32 `protobuf:"varint,4,opt,name=presence_count,json=presenceCount,proto3" json:"presence_count,omitempty"`
	// Current number of active authoritative matches.
	MatchCount int32 `protobuf:"varint,5,opt,name=match_count,json=matchCount,proto3" json:"match_count,omitempty"`
	// Current number of running goroutines.
	GoroutineCount int32 `protobuf:"varint,6,opt,name=goroutine_count,json=goroutineCount,proto3" json:"goroutine_count,omitempty"`
	// Average response latency in milliseconds.
	AvgLatencyMs float64 `protobuf:"fixed64,7,opt,name=avg_latency_ms,json=avgLatencyMs,proto3" json:"avg_latency_ms,omitempty"`
	// Average number of requests per second.
	AvgRateSec float64 `protobuf:"fixed64,8,opt,name=avg_rate_sec,json=avgRateSec,proto3" json:"avg_rate_sec,omitempty"`
	// Average input bandwidth usage.
	AvgInputKbs float64 `protobuf:"fixed64,9,opt,name=avg_input_kbs,json=avgInputKbs,proto3" json:"avg_input_kbs,omitempty"`
	// Average output bandwidth usage.
	AvgOutputKbs float64 `protobuf:"fixed64,10,opt,name=avg_output_kbs,json=avgOutputKbs,proto3" json:"avg_output_kbs,omitempty"`
}

func (x *StatusList_Status) Reset() {
	*x = StatusList_Status{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusList_Status) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusList_Status) ProtoMessage() {}

func (x *StatusList_Status) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusList_Status.ProtoReflect.Descriptor instead.
func (*StatusList_Status) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{56, 0}
}

func (x *StatusList_Status) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StatusList_Status) GetHealth() StatusHealth {
	if x != nil {
		return x.Health
	}
	return StatusHealth_STATUS_HEALTH_OK
}

func (x *StatusList_Status) GetSessionCount() int32 {
	if x != nil {
		return x.SessionCount
	}
	return 0
}

func (x *StatusList_Status) GetPresenceCount() int32 {
	if x != nil {
		return x.PresenceCount
	}
	return 0
}

func (x *StatusList_Status) GetMatchCount() int32 {
	if x != nil {
		return x.MatchCount
	}
	return 0
}

func (x *StatusList_Status) GetGoroutineCount() int32 {
	if x != nil {
		return x.GoroutineCount
	}
	return 0
}

func (x *StatusList_Status) GetAvgLatencyMs() float64 {
	if x != nil {
		return x.AvgLatencyMs
	}
	return 0
}

func (x *StatusList_Status) GetAvgRateSec() float64 {
	if x != nil {
		return x.AvgRateSec
	}
	return 0
}

func (x *StatusList_Status) GetAvgInputKbs() float64 {
	if x != nil {
		return x.AvgInputKbs
	}
	return 0
}

func (x *StatusList_Status) GetAvgOutputKbs() float64 {
	if x != nil {
		return x.AvgOutputKbs
	}
	return 0
}

// Module information
type RuntimeInfo_ModuleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Module path
	Path string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	// Module last modified date
	ModTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=mod_time,json=modTime,proto3" json:"mod_time,omitempty"`
}

func (x *RuntimeInfo_ModuleInfo) Reset() {
	*x = RuntimeInfo_ModuleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_console_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuntimeInfo_ModuleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuntimeInfo_ModuleInfo) ProtoMessage() {}

func (x *RuntimeInfo_ModuleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_console_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuntimeInfo_ModuleInfo.ProtoReflect.Descriptor instead.
func (*RuntimeInfo_ModuleInfo) Descriptor() ([]byte, []int) {
	return file_console_proto_rawDescGZIP(), []int{57, 0}
}

func (x *RuntimeInfo_ModuleInfo) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *RuntimeInfo_ModuleInfo) GetModTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ModTime
	}
	return nil
}

var File_console_proto protoreflect.FileDescriptor

var file_console_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x1a,
	0x0d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x68, 0x65, 0x72, 0x6f, 0x69,
	0x63, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2d, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x72, 0x74, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e,
	0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x54, 0x0a, 0x15, 0x41, 0x70, 0x69, 0x45, 0x6e, 0x64, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x12, 0x16, 0x0a,
	0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x6f, 0x64, 0x79, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x6f,
	0x64, 0x79, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22, 0x77, 0x0a, 0x07, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x07, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0x6b, 0x0a, 0x14, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x43, 0x0a, 0x0f, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0xd9, 0x03, 0x0a, 0x0d, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x2d, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x33, 0x0a, 0x07, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x6f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x2c, 0x0a, 0x07, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x52, 0x07, 0x66, 0x72, 0x69,
	0x65, 0x6e, 0x64, 0x73, 0x12, 0x29, 0x0a, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12,
	0x36, 0x0a, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x08, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x4e, 0x0a, 0x13, 0x6c, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x12, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x3e, 0x0a, 0x0d, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x43, 0x0a, 0x0e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x5f, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65,
	0x2e, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x4c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x52, 0x0d, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x4c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x73, 0x22, 0x1b, 0x0a, 0x09,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x77, 0x0a, 0x0b, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x43, 0x75, 0x72, 0x73,
	0x6f, 0x72, 0x22, 0x19, 0x0a, 0x07, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x78, 0x0a,
	0x09, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x06, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x63,
	0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x65, 0x78,
	0x74, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x22, 0x75, 0x0a, 0x0b, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x27, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12,
	0x3d, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x2e, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x55, 0x73, 0x65, 0x72, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x22, 0x93,
	0x01, 0x0a, 0x09, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x07,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x07,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x1a, 0x4b, 0x0a, 0x05, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x12, 0x2e, 0x0a, 0x09, 0x61, 0x70, 0x69, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x08, 0x61, 0x70, 0x69, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x6f, 0x64, 0x65, 0x22, 0xe8, 0x01, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x2c, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x04, 0x72,
	0x6f, 0x6c, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x66, 0x61, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6d, 0x66, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x37, 0x0a, 0x17, 0x6e, 0x65, 0x77, 0x73, 0x6c, 0x65,
	0x74, 0x74, 0x65, 0x72, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x6e, 0x65, 0x77, 0x73, 0x6c, 0x65, 0x74,
	0x74, 0x65, 0x72, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x66, 0x0a, 0x14, 0x41, 0x64, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6a, 0x6f, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6a, 0x6f, 0x69, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xa2, 0x01, 0x0a, 0x0f, 0x41, 0x70, 0x69, 0x45,
	0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x09, 0x65,
	0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e,
	0x41, 0x70, 0x69, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x6f, 0x72, 0x52, 0x09, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x12, 0x4a, 0x0a, 0x0d, 0x72, 0x70, 0x63, 0x5f, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x6e, 0x64, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x52, 0x0c,
	0x72, 0x70, 0x63, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22, 0x5f, 0x0a, 0x13,
	0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x66, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x66, 0x61, 0x22, 0x43, 0x0a,
	0x1b, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x4d, 0x46, 0x41,
	0x53, 0x65, 0x74, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x66, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x66, 0x61, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x45, 0x0a, 0x1c, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61,
	0x74, 0x65, 0x4d, 0x46, 0x41, 0x53, 0x65, 0x74, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x22, 0x31, 0x0a, 0x19, 0x41, 0x75, 0x74,
	0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xf9, 0x01, 0x0a,
	0x16, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x70, 0x69, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62,
	0x6f, 0x64, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x0c,
	0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x61, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x6f, 0x6c, 0x65, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x70, 0x69, 0x45, 0x6e, 0x64, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x56, 0x61, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x72, 0x73, 0x1a, 0x3e, 0x0a, 0x10, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x56, 0x61, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x52, 0x0a, 0x17, 0x43, 0x61, 0x6c, 0x6c,
	0x41, 0x70, 0x69, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xbe, 0x01, 0x0a,
	0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x3a, 0x0a, 0x08, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f,
	0x6c, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e,
	0x67, 0x52, 0x08, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x1a, 0x39, 0x0a, 0x07, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x14, 0x0a,
	0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x5f, 0x0a,
	0x0e, 0x43, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x37, 0x0a, 0x08, 0x6d, 0x66, 0x61, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x6d, 0x66, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x64,
	0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32,
	0x0a, 0x06, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x06, 0x62, 0x65, 0x66, 0x6f,
	0x72, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x03, 0x69, 0x64, 0x73, 0x22, 0x42, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x72,
	0x69, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66,
	0x72, 0x69, 0x65, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x49, 0x64, 0x22, 0x24, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x43,
	0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x64, 0x22, 0x48, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x22, 0x31, 0x0a,
	0x13, 0x52, 0x65, 0x73, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x66, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0x4f, 0x0a, 0x15, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x4d,
	0x66, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x22, 0x4b, 0x0a, 0x1e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x22, 0x2b,
	0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x81, 0x01, 0x0a, 0x1a,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x17, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0x48, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x4c,
	0x65, 0x64, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x49, 0x64, 0x22, 0xf8, 0x05, 0x0a, 0x0b, 0x4c, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6d,
	0x61, 0x78, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x4e, 0x75, 0x6d, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x65,
	0x6e, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x65, 0x6e, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65,
	0x73, 0x65, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3b, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6a,
	0x6f, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x6a, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x12, 0x24, 0x0a, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x75, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x74, 0x6f, 0x75, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x65, 0x76, 0x5f, 0x72,
	0x65, 0x73, 0x65, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x72, 0x65, 0x76,
	0x52, 0x65, 0x73, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x72, 0x65,
	0x73, 0x65, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6e, 0x65, 0x78, 0x74, 0x52,
	0x65, 0x73, 0x65, 0x74, 0x22, 0x30, 0x0a, 0x16, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x22, 0x80, 0x01, 0x0a, 0x0f, 0x4c, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x0c, 0x6c, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c,
	0x65, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x0c, 0x6c,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x22, 0x24, 0x0a, 0x12, 0x4c, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x65, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1e,
	0x0a, 0x0a, 0x74, 0x6f, 0x6d, 0x62, 0x73, 0x74, 0x6f, 0x6e, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x74, 0x6f, 0x6d, 0x62, 0x73, 0x74, 0x6f, 0x6e, 0x65, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x22, 0xa0, 0x02, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x4f, 0x6e, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x74, 0x77, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x54, 0x77, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x63,
	0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x72,
	0x73, 0x6f, 0x72, 0x22, 0x34, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x52, 0x4f, 0x4f, 0x4d,
	0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x10, 0x03, 0x12, 0x0a, 0x0a,
	0x06, 0x44, 0x49, 0x52, 0x45, 0x43, 0x54, 0x10, 0x04, 0x22, 0x43, 0x0a, 0x11, 0x4c, 0x69, 0x73,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x22, 0xae,
	0x03, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x40, 0x0a, 0x0d, 0x61, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x69, 0x74, 0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x61, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x74, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x36,
	0x0a, 0x08, 0x6d, 0x69, 0x6e, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x6d,
	0x69, 0x6e, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x05, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x30, 0x0a,
	0x04, 0x6e, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x22,
	0x61, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75,
	0x72, 0x73, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73,
	0x6f, 0x72, 0x22, 0x5d, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61,
	0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72,
	0x73, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f,
	0x72, 0x22, 0x61, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x75,
	0x72, 0x73, 0x6f, 0x72, 0x22, 0x77, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x22, 0x73, 0x0a,
	0x0a, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x09, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x63, 0x6b,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x69, 0x63, 0x6b, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x22, 0x23, 0x0a, 0x11, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xf9, 0x01, 0x0a, 0x0c, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3b, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65,
	0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x70, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x22, 0x98, 0x01, 0x0a, 0x10, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x42, 0x0a, 0x0d, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65,
	0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x6e, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x1f, 0x0a,
	0x0b, 0x70, 0x72, 0x65, 0x76, 0x5f, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x76, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x22, 0x35,
	0x0a, 0x1d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x8c, 0x01, 0x0a, 0x0b, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x07, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x73,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x43, 0x75,
	0x72, 0x73, 0x6f, 0x72, 0x22, 0x3a, 0x0a, 0x16, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20,
	0x0a, 0x0b, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0x42, 0x0a, 0x13, 0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x22, 0xb6, 0x06, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x38, 0x0a,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x3b, 0x0a, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x12,
	0x37, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x07, 0x6c, 0x61, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x12, 0x38, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x39, 0x0a, 0x09,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x38, 0x0a, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x52, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x34, 0x0a, 0x06, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x1a,
	0x3c, 0x0a, 0x0e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb0, 0x03,
	0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x5f, 0x74,
	0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x6c, 0x61, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x12,
	0x38, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x0a, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x2e, 0x0a, 0x04, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x04, 0x6f, 0x70, 0x65, 0x6e, 0x12, 0x38, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x26, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xec, 0x01, 0x0a, 0x08, 0x55, 0x73, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x1a, 0xaa, 0x01, 0x0a, 0x04, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x2c, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x04, 0x72,
	0x6f, 0x6c, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x66, 0x61, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6d, 0x66, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x66, 0x61, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6d, 0x66, 0x61,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0xfc, 0x03, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12,
	0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a, 0xfa, 0x02, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d,
	0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x52, 0x06, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x12, 0x23,
	0x0a, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x70, 0x72, 0x65,
	0x73, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x67,
	0x6f, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x67, 0x6f, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x76, 0x67, 0x5f, 0x6c, 0x61, 0x74, 0x65,
	0x6e, 0x63, 0x79, 0x5f, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x61, 0x76,
	0x67, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x4d, 0x73, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x76,
	0x67, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0a, 0x61, 0x76, 0x67, 0x52, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x12, 0x22, 0x0a, 0x0d,
	0x61, 0x76, 0x67, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x6b, 0x62, 0x73, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0b, 0x61, 0x76, 0x67, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4b, 0x62, 0x73,
	0x12, 0x24, 0x0a, 0x0e, 0x61, 0x76, 0x67, 0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x6b,
	0x62, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x61, 0x76, 0x67, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x4b, 0x62, 0x73, 0x22, 0xbd, 0x03, 0x0a, 0x0b, 0x52, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x75, 0x61, 0x5f, 0x72, 0x70,
	0x63, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0f, 0x6c, 0x75, 0x61, 0x52, 0x70, 0x63, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x67, 0x6f, 0x5f, 0x72, 0x70, 0x63, 0x5f, 0x66, 0x75, 0x6e,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x67, 0x6f,
	0x52, 0x70, 0x63, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x28, 0x0a, 0x10,
	0x6a, 0x73, 0x5f, 0x72, 0x70, 0x63, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x6a, 0x73, 0x52, 0x70, 0x63, 0x46, 0x75, 0x6e,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x45, 0x0a, 0x0a, 0x67, 0x6f, 0x5f, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x09, 0x67, 0x6f, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x47, 0x0a,
	0x0b, 0x6c, 0x75, 0x61, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x6f, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x6c, 0x75, 0x61, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x45, 0x0a, 0x0a, 0x6a, 0x73, 0x5f, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x09, 0x6a, 0x73, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x1a, 0x57, 0x0a,
	0x0a, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12,
	0x35, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x6d,
	0x6f, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xeb, 0x01, 0x0a, 0x0c, 0x57, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x4c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x65, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0x88, 0x01, 0x0a, 0x10, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x4c,
	0x65, 0x64, 0x67, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d,
	0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x4c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x72, 0x65, 0x76, 0x5f, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x76, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x22,
	0xa4, 0x02, 0x0a, 0x19, 0x57, 0x72, 0x69, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a,
	0x0a, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x12, 0x46,
	0x0a, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x57, 0x72, 0x69, 0x74, 0x65, 0x22, 0x65, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x57, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x4c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x22, 0x28, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x3b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a,
	0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x22, 0x50, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36,
	0x0a, 0x17, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x15, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xc6, 0x02, 0x0a, 0x11, 0x53, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x72, 0x65, 0x61, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x57, 0x72, 0x69, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x2a,
	0x81, 0x01, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x15, 0x0a, 0x11,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x52, 0x4f, 0x4c, 0x45,
	0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x44, 0x45, 0x56, 0x45, 0x4c, 0x4f, 0x50, 0x45, 0x52, 0x10,
	0x02, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x4d,
	0x41, 0x49, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x45, 0x52, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x55,
	0x53, 0x45, 0x52, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x4f, 0x4e, 0x4c,
	0x59, 0x10, 0x04, 0x2a, 0x7c, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x48, 0x45,
	0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x48, 0x45, 0x41,
	0x4c, 0x54, 0x48, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x02,
	0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x48, 0x45, 0x41, 0x4c, 0x54,
	0x48, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4e, 0x47, 0x10,
	0x03, 0x32, 0xf6, 0x41, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x12, 0x83, 0x01,
	0x0a, 0x0c, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x12, 0x23,
	0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e,
	0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0x2e, 0x92, 0x41, 0x08, 0x62, 0x06, 0x0a, 0x04, 0x0a, 0x00, 0x12, 0x00,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x12, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69,
	0x63, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x12, 0x29, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x68,
	0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2a, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x24, 0x3a, 0x01, 0x2a, 0x22, 0x1f, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61,
	0x74, 0x65, 0x2f, 0x6c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x12, 0x9a, 0x01, 0x0a, 0x14, 0x41, 0x75,
	0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x4d, 0x46, 0x41, 0x53, 0x65, 0x74,
	0x75, 0x70, 0x12, 0x2b, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65,
	0x4d, 0x46, 0x41, 0x53, 0x65, 0x74, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2c, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65,
	0x2e, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x4d, 0x46, 0x41,
	0x53, 0x65, 0x74, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x27, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22, 0x1c, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61,
	0x74, 0x65, 0x2f, 0x6d, 0x66, 0x61, 0x12, 0x5e, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x1e, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f,
	0x6c, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x15, 0x3a, 0x01, 0x2a, 0x22, 0x10, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c,
	0x65, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x12, 0x7a, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x24, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a,
	0x22, 0x20, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x2f, 0x7b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x61,
	0x64, 0x64, 0x12, 0x65, 0x0a, 0x0a, 0x42, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c,
	0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x22, 0x1c, 0x2f, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x62, 0x61, 0x6e, 0x12, 0x91, 0x01, 0x0a, 0x0f, 0x43, 0x61,
	0x6c, 0x6c, 0x41, 0x70, 0x69, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x26, 0x2e,
	0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x43,
	0x61, 0x6c, 0x6c, 0x41, 0x70, 0x69, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x70, 0x69, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2d,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x3a, 0x01, 0x2a, 0x22, 0x22, 0x2f, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x6e, 0x64, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x2f, 0x7b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x7d, 0x12, 0x95, 0x01,
	0x0a, 0x0f, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x70, 0x63, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x12, 0x26, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f,
	0x6c, 0x65, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x70, 0x69, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x41,
	0x70, 0x69, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x3a, 0x01, 0x2a, 0x22, 0x26, 0x2f,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65,
	0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x7b, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x7d, 0x12, 0x58, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41,
	0x6c, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x2a, 0x0f,
	0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x6c, 0x6c, 0x12,
	0x6f, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x24, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c,
	0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x20,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x2a, 0x18, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73,
	0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d,
	0x12, 0x91, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2c, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d,
	0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x2a,
	0x13, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x80, 0x01, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46,
	0x72, 0x69, 0x65, 0x6e, 0x64, 0x12, 0x23, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x72, 0x69,
	0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x33, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x2a, 0x2b, 0x2f, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x2f, 0x7b, 0x66, 0x72, 0x69,
	0x65, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x69, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x22, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x2a, 0x16, 0x2f, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x7b, 0x69,
	0x64, 0x7d, 0x12, 0x84, 0x01, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x55, 0x73, 0x65, 0x72, 0x12, 0x26, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x2a, 0x29,
	0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x7b,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x5c, 0x0a, 0x0d, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x15, 0x2a, 0x13, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f,
	0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x12, 0xd1, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12,
	0x2a, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x76, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x70, 0x5a, 0x3c, 0x2a, 0x3a, 0x2f,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x2f, 0x7b, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f,
	0x7b, 0x6b, 0x65, 0x79, 0x7d, 0x2f, 0x7b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x7d, 0x2f,
	0x7b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x7d, 0x2a, 0x30, 0x2f, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2f, 0x7b,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x7b, 0x6b, 0x65, 0x79,
	0x7d, 0x2f, 0x7b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x5d, 0x0a, 0x0e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1b, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x15, 0x2a, 0x13, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f,
	0x6c, 0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x75, 0x0a, 0x11, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12,
	0x22, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65,
	0x2e, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x24, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1e, 0x2a, 0x1c, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65,
	0x2f, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2f, 0x7b, 0x69, 0x64,
	0x7d, 0x12, 0x98, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x2e, 0x2e,
	0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x35, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2f, 0x2a, 0x2d, 0x2f,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x6c, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x2f, 0x7b, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x7e, 0x0a, 0x12,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x29, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x6f, 0x6c, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x2a, 0x1d, 0x2f,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x58, 0x0a, 0x0a,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x18, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x18, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x12, 0x2a, 0x10, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c,
	0x65, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x12, 0x8c, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x4c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x12, 0x29, 0x2e,
	0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x4c, 0x65, 0x64, 0x67, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x33, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x2a, 0x2b, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x7b, 0x69,
	0x64, 0x7d, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x7b, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x92, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2b, 0x2e, 0x6e, 0x61,
	0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x38, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x32, 0x22, 0x30, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x7b, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x7b,
	0x69, 0x64, 0x7d, 0x2f, 0x64, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x12, 0x72, 0x0a, 0x0d, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x19, 0x2e, 0x6e, 0x61,
	0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x1a, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x12, 0x1f, 0x2f,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x6a,
	0x0a, 0x0b, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x17, 0x2e,
	0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x1a, 0x1b, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x12, 0x1d, 0x2f, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x7b,
	0x69, 0x64, 0x7d, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x62, 0x0a, 0x0a, 0x47, 0x65,
	0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d,
	0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x1a, 0x17, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x20, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c,
	0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x57,
	0x0a, 0x09, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x1a, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x14, 0x12, 0x12, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65,
	0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x68, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x46, 0x72,
	0x69, 0x65, 0x6e, 0x64, 0x73, 0x12, 0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x1a, 0x16, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x46, 0x72,
	0x69, 0x65, 0x6e, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21,
	0x12, 0x1f, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x66, 0x72, 0x69, 0x65, 0x6e,
	0x64, 0x12, 0x56, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x17, 0x2e,
	0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x1a, 0x11, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x18, 0x12, 0x16, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x67, 0x0a, 0x0a, 0x47, 0x65, 0x74,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x17, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64,
	0x1a, 0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x25, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1f, 0x12, 0x1d, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65,
	0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x69, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12,
	0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x1a, 0x19, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x12, 0x1e, 0x2f,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x77, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12,
	0x22, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65,
	0x2e, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x12, 0x1c, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x74, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x21, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x12, 0x1c,
	0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x5e, 0x0a, 0x0a,
	0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x6f, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e,
	0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x5b, 0x0a, 0x09,
	0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x1a, 0x1a, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f,
	0x6c, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x1a, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x14, 0x12, 0x12, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f,
	0x6c, 0x65, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x82, 0x01, 0x0a, 0x0a, 0x47, 0x65,
	0x74, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x1a, 0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x22, 0x38, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x32, 0x12, 0x30, 0x2f, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x2f, 0x7b, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x7b,
	0x6b, 0x65, 0x79, 0x7d, 0x2f, 0x7b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x8c,
	0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x4c, 0x65, 0x64, 0x67,
	0x65, 0x72, 0x12, 0x26, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x6f, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x4c, 0x65, 0x64,
	0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x57, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x4c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x2f, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x29, 0x12, 0x27, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c,
	0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x12, 0x7e, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x26, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d,
	0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x12, 0x1d,
	0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x83, 0x01,
	0x0a, 0x0b, 0x47, 0x65, 0x74, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x12, 0x22, 0x2e,
	0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x64, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65,
	0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x12, 0x29, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x69, 0x61, 0x70, 0x2f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61,
	0x73, 0x65, 0x2f, 0x7b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x7d, 0x12, 0x9c, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x21, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x64, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x3e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x38, 0x12, 0x36, 0x2f, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x69, 0x61, 0x70, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x61, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x7d, 0x12, 0x6e, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x69, 0x45, 0x6e, 0x64,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1f,
	0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e,
	0x41, 0x70, 0x69, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e,
	0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x12, 0xa0, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x29, 0x2e,
	0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x38, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x32, 0x12, 0x30, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65,
	0x2f, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2f, 0x7b, 0x6c, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x7c, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x73, 0x12, 0x26, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1f, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f,
	0x6c, 0x65, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x12, 0x17, 0x2f, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x12, 0x6b, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x12, 0x22, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x6f, 0x6c, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x12, 0x81, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x1a, 0x26, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x27, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x21, 0x12, 0x1f, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65,
	0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x6d, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x12, 0x23, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13,
	0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x7e, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2a, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13,
	0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x12, 0x65, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x12, 0x21, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f,
	0x6c, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x12, 0x11, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e,
	0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x81, 0x01, 0x0a, 0x11, 0x4c,
	0x69, 0x73, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x28, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c,
	0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x20, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c,
	0x65, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x67,
	0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x12, 0x22, 0x2e,
	0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f,
	0x6c, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x19, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x13, 0x12, 0x11, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c,
	0x65, 0x2f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x6d, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x73, 0x12, 0x24, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d,
	0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18,
	0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x75, 0x72, 0x63,
	0x68, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16,
	0x12, 0x14, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x70, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x12, 0x7d, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x28, 0x2e, 0x6e, 0x61,
	0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x57, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x18, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x12, 0x94,
	0x01, 0x0a, 0x12, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2b, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x39, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x33, 0x22, 0x31, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x7b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x7d,
	0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x70, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x66, 0x61, 0x12, 0x25, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d,
	0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x66, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x3a,
	0x01, 0x2a, 0x22, 0x27, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x2f, 0x7b, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x6d, 0x66, 0x61, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x12, 0x7a, 0x0a, 0x0c, 0x52,
	0x65, 0x73, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x66, 0x61, 0x12, 0x23, 0x2e, 0x6e, 0x61,
	0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x66, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27,
	0x22, 0x25, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x2f, 0x7b, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6d, 0x66,
	0x61, 0x2f, 0x72, 0x65, 0x73, 0x65, 0x74, 0x12, 0x69, 0x0a, 0x0c, 0x55, 0x6e, 0x62, 0x61, 0x6e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x20, 0x22, 0x1e, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x75, 0x6e, 0x62,
	0x61, 0x6e, 0x12, 0x71, 0x0a, 0x0c, 0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x12, 0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x28, 0x22, 0x26, 0x2f,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x75, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x12, 0x7e, 0x0a, 0x0c, 0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x23, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x3a, 0x01, 0x2a, 0x22, 0x26, 0x2f,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x75, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6f, 0x0a, 0x0b, 0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x22,
	0x25, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x75, 0x6e, 0x6c, 0x69, 0x6e, 0x6b,
	0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x6f, 0x0a, 0x0b, 0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b,
	0x41, 0x70, 0x70, 0x6c, 0x65, 0x12, 0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27,
	0x22, 0x25, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x75, 0x6e, 0x6c, 0x69, 0x6e,
	0x6b, 0x2f, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x12, 0x75, 0x0a, 0x0e, 0x55, 0x6e, 0x6c, 0x69, 0x6e,
	0x6b, 0x46, 0x61, 0x63, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x12, 0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x30, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2a, 0x22, 0x28, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c,
	0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x75,
	0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x66, 0x61, 0x63, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x12, 0x8b,
	0x01, 0x0a, 0x19, 0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x46, 0x61, 0x63, 0x65, 0x62, 0x6f, 0x6f,
	0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x2e, 0x6e,
	0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x3b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x35, 0x22, 0x33, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e,
	0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64,
	0x7d, 0x2f, 0x75, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x66, 0x61, 0x63, 0x65, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x12, 0x79, 0x0a, 0x10,
	0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x12, 0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c,
	0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x22, 0x2a, 0x2f, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x75, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x67, 0x61, 0x6d,
	0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x71, 0x0a, 0x0c, 0x55, 0x6e, 0x6c, 0x69, 0x6e,
	0x6b, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x12, 0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2e, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x28, 0x22, 0x26, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x75, 0x6e, 0x6c,
	0x69, 0x6e, 0x6b, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x12, 0x6f, 0x0a, 0x0b, 0x55, 0x6e,
	0x6c, 0x69, 0x6e, 0x6b, 0x53, 0x74, 0x65, 0x61, 0x6d, 0x12, 0x19, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2d, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x27, 0x22, 0x25, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c,
	0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x75,
	0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x73, 0x74, 0x65, 0x61, 0x6d, 0x12, 0x72, 0x0a, 0x0d, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x2e, 0x6e,
	0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f,
	0x6c, 0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12,
	0x6c, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x22,
	0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f,
	0x6c, 0x65, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x9a, 0x01,
	0x0a, 0x12, 0x57, 0x72, 0x69, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x12, 0x29, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2e, 0x57, 0x72, 0x69, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1c, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x41, 0x63, 0x6b, 0x22, 0x3b, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x35, 0x3a, 0x01, 0x2a, 0x1a, 0x30, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2f, 0x7b, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x7b, 0x6b, 0x65, 0x79, 0x7d,
	0x2f, 0x7b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x7d, 0x42, 0xed, 0x02, 0x92, 0x41, 0xc0,
	0x02, 0x12, 0x7d, 0x0a, 0x15, 0x4e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x20, 0x63, 0x6f, 0x6e, 0x73,
	0x6f, 0x6c, 0x65, 0x20, 0x41, 0x50, 0x49, 0x20, 0x76, 0x32, 0x22, 0x5f, 0x0a, 0x21, 0x54, 0x68,
	0x65, 0x20, 0x4e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x20, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x73,
	0x20, 0x26, 0x20, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x12,
	0x24, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x68, 0x65, 0x72, 0x6f, 0x69, 0x63, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x6e,
	0x61, 0x6b, 0x61, 0x6d, 0x61, 0x1a, 0x14, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x40, 0x68, 0x65, 0x72,
	0x6f, 0x69, 0x63, 0x6c, 0x61, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x32, 0x03, 0x32, 0x2e, 0x30,
	0x1a, 0x0e, 0x31, 0x32, 0x37, 0x2e, 0x30, 0x2e, 0x30, 0x2e, 0x31, 0x3a, 0x37, 0x33, 0x35, 0x31,
	0x2a, 0x01, 0x01, 0x32, 0x10, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x10, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x6a, 0x73, 0x6f, 0x6e, 0x5a, 0x33, 0x0a, 0x0f, 0x0a, 0x09, 0x42, 0x61, 0x73,
	0x69, 0x63, 0x41, 0x75, 0x74, 0x68, 0x12, 0x02, 0x08, 0x01, 0x0a, 0x20, 0x0a, 0x09, 0x42, 0x65,
	0x61, 0x72, 0x65, 0x72, 0x4a, 0x77, 0x74, 0x12, 0x13, 0x08, 0x02, 0x1a, 0x0d, 0x41, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x02, 0x62, 0x0f, 0x0a, 0x0d,
	0x0a, 0x09, 0x42, 0x65, 0x61, 0x72, 0x65, 0x72, 0x4a, 0x77, 0x74, 0x12, 0x00, 0x72, 0x42, 0x0a,
	0x23, 0x4e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x20, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x20, 0x63,
	0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x20, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x68, 0x65,
	0x72, 0x6f, 0x69, 0x63, 0x6c, 0x61, 0x62, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x6f, 0x63,
	0x73, 0x5a, 0x27, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x68, 0x65,
	0x72, 0x6f, 0x69, 0x63, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2f,
	0x76, 0x33, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_console_proto_rawDescOnce sync.Once
	file_console_proto_rawDescData = file_console_proto_rawDesc
)

func file_console_proto_rawDescGZIP() []byte {
	file_console_proto_rawDescOnce.Do(func() {
		file_console_proto_rawDescData = protoimpl.X.CompressGZIP(file_console_proto_rawDescData)
	})
	return file_console_proto_rawDescData
}

var file_console_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_console_proto_msgTypes = make([]protoimpl.MessageInfo, 73)
var file_console_proto_goTypes = []interface{}{
	(UserRole)(0),                             // 0: nakama.console.UserRole
	(StatusHealth)(0),                         // 1: nakama.console.StatusHealth
	(ListChannelMessagesRequest_Type)(0),      // 2: nakama.console.ListChannelMessagesRequest.Type
	(*ApiEndpointDescriptor)(nil),             // 3: nakama.console.ApiEndpointDescriptor
	(*Account)(nil),                           // 4: nakama.console.Account
	(*AccountDeleteRequest)(nil),              // 5: nakama.console.AccountDeleteRequest
	(*AccountExport)(nil),                     // 6: nakama.console.AccountExport
	(*AccountId)(nil),                         // 7: nakama.console.AccountId
	(*AccountList)(nil),                       // 8: nakama.console.AccountList
	(*GroupId)(nil),                           // 9: nakama.console.GroupId
	(*GroupList)(nil),                         // 10: nakama.console.GroupList
	(*GroupExport)(nil),                       // 11: nakama.console.GroupExport
	(*MatchList)(nil),                         // 12: nakama.console.MatchList
	(*AddUserRequest)(nil),                    // 13: nakama.console.AddUserRequest
	(*AddGroupUsersRequest)(nil),              // 14: nakama.console.AddGroupUsersRequest
	(*ApiEndpointList)(nil),                   // 15: nakama.console.ApiEndpointList
	(*AuthenticateRequest)(nil),               // 16: nakama.console.AuthenticateRequest
	(*AuthenticateMFASetupRequest)(nil),       // 17: nakama.console.AuthenticateMFASetupRequest
	(*AuthenticateMFASetupResponse)(nil),      // 18: nakama.console.AuthenticateMFASetupResponse
	(*AuthenticateLogoutRequest)(nil),         // 19: nakama.console.AuthenticateLogoutRequest
	(*CallApiEndpointRequest)(nil),            // 20: nakama.console.CallApiEndpointRequest
	(*CallApiEndpointResponse)(nil),           // 21: nakama.console.CallApiEndpointResponse
	(*Config)(nil),                            // 22: nakama.console.Config
	(*ConsoleSession)(nil),                    // 23: nakama.console.ConsoleSession
	(*DeleteChannelMessagesRequest)(nil),      // 24: nakama.console.DeleteChannelMessagesRequest
	(*DeleteFriendRequest)(nil),               // 25: nakama.console.DeleteFriendRequest
	(*DeleteGroupRequest)(nil),                // 26: nakama.console.DeleteGroupRequest
	(*DeleteGroupUserRequest)(nil),            // 27: nakama.console.DeleteGroupUserRequest
	(*UpdateGroupUserStateRequest)(nil),       // 28: nakama.console.UpdateGroupUserStateRequest
	(*ResetUserMfaRequest)(nil),               // 29: nakama.console.ResetUserMfaRequest
	(*RequireUserMfaRequest)(nil),             // 30: nakama.console.RequireUserMfaRequest
	(*DeleteLeaderboardRecordRequest)(nil),    // 31: nakama.console.DeleteLeaderboardRecordRequest
	(*DeleteNotificationRequest)(nil),         // 32: nakama.console.DeleteNotificationRequest
	(*DeleteStorageObjectRequest)(nil),        // 33: nakama.console.DeleteStorageObjectRequest
	(*DeleteWalletLedgerRequest)(nil),         // 34: nakama.console.DeleteWalletLedgerRequest
	(*Leaderboard)(nil),                       // 35: nakama.console.Leaderboard
	(*LeaderboardListRequest)(nil),            // 36: nakama.console.LeaderboardListRequest
	(*LeaderboardList)(nil),                   // 37: nakama.console.LeaderboardList
	(*LeaderboardRequest)(nil),                // 38: nakama.console.LeaderboardRequest
	(*ListAccountsRequest)(nil),               // 39: nakama.console.ListAccountsRequest
	(*ListChannelMessagesRequest)(nil),        // 40: nakama.console.ListChannelMessagesRequest
	(*ListGroupsRequest)(nil),                 // 41: nakama.console.ListGroupsRequest
	(*ListMatchesRequest)(nil),                // 42: nakama.console.ListMatchesRequest
	(*ListNotificationsRequest)(nil),          // 43: nakama.console.ListNotificationsRequest
	(*ListPurchasesRequest)(nil),              // 44: nakama.console.ListPurchasesRequest
	(*ListSubscriptionsRequest)(nil),          // 45: nakama.console.ListSubscriptionsRequest
	(*ListStorageRequest)(nil),                // 46: nakama.console.ListStorageRequest
	(*MatchState)(nil),                        // 47: nakama.console.MatchState
	(*MatchStateRequest)(nil),                 // 48: nakama.console.MatchStateRequest
	(*Notification)(nil),                      // 49: nakama.console.Notification
	(*NotificationList)(nil),                  // 50: nakama.console.NotificationList
	(*DeleteChannelMessagesResponse)(nil),     // 51: nakama.console.DeleteChannelMessagesResponse
	(*StorageList)(nil),                       // 52: nakama.console.StorageList
	(*StorageCollectionsList)(nil),            // 53: nakama.console.StorageCollectionsList
	(*UnlinkDeviceRequest)(nil),               // 54: nakama.console.UnlinkDeviceRequest
	(*UpdateAccountRequest)(nil),              // 55: nakama.console.UpdateAccountRequest
	(*UpdateGroupRequest)(nil),                // 56: nakama.console.UpdateGroupRequest
	(*Username)(nil),                          // 57: nakama.console.Username
	(*UserList)(nil),                          // 58: nakama.console.UserList
	(*StatusList)(nil),                        // 59: nakama.console.StatusList
	(*RuntimeInfo)(nil),                       // 60: nakama.console.RuntimeInfo
	(*WalletLedger)(nil),                      // 61: nakama.console.WalletLedger
	(*WalletLedgerList)(nil),                  // 62: nakama.console.WalletLedgerList
	(*WriteStorageObjectRequest)(nil),         // 63: nakama.console.WriteStorageObjectRequest
	(*GetWalletLedgerRequest)(nil),            // 64: nakama.console.GetWalletLedgerRequest
	(*GetNotificationRequest)(nil),            // 65: nakama.console.GetNotificationRequest
	(*GetPurchaseRequest)(nil),                // 66: nakama.console.GetPurchaseRequest
	(*GetSubscriptionRequest)(nil),            // 67: nakama.console.GetSubscriptionRequest
	(*StorageListObject)(nil),                 // 68: nakama.console.StorageListObject
	(*MatchList_Match)(nil),                   // 69: nakama.console.MatchList.Match
	nil,                                       // 70: nakama.console.CallApiEndpointRequest.SessionVarsEntry
	(*Config_Warning)(nil),                    // 71: nakama.console.Config.Warning
	nil,                                       // 72: nakama.console.UpdateAccountRequest.DeviceIdsEntry
	(*UserList_User)(nil),                     // 73: nakama.console.UserList.User
	(*StatusList_Status)(nil),                 // 74: nakama.console.StatusList.Status
	(*RuntimeInfo_ModuleInfo)(nil),            // 75: nakama.console.RuntimeInfo.ModuleInfo
	(*api.Account)(nil),                       // 76: nakama.api.Account
	(*timestamppb.Timestamp)(nil),             // 77: google.protobuf.Timestamp
	(*wrapperspb.BoolValue)(nil),              // 78: google.protobuf.BoolValue
	(*api.StorageObject)(nil),                 // 79: nakama.api.StorageObject
	(*api.Friend)(nil),                        // 80: nakama.api.Friend
	(*api.Group)(nil),                         // 81: nakama.api.Group
	(*api.ChannelMessage)(nil),                // 82: nakama.api.ChannelMessage
	(*api.LeaderboardRecord)(nil),             // 83: nakama.api.LeaderboardRecord
	(*api.Notification)(nil),                  // 84: nakama.api.Notification
	(*api.User)(nil),                          // 85: nakama.api.User
	(*api.GroupUserList_GroupUser)(nil),       // 86: nakama.api.GroupUserList.GroupUser
	(*wrapperspb.StringValue)(nil),            // 87: google.protobuf.StringValue
	(*wrapperspb.Int32Value)(nil),             // 88: google.protobuf.Int32Value
	(*rtapi.UserPresence)(nil),                // 89: nakama.realtime.UserPresence
	(*api.Match)(nil),                         // 90: nakama.api.Match
	(*emptypb.Empty)(nil),                     // 91: google.protobuf.Empty
	(*api.ReadStorageObjectId)(nil),           // 92: nakama.api.ReadStorageObjectId
	(*api.ListLeaderboardRecordsRequest)(nil), // 93: nakama.api.ListLeaderboardRecordsRequest
	(*api.FriendList)(nil),                    // 94: nakama.api.FriendList
	(*api.GroupUserList)(nil),                 // 95: nakama.api.GroupUserList
	(*api.UserGroupList)(nil),                 // 96: nakama.api.UserGroupList
	(*api.ValidatedPurchase)(nil),             // 97: nakama.api.ValidatedPurchase
	(*api.ValidatedSubscription)(nil),         // 98: nakama.api.ValidatedSubscription
	(*api.LeaderboardRecordList)(nil),         // 99: nakama.api.LeaderboardRecordList
	(*api.ChannelMessageList)(nil),            // 100: nakama.api.ChannelMessageList
	(*api.PurchaseList)(nil),                  // 101: nakama.api.PurchaseList
	(*api.SubscriptionList)(nil),              // 102: nakama.api.SubscriptionList
	(*api.StorageObjectAck)(nil),              // 103: nakama.api.StorageObjectAck
}
var file_console_proto_depIdxs = []int32{
	76,  // 0: nakama.console.Account.account:type_name -> nakama.api.Account
	77,  // 1: nakama.console.Account.disable_time:type_name -> google.protobuf.Timestamp
	78,  // 2: nakama.console.AccountDeleteRequest.record_deletion:type_name -> google.protobuf.BoolValue
	76,  // 3: nakama.console.AccountExport.account:type_name -> nakama.api.Account
	79,  // 4: nakama.console.AccountExport.objects:type_name -> nakama.api.StorageObject
	80,  // 5: nakama.console.AccountExport.friends:type_name -> nakama.api.Friend
	81,  // 6: nakama.console.AccountExport.groups:type_name -> nakama.api.Group
	82,  // 7: nakama.console.AccountExport.messages:type_name -> nakama.api.ChannelMessage
	83,  // 8: nakama.console.AccountExport.leaderboard_records:type_name -> nakama.api.LeaderboardRecord
	84,  // 9: nakama.console.AccountExport.notifications:type_name -> nakama.api.Notification
	61,  // 10: nakama.console.AccountExport.wallet_ledgers:type_name -> nakama.console.WalletLedger
	85,  // 11: nakama.console.AccountList.users:type_name -> nakama.api.User
	81,  // 12: nakama.console.GroupList.groups:type_name -> nakama.api.Group
	81,  // 13: nakama.console.GroupExport.group:type_name -> nakama.api.Group
	86,  // 14: nakama.console.GroupExport.members:type_name -> nakama.api.GroupUserList.GroupUser
	69,  // 15: nakama.console.MatchList.matches:type_name -> nakama.console.MatchList.Match
	0,   // 16: nakama.console.AddUserRequest.role:type_name -> nakama.console.UserRole
	3,   // 17: nakama.console.ApiEndpointList.endpoints:type_name -> nakama.console.ApiEndpointDescriptor
	3,   // 18: nakama.console.ApiEndpointList.rpc_endpoints:type_name -> nakama.console.ApiEndpointDescriptor
	70,  // 19: nakama.console.CallApiEndpointRequest.session_vars:type_name -> nakama.console.CallApiEndpointRequest.SessionVarsEntry
	71,  // 20: nakama.console.Config.warnings:type_name -> nakama.console.Config.Warning
	87,  // 21: nakama.console.ConsoleSession.mfa_code:type_name -> google.protobuf.StringValue
	77,  // 22: nakama.console.DeleteChannelMessagesRequest.before:type_name -> google.protobuf.Timestamp
	77,  // 23: nakama.console.Leaderboard.create_time:type_name -> google.protobuf.Timestamp
	77,  // 24: nakama.console.Leaderboard.start_time:type_name -> google.protobuf.Timestamp
	77,  // 25: nakama.console.Leaderboard.end_time:type_name -> google.protobuf.Timestamp
	35,  // 26: nakama.console.LeaderboardList.leaderboards:type_name -> nakama.console.Leaderboard
	2,   // 27: nakama.console.ListChannelMessagesRequest.type:type_name -> nakama.console.ListChannelMessagesRequest.Type
	88,  // 28: nakama.console.ListMatchesRequest.limit:type_name -> google.protobuf.Int32Value
	78,  // 29: nakama.console.ListMatchesRequest.authoritative:type_name -> google.protobuf.BoolValue
	87,  // 30: nakama.console.ListMatchesRequest.label:type_name -> google.protobuf.StringValue
	88,  // 31: nakama.console.ListMatchesRequest.min_size:type_name -> google.protobuf.Int32Value
	88,  // 32: nakama.console.ListMatchesRequest.max_size:type_name -> google.protobuf.Int32Value
	87,  // 33: nakama.console.ListMatchesRequest.query:type_name -> google.protobuf.StringValue
	87,  // 34: nakama.console.ListMatchesRequest.node:type_name -> google.protobuf.StringValue
	89,  // 35: nakama.console.MatchState.presences:type_name -> nakama.realtime.UserPresence
	77,  // 36: nakama.console.Notification.create_time:type_name -> google.protobuf.Timestamp
	49,  // 37: nakama.console.NotificationList.notifications:type_name -> nakama.console.Notification
	68,  // 38: nakama.console.StorageList.objects:type_name -> nakama.console.StorageListObject
	87,  // 39: nakama.console.UpdateAccountRequest.username:type_name -> google.protobuf.StringValue
	87,  // 40: nakama.console.UpdateAccountRequest.display_name:type_name -> google.protobuf.StringValue
	87,  // 41: nakama.console.UpdateAccountRequest.metadata:type_name -> google.protobuf.StringValue
	87,  // 42: nakama.console.UpdateAccountRequest.avatar_url:type_name -> google.protobuf.StringValue
	87,  // 43: nakama.console.UpdateAccountRequest.lang_tag:type_name -> google.protobuf.StringValue
	87,  // 44: nakama.console.UpdateAccountRequest.location:type_name -> google.protobuf.StringValue
	87,  // 45: nakama.console.UpdateAccountRequest.timezone:type_name -> google.protobuf.StringValue
	87,  // 46: nakama.console.UpdateAccountRequest.custom_id:type_name -> google.protobuf.StringValue
	87,  // 47: nakama.console.UpdateAccountRequest.email:type_name -> google.protobuf.StringValue
	87,  // 48: nakama.console.UpdateAccountRequest.password:type_name -> google.protobuf.StringValue
	72,  // 49: nakama.console.UpdateAccountRequest.device_ids:type_name -> nakama.console.UpdateAccountRequest.DeviceIdsEntry
	87,  // 50: nakama.console.UpdateAccountRequest.wallet:type_name -> google.protobuf.StringValue
	87,  // 51: nakama.console.UpdateGroupRequest.name:type_name -> google.protobuf.StringValue
	87,  // 52: nakama.console.UpdateGroupRequest.description:type_name -> google.protobuf.StringValue
	87,  // 53: nakama.console.UpdateGroupRequest.lang_tag:type_name -> google.protobuf.StringValue
	87,  // 54: nakama.console.UpdateGroupRequest.metadata:type_name -> google.protobuf.StringValue
	87,  // 55: nakama.console.UpdateGroupRequest.avatar_url:type_name -> google.protobuf.StringValue
	78,  // 56: nakama.console.UpdateGroupRequest.open:type_name -> google.protobuf.BoolValue
	88,  // 57: nakama.console.UpdateGroupRequest.max_count:type_name -> google.protobuf.Int32Value
	73,  // 58: nakama.console.UserList.users:type_name -> nakama.console.UserList.User
	74,  // 59: nakama.console.StatusList.nodes:type_name -> nakama.console.StatusList.Status
	77,  // 60: nakama.console.StatusList.timestamp:type_name -> google.protobuf.Timestamp
	75,  // 61: nakama.console.RuntimeInfo.go_modules:type_name -> nakama.console.RuntimeInfo.ModuleInfo
	75,  // 62: nakama.console.RuntimeInfo.lua_modules:type_name -> nakama.console.RuntimeInfo.ModuleInfo
	75,  // 63: nakama.console.RuntimeInfo.js_modules:type_name -> nakama.console.RuntimeInfo.ModuleInfo
	77,  // 64: nakama.console.WalletLedger.create_time:type_name -> google.protobuf.Timestamp
	77,  // 65: nakama.console.WalletLedger.update_time:type_name -> google.protobuf.Timestamp
	61,  // 66: nakama.console.WalletLedgerList.items:type_name -> nakama.console.WalletLedger
	88,  // 67: nakama.console.WriteStorageObjectRequest.permission_read:type_name -> google.protobuf.Int32Value
	88,  // 68: nakama.console.WriteStorageObjectRequest.permission_write:type_name -> google.protobuf.Int32Value
	77,  // 69: nakama.console.StorageListObject.create_time:type_name -> google.protobuf.Timestamp
	77,  // 70: nakama.console.StorageListObject.update_time:type_name -> google.protobuf.Timestamp
	90,  // 71: nakama.console.MatchList.Match.api_match:type_name -> nakama.api.Match
	0,   // 72: nakama.console.UserList.User.role:type_name -> nakama.console.UserRole
	1,   // 73: nakama.console.StatusList.Status.health:type_name -> nakama.console.StatusHealth
	77,  // 74: nakama.console.RuntimeInfo.ModuleInfo.mod_time:type_name -> google.protobuf.Timestamp
	16,  // 75: nakama.console.Console.Authenticate:input_type -> nakama.console.AuthenticateRequest
	19,  // 76: nakama.console.Console.AuthenticateLogout:input_type -> nakama.console.AuthenticateLogoutRequest
	17,  // 77: nakama.console.Console.AuthenticateMFASetup:input_type -> nakama.console.AuthenticateMFASetupRequest
	13,  // 78: nakama.console.Console.AddUser:input_type -> nakama.console.AddUserRequest
	14,  // 79: nakama.console.Console.AddGroupUsers:input_type -> nakama.console.AddGroupUsersRequest
	7,   // 80: nakama.console.Console.BanAccount:input_type -> nakama.console.AccountId
	20,  // 81: nakama.console.Console.CallApiEndpoint:input_type -> nakama.console.CallApiEndpointRequest
	20,  // 82: nakama.console.Console.CallRpcEndpoint:input_type -> nakama.console.CallApiEndpointRequest
	91,  // 83: nakama.console.Console.DeleteAllData:input_type -> google.protobuf.Empty
	5,   // 84: nakama.console.Console.DeleteAccount:input_type -> nakama.console.AccountDeleteRequest
	24,  // 85: nakama.console.Console.DeleteChannelMessages:input_type -> nakama.console.DeleteChannelMessagesRequest
	25,  // 86: nakama.console.Console.DeleteFriend:input_type -> nakama.console.DeleteFriendRequest
	26,  // 87: nakama.console.Console.DeleteGroup:input_type -> nakama.console.DeleteGroupRequest
	27,  // 88: nakama.console.Console.DeleteGroupUser:input_type -> nakama.console.DeleteGroupUserRequest
	91,  // 89: nakama.console.Console.DeleteStorage:input_type -> google.protobuf.Empty
	33,  // 90: nakama.console.Console.DeleteStorageObject:input_type -> nakama.console.DeleteStorageObjectRequest
	91,  // 91: nakama.console.Console.DeleteAccounts:input_type -> google.protobuf.Empty
	38,  // 92: nakama.console.Console.DeleteLeaderboard:input_type -> nakama.console.LeaderboardRequest
	31,  // 93: nakama.console.Console.DeleteLeaderboardRecord:input_type -> nakama.console.DeleteLeaderboardRecordRequest
	32,  // 94: nakama.console.Console.DeleteNotification:input_type -> nakama.console.DeleteNotificationRequest
	57,  // 95: nakama.console.Console.DeleteUser:input_type -> nakama.console.Username
	34,  // 96: nakama.console.Console.DeleteWalletLedger:input_type -> nakama.console.DeleteWalletLedgerRequest
	28,  // 97: nakama.console.Console.DemoteGroupMember:input_type -> nakama.console.UpdateGroupUserStateRequest
	7,   // 98: nakama.console.Console.ExportAccount:input_type -> nakama.console.AccountId
	9,   // 99: nakama.console.Console.ExportGroup:input_type -> nakama.console.GroupId
	7,   // 100: nakama.console.Console.GetAccount:input_type -> nakama.console.AccountId
	91,  // 101: nakama.console.Console.GetConfig:input_type -> google.protobuf.Empty
	7,   // 102: nakama.console.Console.GetFriends:input_type -> nakama.console.AccountId
	9,   // 103: nakama.console.Console.GetGroup:input_type -> nakama.console.GroupId
	9,   // 104: nakama.console.Console.GetMembers:input_type -> nakama.console.GroupId
	7,   // 105: nakama.console.Console.GetGroups:input_type -> nakama.console.AccountId
	38,  // 106: nakama.console.Console.GetLeaderboard:input_type -> nakama.console.LeaderboardRequest
	48,  // 107: nakama.console.Console.GetMatchState:input_type -> nakama.console.MatchStateRequest
	91,  // 108: nakama.console.Console.GetRuntime:input_type -> google.protobuf.Empty
	91,  // 109: nakama.console.Console.GetStatus:input_type -> google.protobuf.Empty
	92,  // 110: nakama.console.Console.GetStorage:input_type -> nakama.api.ReadStorageObjectId
	64,  // 111: nakama.console.Console.GetWalletLedger:input_type -> nakama.console.GetWalletLedgerRequest
	65,  // 112: nakama.console.Console.GetNotification:input_type -> nakama.console.GetNotificationRequest
	66,  // 113: nakama.console.Console.GetPurchase:input_type -> nakama.console.GetPurchaseRequest
	67,  // 114: nakama.console.Console.GetSubscription:input_type -> nakama.console.GetSubscriptionRequest
	91,  // 115: nakama.console.Console.ListApiEndpoints:input_type -> google.protobuf.Empty
	93,  // 116: nakama.console.Console.ListLeaderboardRecords:input_type -> nakama.api.ListLeaderboardRecordsRequest
	36,  // 117: nakama.console.Console.ListLeaderboards:input_type -> nakama.console.LeaderboardListRequest
	46,  // 118: nakama.console.Console.ListStorage:input_type -> nakama.console.ListStorageRequest
	91,  // 119: nakama.console.Console.ListStorageCollections:input_type -> google.protobuf.Empty
	39,  // 120: nakama.console.Console.ListAccounts:input_type -> nakama.console.ListAccountsRequest
	40,  // 121: nakama.console.Console.ListChannelMessages:input_type -> nakama.console.ListChannelMessagesRequest
	41,  // 122: nakama.console.Console.ListGroups:input_type -> nakama.console.ListGroupsRequest
	43,  // 123: nakama.console.Console.ListNotifications:input_type -> nakama.console.ListNotificationsRequest
	42,  // 124: nakama.console.Console.ListMatches:input_type -> nakama.console.ListMatchesRequest
	44,  // 125: nakama.console.Console.ListPurchases:input_type -> nakama.console.ListPurchasesRequest
	45,  // 126: nakama.console.Console.ListSubscriptions:input_type -> nakama.console.ListSubscriptionsRequest
	91,  // 127: nakama.console.Console.ListUsers:input_type -> google.protobuf.Empty
	28,  // 128: nakama.console.Console.PromoteGroupMember:input_type -> nakama.console.UpdateGroupUserStateRequest
	30,  // 129: nakama.console.Console.RequireUserMfa:input_type -> nakama.console.RequireUserMfaRequest
	29,  // 130: nakama.console.Console.ResetUserMfa:input_type -> nakama.console.ResetUserMfaRequest
	7,   // 131: nakama.console.Console.UnbanAccount:input_type -> nakama.console.AccountId
	7,   // 132: nakama.console.Console.UnlinkCustom:input_type -> nakama.console.AccountId
	54,  // 133: nakama.console.Console.UnlinkDevice:input_type -> nakama.console.UnlinkDeviceRequest
	7,   // 134: nakama.console.Console.UnlinkEmail:input_type -> nakama.console.AccountId
	7,   // 135: nakama.console.Console.UnlinkApple:input_type -> nakama.console.AccountId
	7,   // 136: nakama.console.Console.UnlinkFacebook:input_type -> nakama.console.AccountId
	7,   // 137: nakama.console.Console.UnlinkFacebookInstantGame:input_type -> nakama.console.AccountId
	7,   // 138: nakama.console.Console.UnlinkGameCenter:input_type -> nakama.console.AccountId
	7,   // 139: nakama.console.Console.UnlinkGoogle:input_type -> nakama.console.AccountId
	7,   // 140: nakama.console.Console.UnlinkSteam:input_type -> nakama.console.AccountId
	55,  // 141: nakama.console.Console.UpdateAccount:input_type -> nakama.console.UpdateAccountRequest
	56,  // 142: nakama.console.Console.UpdateGroup:input_type -> nakama.console.UpdateGroupRequest
	63,  // 143: nakama.console.Console.WriteStorageObject:input_type -> nakama.console.WriteStorageObjectRequest
	23,  // 144: nakama.console.Console.Authenticate:output_type -> nakama.console.ConsoleSession
	91,  // 145: nakama.console.Console.AuthenticateLogout:output_type -> google.protobuf.Empty
	18,  // 146: nakama.console.Console.AuthenticateMFASetup:output_type -> nakama.console.AuthenticateMFASetupResponse
	91,  // 147: nakama.console.Console.AddUser:output_type -> google.protobuf.Empty
	91,  // 148: nakama.console.Console.AddGroupUsers:output_type -> google.protobuf.Empty
	91,  // 149: nakama.console.Console.BanAccount:output_type -> google.protobuf.Empty
	21,  // 150: nakama.console.Console.CallApiEndpoint:output_type -> nakama.console.CallApiEndpointResponse
	21,  // 151: nakama.console.Console.CallRpcEndpoint:output_type -> nakama.console.CallApiEndpointResponse
	91,  // 152: nakama.console.Console.DeleteAllData:output_type -> google.protobuf.Empty
	91,  // 153: nakama.console.Console.DeleteAccount:output_type -> google.protobuf.Empty
	51,  // 154: nakama.console.Console.DeleteChannelMessages:output_type -> nakama.console.DeleteChannelMessagesResponse
	91,  // 155: nakama.console.Console.DeleteFriend:output_type -> google.protobuf.Empty
	91,  // 156: nakama.console.Console.DeleteGroup:output_type -> google.protobuf.Empty
	91,  // 157: nakama.console.Console.DeleteGroupUser:output_type -> google.protobuf.Empty
	91,  // 158: nakama.console.Console.DeleteStorage:output_type -> google.protobuf.Empty
	91,  // 159: nakama.console.Console.DeleteStorageObject:output_type -> google.protobuf.Empty
	91,  // 160: nakama.console.Console.DeleteAccounts:output_type -> google.protobuf.Empty
	91,  // 161: nakama.console.Console.DeleteLeaderboard:output_type -> google.protobuf.Empty
	91,  // 162: nakama.console.Console.DeleteLeaderboardRecord:output_type -> google.protobuf.Empty
	91,  // 163: nakama.console.Console.DeleteNotification:output_type -> google.protobuf.Empty
	91,  // 164: nakama.console.Console.DeleteUser:output_type -> google.protobuf.Empty
	91,  // 165: nakama.console.Console.DeleteWalletLedger:output_type -> google.protobuf.Empty
	91,  // 166: nakama.console.Console.DemoteGroupMember:output_type -> google.protobuf.Empty
	6,   // 167: nakama.console.Console.ExportAccount:output_type -> nakama.console.AccountExport
	11,  // 168: nakama.console.Console.ExportGroup:output_type -> nakama.console.GroupExport
	4,   // 169: nakama.console.Console.GetAccount:output_type -> nakama.console.Account
	22,  // 170: nakama.console.Console.GetConfig:output_type -> nakama.console.Config
	94,  // 171: nakama.console.Console.GetFriends:output_type -> nakama.api.FriendList
	81,  // 172: nakama.console.Console.GetGroup:output_type -> nakama.api.Group
	95,  // 173: nakama.console.Console.GetMembers:output_type -> nakama.api.GroupUserList
	96,  // 174: nakama.console.Console.GetGroups:output_type -> nakama.api.UserGroupList
	35,  // 175: nakama.console.Console.GetLeaderboard:output_type -> nakama.console.Leaderboard
	47,  // 176: nakama.console.Console.GetMatchState:output_type -> nakama.console.MatchState
	60,  // 177: nakama.console.Console.GetRuntime:output_type -> nakama.console.RuntimeInfo
	59,  // 178: nakama.console.Console.GetStatus:output_type -> nakama.console.StatusList
	79,  // 179: nakama.console.Console.GetStorage:output_type -> nakama.api.StorageObject
	62,  // 180: nakama.console.Console.GetWalletLedger:output_type -> nakama.console.WalletLedgerList
	49,  // 181: nakama.console.Console.GetNotification:output_type -> nakama.console.Notification
	97,  // 182: nakama.console.Console.GetPurchase:output_type -> nakama.api.ValidatedPurchase
	98,  // 183: nakama.console.Console.GetSubscription:output_type -> nakama.api.ValidatedSubscription
	15,  // 184: nakama.console.Console.ListApiEndpoints:output_type -> nakama.console.ApiEndpointList
	99,  // 185: nakama.console.Console.ListLeaderboardRecords:output_type -> nakama.api.LeaderboardRecordList
	37,  // 186: nakama.console.Console.ListLeaderboards:output_type -> nakama.console.LeaderboardList
	52,  // 187: nakama.console.Console.ListStorage:output_type -> nakama.console.StorageList
	53,  // 188: nakama.console.Console.ListStorageCollections:output_type -> nakama.console.StorageCollectionsList
	8,   // 189: nakama.console.Console.ListAccounts:output_type -> nakama.console.AccountList
	100, // 190: nakama.console.Console.ListChannelMessages:output_type -> nakama.api.ChannelMessageList
	10,  // 191: nakama.console.Console.ListGroups:output_type -> nakama.console.GroupList
	50,  // 192: nakama.console.Console.ListNotifications:output_type -> nakama.console.NotificationList
	12,  // 193: nakama.console.Console.ListMatches:output_type -> nakama.console.MatchList
	101, // 194: nakama.console.Console.ListPurchases:output_type -> nakama.api.PurchaseList
	102, // 195: nakama.console.Console.ListSubscriptions:output_type -> nakama.api.SubscriptionList
	58,  // 196: nakama.console.Console.ListUsers:output_type -> nakama.console.UserList
	91,  // 197: nakama.console.Console.PromoteGroupMember:output_type -> google.protobuf.Empty
	91,  // 198: nakama.console.Console.RequireUserMfa:output_type -> google.protobuf.Empty
	91,  // 199: nakama.console.Console.ResetUserMfa:output_type -> google.protobuf.Empty
	91,  // 200: nakama.console.Console.UnbanAccount:output_type -> google.protobuf.Empty
	91,  // 201: nakama.console.Console.UnlinkCustom:output_type -> google.protobuf.Empty
	91,  // 202: nakama.console.Console.UnlinkDevice:output_type -> google.protobuf.Empty
	91,  // 203: nakama.console.Console.UnlinkEmail:output_type -> google.protobuf.Empty
	91,  // 204: nakama.console.Console.UnlinkApple:output_type -> google.protobuf.Empty
	91,  // 205: nakama.console.Console.UnlinkFacebook:output_type -> google.protobuf.Empty
	91,  // 206: nakama.console.Console.UnlinkFacebookInstantGame:output_type -> google.protobuf.Empty
	91,  // 207: nakama.console.Console.UnlinkGameCenter:output_type -> google.protobuf.Empty
	91,  // 208: nakama.console.Console.UnlinkGoogle:output_type -> google.protobuf.Empty
	91,  // 209: nakama.console.Console.UnlinkSteam:output_type -> google.protobuf.Empty
	91,  // 210: nakama.console.Console.UpdateAccount:output_type -> google.protobuf.Empty
	91,  // 211: nakama.console.Console.UpdateGroup:output_type -> google.protobuf.Empty
	103, // 212: nakama.console.Console.WriteStorageObject:output_type -> nakama.api.StorageObjectAck
	144, // [144:213] is the sub-list for method output_type
	75,  // [75:144] is the sub-list for method input_type
	75,  // [75:75] is the sub-list for extension type_name
	75,  // [75:75] is the sub-list for extension extendee
	0,   // [0:75] is the sub-list for field type_name
}

func init() { file_console_proto_init() }
func file_console_proto_init() {
	if File_console_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_console_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApiEndpointDescriptor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Account); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountDeleteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountExport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupExport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddGroupUsersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApiEndpointList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthenticateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthenticateMFASetupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthenticateMFASetupResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthenticateLogoutRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallApiEndpointRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallApiEndpointResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsoleSession); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteChannelMessagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteFriendRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteGroupUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateGroupUserStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetUserMfaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequireUserMfaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLeaderboardRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteNotificationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteStorageObjectRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteWalletLedgerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Leaderboard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LeaderboardListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LeaderboardList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LeaderboardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAccountsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListChannelMessagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGroupsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMatchesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListNotificationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPurchasesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSubscriptionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStorageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Notification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotificationList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteChannelMessagesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StorageList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StorageCollectionsList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnlinkDeviceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Username); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuntimeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WalletLedger); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WalletLedgerList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WriteStorageObjectRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWalletLedgerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNotificationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPurchaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubscriptionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StorageListObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchList_Match); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Config_Warning); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserList_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusList_Status); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_console_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuntimeInfo_ModuleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_console_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   73,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_console_proto_goTypes,
		DependencyIndexes: file_console_proto_depIdxs,
		EnumInfos:         file_console_proto_enumTypes,
		MessageInfos:      file_console_proto_msgTypes,
	}.Build()
	File_console_proto = out.File
	file_console_proto_rawDesc = nil
	file_console_proto_goTypes = nil
	file_console_proto_depIdxs = nil
}
