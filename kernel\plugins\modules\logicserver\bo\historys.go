package bo

import (
	"bytes"
	"fmt"
	"kernel/plugins/common"
	"kernel/plugins/models"
	"reflect"

	json "github.com/json-iterator/go"
)

type Historys struct {
	UserId string `json:"user_id,omitempty"`
	List   string `json:"list,omitempty"`
}

func (o *Historys) GetTable() string {
	return "historys"
}

func (o *Historys) GetKeyName() string {
	return "user_id"
}

func (o *Historys) GetUniqueKeys() []string {
	return nil
}

func (o *Historys) GetSecondKeyName() string {
	return ""
}

func (o *Historys) GetRowResultType() models.RowResultType {
	return models.ROWRESULT_TYPE_ONLYONE
}

func (o *Historys) GetQueryArgs() string {
	return "*"
}
func (o *Historys) GetFlushType() models.FlushType {
	return models.FLUSH_TYPE_ASYNC
}

func (o *Historys) GetCacheLevel() models.CacheLevel {
	return models.CACHE_LEVEL_NORMAL
}

func (o *Historys) GetVersionName() string {
	return ""
}

func (o *Historys) Marshal() ([]byte, error) {
	return json.Marshal(o)
}

func (o *Historys) MarshalToMap(filter map[string]struct{}) map[string]interface{} {
	dst := make(map[string]interface{})
	err := common.StrcutToMap(o, &dst)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	for k := range filter {
		delete(dst, k)
	}
	return dst
}

func (o *Historys) Unmarshal(buf []byte) error {
	d := json.NewDecoder(bytes.NewReader(buf))
	d.UseNumber()
	return d.Decode(o)
}

func (o *Historys) Clear() {
	p := reflect.ValueOf(o).Elem()
	p.Set(reflect.Zero(p.Type()))
}
