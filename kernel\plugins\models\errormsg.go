package models

import (
	"errors"
	"fmt"
)

var (
	ErrMsgOK           = NewErrorMsg(0, "ok")
	ErrMsgTimeout      = NewErrorMsg(21, "timeout")        // 超时
	ErrMsgFailed       = NewErrorMsg(22, "failed")         // 执行失败
	ErrMsgBusy         = NewErrorMsg(23, "busy")           // 繁忙
	ErrMsgException    = NewErrorMsg(24, "exception")      // 异常
	ErrMsgParamErr     = NewErrorMsg(25, "param error")    // 参数错误
	ErrMsgLockErr      = NewErrorMsg(26, "lock error")     // 锁错误
	ErrMsgSessionErr   = NewErrorMsg(27, "session error")  // 会话错误
	ErrMsgAccessDenied = NewErrorMsg(28, "access denied")  // 访问拒绝
	ErrMsgAuthCode     = NewErrorMsg(29, "auth code")      // 告诉客户端需要验证码
	ErrMsgAuthErr      = NewErrorMsg(30, "auth error")     // 验证错误
	ErrMsgRepeatReg    = NewErrorMsg(31, "repeat reg")     // 重复注册
	ErrMsgDataExist    = NewErrorMsg(32, "data exist")     // 数据已存在
	ErrMsgDataNotExist = NewErrorMsg(33, "data not exist") // 数据不存在
	ErrMsgAggAuthErr   = NewErrorMsg(34, "agg auth error") // 聚合登陆验证失败
)

type ErrorMsg struct {
	Code    int32
	Message string
}

func (e *ErrorMsg) Error() string {
	return fmt.Sprintf("code=%d, message=%s", e.Code, e.Message)
}

func (e *ErrorMsg) Err() error {
	return fmt.Errorf("code=%d, message=%s", e.Code, e.Message)
}

func (e *ErrorMsg) Is(target *ErrorMsg) bool {
	if target == nil {
		return false
	}
	return e.Code == target.Code
}

func NewErrorMsg(code int32, msg string) *ErrorMsg {
	err := &ErrorMsg{
		Code:    code,
		Message: msg,
	}

	return err
}

func IsErrOK(err *ErrorMsg) bool {
	if err == nil {
		return true
	}
	return err.Code == ErrMsgOK.Code
}

func DecodeErrorMsg(err error) (int32, string) {
	if err == nil {
		return ErrMsgOK.Code, ErrMsgOK.Message
	}

	var errno *ErrorMsg
	if errors.As(err, &errno) {
		return errno.Code, errno.Message
	}

	return ErrMsgException.Code, err.Error()
}
