package obs

import (
	"bytes"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io/ioutil"
	"log"
	"net/url"

	"github.com/huaweicloud/huaweicloud-sdk-go-obs/obs"
)

type ObsClient struct {
	OSClientBase
	client *obs.ObsClient
}

func NewOSHuawei(osconfig *OSConfig) IOSClient {
	client := &ObsClient{}
	client.Name = ObsTypeNameHuawei
	client.OSConfig = *osconfig

	obs, err := obs.New(osconfig.AccessKey, osconfig.Secretkey, osconfig.Endpoint)
	if err != nil {
		log.Panic(err.Error())
		return client
	}

	client.client = obs
	return client
}

func (o *ObsClient) GetObject(key string) ([]byte, error) {
	input := &obs.GetObjectInput{}
	input.Bucket = o.Bucket
	input.Key = key

	output, err := o.client.GetObject(input)
	if err != nil {
		log.Printf("[huawei]get object fail, key=%s, err=%s", key, err.Error())
		return nil, err
	}

	defer output.Body.Close()
	body, err := ioutil.ReadAll(output.Body)
	if err != nil {
		log.Printf("[huawei]read response fail, key=%s, err=%s", key, err.Error())
		return nil, err
	}

	return body, nil
}

func (o *ObsClient) PutObject(key string, data []byte) error {
	input := &obs.PutObjectInput{}
	input.Bucket = o.Bucket
	input.Key = key
	input.Body = bytes.NewReader(data)
	base64md5 := obs.Base64Md5(data)
	// 这里不直接存hex md5的原因是与迷你obs兼容
	input.ContentMD5 = base64md5
	input.Metadata = make(map[string]string)
	input.Metadata[Base64MetaKey] = base64md5

	_, err := o.client.PutObject(input)
	if err != nil {
		log.Printf("[huawei]put object fail, key=%s, err=%s", key, err.Error())
		return err
	}

	return nil
}

func (o *ObsClient) PutObjectWithMetadata(key string, data []byte, metaData map[string]string) error {
	if len(metaData) == 0 {
		metaData = make(map[string]string)
	}

	input := &obs.PutObjectInput{}
	input.Bucket = o.Bucket
	input.Key = key
	input.Body = bytes.NewReader(data)
	base64md5 := obs.Base64Md5(data)
	// 这里不直接存hex md5的原因是与迷你obs兼容
	input.ContentMD5 = base64md5
	input.Metadata = metaData
	input.Metadata[Base64MetaKey] = base64md5

	_, err := o.client.PutObject(input)
	if err != nil {
		log.Printf("[huawei]put object fail, key=%s, err=%s", key, err.Error())
		return err
	}

	return nil
}

func (o *ObsClient) PutFile(path, key string) error {
	data, err := ioutil.ReadFile(path)
	if err != nil {
		log.Printf("[huawei]read file fail, path=%s, err=%s", path, err.Error())
		return err
	}

	input := &obs.PutFileInput{}
	input.Bucket = o.Bucket
	input.Key = key
	input.SourceFile = path
	base64md5 := obs.Base64Md5([]byte(data))
	input.ContentMD5 = base64md5
	input.Metadata = make(map[string]string)
	input.Metadata[Base64MetaKey] = base64md5

	_, err = o.client.PutFile(input)
	if err != nil {
		log.Printf("[huawei]put file fail, path=%s, key=%s, err=%s", path, key, err.Error())
		return err
	}

	return nil
}

func (o *ObsClient) GetFile(key, file string) error {
	var input = &obs.DownloadFileInput{
		GetObjectMetadataInput: obs.GetObjectMetadataInput{Bucket: o.Bucket, Key: key},
		DownloadFile:           file}
	_, err := o.client.DownloadFile(input)
	if err != nil {
		log.Printf("[huawei]get file fail, key=%s,  file=%s, err=%s", key, file, err.Error())
		return err
	}

	return nil
}

func (o *ObsClient) DelObject(key string) error {
	input := &obs.DeleteObjectInput{
		Bucket:    o.Bucket,
		Key:       key,
		VersionId: "",
	}

	_, err := o.client.DeleteObject(input)
	if err != nil {
		log.Printf("[huawei]delete object fail, key=%s, err=%s", key, err.Error())
		return err
	}

	return nil
}

func (o *ObsClient) DelObjects(keys ...string) error {
	var deletesKeyInput []obs.ObjectToDelete
	for _, o := range keys {
		deletesKeyInput = append(deletesKeyInput, obs.ObjectToDelete{Key: o})
	}
	input := obs.DeleteObjectsInput{Bucket: o.Bucket, Quiet: true, Objects: deletesKeyInput}
	_, err := o.client.DeleteObjects(&input)
	if err != nil {
		log.Printf("[huawei]delete objects fail, keys=%v, err=%s", keys, err.Error())
		return err
	}

	return nil
}

func (o *ObsClient) Close() {
	o.client.Close()
}

func (o *ObsClient) GetObjectDownloadURL(key string) string {
	//doc: https://support.huaweicloud.com/api-obs/obs_04_0007.html
	reqUrl, err := url.Parse(o.Endpoint)
	if err != nil {
		log.Printf("[huawei]get object download url fail, key=%v, err=%s", key, err.Error())
		return ""
	}

	buff := new(bytes.Buffer)
	buff.WriteString("http://")
	buff.WriteString(o.Bucket + ".")
	buff.WriteString(reqUrl.Host)
	buff.WriteString("/" + key)

	return buff.String()
}

func (o *ObsClient) GetObjectMetadata(key string) (map[string]string, error) {
	input := &obs.GetObjectMetadataInput{}
	input.Bucket = o.Bucket
	input.Key = key

	metaData, err := o.client.GetObjectMetadata(input)
	if err != nil {
		log.Printf("[huawei]get object metadata fail, key=%v, err=%s", key, err.Error())
		return nil, err
	}

	return metaData.Metadata, nil
}

func (o *ObsClient) SetObjectMetadata(key string, metaData map[string]string) error {
	if len(metaData) == 0 {
		return fmt.Errorf("[huawei]set object metadata fail, meta size 0, key:%v", key)
	}

	input := &obs.SetObjectMetadataInput{}
	input.Bucket = o.Bucket
	input.Key = key
	input.Metadata = metaData

	_, err := o.client.SetObjectMetadata(input)
	if err != nil {
		log.Printf("[huawei]set object metadata fail, key=%v, err=%s", key, err.Error())
		return err
	}

	return nil
}

func (o *ObsClient) GetObjectMD5(key string) string {
	metaData, err := o.GetObjectMetadata(key)
	if err != nil {
		log.Printf("[huawei]get object metadata fail, key=%v, err=%s", key, err.Error())
		return ""
	}

	metaBase64, ok := metaData[Base64MetaKey]
	if !ok {
		log.Printf("[huawei]object metadata access fail, key=%v", key)
		return ""
	}

	decodeBytes, err := base64.StdEncoding.DecodeString(metaBase64)
	if err != nil {
		log.Printf("[huawei]object metadata decode fail, key=%v, err=%s", key, err.Error())
		return ""
	}

	metaMd5 := hex.EncodeToString(decodeBytes)
	return metaMd5
}

func (o *ObsClient) ListObjects(prefix string) ([]string, error) {
	listObjectsInput := obs.ListObjectsInput{Bucket: o.Bucket, ListObjsInput: obs.ListObjsInput{Prefix: prefix}}
	objectsOutput, err := o.client.ListObjects(&listObjectsInput)
	if err != nil {
		log.Printf("[huawei]list objes fail, prefix=%v", prefix)
		return nil, err
	}
	var keys = []string{}
	for _, content := range objectsOutput.Contents {
		keys = append(keys, content.Key)
	}
	return keys, nil
}

func (o *ObsClient) GetSafeUploadURL(key string, expiresSeconds int, maxSize int64, expectedMD5 string) string {
	// 创建临时授权请求
	input := &obs.CreateSignedUrlInput{
		Method:  obs.HttpMethodPut,
		Bucket:  o.Bucket,
		Key:     key,
		Expires: expiresSeconds,
		Headers: make(map[string]string),
	}

	// 设置安全相关的请求头
	input.Headers[obs.HEADER_CONTENT_TYPE_CAML] = "application/octet-stream"
	input.Headers[obs.HEADER_CACHE_CONTROL_CAMEL] = "no-cache"
	input.Headers[obs.HEADER_CONTENT_DISPOSITION_CAMEL] = "attachment"

	// 设置ACL为私有访问
	input.Headers[obs.HEADER_ACL_OBS] = "private"

	// 设置文件大小限制
	if maxSize > 0 {
		input.Headers[obs.HEADER_CONTENT_LENGTH_CAMEL] = fmt.Sprintf("%d", maxSize)
	}

	// 设置MD5校验
	if expectedMD5 != "" {
		input.Headers[obs.HEADER_MD5_CAMEL] = expectedMD5
	}

	// 生成带签名的URL
	output, err := o.client.CreateSignedUrl(input)
	if err != nil {
		log.Printf("[huawei]create signed url fail, key=%s, err=%s", key, err.Error())
		return ""
	}

	return output.SignedUrl
}
