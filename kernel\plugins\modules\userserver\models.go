package main

import (
	"kernel/plugins/models"
)

// 请求查询基础信息
type ReqQueryBase struct {
	UserIDs []string `json:"uids,omitempty"`   // 多个用户ID, 如果*则查询所有用户
	Uins    []int32  `json:"uins,omitempty"`   // 多个Uins
	Fields  []string `json:"fields,omitempty"` // 查询时可选，如果*则查询所有字段, 字段名看models.User内的json tag
	Page    int32    `json:"page,omitempty"`   // 页码，查询所有用户时有效
	Size    int32    `json:"size,omitempty"`   // 每页数量，查询所有用户时有效
}

// 响应查询基础信息
type RespQueryBase struct {
	Total int32                   `json:"total,omitempty"` // 总数量
	Users map[string]*models.User `json:"users,omitempty"` // key：用户id
}

// 请求修改基础信息
type ReqModifyBase struct {
	UserID string                 `json:"user_id,omitempty"` // 用户ID
	Fields map[string]interface{} `json:"fields,omitempty"`  // 字段名看models.User内的json tag {"name":"张三","age":18}
}

// 请求封禁用户
type ReqBanUser struct {
	UserIDs []string `json:"uids,omitempty"`   // 多个用户ID
	Reason  string   `json:"reason,omitempty"` // 封禁原因
	Time    int64    `json:"time,omitempty"`   // 封禁时长，单位秒
}

// 请求解封用户
type ReqUnbanUser struct {
	UserIDs []string `json:"uids,omitempty"` // 多个用户ID
}

// 请求修改性别
type ReqModifyGender struct {
	UserID string `json:"user_id,omitempty"` // 用户ID
	Gender int32  `json:"gender,omitempty"`  // 性别
}
