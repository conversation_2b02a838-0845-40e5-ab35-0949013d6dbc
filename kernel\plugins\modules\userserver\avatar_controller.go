package main

import (
	"context"
	"database/sql"
	"kernel/kernel-common/runtime"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"kernel/plugins/modules/userserver/bo"
	"kernel/plugins/pb"
	"time"

	json "github.com/json-iterator/go"
	"google.golang.org/protobuf/proto"
)

// @Summary 获取装扮列表
// @Description 获取装扮列表
// @Tags userserver
// @Accept json
// @Produce json
// @Success 200 {object} RespAvatarList "返回结果"
// @Router /v2/rpc/userserver.avatar.list [post]
func (c *Controller) AvatarList(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		return json.MarshalToString(&RespAvatarList{Code: int64(models.FAILED), Message: "session error"})
	}

	avatars := bo.Avatars{}
	obj := c.server.dbagent.Create(ctx, &avatars, models.WithKeys(map[string]interface{}{avatars.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err := obj.Query(ctx).As(&avatars)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query avatars for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespAvatarList{Code: int64(models.FAILED), Message: err.Error()})
		}
		avatars.Items = "{}"
	}
	items := map[int64]*Avatar{} // item_id -> item
	err = json.Unmarshal([]byte(avatars.Items), &items)
	if err != nil {
		logger.Error("Failed to unmarshal avatars items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespAvatarList{Code: int64(models.FAILED), Message: err.Error()})
	}

	// 检查装扮是否过期，并更新
	isupdate := false
	now := time.Now()
	for itemId, item := range items {
		if item.ExpireTime > 0 && item.ExpireTime < now.Unix() {
			delete(items, itemId)
			isupdate = true
		}
	}
	if isupdate {
		avatars.UpdatedAt = now
		avatars.Items, err = json.MarshalToString(items)
		if err != nil {
			logger.Error("Failed to marshal avatars items for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespAvatarList{Code: int64(models.FAILED), Message: err.Error()})
		}
		resp, err := obj.WithReqOption(models.WithValues(map[string]interface{}{"items": avatars.Items, "updated_at": avatars.UpdatedAt})).Update(ctx).Result()
		if err != nil {
			logger.Error("Failed to update avatars for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespAvatarList{Code: int64(models.FAILED), Message: err.Error()})
		}
		if resp.RowsAffected > 0 {
			// nothing to do
		} else {
			return json.MarshalToString(&RespAvatarList{Code: int64(models.FAILED), Message: "update failed"})
		}
	}

	list := pb.AvatarList{}
	list.List = make([]*pb.AvatarInfo, 0, len(items))
	for _, item := range items {
		id := uint32(item.AvatarId)
		expireTime := item.ExpireTime
		list.List = append(list.List, &pb.AvatarInfo{
			Id:         &id,
			ExpireTime: &expireTime,
		})
	}
	bytes, err := proto.Marshal(&list)
	if err != nil {
		logger.Error("Failed to marshal avatar list for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespAvatarList{Code: int64(models.FAILED), Message: err.Error()})
	}

	return json.MarshalToString(&RespAvatarList{Code: int64(models.OK), Message: "success", Data: string(bytes)})
}

// @Summary 添加装扮
// @Description 添加装扮
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqAvatarOpt true "请求参数"
// @Success 200 {object} models.CommonResp "返回结果"
// @Router /v2/rpc/userserver.avatar.add [post]
func (c *Controller) AvatarAdd(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if c.server.common.GetUserID(ctx) != "" {
		logger.Error("session not allowed, add avatar")
		return json.MarshalToString(&models.CommonResp{Code: models.ACCESS_DENIED, Msg: "session not allowed"})
	}

	var reqAvatar ReqAvatarOpt
	err := json.Unmarshal([]byte(payload), &reqAvatar)
	if err != nil {
		logger.Error("Failed to unmarshal avatar add: %v", err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}

	if reqAvatar.UserId == "" || reqAvatar.Avatar == nil {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "param is invalid"})
	}

	user_id := reqAvatar.UserId
	isInsert := false
	avatars := bo.Avatars{}
	obj := c.server.dbagent.Create(ctx, &avatars, models.WithKeys(map[string]interface{}{avatars.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx).As(&avatars)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query avatars for user_id %s: %v", user_id, err)
			return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
		}
		avatars.Items = "{}"
		isInsert = true
	}
	items := map[int64]*Avatar{} // item_id -> item
	err = json.Unmarshal([]byte(avatars.Items), &items)
	if err != nil {
		logger.Error("Failed to unmarshal avatars items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}

	// 检查装扮是否过期，并更新
	isupdate := false
	now := time.Now()
	for itemId, item := range items {
		if item.ExpireTime > 0 && item.ExpireTime < now.Unix() {
			delete(items, itemId)
			isupdate = true
		}
	}

	// 检查是否已经存在
	if _, ok := items[reqAvatar.Avatar.AvatarId]; ok {
		if isupdate {
			avatars.UpdatedAt = now
			_, err := obj.WithReqOption(models.WithValues(map[string]interface{}{"items": avatars.Items, "updated_at": avatars.UpdatedAt})).Update(ctx).Result()
			if err != nil {
				logger.Error("Failed to update avatars for user_id %s: %v", user_id, err)
				return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
			}
		}
		return json.MarshalToString(&models.CommonResp{Code: models.DATA_EXIST, Msg: "avatar already exists"})
	}

	// 添加新的装扮
	items[reqAvatar.Avatar.AvatarId] = reqAvatar.Avatar

	avatars.UpdatedAt = now
	avatars.Items, err = json.MarshalToString(items)
	if err != nil {
		logger.Error("Failed to marshal avatars items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}
	var resp *models.DBAgentResp
	obj.WithReqOption(models.WithValues(map[string]interface{}{"items": avatars.Items, "updated_at": avatars.UpdatedAt}))
	if isInsert {
		resp, err = obj.Insert(ctx).Result()
	} else {
		resp, err = obj.Update(ctx).Result()
	}
	if err != nil {
		logger.Error("Failed to update avatars for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}
	if resp.RowsAffected > 0 {
		// nothing to do
	} else {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "update failed"})
	}

	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "success"})
}

// @Summary 删除装扮
// @Description 删除装扮
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqAvatarOpt true "请求参数"
// @Success 200 {object} models.CommonResp "返回结果"
// @Router /v2/rpc/userserver.avatar.delete [post]
func (c *Controller) AvatarDelete(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if c.server.common.GetUserID(ctx) != "" {
		logger.Error("session not allowed, add avatar")
		return json.MarshalToString(&models.CommonResp{Code: models.ACCESS_DENIED, Msg: "session not allowed"})
	}

	var reqAvatar ReqAvatarOpt
	err := json.Unmarshal([]byte(payload), &reqAvatar)
	if err != nil {
		logger.Error("Failed to unmarshal avatar delete: %v", err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}

	if reqAvatar.UserId == "" || reqAvatar.Avatar == nil {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "param is invalid"})
	}

	user_id := reqAvatar.UserId
	avatars := bo.Avatars{}
	obj := c.server.dbagent.Create(ctx, &avatars, models.WithKeys(map[string]interface{}{avatars.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx).As(&avatars)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query avatars for user_id %s: %v", user_id, err)
			return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
		}
		avatars.Items = "{}"
	}
	items := map[int64]*Avatar{} // item_id -> item
	err = json.Unmarshal([]byte(avatars.Items), &items)
	if err != nil {
		logger.Error("Failed to unmarshal avatars items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}

	// 检查装扮是否存在
	if _, ok := items[reqAvatar.Avatar.AvatarId]; !ok {
		return json.MarshalToString(&models.CommonResp{Code: models.DATA_NOT_EXIST, Msg: "avatar not exists"})
	}

	// 检查装扮是否过期
	now := time.Now()
	for itemId, item := range items {
		if item.ExpireTime > 0 && item.ExpireTime < now.Unix() {
			delete(items, itemId)
		}
	}

	// 添加新的装扮
	delete(items, reqAvatar.Avatar.AvatarId)

	avatars.UpdatedAt = now
	avatars.Items, err = json.MarshalToString(items)
	if err != nil {
		logger.Error("Failed to marshal avatars items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}
	resp, err := obj.WithReqOption(models.WithValues(map[string]interface{}{"items": avatars.Items, "updated_at": avatars.UpdatedAt})).Update(ctx).Result()
	if err != nil {
		logger.Error("Failed to update avatars for user_id %s: %v", user_id, err)
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: err.Error()})
	}
	if resp.RowsAffected > 0 {
		// nothing to do
	} else {
		return json.MarshalToString(&models.CommonResp{Code: models.FAILED, Msg: "update failed"})
	}

	return json.MarshalToString(&models.CommonResp{Code: models.OK, Msg: "success"})
}

// @Summary 查询装扮
// @Description 查询装扮
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqAvatarOpt true "请求参数"
// @Success 200 {object} RespAvatarQuery "返回结果"
// @Router /v2/rpc/userserver.avatar.query [post]
func (c *Controller) AvatarQuery(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if c.server.common.GetUserID(ctx) != "" {
		logger.Error("session not allowed, query avatar")
		return json.MarshalToString(&RespAvatarQuery{Code: int64(models.ACCESS_DENIED), Message: "session not allowed", Data: nil})
	}

	var reqAvatar ReqAvatarOpt
	err := json.Unmarshal([]byte(payload), &reqAvatar)
	if err != nil {
		logger.Error("Failed to unmarshal avatar query: %v", err)
		return json.MarshalToString(&RespAvatarQuery{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	if reqAvatar.UserId == "" || reqAvatar.Avatar == nil {
		return json.MarshalToString(&RespAvatarQuery{Code: int64(models.FAILED), Message: "param is invalid", Data: nil})
	}

	user_id := reqAvatar.UserId
	avatars := bo.Avatars{}
	obj := c.server.dbagent.Create(ctx, &avatars, models.WithKeys(map[string]interface{}{avatars.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx).As(&avatars)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query avatars for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespAvatarQuery{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
		}
		avatars.Items = "{}"
	}
	items := map[int64]*Avatar{} // item_id -> item
	err = json.Unmarshal([]byte(avatars.Items), &items)
	if err != nil {
		logger.Error("Failed to unmarshal avatars items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespAvatarQuery{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	// 检查装扮是否存在
	if _, ok := items[reqAvatar.Avatar.AvatarId]; !ok {
		return json.MarshalToString(&RespAvatarQuery{Code: int64(models.DATA_NOT_EXIST), Message: "avatar not exists", Data: nil})
	} else {
		return json.MarshalToString(&RespAvatarQuery{Code: int64(models.OK), Message: "success", Data: items[reqAvatar.Avatar.AvatarId]})
	}
}

// @Summary 更新装扮
// @Description 更新装扮
// @Tags userserver
// @Accept json
// @Produce json
// @Param request body ReqAvatarOpt true "请求参数"
// @Success 200 {object} RespAvatarUpdate "返回结果"
// @Router /v2/rpc/userserver.avatar.update [post]
func (c *Controller) AvatarUpdate(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if c.server.common.GetUserID(ctx) != "" {
		logger.Error("session not allowed, update avatar")
		return json.MarshalToString(&RespAvatarUpdate{Code: int64(models.ACCESS_DENIED), Message: "session not allowed", Data: nil})
	}

	var reqAvatar ReqAvatarOpt
	err := json.Unmarshal([]byte(payload), &reqAvatar)
	if err != nil {
		logger.Error("Failed to unmarshal avatar update: %v", err)
		return json.MarshalToString(&RespAvatarUpdate{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	if reqAvatar.UserId == "" || reqAvatar.Avatar == nil {
		return json.MarshalToString(&RespAvatarUpdate{Code: int64(models.FAILED), Message: "param is invalid", Data: nil})
	}

	user_id := reqAvatar.UserId
	avatars := bo.Avatars{}
	obj := c.server.dbagent.Create(ctx, &avatars, models.WithKeys(map[string]interface{}{avatars.GetKeyName(): user_id})).WithOption(logic.WithAutoLock())
	defer func() {
		c.server.dbagent.Release(obj)
	}()
	err = obj.Query(ctx).As(&avatars)
	if err != nil {
		if err != models.ErrDBResEmpty {
			logger.Error("Failed to query avatars for user_id %s: %v", user_id, err)
			return json.MarshalToString(&RespAvatarUpdate{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
		}
		avatars.Items = "{}"
	}
	items := map[int64]*Avatar{} // item_id -> item
	err = json.Unmarshal([]byte(avatars.Items), &items)
	if err != nil {
		logger.Error("Failed to unmarshal avatars items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespAvatarUpdate{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}

	// 检查装扮是否存在
	if _, ok := items[reqAvatar.Avatar.AvatarId]; !ok {
		return json.MarshalToString(&RespAvatarUpdate{Code: int64(models.DATA_NOT_EXIST), Message: "avatar not exists", Data: nil})
	}

	// 检查装扮是否过期
	now := time.Now()
	for itemId, item := range items {
		if item.ExpireTime > 0 && item.ExpireTime < now.Unix() {
			delete(items, itemId)
		}
	}

	// 更新新的装扮
	items[reqAvatar.Avatar.AvatarId].ExpireTime = reqAvatar.Avatar.ExpireTime

	avatars.UpdatedAt = now
	avatars.Items, err = json.MarshalToString(items)
	if err != nil {
		logger.Error("Failed to marshal avatars items for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespAvatarUpdate{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}
	resp, err := obj.WithReqOption(models.WithValues(map[string]interface{}{"items": avatars.Items, "updated_at": avatars.UpdatedAt})).Update(ctx).Result()
	if err != nil {
		logger.Error("Failed to update avatars for user_id %s: %v", user_id, err)
		return json.MarshalToString(&RespAvatarUpdate{Code: int64(models.FAILED), Message: err.Error(), Data: nil})
	}
	if resp.RowsAffected > 0 {
		// nothing to do
	} else {
		return json.MarshalToString(&RespAvatarUpdate{Code: int64(models.FAILED), Message: "update failed", Data: nil})
	}

	return json.MarshalToString(&RespAvatarUpdate{Code: int64(models.OK), Message: "success", Data: items[reqAvatar.Avatar.AvatarId]})
}
