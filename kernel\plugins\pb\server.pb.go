// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.29.1
// source: server.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VaultInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         *uint32 `protobuf:"varint,1,opt,name=Id,proto3,oneof" json:"Id,omitempty"`
	ExpireTime *int64  `protobuf:"varint,2,opt,name=ExpireTime,proto3,oneof" json:"ExpireTime,omitempty"`
	Count      *int64  `protobuf:"varint,3,opt,name=count,proto3,oneof" json:"count,omitempty"`
}

func (x *VaultInfo) Reset() {
	*x = VaultInfo{}
	mi := &file_server_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VaultInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VaultInfo) ProtoMessage() {}

func (x *VaultInfo) ProtoReflect() protoreflect.Message {
	mi := &file_server_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VaultInfo.ProtoReflect.Descriptor instead.
func (*VaultInfo) Descriptor() ([]byte, []int) {
	return file_server_proto_rawDescGZIP(), []int{0}
}

func (x *VaultInfo) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *VaultInfo) GetExpireTime() int64 {
	if x != nil && x.ExpireTime != nil {
		return *x.ExpireTime
	}
	return 0
}

func (x *VaultInfo) GetCount() int64 {
	if x != nil && x.Count != nil {
		return *x.Count
	}
	return 0
}

// 仓库列表
type VaultList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*VaultInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *VaultList) Reset() {
	*x = VaultList{}
	mi := &file_server_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VaultList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VaultList) ProtoMessage() {}

func (x *VaultList) ProtoReflect() protoreflect.Message {
	mi := &file_server_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VaultList.ProtoReflect.Descriptor instead.
func (*VaultList) Descriptor() ([]byte, []int) {
	return file_server_proto_rawDescGZIP(), []int{1}
}

func (x *VaultList) GetList() []*VaultInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type SkinInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         *uint32 `protobuf:"varint,1,opt,name=Id,proto3,oneof" json:"Id,omitempty"`
	ExpireTime *int64  `protobuf:"varint,2,opt,name=ExpireTime,proto3,oneof" json:"ExpireTime,omitempty"`
}

func (x *SkinInfo) Reset() {
	*x = SkinInfo{}
	mi := &file_server_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SkinInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkinInfo) ProtoMessage() {}

func (x *SkinInfo) ProtoReflect() protoreflect.Message {
	mi := &file_server_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkinInfo.ProtoReflect.Descriptor instead.
func (*SkinInfo) Descriptor() ([]byte, []int) {
	return file_server_proto_rawDescGZIP(), []int{2}
}

func (x *SkinInfo) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *SkinInfo) GetExpireTime() int64 {
	if x != nil && x.ExpireTime != nil {
		return *x.ExpireTime
	}
	return 0
}

// 皮肤列表
type SkinList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*SkinInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *SkinList) Reset() {
	*x = SkinList{}
	mi := &file_server_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SkinList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkinList) ProtoMessage() {}

func (x *SkinList) ProtoReflect() protoreflect.Message {
	mi := &file_server_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkinList.ProtoReflect.Descriptor instead.
func (*SkinList) Descriptor() ([]byte, []int) {
	return file_server_proto_rawDescGZIP(), []int{3}
}

func (x *SkinList) GetList() []*SkinInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type AvatarInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         *uint32 `protobuf:"varint,1,opt,name=Id,proto3,oneof" json:"Id,omitempty"`
	ExpireTime *int64  `protobuf:"varint,2,opt,name=ExpireTime,proto3,oneof" json:"ExpireTime,omitempty"`
}

func (x *AvatarInfo) Reset() {
	*x = AvatarInfo{}
	mi := &file_server_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvatarInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvatarInfo) ProtoMessage() {}

func (x *AvatarInfo) ProtoReflect() protoreflect.Message {
	mi := &file_server_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvatarInfo.ProtoReflect.Descriptor instead.
func (*AvatarInfo) Descriptor() ([]byte, []int) {
	return file_server_proto_rawDescGZIP(), []int{4}
}

func (x *AvatarInfo) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *AvatarInfo) GetExpireTime() int64 {
	if x != nil && x.ExpireTime != nil {
		return *x.ExpireTime
	}
	return 0
}

// avatar列表
type AvatarList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*AvatarInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *AvatarList) Reset() {
	*x = AvatarList{}
	mi := &file_server_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvatarList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvatarList) ProtoMessage() {}

func (x *AvatarList) ProtoReflect() protoreflect.Message {
	mi := &file_server_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvatarList.ProtoReflect.Descriptor instead.
func (*AvatarList) Descriptor() ([]byte, []int) {
	return file_server_proto_rawDescGZIP(), []int{5}
}

func (x *AvatarList) GetList() []*AvatarInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type MountInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         *uint32 `protobuf:"varint,1,opt,name=Id,proto3,oneof" json:"Id,omitempty"`
	ExpireTime *int64  `protobuf:"varint,2,opt,name=ExpireTime,proto3,oneof" json:"ExpireTime,omitempty"`
}

func (x *MountInfo) Reset() {
	*x = MountInfo{}
	mi := &file_server_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MountInfo) ProtoMessage() {}

func (x *MountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_server_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MountInfo.ProtoReflect.Descriptor instead.
func (*MountInfo) Descriptor() ([]byte, []int) {
	return file_server_proto_rawDescGZIP(), []int{6}
}

func (x *MountInfo) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *MountInfo) GetExpireTime() int64 {
	if x != nil && x.ExpireTime != nil {
		return *x.ExpireTime
	}
	return 0
}

// 坐骑列表
type MountList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*MountInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *MountList) Reset() {
	*x = MountList{}
	mi := &file_server_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MountList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MountList) ProtoMessage() {}

func (x *MountList) ProtoReflect() protoreflect.Message {
	mi := &file_server_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MountList.ProtoReflect.Descriptor instead.
func (*MountList) Descriptor() ([]byte, []int) {
	return file_server_proto_rawDescGZIP(), []int{7}
}

func (x *MountList) GetList() []*MountInfo {
	if x != nil {
		return x.List
	}
	return nil
}

var File_server_proto protoreflect.FileDescriptor

var file_server_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02,
	0x70, 0x62, 0x22, 0x80, 0x01, 0x0a, 0x09, 0x56, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x13, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x02,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0a, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x88, 0x01, 0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x49, 0x64, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x2e, 0x0a, 0x09, 0x56, 0x61, 0x75, 0x6c, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x21, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x70, 0x62, 0x2e, 0x56, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x5a, 0x0a, 0x08, 0x53, 0x6b, 0x69, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x13, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52,
	0x02, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0a, 0x45, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f,
	0x49, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x22, 0x2c, 0x0a, 0x08, 0x53, 0x6b, 0x69, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x62,
	0x2e, 0x53, 0x6b, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22,
	0x5c, 0x0a, 0x0a, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x13, 0x0a,
	0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x02, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x23, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x49, 0x64, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x30, 0x0a,
	0x0a, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x62, 0x2e, 0x41,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22,
	0x5b, 0x0a, 0x09, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x13, 0x0a, 0x02,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x02, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x23, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x49, 0x64, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x2e, 0x0a, 0x09,
	0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x62, 0x2e, 0x4d, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x42, 0x0c, 0x5a, 0x0a,
	0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_server_proto_rawDescOnce sync.Once
	file_server_proto_rawDescData = file_server_proto_rawDesc
)

func file_server_proto_rawDescGZIP() []byte {
	file_server_proto_rawDescOnce.Do(func() {
		file_server_proto_rawDescData = protoimpl.X.CompressGZIP(file_server_proto_rawDescData)
	})
	return file_server_proto_rawDescData
}

var file_server_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_server_proto_goTypes = []any{
	(*VaultInfo)(nil),  // 0: pb.VaultInfo
	(*VaultList)(nil),  // 1: pb.VaultList
	(*SkinInfo)(nil),   // 2: pb.SkinInfo
	(*SkinList)(nil),   // 3: pb.SkinList
	(*AvatarInfo)(nil), // 4: pb.AvatarInfo
	(*AvatarList)(nil), // 5: pb.AvatarList
	(*MountInfo)(nil),  // 6: pb.MountInfo
	(*MountList)(nil),  // 7: pb.MountList
}
var file_server_proto_depIdxs = []int32{
	0, // 0: pb.VaultList.list:type_name -> pb.VaultInfo
	2, // 1: pb.SkinList.list:type_name -> pb.SkinInfo
	4, // 2: pb.AvatarList.list:type_name -> pb.AvatarInfo
	6, // 3: pb.MountList.list:type_name -> pb.MountInfo
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_server_proto_init() }
func file_server_proto_init() {
	if File_server_proto != nil {
		return
	}
	file_server_proto_msgTypes[0].OneofWrappers = []any{}
	file_server_proto_msgTypes[2].OneofWrappers = []any{}
	file_server_proto_msgTypes[4].OneofWrappers = []any{}
	file_server_proto_msgTypes[6].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_server_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_server_proto_goTypes,
		DependencyIndexes: file_server_proto_depIdxs,
		MessageInfos:      file_server_proto_msgTypes,
	}.Build()
	File_server_proto = out.File
	file_server_proto_rawDesc = nil
	file_server_proto_goTypes = nil
	file_server_proto_depIdxs = nil
}
