package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/logic"
	"kernel/plugins/models"
	"sync"

	"go.uber.org/zap/zapcore"
)

type SessionServerModule struct {
	name            string
	logger          runtime.Logger
	db              *sql.DB
	nk              runtime.NakamaModule
	config          runtime.Config // nakama 配置
	CustomConfig    *CustomConfig  // 自定义配置
	isRegisterSelf  bool
	ProxyPassConfig sync.Map
	controller      *Controller

	common  *logic.CommonGlobalDataStruct
	online  *logic.OnlineGlobalDataStruct
	send    *logic.SendGlobalDataStruct
	dbagent *logic.DbAgentGlobalDataStruct
	notify  *logic.NotifyGlobalDataStruct
}

var SessionServerData *SessionServerModule

func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
	SessionServerData = new(SessionServerModule)
	SessionServerData.name = models.SERVER_NAME_SESSION
	SessionServerData.logger = logger
	SessionServerData.db = db
	SessionServerData.nk = nk
	SessionServerData.common = logic.NewCommonGlobalDataStruct()
	SessionServerData.send = logic.NewSendGlobalDataStruct(SessionServerData)
	SessionServerData.dbagent = logic.NewDbAgentGlobalDataStruct(SessionServerData)
	SessionServerData.online = logic.NewOnlineGlobalDataStruct(SessionServerData)
	SessionServerData.notify = logic.NewNotifyGlobalDataStruct(SessionServerData)
	config, err := initializer.GetConfig()
	if err != nil {
		return err
	}
	SessionServerData.config = config
	if err := SessionServerData.common.Init(SessionServerData, SessionServerData.CustomConfig, SessionServerData.customConfigEvent); err != nil {
		logger.Error("init common logic error %v", err)
		return err
	}

	initializer.RegisterShutdown(SessionServerData.Shutdown)

	// 自定义路由注册
	SessionServerData.controller = NewController(SessionServerData)
	initializer.RegisterRpc(models.RPCID_SESSIONSERVER_SESSION_ONLINE, SessionServerData.controller.SessionOnline)
	initializer.RegisterRpc(models.RPCID_SESSIONSERVER_SESSION_OFFLINE, SessionServerData.controller.SessionOffline)
	initializer.RegisterRpc(models.RPCID_SESSIONSERVER_SESSION_BATCHOFFLINE, SessionServerData.controller.SessionBatchOffline)

	return nil
}

func (s *SessionServerModule) GetCommon() *logic.CommonGlobalDataStruct {
	return s.common
}
func (s *SessionServerModule) GetOnline() *logic.OnlineGlobalDataStruct {
	return s.online
}
func (s *SessionServerModule) GetSend() *logic.SendGlobalDataStruct {
	return s.send
}
func (s *SessionServerModule) GetDbAgent() *logic.DbAgentGlobalDataStruct {
	return s.dbagent
}
func (s *SessionServerModule) GetNotify() *logic.NotifyGlobalDataStruct {
	return s.notify
}
func (s *SessionServerModule) GetStream() *logic.StreamGlobalDataStruct {
	return nil
}
func (s *SessionServerModule) GetChannel() *logic.ChannelGlobalDataStruct {
	return nil
}
func (s *SessionServerModule) GetLogger() runtime.Logger {
	return s.logger
}
func (s *SessionServerModule) GetConfig() runtime.Config {
	return s.config
}
func (s *SessionServerModule) GetName() string {
	return s.name
}
func (s *SessionServerModule) GetDb() *sql.DB {
	return s.db
}
func (s *SessionServerModule) GetNk() runtime.NakamaModule {
	return s.nk
}

// 配置读取回调
func (s *SessionServerModule) customConfigEvent(dataId string, newobj any) {
	tmpobj, ok := newobj.(*CustomConfig)
	if !ok {
		return
	}
	if tmpobj.Config == nil {
		tmpobj.Config = &SessionServerConfig{}
	}
	defaultNodeCfg := false
	if tmpobj.Config.NodeConfig == nil {
		// 使用默认节点配置
		scheme := "http"
		if s.config.GetSocket().GetUseTLS() {
			scheme = "https"
		}
		tmpobj.Config.NodeConfig = &models.NodeConfig{
			NodeId:   s.config.GetName(),
			SvcGroup: fmt.Sprintf("%s@@%s", s.name, models.NACOS_DEFAULT_GROUP),
			Host:     fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			NetHost:  fmt.Sprintf("%s:%d", common.GetLocalIP(), s.config.GetSocket().GetPort()),
			AuthKey:  s.config.GetRuntime().GetHTTPKey(),
			Weight:   10,
			Scheme:   scheme,
		}
		defaultNodeCfg = true
	}
	tmp, _ := json.Marshal(tmpobj)
	s.logger.Debug("customConfigEvent defaultNodeCfg: %v, customCfg: %s", defaultNodeCfg, string(tmp))
	s.common.SetNodeConfig(tmpobj.Config.NodeConfig)
	s.common.WatchNodeList.InitWatchSvr(tmpobj.Config.WatchSvcGroupList, tmpobj.Config.NodeConfig.SvcGroup)
	s.CustomConfig = tmpobj

	// 注册服务
	selfcfg := map[string]interface{}{}
	if err := common.StrcutToMap(tmpobj.Config.NodeConfig, &selfcfg); err == nil {
		if !s.isRegisterSelf {
			go s.common.RegisterAndUpdateSelfToNacos(false, selfcfg)
			s.isRegisterSelf = true
		} else {
			go s.common.RegisterAndUpdateSelfToNacos(true, selfcfg)
		}
	} else {
		s.logger.Error("register self, config error %v", err)
	}

	s.common.UpdateAccessLogConfig(tmpobj.AccessLog)
	loglevel, err := zapcore.ParseLevel(tmpobj.Logger.Level)
	if err != nil {
		s.logger.Error("parse log level error %v", err)
	} else {
		s.nk.SetLoggerLevel(loglevel)
	}
}

func (s *SessionServerModule) Shutdown(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, reload bool) {
	logger.Info("%s shutdown", s.name)
	if !reload {
		s.common.UnRegisterSelfToNacos()
	}
}
