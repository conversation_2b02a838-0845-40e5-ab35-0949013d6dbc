package common

import "sync"

// 加权轮询负载均衡
type WRRServer struct {
	nodes []*WRRNode // 节点列表
	lock  sync.Mutex
}

type WRRNode struct {
	Host          string
	Weight        int
	CurrentWeight int
}

func NewWRRServer() *WRRServer {
	return new(WRRServer)
}

func (s *WRRServer) Set(node *WRRNode) {
	if node == nil || node.Host == "" {
		return
	}
	s.lock.Lock()
	defer s.lock.Unlock()
	if s.nodes == nil {
		s.nodes = []*WRRNode{node}
	} else {
		isfind := false
		for _, v := range s.nodes {
			if v.Host == node.Host && v.Weight == node.Weight {
				return
			}
			if v.Host == node.Host {
				v.Weight = node.Weight
				isfind = true
				break
			}
		}
		if !isfind {
			s.nodes = append(s.nodes, node)
		}
	}
}

func (s *WRRServer) Get() (n *WRRNode) {
	s.lock.Lock()
	defer s.lock.Unlock()
	allWeight := 0
	for _, server := range s.nodes {
		if server == nil {
			return nil
		}
		if server.Weight == 0 {
			continue
		}

		allWeight += server.Weight            // 计算总权重
		server.CurrentWeight += server.Weight // 当前权重加上权重

		if n == nil || server.CurrentWeight > n.CurrentWeight { // 如果最优节点不存在或者当前节点由于最优节点，则赋值或者替换
			n = server
		}
	}

	if n == nil {
		return nil
	}

	n.CurrentWeight -= allWeight

	return n
}

func (s *WRRServer) Del(host string) {
	if host == "" {
		return
	}
	s.lock.Lock()
	defer s.lock.Unlock()
	for i := 0; i < len(s.nodes); i++ {
		v := s.nodes[i]
		if v.Host == host {
			s.nodes = append(s.nodes[:i], s.nodes[i+1:]...)
			break
		}
	}
}
