<ngb-alert [dismissible]="false" type="danger" class="mb-3" *ngIf="error">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">An error occurred: {{error}}</h6>
</ngb-alert>

<ngb-alert [dismissible]="false" type="success" class="mb-3" *ngIf="updated">
  <img src="/static/svg/green-tick.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">Account was modified successfully.</h6>
</ngb-alert>

<form [formGroup]="accountForm" (ngSubmit)="updateAccount()" class="add-border">
  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="selected_device_id_index">Device IDs</label>
      </div>
      <div class="input-group">
        <button (click)="copyDeviceIdToClipboard(this.f.selected_device_id_index.value)" type="button" class="btn btn-outline-secondary">
          <img class="" src="/static/svg/copy.svg" alt="" width="16" height="">
          Copy
        </button>
        <select class="form-control custom-select custom-select-sm" id="selected_device_id_index" formControlName="selected_device_id_index">
          <option *ngFor="let d of account.devices; index as i" value="{{i}}" [selected]="i === 0">{{d.id}}</option>
        </select>
        <div class="input-group-append">
          <button [disabled]="!updateAllowed() || account.devices.length === 0" class="btn btn-sm btn-outline-danger" (click)="unlinkDeviceId($event)" type="button">Unlink</button>
        </div>
      </div>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="custom_id">Custom ID</label>
      </div>
      <div class="input-group">
        <input type="text" id="custom_id" [value]="account.custom_id" class="form-control form-control-sm" disabled>
        <div class="input-group-append">
          <button [disabled]="!updateAllowed() || !account.custom_id || account.custom_id === ''" class="btn btn-sm btn-outline-danger" (click)="unlinkCustomID($event)" type="button">Unlink</button>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="facebook_id">Facebook ID</label>
      </div>
      <div class="input-group">
        <input type="text" id="facebook_id" [value]="account.user.facebook_id" class="form-control form-control-sm" disabled>
        <div class="input-group-append">
          <button [disabled]="!updateAllowed() || !account.user.facebook_id || account.user.facebook_id === ''" class="btn btn-sm btn-outline-danger" (click)="unlinkFacebook($event)" type="button">Unlink</button>
        </div>
      </div>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline align-self-center">
      <div class="col-3 pl-0">
        <label class="d-inline" for="facebook_instant_game_id">FB Instant Game ID</label>
      </div>
      <div class="input-group">
        <input type="text" id="facebook_instant_game_id" [value]="account.user.facebook_instant_game_id" class="form-control form-control-sm" disabled>
        <div class="input-group-append">
          <button [disabled]="!updateAllowed() || !account.user.facebook_instant_game_id || account.user.facebook_instant_game_id === ''" class="btn btn-sm btn-outline-danger" (click)="unlinkFacebookInstantGames($event)" type="button">Unlink</button>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="apple_id">Apple ID</label>
      </div>
      <div class="input-group">
        <input type="text" id="apple_id" [value]="account.user.apple_id" class="form-control form-control-sm" disabled>
        <div class="input-group-append">
          <button [disabled]="!updateAllowed() || !account.user.apple_id || account.user.apple_id === ''" class="btn btn-sm btn-outline-danger" (click)="unlinkApple($event)" type="button">Unlink</button>
        </div>
      </div>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline align-self-center">
      <div class="col-3 pl-0">
        <label class="d-inline" for="gamecenter_id">GameCenter ID</label>
      </div>
      <div class="input-group">
        <input type="text" id="gamecenter_id" [value]="account.user.gamecenter_id" class="form-control form-control-sm" disabled>
        <div class="input-group-append">
          <button [disabled]="!updateAllowed() || !account.user.gamecenter_id || account.user.gamecenter_id === ''" class="btn btn-sm btn-outline-danger" (click)="unlinkGameCenter($event)" type="button">Unlink</button>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="google_id">Google ID</label>
      </div>
      <div class="input-group">
        <input type="text" id="google_id" [value]="account.user.google_id" class="form-control form-control-sm" disabled>
        <div class="input-group-append">
          <button [disabled]="!updateAllowed() || !account.user.google_id || account.user.google_id === ''" class="btn btn-sm btn-outline-danger" (click)="unlinkGoogle($event)" type="button">Unlink</button>
        </div>
      </div>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="steam_id">Steam ID</label>
      </div>
      <div class="input-group">
        <input type="text" id="steam_id" [value]="account.user.steam_id" class="form-control form-control-sm" disabled>
        <div class="input-group-append">
          <button  [disabled]="!updateAllowed() || !account.user.steam_id || account.user.steam_id === ''" class="btn btn-sm btn-outline-danger" (click)="unlinkSteam($event)" type="button">Unlink</button>
        </div>
      </div>
    </div>
  </div>

  <!-- <hr class="my-4" /> -->

  <div class="row">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="email">Email</label>
        <br />

      </div>
      <input type="text" id="email" class="form-control form-control-sm" placeholder="Email" formControlName="email">
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0">
        <label class="d-inline" for="password">Password</label>
      </div>
      <input type="text" id="password" class="form-control form-control-sm" placeholder="Password" formControlName="password">
    </div>
  </div>

  <div class="row remove-sides">
    <div class="col-md-6 d-flex justify-content-start align-items-baseline">
      <div class="col-3 pl-0"></div>
      <span class="small">Verification Status: {{this.account.verify_time === null ? 'Not Verified' : 'Verified'}}</span>
    </div>
    <div class="col-md-6 d-flex justify-content-start align-items-baseline"></div>
  </div>

  <button type="submit" class="btn btn-primary" [disabled]="updating" *ngIf="updateAllowed()">Save</button>
</form>
