// Code generated by swaggo/swag. DO NOT EDIT.

package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/v2/rpc/dbagentserver.delete": {
            "post": {
                "description": "数据库代理删除",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dbagentserver"
                ],
                "summary": "数据库代理删除",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DBAgentReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/models.DBAgentResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/dbagentserver.insert": {
            "post": {
                "description": "数据库代理插入",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dbagentserver"
                ],
                "summary": "数据库代理插入",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DBAgentReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/models.DBAgentResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/dbagentserver.query": {
            "post": {
                "description": "数据库代理查询",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dbagentserver"
                ],
                "summary": "数据库代理查询",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DBAgentReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/models.DBAgentResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/dbagentserver.rawexec": {
            "post": {
                "description": "数据库代理执行原始sql",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dbagentserver"
                ],
                "summary": "数据库代理执行原始sql",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DBAgentReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/models.DBAgentResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/dbagentserver.rawquery": {
            "post": {
                "description": "数据库代理执行原始sql，不支持联表查询(因为更新会失败)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dbagentserver"
                ],
                "summary": "数据库代理执行原始sql",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DBAgentReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/models.DBAgentResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/dbagentserver.update": {
            "post": {
                "description": "数据库代理更新",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dbagentserver"
                ],
                "summary": "数据库代理更新",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DBAgentReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/models.DBAgentResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/gateserver.boardcast": {
            "post": {
                "description": "广播数据",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "gateserver"
                ],
                "summary": "广播数据",
                "parameters": [
                    {
                        "description": "数据内容",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.CommonPush"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/gateserver.close": {
            "post": {
                "description": "关闭连接",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "gateserver"
                ],
                "summary": "关闭连接",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.ClosePush"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/gateserver.multicast": {
            "post": {
                "description": "多播数据",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "gateserver"
                ],
                "summary": "多播数据",
                "parameters": [
                    {
                        "description": "数据内容",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.CommonPush"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/gateserver.send": {
            "post": {
                "description": "发送数据",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "gateserver"
                ],
                "summary": "发送数据",
                "parameters": [
                    {
                        "description": "数据内容",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.CommonPush"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/imserver.party.detail": {
            "post": {
                "description": "获取用户组队详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Party"
                ],
                "summary": "获取用户组队详情",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.PartyDetailReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.PartyDetailResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/imserver.party.invite": {
            "post": {
                "description": "邀请加入队伍",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Party"
                ],
                "summary": "邀请加入队伍",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.PartyInviteReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/imserver.party.list": {
            "post": {
                "description": "获取用户组队列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Party"
                ],
                "summary": "获取用户组队列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "page_num",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.PartyListReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.PartyListResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/imserver.party.update": {
            "post": {
                "description": "修改组队信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Party"
                ],
                "summary": "修改组队信息",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.PartyUpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.PartyUpdateResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/logicserver.addfavoriteroom": {
            "post": {
                "description": "添加一个收藏",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "logicserver"
                ],
                "summary": "添加房间收藏",
                "parameters": [
                    {
                        "description": "房间ID",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功添加收藏",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/logicserver.addhistoryroom": {
            "post": {
                "description": "添加一个历史房间",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "logicserver"
                ],
                "summary": "添加历史房间",
                "parameters": [
                    {
                        "description": "房间ID",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功添加历史房间",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/logicserver.createrole": {
            "post": {
                "description": "根据用户ID创建一个新角色，如果角色已存在则返回失败",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "logicserver"
                ],
                "summary": "创建一个新角色",
                "parameters": [
                    {
                        "description": "角色数据，JSON格式",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功创建角色",
                        "schema": {
                            "$ref": "#/definitions/main.RespCreateRole"
                        }
                    }
                }
            }
        },
        "/v2/rpc/logicserver.delfavoriterooms": {
            "post": {
                "description": "删除收藏房间",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "logicserver"
                ],
                "summary": "删除收藏房间",
                "parameters": [
                    {
                        "description": "房间ID列表 json",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功删除收藏",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/logicserver.delhistoryrooms": {
            "post": {
                "description": "删除历史房间",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "logicserver"
                ],
                "summary": "删除历史房间",
                "parameters": [
                    {
                        "description": "房间ID列表",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功删除历史房间",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/logicserver.getfavoriterooms": {
            "post": {
                "description": "获取用户的收藏列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "logicserver"
                ],
                "summary": "获取收藏房间列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/main.RespGetFavoriteRooms"
                        }
                    }
                }
            }
        },
        "/v2/rpc/logicserver.gethistoryrooms": {
            "post": {
                "description": "获取用户的历史列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "logicserver"
                ],
                "summary": "获取历史房间列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/main.RespGetHistoryRooms"
                        }
                    }
                }
            }
        },
        "/v2/rpc/logicserver.getuploadurl": {
            "post": {
                "description": "获取一个上传文件的url",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "logicserver"
                ],
                "summary": "获取一个上传文件的url",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.GetUploadURLReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/main.GetUploadURLResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/logicserver.loginfinish": {
            "post": {
                "description": "登录完成",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "logicserver"
                ],
                "summary": "登录完成",
                "responses": {
                    "200": {
                        "description": "响应结果",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/logicserver.queryreport": {
            "post": {
                "description": "查询举报",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "logicserver"
                ],
                "summary": "查询举报",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.QueryReportReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/main.QueryReportResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/logicserver.querystatus": {
            "post": {
                "description": "查询用户状态 logicserver.querystatus",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "logicserver"
                ],
                "summary": "查询用户状态",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqQueryStatus"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespQueryStatus"
                        }
                    }
                }
            }
        },
        "/v2/rpc/logicserver.report": {
            "post": {
                "description": "举报",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "logicserver"
                ],
                "summary": "举报",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReportReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/main.ReportResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/logicserver.reportcallback": {
            "post": {
                "description": "举报回调",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "logicserver"
                ],
                "summary": "举报回调",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReportCallbackReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/main.ReportCallbackResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/loginserver.auth.email": {
            "post": {
                "description": "自定义验证邮箱",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "summary": "自定义验证邮箱",
                "parameters": [
                    {
                        "description": "账号信息",
                        "name": "account",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.AccountEmail"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespAuthenticateEmail"
                        }
                    }
                }
            }
        },
        "/v2/rpc/loginserver.auth.emailcode": {
            "post": {
                "description": "验证邮箱验证码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "summary": "验证邮箱验证码",
                "parameters": [
                    {
                        "description": "账号信息",
                        "name": "account",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.AccountEmail"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "验证结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespAuthenticateEmail"
                        }
                    }
                }
            }
        },
        "/v2/rpc/loginserver.createaccount.email": {
            "post": {
                "description": "根据邮箱注册账号，不需要验证邮箱，一般给运营后台调用",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "summary": "根据邮箱注册账号",
                "parameters": [
                    {
                        "description": "账号信息",
                        "name": "account",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.AccountEmail"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建结果",
                        "schema": {
                            "$ref": "#/definitions/main.AccountEmailResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/loginserver.queryloginlogs": {
            "post": {
                "description": "查询用户登录记录",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "summary": "查询用户登录记录",
                "parameters": [
                    {
                        "description": "查询条件",
                        "name": "account",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqQueryLoginLogs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespQueryLoginLogs"
                        }
                    }
                }
            }
        },
        "/v2/rpc/queueserver.getqueuestatus": {
            "post": {
                "description": "获取排队状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "queueserver"
                ],
                "summary": "获取排队状态",
                "parameters": [
                    {
                        "description": "获取排队状态请求",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqGetQueueStatus"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/main.RespGetQueueStatus"
                        }
                    }
                }
            }
        },
        "/v2/rpc/queueserver.joinqueue": {
            "post": {
                "description": "加入排队",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "queueserver"
                ],
                "summary": "加入排队",
                "parameters": [
                    {
                        "description": "加入排队请求",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqJoinQueue"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/main.RespJoinQueue"
                        }
                    }
                }
            }
        },
        "/v2/rpc/queueserver.leavequeue": {
            "post": {
                "description": "离开排队",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "queueserver"
                ],
                "summary": "离开排队",
                "parameters": [
                    {
                        "description": "离开排队请求",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqLeaveQueue"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/main.ReqLeaveQueue"
                        }
                    }
                }
            }
        },
        "/v2/rpc/sessionserver.session.batchoffline": {
            "post": {
                "description": "批量下线,网关关闭的时候调用",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "sessionserver"
                ],
                "summary": "批量下线",
                "responses": {
                    "200": {
                        "description": "响应结果",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/sessionserver.session.offline": {
            "post": {
                "description": "用户下线",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "sessionserver"
                ],
                "summary": "用户下线",
                "parameters": [
                    {
                        "description": "用户下线信息",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UserOnlineInfo"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "响应结果",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/sessionserver.session.online": {
            "post": {
                "description": "用户上线",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "sessionserver"
                ],
                "summary": "用户上线",
                "parameters": [
                    {
                        "description": "用户上线信息",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UserOnlineInfo"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "响应结果",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/statusserver.querybyuid": {
            "post": {
                "description": "根据uid查询在线信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "summary": "根据uid查询用户在线信息",
                "parameters": [
                    {
                        "description": "查询用户状态请求,多个uid用逗号分隔，例如: 1,2,3",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询用户状态响应",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "$ref": "#/definitions/models.UserOnlineInfo"
                            }
                        }
                    }
                }
            }
        },
        "/v2/rpc/statusserver.querybyuin": {
            "post": {
                "description": "根据uin查询用户在线信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "summary": "根据uin查询用户在线信息",
                "parameters": [
                    {
                        "description": "查询用户状态请求,多个uin用逗号分隔，例如: 1,2,3",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询用户状态响应",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "$ref": "#/definitions/models.UserOnlineInfo"
                            }
                        }
                    }
                }
            }
        },
        "/v2/rpc/statusserver.queryonlinebyuid": {
            "post": {
                "description": "根据uid查询是否在线",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "summary": "根据uid查询用户是否在线",
                "parameters": [
                    {
                        "description": "查询用户状态请求,多个uid用逗号分隔，例如: 1,2,3",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询用户状态响应",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "boolean"
                            }
                        }
                    }
                }
            }
        },
        "/v2/rpc/statusserver.queryonlinebyuin": {
            "post": {
                "description": "根据uin查询是否在线",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "summary": "根据uin查询用户是否在线",
                "parameters": [
                    {
                        "description": "多个uin用逗号分隔，例如: 1,2,3",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询用户状态响应",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "boolean"
                            }
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.avatar.add": {
            "post": {
                "description": "添加装扮",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "添加装扮",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqAvatarOpt"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.avatar.delete": {
            "post": {
                "description": "删除装扮",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "删除装扮",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqAvatarOpt"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.avatar.list": {
            "post": {
                "description": "获取装扮列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "获取装扮列表",
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespAvatarList"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.avatar.query": {
            "post": {
                "description": "查询装扮",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "查询装扮",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqAvatarOpt"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespAvatarQuery"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.avatar.update": {
            "post": {
                "description": "更新装扮",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "更新装扮",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqAvatarOpt"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespAvatarUpdate"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.ban": {
            "post": {
                "description": "封禁用户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "封禁用户",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqBanUser"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.modify": {
            "post": {
                "description": "修改用户信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "修改用户信息",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqModifyBase"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.modifygender": {
            "post": {
                "description": "修改用户性别",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "修改用户性别",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqModifyGender"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.mount.add": {
            "post": {
                "description": "添加坐骑",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "添加坐骑",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqMountOpt"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.mount.delete": {
            "post": {
                "description": "删除坐骑",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "删除坐骑",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqMountOpt"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.mount.list": {
            "post": {
                "description": "获取坐骑列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "获取坐骑列表",
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespMountList"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.mount.query": {
            "post": {
                "description": "查询坐骑",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "查询坐骑",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqMountOpt"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespMountQuery"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.mount.update": {
            "post": {
                "description": "更新坐骑",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "更新坐骑",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqMountOpt"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespMountUpdate"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.query": {
            "post": {
                "description": "查询用户信息 userserver.query",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "查询用户信息",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqQueryBase"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespQueryBase"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.skin.add": {
            "post": {
                "description": "添加皮肤",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "添加皮肤",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqSkinOpt"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.skin.delete": {
            "post": {
                "description": "删除皮肤",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "删除皮肤",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqSkinOpt"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.skin.list": {
            "post": {
                "description": "获取皮肤列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "获取皮肤列表",
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespSkinList"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.skin.query": {
            "post": {
                "description": "查询皮肤",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "查询皮肤",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqSkinOpt"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespSkinQuery"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.skin.update": {
            "post": {
                "description": "更新皮肤",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "更新皮肤",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqSkinOpt"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespSkinUpdate"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.storage.delete": {
            "post": {
                "description": "删除仓库中指定物品",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "删除仓库中指定物品",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqVaultsDelete"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespVaultsDelete"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.storage.deposit": {
            "post": {
                "description": "存入指定物品列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "存入指定物品列表",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqVaultsDeposit"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespVaultsDeposit"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.storage.expand": {
            "post": {
                "description": "仓库扩容",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "仓库扩容",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqVaultsExpand"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespVaultsExpand"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.storage.extract": {
            "post": {
                "description": "提取指定物品列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "提取指定物品列表",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqVaultsExtract"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespVaultsExtract"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.storage.info": {
            "post": {
                "description": "获取仓库状态及物品详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "获取仓库状态及物品详情",
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespVaultsList"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.unban": {
            "post": {
                "description": "解封用户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "解封用户",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqUnbanUser"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResp"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.wallet.add": {
            "post": {
                "description": "添加钻石，金币",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "添加钻石，金币",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqWalletAdd"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespWallet"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.wallet.query": {
            "post": {
                "description": "获取钱包信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "获取钱包信息",
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespWallet"
                        }
                    }
                }
            }
        },
        "/v2/rpc/userserver.wallet.sub": {
            "post": {
                "description": "扣除钻石，金币",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "userserver"
                ],
                "summary": "扣除钻石，金币",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/main.ReqWalletSub"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回结果",
                        "schema": {
                            "$ref": "#/definitions/main.RespWallet"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "main.AccountEmail": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "vars": {
                    "description": "nick, clientip",
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                }
            }
        },
        "main.AccountEmailResp": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/models.ErrorCode"
                },
                "msg": {
                    "type": "string"
                }
            }
        },
        "main.Avatar": {
            "type": "object",
            "properties": {
                "avatar_id": {
                    "type": "integer"
                },
                "expire_time": {
                    "type": "integer"
                }
            }
        },
        "main.EvidenceInfo": {
            "type": "object",
            "properties": {
                "evidence_type": {
                    "description": "证据类型",
                    "type": "integer"
                },
                "evidence_url": {
                    "description": "证据链接地址",
                    "type": "string"
                }
            }
        },
        "main.Favorite": {
            "type": "object",
            "properties": {
                "time": {
                    "description": "收藏时间",
                    "type": "integer"
                }
            }
        },
        "main.GetUploadURLReq": {
            "type": "object",
            "properties": {
                "files": {
                    "description": "上传文件信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/main.UploadFileInfo"
                    }
                }
            }
        },
        "main.GetUploadURLResp": {
            "type": "object",
            "properties": {
                "error_code": {
                    "description": "错误码",
                    "type": "integer"
                },
                "error_msg": {
                    "description": "错误信息",
                    "type": "string"
                },
                "urls": {
                    "description": "上传URL",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "main.History": {
            "type": "object",
            "properties": {
                "time": {
                    "description": "时间",
                    "type": "integer"
                }
            }
        },
        "main.LoginLog": {
            "type": "object",
            "properties": {
                "device": {
                    "type": "string"
                },
                "ip_address": {
                    "type": "string"
                },
                "login_time": {
                    "type": "string"
                },
                "metadata": {
                    "type": "string"
                },
                "uin": {
                    "type": "integer"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "main.Mount": {
            "type": "object",
            "properties": {
                "expire_time": {
                    "type": "integer"
                },
                "mount_id": {
                    "type": "integer"
                }
            }
        },
        "main.PartyDetailData": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "desc": {
                    "description": "招募描述",
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "labels": {
                    "description": "招募标签",
                    "type": "string"
                },
                "leader": {
                    "type": "string"
                },
                "members": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Presence"
                    }
                }
            }
        },
        "main.PartyDetailReq": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                }
            }
        },
        "main.PartyDetailResp": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/main.PartyDetailData"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.PartyInviteReq": {
            "type": "object",
            "properties": {
                "uin": {
                    "type": "string"
                }
            }
        },
        "main.PartyListReq": {
            "type": "object",
            "properties": {
                "page_num": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                }
            }
        },
        "main.PartyListResp": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/main.PartyDetailData"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.PartyUpdateReq": {
            "type": "object",
            "properties": {
                "desc": {
                    "type": "string"
                },
                "labels": {
                    "type": "string"
                },
                "open": {
                    "description": "是否公开",
                    "type": "boolean"
                }
            }
        },
        "main.PartyUpdateResp": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.PlayerWallet": {
            "type": "object",
            "properties": {
                "diamond": {
                    "description": "购买获得，钻石数量",
                    "type": "integer"
                },
                "diamond_gift": {
                    "description": "非购买获得钻石数量",
                    "type": "integer"
                },
                "gold": {
                    "description": "金币数量",
                    "type": "integer"
                }
            }
        },
        "main.QueryReportReq": {
            "type": "object",
            "properties": {
                "begin_time": {
                    "description": "举报时间（可选），不填则默认最近一周",
                    "type": "string"
                },
                "client_version": {
                    "description": "客户端版本",
                    "type": "string"
                },
                "end_time": {
                    "description": "举报时间（可选），不填则默认最近一周",
                    "type": "string"
                },
                "is_appealable": {
                    "description": "是否可申诉",
                    "type": "integer"
                },
                "limit": {
                    "description": "返回的行数（limit）：表示最多返回多少行数据。",
                    "type": "integer"
                },
                "offset": {
                    "description": "偏移量（offset）：表示从结果集的哪个位置开始返回数据，0 表示第一行。",
                    "type": "integer"
                },
                "report_id": {
                    "description": "举报请求的唯一ID，该id设置后，其他参数自动失效",
                    "type": "string"
                },
                "report_type": {
                    "description": "举报类型",
                    "type": "integer"
                },
                "reported_id": {
                    "description": "被举报人玩家ID",
                    "type": "string"
                },
                "reporter_id": {
                    "description": "举报人玩家ID",
                    "type": "string"
                },
                "reporter_region": {
                    "description": "区服标识",
                    "type": "string"
                },
                "status": {
                    "description": "处理状态",
                    "type": "integer"
                },
                "violation_type": {
                    "description": "违规类型",
                    "type": "string"
                }
            }
        },
        "main.QueryReportResp": {
            "type": "object",
            "properties": {
                "error_code": {
                    "description": "错误码",
                    "type": "integer"
                },
                "error_msg": {
                    "description": "错误信息",
                    "type": "string"
                },
                "report_record_item": {
                    "description": "举报记录列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/main.ReportRecordItem"
                    }
                },
                "total": {
                    "description": "总行数",
                    "type": "integer"
                }
            }
        },
        "main.QueryStatusData": {
            "type": "object",
            "properties": {
                "uids": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "boolean"
                    }
                },
                "uins": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "boolean"
                    }
                }
            }
        },
        "main.QueueStatus": {
            "type": "object",
            "properties": {
                "rank": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "main.ReportCallbackReq": {
            "type": "object",
            "properties": {
                "is_appealable": {
                    "description": "是否可申诉",
                    "type": "boolean"
                },
                "punishment_hours": {
                    "description": "惩罚时长",
                    "type": "integer"
                },
                "punishment_id": {
                    "description": "惩罚类型",
                    "type": "string"
                },
                "report_id": {
                    "description": "举报请求的唯一ID，该id设置后，其他参数自动失效",
                    "type": "string"
                },
                "reviewer_id": {
                    "description": "审核人ID",
                    "type": "string"
                },
                "status": {
                    "description": "处理状态,0：无需处理,1: 未处理,2: 已经处理",
                    "type": "integer"
                },
                "violation_type": {
                    "description": "违规类型",
                    "type": "string"
                }
            }
        },
        "main.ReportCallbackResp": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "错误码",
                    "type": "integer"
                },
                "message": {
                    "description": "错误信息",
                    "type": "string"
                }
            }
        },
        "main.ReportRecordItem": {
            "type": "object",
            "properties": {
                "client_version": {
                    "description": "客户端版本",
                    "type": "string"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "description": {
                    "description": "问题描述",
                    "type": "string"
                },
                "evidence_infos": {
                    "description": "证据截图、视频链接（可选）",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/main.EvidenceInfo"
                    }
                },
                "handled_at": {
                    "description": "审核处理时间",
                    "type": "string"
                },
                "is_appealable": {
                    "description": "是否可申诉",
                    "type": "boolean"
                },
                "punishment_hours": {
                    "description": "惩罚时长",
                    "type": "integer"
                },
                "punishment_id": {
                    "description": "惩罚类型",
                    "type": "string"
                },
                "report_id": {
                    "description": "举报请求的唯一ID，该id设置后，其他参数自动失效",
                    "type": "string"
                },
                "report_source": {
                    "description": "举报来源",
                    "type": "string"
                },
                "report_type": {
                    "description": "举报类型",
                    "type": "integer"
                },
                "reported_id": {
                    "description": "被举报人玩家ID",
                    "type": "string"
                },
                "reporter_id": {
                    "description": "举报人玩家ID",
                    "type": "string"
                },
                "reporter_region": {
                    "description": "区服标识",
                    "type": "string"
                },
                "status": {
                    "description": "处理状态",
                    "type": "integer"
                },
                "violation_type": {
                    "description": "违规类型",
                    "type": "string"
                }
            }
        },
        "main.ReportReq": {
            "type": "object",
            "properties": {
                "client_version": {
                    "description": "客户端版本（可选）",
                    "type": "string"
                },
                "created_at": {
                    "description": "举报时间（可选，不填则以入库时间为准）",
                    "type": "string"
                },
                "description": {
                    "description": "问题描述",
                    "type": "string"
                },
                "evidence_infos": {
                    "description": "证据截图、视频链接（可选）",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/main.EvidenceInfo"
                    }
                },
                "report_source": {
                    "description": "举报来源(game, website等)",
                    "type": "string"
                },
                "report_type": {
                    "description": "举报类型",
                    "type": "integer"
                },
                "reported_id": {
                    "description": "被举报人玩家ID",
                    "type": "string"
                },
                "reporter_id": {
                    "description": "举报人玩家ID",
                    "type": "string"
                },
                "reporter_region": {
                    "description": "区服标识（可选）",
                    "type": "string"
                }
            }
        },
        "main.ReportResp": {
            "type": "object",
            "properties": {
                "error_code": {
                    "description": "错误码",
                    "type": "integer"
                },
                "error_msg": {
                    "description": "错误信息",
                    "type": "string"
                },
                "report_id": {
                    "description": "举报请求的唯一ID",
                    "type": "string"
                }
            }
        },
        "main.ReqAvatarOpt": {
            "type": "object",
            "properties": {
                "avatar": {
                    "$ref": "#/definitions/main.Avatar"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "main.ReqBanUser": {
            "type": "object",
            "properties": {
                "reason": {
                    "description": "封禁原因",
                    "type": "string"
                },
                "time": {
                    "description": "封禁时长，单位秒",
                    "type": "integer"
                },
                "uids": {
                    "description": "多个用户ID",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "main.ReqGetQueueStatus": {
            "type": "object",
            "properties": {
                "room_id": {
                    "type": "string"
                }
            }
        },
        "main.ReqJoinQueue": {
            "type": "object",
            "properties": {
                "room_id": {
                    "type": "string"
                }
            }
        },
        "main.ReqLeaveQueue": {
            "type": "object",
            "properties": {
                "room_id": {
                    "type": "string"
                }
            }
        },
        "main.ReqModifyBase": {
            "type": "object",
            "properties": {
                "fields": {
                    "description": "字段名看models.User内的json tag {\"name\":\"张三\",\"age\":18}",
                    "type": "object",
                    "additionalProperties": true
                },
                "user_id": {
                    "description": "用户ID",
                    "type": "string"
                }
            }
        },
        "main.ReqModifyGender": {
            "type": "object",
            "properties": {
                "gender": {
                    "description": "性别",
                    "type": "integer"
                },
                "user_id": {
                    "description": "用户ID",
                    "type": "string"
                }
            }
        },
        "main.ReqMountOpt": {
            "type": "object",
            "properties": {
                "mount": {
                    "$ref": "#/definitions/main.Mount"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "main.ReqQueryBase": {
            "type": "object",
            "properties": {
                "fields": {
                    "description": "查询时可选，如果*则查询所有字段, 字段名看models.User内的json tag",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "page": {
                    "description": "页码，查询所有用户时有效",
                    "type": "integer"
                },
                "size": {
                    "description": "每页数量，查询所有用户时有效",
                    "type": "integer"
                },
                "uids": {
                    "description": "多个用户ID, 如果*则查询所有用户",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "uins": {
                    "description": "多个Uins",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "main.ReqQueryLoginLogs": {
            "type": "object",
            "properties": {
                "end_time": {
                    "description": "结束时间",
                    "type": "integer"
                },
                "page_num": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "sort": {
                    "description": "排序方式 asc, desc",
                    "type": "string"
                },
                "start_time": {
                    "description": "开始时间",
                    "type": "integer"
                },
                "uin": {
                    "type": "integer"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "main.ReqQueryStatus": {
            "type": "object",
            "properties": {
                "uids": {
                    "description": "多个用户ID",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "uins": {
                    "description": "多个Uins",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "main.ReqSkinOpt": {
            "type": "object",
            "properties": {
                "skin": {
                    "$ref": "#/definitions/main.Skin"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "main.ReqUnbanUser": {
            "type": "object",
            "properties": {
                "uids": {
                    "description": "多个用户ID",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "main.ReqVaultsDelete": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/main.VaultItem"
                    }
                }
            }
        },
        "main.ReqVaultsDeposit": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/main.VaultItem"
                    }
                }
            }
        },
        "main.ReqVaultsExpand": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                }
            }
        },
        "main.ReqVaultsExtract": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/main.VaultItem"
                    }
                }
            }
        },
        "main.ReqWalletAdd": {
            "type": "object",
            "properties": {
                "diamond": {
                    "description": "添加购买获得钻石数量",
                    "type": "integer"
                },
                "diamond_gift": {
                    "description": "赠送获得钻石数量",
                    "type": "integer"
                },
                "gold": {
                    "description": "添加金币数量",
                    "type": "integer"
                },
                "reason": {
                    "description": "添加原因",
                    "type": "string"
                },
                "user_id": {
                    "description": "用户ID",
                    "type": "string"
                }
            }
        },
        "main.ReqWalletSub": {
            "type": "object",
            "properties": {
                "diamond": {
                    "description": "扣除购买获得钻石数量",
                    "type": "integer"
                },
                "diamond_gift": {
                    "description": "扣除赠送获得钻石数量",
                    "type": "integer"
                },
                "gold": {
                    "description": "扣除金币数量",
                    "type": "integer"
                },
                "reason": {
                    "description": "扣除原因",
                    "type": "string"
                },
                "user_id": {
                    "description": "用户ID",
                    "type": "string"
                }
            }
        },
        "main.RespAuthenticateEmail": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/models.ErrorCode"
                },
                "created": {
                    "type": "boolean"
                },
                "message": {
                    "type": "string"
                },
                "refresh_token": {
                    "type": "string"
                },
                "token": {
                    "type": "string"
                }
            }
        },
        "main.RespAvatarList": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "description": "pb string",
                    "type": "string"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespAvatarQuery": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/main.Avatar"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespAvatarUpdate": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/main.Avatar"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespCreateRole": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/models.ErrorCode"
                },
                "data": {
                    "$ref": "#/definitions/main.RoleInfo"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespGetFavoriteRooms": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/models.ErrorCode"
                },
                "data": {
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/main.Favorite"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespGetHistoryRooms": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/models.ErrorCode"
                },
                "data": {
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/main.History"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespGetQueueStatus": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/models.ErrorCode"
                },
                "data": {
                    "$ref": "#/definitions/main.QueueStatus"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespJoinQueue": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/models.ErrorCode"
                },
                "data": {
                    "$ref": "#/definitions/main.QueueStatus"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespMountList": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "description": "pb string",
                    "type": "string"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespMountQuery": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/main.Mount"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespMountUpdate": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/main.Mount"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespQueryBase": {
            "type": "object",
            "properties": {
                "total": {
                    "description": "总数量",
                    "type": "integer"
                },
                "users": {
                    "description": "key：用户id",
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/models.User"
                    }
                }
            }
        },
        "main.RespQueryLoginLogs": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/models.ErrorCode"
                },
                "logs": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/main.LoginLog"
                    }
                },
                "message": {
                    "type": "string"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "main.RespQueryStatus": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/models.ErrorCode"
                },
                "data": {
                    "$ref": "#/definitions/main.QueryStatusData"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespSkinList": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "description": "pb string",
                    "type": "string"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespSkinQuery": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/main.Skin"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespSkinUpdate": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/main.Skin"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespVaultsDelete": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/models.ErrorCode"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespVaultsDeposit": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/models.ErrorCode"
                },
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/main.VaultItem"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespVaultsExpand": {
            "type": "object",
            "properties": {
                "capacity": {
                    "type": "integer"
                },
                "code": {
                    "$ref": "#/definitions/models.ErrorCode"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespVaultsExtract": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/models.ErrorCode"
                },
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/main.VaultItem"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespVaultsList": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "description": "pb string",
                    "type": "string"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RespWallet": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/main.PlayerWallet"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "main.RoleInfo": {
            "type": "object",
            "properties": {
                "last_login_time": {
                    "type": "integer"
                },
                "role_data": {
                    "type": "string"
                },
                "role_id": {
                    "type": "integer"
                }
            }
        },
        "main.Skin": {
            "type": "object",
            "properties": {
                "expire_time": {
                    "type": "integer"
                },
                "skin_id": {
                    "type": "integer"
                }
            }
        },
        "main.UploadFileInfo": {
            "type": "object",
            "properties": {
                "file_md5": {
                    "description": "文件MD5",
                    "type": "string"
                },
                "file_name": {
                    "description": "文件名",
                    "type": "string"
                },
                "file_size": {
                    "description": "文件大小",
                    "type": "integer"
                }
            }
        },
        "main.VaultItem": {
            "type": "object",
            "properties": {
                "bind_status": {
                    "type": "integer"
                },
                "count": {
                    "type": "integer"
                },
                "expire_time": {
                    "type": "integer"
                },
                "item_id": {
                    "type": "integer"
                },
                "slot_id": {
                    "type": "integer"
                }
            }
        },
        "models.CacheLevel": {
            "type": "integer",
            "enum": [
                1,
                2
            ],
            "x-enum-comments": {
                "CACHE_LEVEL_IMPORT": "重要",
                "CACHE_LEVEL_NORMAL": "正常"
            },
            "x-enum-varnames": [
                "CACHE_LEVEL_NORMAL",
                "CACHE_LEVEL_IMPORT"
            ]
        },
        "models.ClosePush": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "object"
                },
                "reason": {
                    "type": "integer",
                    "enum": [
                        0,
                        1,
                        2,
                        3,
                        4
                    ]
                },
                "sessions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "models.CommonPush": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "object"
                },
                "sessions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "models.CommonResp": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/models.ErrorCode"
                },
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        },
        "models.DBAgentReq": {
            "type": "object",
            "properties": {
                "cachelevel": {
                    "description": "缓存级别",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.CacheLevel"
                        }
                    ]
                },
                "errdiscard": {
                    "description": "写db失败是否丢弃写",
                    "type": "boolean"
                },
                "flushtype": {
                    "description": "刷db类型",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.FlushType"
                        }
                    ]
                },
                "keys": {
                    "description": "键",
                    "type": "object",
                    "additionalProperties": true
                },
                "lockid": {
                    "description": "远程锁上下文id",
                    "type": "integer"
                },
                "lockkey": {
                    "description": "远程锁的键",
                    "type": "string"
                },
                "lockopt": {
                    "description": "远程锁操作类型",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.LockOpt"
                        }
                    ]
                },
                "locktimeout": {
                    "description": "远程锁，等待加锁超时时间，毫秒为单位",
                    "type": "integer"
                },
                "notoptcache": {
                    "description": "不操作缓存(false:操作缓存，true:不操作缓存)",
                    "type": "boolean"
                },
                "queryargsql": {
                    "description": "查询参数sql",
                    "type": "string"
                },
                "rowresulttype": {
                    "description": "结果集类型",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.RowResultType"
                        }
                    ]
                },
                "rowssecondname": {
                    "description": "多行结果集二级key",
                    "type": "string"
                },
                "table": {
                    "description": "数据库表名",
                    "type": "string"
                },
                "ttl": {
                    "description": "过期时间，单位秒",
                    "type": "integer"
                },
                "uniquekeys": {
                    "description": "唯一键字段名",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "values": {
                    "description": "值",
                    "type": "object",
                    "additionalProperties": true
                },
                "versionname": {
                    "description": "版本检查字段名，如果为空，则不进行版本检查，只支持单行结果集数据，版本字段类型为int64",
                    "type": "string"
                }
            }
        },
        "models.DBAgentResp": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/models.ErrorCode"
                },
                "data": {},
                "lastinsertid": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                },
                "rowsaffected": {
                    "type": "integer"
                }
            }
        },
        "models.ErrorCode": {
            "type": "integer",
            "enum": [
                0,
                21,
                22,
                23,
                24,
                25,
                26,
                27,
                28,
                29,
                30,
                31,
                32,
                33
            ],
            "x-enum-comments": {
                "AUTH_CODE": "告诉客户端需要验证码",
                "AUTH_ERR": "验证错误",
                "DATA_EXIST": "数据已存在",
                "DATA_NOT_EXIST": "数据不存在",
                "REPEAT_REG": "重复注册"
            },
            "x-enum-varnames": [
                "OK",
                "TIMEOUT",
                "FAILED",
                "BUSY",
                "EXCEPTION",
                "PARAM_ERR",
                "LOCK_ERR",
                "SESSION_ERR",
                "ACCESS_DENIED",
                "AUTH_CODE",
                "AUTH_ERR",
                "REPEAT_REG",
                "DATA_EXIST",
                "DATA_NOT_EXIST"
            ]
        },
        "models.FlushType": {
            "type": "integer",
            "enum": [
                0,
                1,
                2,
                3
            ],
            "x-enum-comments": {
                "FLUSH_TYPE_ASYNC": "立即异步回写",
                "FLUSH_TYPE_DEL": "删除回写",
                "FLUSH_TYPE_SYNC": "立即同步回写，默认值",
                "FLUSH_TYPE_TIMER": "定时回写"
            },
            "x-enum-varnames": [
                "FLUSH_TYPE_SYNC",
                "FLUSH_TYPE_ASYNC",
                "FLUSH_TYPE_TIMER",
                "FLUSH_TYPE_DEL"
            ]
        },
        "models.LockOpt": {
            "type": "integer",
            "enum": [
                0,
                1,
                2,
                11,
                12
            ],
            "x-enum-comments": {
                "LOCKOPT_LOCK_READ": "读锁",
                "LOCKOPT_LOCK_WRITE": "写锁",
                "LOCKOPT_NONE": "无锁",
                "LOCKOPT_UNLOCK_READ": "解读锁",
                "LOCKOPT_UNLOCK_WRITE": "解写锁"
            },
            "x-enum-varnames": [
                "LOCKOPT_NONE",
                "LOCKOPT_LOCK_READ",
                "LOCKOPT_LOCK_WRITE",
                "LOCKOPT_UNLOCK_READ",
                "LOCKOPT_UNLOCK_WRITE"
            ]
        },
        "models.Presence": {
            "type": "object",
            "properties": {
                "id": {
                    "$ref": "#/definitions/models.PresenceID"
                },
                "meta": {
                    "$ref": "#/definitions/models.PresenceMeta"
                },
                "stream": {
                    "$ref": "#/definitions/models.PresenceStream"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "models.PresenceID": {
            "type": "object",
            "properties": {
                "node": {
                    "type": "string"
                },
                "session_id": {
                    "type": "string"
                }
            }
        },
        "models.PresenceMeta": {
            "type": "object",
            "properties": {
                "format": {
                    "$ref": "#/definitions/models.SessionFormat"
                },
                "hidden": {
                    "type": "boolean"
                },
                "persistence": {
                    "type": "boolean"
                },
                "reason": {
                    "type": "integer"
                },
                "status": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "models.PresenceStream": {
            "type": "object",
            "properties": {
                "label": {
                    "type": "string"
                },
                "mode": {
                    "type": "integer"
                },
                "subcontext": {
                    "type": "string"
                },
                "subject": {
                    "type": "string"
                }
            }
        },
        "models.RowResultType": {
            "type": "string",
            "enum": [
                "onlyone",
                "multi"
            ],
            "x-enum-comments": {
                "ROWRESULT_TYPE_MULTI": "多行",
                "ROWRESULT_TYPE_ONLYONE": "单行"
            },
            "x-enum-varnames": [
                "ROWRESULT_TYPE_ONLYONE",
                "ROWRESULT_TYPE_MULTI"
            ]
        },
        "models.SessionFormat": {
            "type": "integer",
            "enum": [
                0,
                1
            ],
            "x-enum-varnames": [
                "SessionFormatJson",
                "SessionFormatProtobuf"
            ]
        },
        "models.User": {
            "type": "object",
            "properties": {
                "apple_id": {
                    "description": "The Apple Sign In ID in the user's account.",
                    "type": "string"
                },
                "avatar_url": {
                    "description": "A URL for an avatar image.",
                    "type": "string"
                },
                "create_time": {
                    "description": "The UNIX time (for gRPC clients) or ISO string (for REST clients) when the user was created.",
                    "type": "string",
                    "format": "date-time"
                },
                "disable_time": {
                    "description": "DisableTime",
                    "type": "string",
                    "format": "date-time"
                },
                "display_name": {
                    "description": "The display name of the user.",
                    "type": "string"
                },
                "edge_count": {
                    "description": "Number of related edges to this user.",
                    "type": "integer"
                },
                "facebook_id": {
                    "description": "The Facebook id in the user's account.",
                    "type": "string"
                },
                "facebook_instant_game_id": {
                    "description": "The Facebook Instant Game ID in the user's account.",
                    "type": "string"
                },
                "gamecenter_id": {
                    "description": "The Apple Game Center in of the user's account.",
                    "type": "string"
                },
                "google_id": {
                    "description": "The Google id in the user's account.",
                    "type": "string"
                },
                "id": {
                    "description": "The id of the user's account.",
                    "type": "string"
                },
                "lang_tag": {
                    "description": "The language expected to be a tag which follows the BCP-47 spec.",
                    "type": "string"
                },
                "location": {
                    "description": "The location set by the user.",
                    "type": "string"
                },
                "metadata": {
                    "description": "Additional information stored as a JSON object.",
                    "type": "string"
                },
                "online": {
                    "description": "Indicates whether the user is currently online.",
                    "type": "boolean"
                },
                "steam_id": {
                    "description": "The Steam id in the user's account.",
                    "type": "string"
                },
                "timezone": {
                    "description": "The timezone set by the user.",
                    "type": "string"
                },
                "update_time": {
                    "description": "The UNIX time (for gRPC clients) or ISO string (for REST clients) when the user was last updated.",
                    "type": "string",
                    "format": "date-time"
                },
                "username": {
                    "description": "The username of the user's account.",
                    "type": "string"
                }
            }
        },
        "models.UserOnlineInfo": {
            "type": "object",
            "properties": {
                "apiid": {
                    "description": "api id",
                    "type": "integer"
                },
                "client_ip": {
                    "description": "客户端ip",
                    "type": "string"
                },
                "cltversion": {
                    "description": "客户端版本",
                    "type": "integer"
                },
                "node_addr": {
                    "description": "网关节点地址",
                    "type": "string"
                },
                "node_id": {
                    "description": "网关节点id",
                    "type": "string"
                },
                "online_time": {
                    "description": "在线时间",
                    "type": "integer"
                },
                "session_id": {
                    "description": "会话id",
                    "type": "string"
                },
                "uin": {
                    "description": "用户uin",
                    "type": "integer"
                },
                "user_id": {
                    "description": "用户id",
                    "type": "string"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "",
	Description:      "",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
