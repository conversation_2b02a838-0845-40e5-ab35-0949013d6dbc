package main

import (
	"context"
	"database/sql"
	"fmt"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/common/obs"
	"kernel/plugins/models"
	"math/rand"
	"time"

	json "github.com/json-iterator/go"
)

// @Summary 获取一个上传文件的url
// @Description 获取一个上传文件的url
// @Tags logicserver
// @Accept json
// @Produce json
// @Param payload body GetUploadURLReq true "请求参数"
// @Success 200 {object} GetUploadURLResp
// @Router /v2/rpc/logicserver.getuploadurl [post]
func (c *Controller) GetUploadURL(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uin := c.server.common.GetUin(ctx)
	if uin == 0 {
		return json.MarshalToString(&GetUploadURLResp{ErrorCode: int(models.SESSION_ERR), ErrorMsg: "session error"})
	}

	var req GetUploadURLReq
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&GetUploadURLResp{ErrorCode: int(models.PARAM_ERR), ErrorMsg: "json unmarshal failed"})
	}

	if len(req.UploadFileInfos) == 0 {
		return json.MarshalToString(&GetUploadURLResp{ErrorCode: int(models.PARAM_ERR), ErrorMsg: "upload file infos is empty"})
	}
	for _, fileInfo := range req.UploadFileInfos {
		if fileInfo.FileName == "" {
			return json.MarshalToString(&GetUploadURLResp{ErrorCode: int(models.PARAM_ERR), ErrorMsg: "file name is empty"})
		}
		if fileInfo.FileSize == 0 {
			return json.MarshalToString(&GetUploadURLResp{ErrorCode: int(models.PARAM_ERR), ErrorMsg: "file size is empty"})
		}
		if fileInfo.FileMD5 == "" || len(fileInfo.FileMD5) != 32 {
			return json.MarshalToString(&GetUploadURLResp{ErrorCode: int(models.PARAM_ERR), ErrorMsg: "file md5 is error"})
		}
	}

	expiresSeconds := 600
	nowdate := time.Now().Format("200601") // 年月
	obsCfg := c.server.CustomConfig.Config.ObsCfg
	obsClient := obs.NewOSClient(obsCfg.ObsType, &obs.OSConfig{
		AccessKey: obsCfg.AccessKey,
		Secretkey: obsCfg.SecretKey,
		Endpoint:  obsCfg.EndPoint,
		Bucket:    obsCfg.Bucket,
	})
	urls := make([]string, 0)
	for _, fileInfo := range req.UploadFileInfos {
		objectKey := fmt.Sprintf("report/%s/%d_%s", nowdate, uin, fileInfo.FileMD5)
		url := obsClient.GetSafeUploadURL(objectKey, expiresSeconds, int64(fileInfo.FileSize), fileInfo.FileMD5)
		urls = append(urls, url)
	}

	return json.MarshalToString(&GetUploadURLResp{ErrorCode: 0, ErrorMsg: "ok", URLs: urls})
}

// @Summary 举报回调
// @Description 举报回调
// @Tags logicserver
// @Accept json
// @Produce json
// @Param payload body ReportCallbackReq true "请求参数"
// @Success 200 {object} ReportCallbackResp
// @Router /v2/rpc/logicserver.reportcallback [post]
func (c *Controller) ReportCallback(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var req ReportCallbackReq
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&ReportCallbackResp{Code: int(models.PARAM_ERR), Message: "json unmarshal failed"})
	}

	// 处理举报

	return json.MarshalToString(&ReportCallbackResp{Code: int(models.OK), Message: "ok"})
}

// @Summary 举报
// @Description 举报
// @Tags logicserver
// @Accept json
// @Produce json
// @Param payload body ReportReq true "请求参数"
// @Success 200 {object} ReportResp
// @Router /v2/rpc/logicserver.report [post]
func (c *Controller) Report(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uid := c.server.common.GetUserID(ctx)
	if uid == "" {
		return json.MarshalToString(&ReportResp{ErrorCode: int(models.SESSION_ERR), ErrorMsg: "session error"})
	}

	var req ReportReq
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&ReportResp{ErrorCode: int(models.PARAM_ERR), ErrorMsg: "json unmarshal failed"})
	}
	if req.ReportType == 0 {
		return json.MarshalToString(&ReportResp{ErrorCode: int(models.PARAM_ERR), ErrorMsg: "report type is empty"})
	}
	if req.ReportedID == "" {
		return json.MarshalToString(&ReportResp{ErrorCode: int(models.PARAM_ERR), ErrorMsg: "reported id is empty"})
	}

	req.ReporterID = uid
	req.ReportSource = "game"
	req.CreatedAt = time.Now().Format("2006-01-02 15:04:05")

	mapreq := map[string]interface{}{}
	common.StrcutToMap(&req, &mapreq)

	report_cfg := c.server.CustomConfig.Config.ReportCfg
	headers := map[string]string{
		"app_id":           report_cfg.AppID,
		"timestamp":        fmt.Sprintf("%d", time.Now().Unix()),
		"nonce":            fmt.Sprintf("%d", rand.Intn(10000000)),
		"signature_method": "MD5",
	}
	headers["signature"] = ReportSign(report_cfg.Secret, headers, mapreq)
	headers["Content-Type"] = "application/json"

	senddata, _ := json.Marshal(&req)
	_url := fmt.Sprintf("%s/api/v1/report", report_cfg.Url)
	logger.Debug("report url: %s, data: %s", _url, string(senddata))
	resp, err := common.HttpPost(context.TODO(), _url, senddata, headers, 3*time.Second)
	if err != nil {
		return json.MarshalToString(&ReportResp{ErrorCode: int(models.FAILED), ErrorMsg: err.Error()})
	}
	var resp_data ReportResp
	if err := json.Unmarshal(resp, &resp_data); err != nil {
		return json.MarshalToString(&ReportResp{ErrorCode: int(models.FAILED), ErrorMsg: err.Error()})
	}
	return json.MarshalToString(&resp_data)
}

// @Summary 查询举报
// @Description 查询举报
// @Tags logicserver
// @Accept json
// @Produce json
// @Param payload body QueryReportReq true "请求参数"
// @Success 200 {object} QueryReportResp
// @Router /v2/rpc/logicserver.queryreport [post]
func (c *Controller) QueryReport(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	uid := c.server.common.GetUserID(ctx)
	if uid == "" {
		return json.MarshalToString(&QueryReportResp{ErrorCode: int(models.SESSION_ERR), ErrorMsg: "session error"})
	}

	var req QueryReportReq
	if err := json.Unmarshal([]byte(payload), &req); err != nil {
		return json.MarshalToString(&QueryReportResp{ErrorCode: int(models.PARAM_ERR), ErrorMsg: "json unmarshal failed"})
	}

	mapreq := map[string]interface{}{}
	common.StrcutToMap(&req, &mapreq)

	report_cfg := c.server.CustomConfig.Config.ReportCfg
	headers := map[string]string{
		"app_id":           report_cfg.AppID,
		"timestamp":        fmt.Sprintf("%d", time.Now().Unix()),
		"nonce":            fmt.Sprintf("%d", rand.Intn(10000000)),
		"signature_method": "MD5",
	}
	headers["signature"] = ReportSign(report_cfg.Secret, headers, mapreq)
	headers["Content-Type"] = "application/json"

	senddata, _ := json.Marshal(&req)
	_url := fmt.Sprintf("%s/api/v1/query_report", report_cfg.Url)
	logger.Debug("query report url: %s, data: %s", _url, string(senddata))
	resp, err := common.HttpPost(ctx, _url, senddata, headers, 3*time.Second)
	if err != nil {
		return json.MarshalToString(&QueryReportResp{ErrorCode: int(models.FAILED), ErrorMsg: err.Error()})
	}
	var resp_data QueryReportResp
	if err := json.Unmarshal(resp, &resp_data); err != nil {
		return json.MarshalToString(&QueryReportResp{ErrorCode: int(models.FAILED), ErrorMsg: err.Error()})
	}

	return json.MarshalToString(&resp_data)
}
