<ngb-alert [dismissible]="false" type="danger" class="mb-3" *ngIf="error">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">An error occurred: {{error}}</h6>
</ngb-alert>

<div class="row no-gutters mb-3">
  <div class="col d-flex justify-content-between no-gutters">
    <div class="col-md-9"></div>
    <div class="col-md-3 justify-content-end text-right">
      <div class="btn-group page-btns" role="group">
        <button type="button" class="btn btn-outline-secondary" (click)="loadRecords(0)" [disabled]="records.length === 0"><img src="/static/svg/page-first.svg" alt="" width="20" height=""></button>
        <button type="button" class="btn btn-outline-secondary" (click)="loadRecords(-1)" [disabled]="prevCursor === ''"><img src="/static/svg/page-prev.svg" alt="" width="20" height=""></button>
        <button type="button" class="btn btn-outline-secondary" (click)="loadRecords(1)" [disabled]="nextCursor === ''"><img src="/static/svg/page-next.svg" alt="" width="20" height=""></button>
      </div>
    </div>
  </div>
</div>

<div class="row no-gutters">
  <table class="table table-sm table-bordered">
    <thead class="thead-light">
      <tr>
        <th style="width: 380px">Owner ID</th>
        <th>Username</th>
        <th style="width: 150px">Score</th>
        <th style="width: 150px">Subscore</th>
        <th style="width: 150px">Rank</th>
        <th style="width: 150px">No. of scores</th>
        <th style="width: 180px">Expiry Time</th>
        <th style="width: 90px" *ngIf="deleteAllowed()">Remove</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngIf="records.length === 0">
        <td colSpan="8" class="text-muted">No records found.</td>
      </tr>

      <ng-template ngFor let-i="index" let-r [ngForOf]="records">
        <tr>
          <td>
            <div class="arrow" (click)="recordsMetadataOpen[i]=!recordsMetadataOpen[i]">
              <div class="arrow-right" *ngIf="!recordsMetadataOpen[i]"></div>
              <div class="arrow-down" *ngIf="recordsMetadataOpen[i]"></div>
            </div>
            {{r.owner_id}}
          </td>
          <td>{{r.username}}</td>
          <td>{{r.score}}</td>
          <td>{{r.subscore}}</td>
          <td>{{r.rank}}</td>
          <td>{{r.num_score}}</td>
          <td>{{r.expiry_time ? r.expiry_time : '-'}}</td>
          <td class="text-center" *ngIf="deleteAllowed()"><button type="button" class="btn btn-sm btn-danger" (click)="deleteRecord($event, i, r);">Delete</button></td>
        </tr>
        <tr *ngIf="recordsMetadataOpen[i]">
          <td colspan="7" class="align-middle"><pre class="pre-wrap m-0 p-0"><small>{{r.metadata}}</small></pre></td>
        </tr>
      </ng-template>
    </tbody>
  </table>
</div>
