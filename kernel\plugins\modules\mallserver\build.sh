#!/bin/bash

# 记录当前目录
CURRENT_DIR=$(pwd)

# cd到shell脚本所在目录
cd $(dirname $0)
OBJ=$(basename "$(pwd)").so
TARGET=${1:-default}

default() {
  go build -buildmode=plugin  -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn" -trimpath -o ./$OBJ
  rm -f ../../../data/modules/$OBJ
  rm -f ../../../data/logicserver/$OBJ
  cp ./$OBJ ../../../data/modules/ -f
  cp ./$OBJ ../../../data/logicserver/ -f
}

debug() {
  go build -buildmode=plugin  -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn" -gcflags "all=-N -l" -o ./$OBJ
  rm -f ../../../data/modules/$OBJ
  rm -f ../../../data/logicserver/$OBJ
  cp ./$OBJ ../../../data/modules/ -f
  cp ./$OBJ ../../../data/logicserver/ -f
}

test() {
  go build -buildmode=plugin  -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn" -trimpath -o ./$OBJ
  cp ./$OBJ ../../../data/test_env/mallserver/ -f
}

release() {
  go build -buildmode=plugin  -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn" -trimpath -o ./$OBJ
  cp ./$OBJ ../../../data/prod_env/mallserver/ -f
}

# Execute the specified target
$TARGET

# 回到当前目录
cd $CURRENT_DIR

