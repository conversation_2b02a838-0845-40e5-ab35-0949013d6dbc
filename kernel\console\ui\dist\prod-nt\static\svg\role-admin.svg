<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 24.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_extend "http://ns.adobe.com/Extensibility/1.0/">
	<!ENTITY ns_ai "http://ns.adobe.com/AdobeIllustrator/10.0/">
	<!ENTITY ns_graphs "http://ns.adobe.com/Graphs/1.0/">
	<!ENTITY ns_vars "http://ns.adobe.com/Variables/1.0/">
	<!ENTITY ns_imrep "http://ns.adobe.com/ImageReplacement/1.0/">
	<!ENTITY ns_sfw "http://ns.adobe.com/SaveForWeb/1.0/">
	<!ENTITY ns_custom "http://ns.adobe.com/GenericCustomNamespace/1.0/">
	<!ENTITY ns_adobe_xpath "http://ns.adobe.com/XPath/1.0/">
]>
<svg version="1.1" id="Layer_1" xmlns:x="&ns_extend;" xmlns:i="&ns_ai;" xmlns:graph="&ns_graphs;"
	 xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="30px" height="30px"
	 viewBox="0 0 30 30" style="enable-background:new 0 0 30 30;" xml:space="preserve">
<style type="text/css">
	.st0{fill-rule:evenodd;clip-rule:evenodd;fill:#7668ED;}
	.st1{fill:#FFFFFF;}
	.st2{fill:#7668ED;}
</style>
<metadata>
	<sfw  xmlns="&ns_sfw;">
		<slices></slices>
		<sliceSourceBounds  bottomLeftOrigin="true" height="30" width="30" x="0" y="-30"></sliceSourceBounds>
	</sfw>
</metadata>
<g>
	<circle class="st0" cx="15" cy="15" r="15"/>
	<path class="st1" d="M6.3,8.9c0.7,6.6,4,12.3,8.7,15.6c4.7-3.3,8-9,8.7-15.6c-3.4-0.6-6.5-1.8-8.7-3.5C12.8,7.1,9.8,8.4,6.3,8.9z"
		/>
	<g>
		<polygon class="st2" points="17.5,10.2 12.5,10.2 10.1,14.4 12.5,18.7 17.5,18.7 19.9,14.4 		"/>
		<circle class="st1" cx="15" cy="14.4" r="1.8"/>
	</g>
</g>
</svg>
