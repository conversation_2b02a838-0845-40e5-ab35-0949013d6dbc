<script src="status.component.ts"></script><h2 class="pb-4">Status</h2>

<ngb-alert *ngIf="error" type="danger" [dismissible]="false">
  <img src="/static/svg/red-triangle.svg" alt="" width="16" height="" class="mr-2">
  <h6 class="mr-2 d-inline font-weight-bold">An error occurred: {{error}}</h6>
</ngb-alert>

<table class="status-table table table-sm table-striped mb-4" (mouseover)="showDelta = true" (mouseout)="showDelta = false">
  <thead class="thead-light">
    <tr>
      <th scope="col">Node name</th>
      <th scope="col">Sessions</th>
      <th scope="col">Presences</th>
      <th scope="col">Authoritative Matches</th>
      <th scope="col">Goroutines</th>
    </tr>
  </thead>
  <tbody *ngIf="statusData">
    <tr class="clickable" *ngFor="let nodeData of statusData.nodes">
      <td>
        <ng-container [ngSwitch]="nodeData.health">
          <img *ngSwitchCase="0" class="mr-2" src="/static/svg/green-tick.svg" alt="" width="15" height="" ngbTooltip="OK">
          <img *ngSwitchCase="1" class="mr-2" src="/static/svg/red-triangle.svg" alt="" width="15" height="" ngbTooltip="Error">
          <img *ngSwitchCase="2" class="mr-2" src="/static/blue-spinner.svg" alt="" width="15" height="" ngbTooltip="Connecting">
          <img *ngSwitchCase="3" class="mr-2" src="/static/red-spinner.svg" alt="" width="15" height="" ngbTooltip="Disconnecting">
        </ng-container>
        <span>{{nodeData.name}}</span>
      </td>
      <td>{{nodeData.session_count}} <span [hidden]="!showDelta" class="text-muted small">({{getMaxSessionCount() - nodeData.session_count}} delta)</span></td>
      <td>{{nodeData.presence_count}} <span [hidden]="!showDelta" class="text-muted small">({{getMaxPresenceCount() - nodeData.presence_count}} delta)</span></td>
      <td>{{nodeData.match_count}} <span [hidden]="!showDelta" class="text-muted small">({{getMaxMatchCount() - nodeData.match_count}} delta)</span></td>
      <td>{{nodeData.goroutine_count}} <span [hidden]="!showDelta" class="text-muted small">({{getMaxGoroutineCount() - nodeData.goroutine_count}} delta)</span></td>
    </tr>
    <tr class="thead-light">
      <td class="border-0"></td>
      <th class="thead-light">{{getTotalSessionCount()}}</th>
      <th class="thead-light">{{getMaxPresenceCount()}}</th>
      <th class="thead-light">{{getTotalMatchCount()}}</th>
      <th class="thead-light">{{getTotalGorountineCount()}}</th>
    </tr>
  </tbody>
</table>

<form [formGroup]="rangeForm">
  <div class="row no-gutters justify-content-end">
    <div class="col-12 text-right">
      <span>View:</span>
      <div class="btn-group" ngbDropdown role="group">
        <select formControlName="rangeMinutes" class="custom-select custom-select-sm ml-3" (change)=setRange($event)>
          <option *ngFor="let key of rangesKeys | sortNumbers" value="{{key}}">{{ranges[key]}}</option>
        </select>
      </div>
    </div>
  </div>
</form>

<div class="row">
  <div class="col-6 d-inline-flex justify-content-between align-items-center">
    <div class="graph-title"><h5 class="d-inline">Processing Latency</h5><small> (ms)</small></div>
  </div>
  <div class="col-6 d-inline-flex justify-content-between align-items-center">
    <div class="graph-title"><h5 class="d-inline">Rate</h5><small> (rpc/s)</small></div>
  </div>
</div>

<div class="row">
  <div class="col-6">
    <div class="graph" *ngIf="latencyGraphData.length > 0">
      <ngx-charts-line-chart
        [scheme]="colorScheme"
        [legend]="false"
        [showXAxisLabel]="false"
        [showYAxisLabel]="false"
        [animations]="false"
        [timeline]="false"
        [autoScale]="false"
        [xAxis]="true"
        [yAxis]="true"
        xAxisLabel="Time"
        yAxisLabel="Latency (ms)"
        [yScaleMin]="0"
        [roundDomains]="true"
        [results]="latencyGraphData">
        <ng-template #seriesTooltipTemplate let-model="model">
          <div class="chart-legend">
            {{model[0].name.toLocaleString()}}
            <span *ngFor="let g of model">
              <br/>
              <span class="legend-label-color" [ngStyle]="{'background-color': g.color}"></span>
              <span class="legend-label-text">{{g.series}}: {{g.value}}</span>
            </span>
          </div>
        </ng-template>
      </ngx-charts-line-chart>
    </div>
  </div>
  <div class="col-6">
    <div class="graph" *ngIf="rateGraphData.length > 0">
      <ngx-charts-line-chart
        [scheme]="colorScheme"
        [legend]="false"
        [showXAxisLabel]="false"
        [showYAxisLabel]="false"
        [animations]="false"
        [timeline]="false"
        [autoScale]="false"
        [xAxis]="true"
        [yAxis]="true"
        xAxisLabel="Time"
        yAxisLabel="Request Count"
        [yScaleMin]="0"
        [roundDomains]="true"
        [results]="rateGraphData">
        <ng-template #seriesTooltipTemplate let-model="model">
          <div class="chart-legend">
            {{model[0].name.toLocaleString()}}
            <span *ngFor="let g of model">
                <br/>
                <span class="legend-label-color" [ngStyle]="{'background-color': g.color}"></span>
                <span class="legend-label-text">{{g.series}}: {{g.value}}</span>
              </span>
          </div>
        </ng-template>
      </ngx-charts-line-chart>
    </div>
  </div>
</div>


<div class="row mt-4">
  <div class="col-6 d-inline-flex justify-content-between align-items-center">
    <div class="graph-title"><h5 class="d-inline">Input</h5><small> (kb/s)</small></div>
  </div>
  <div class="col-6 d-inline-flex justify-content-between align-items-center">
    <div class="graph-title"><h5 class="d-inline">Output</h5><small> (kb/s)</small></div>
  </div>
</div>

<div class="row">
  <div class="col-6">
    <div class="graph" *ngIf="inputGraphData.length > 0">
      <ngx-charts-line-chart
        [scheme]="colorScheme"
        [legend]="false"
        [showXAxisLabel]="false"
        [showYAxisLabel]="false"
        [animations]="false"
        [timeline]="false"
        [autoScale]="false"
        [xAxis]="true"
        [yAxis]="true"
        xAxisLabel="Time"
        yAxisLabel="Input (kb/s)"
        [yScaleMin]="0"
        [roundDomains]="true"
        [results]="inputGraphData">
        <ng-template #seriesTooltipTemplate let-model="model">
          <div class="chart-legend">
            {{model[0].name.toLocaleString()}}
            <span *ngFor="let g of model">
              <br/>
              <span class="legend-label-color" [ngStyle]="{'background-color': g.color}"></span>
              <span class="legend-label-text">{{g.series}}: {{g.value}}</span>
            </span>
          </div>
        </ng-template>
      </ngx-charts-line-chart>
    </div>
  </div>
  <div class="col-6">
    <div class="graph" *ngIf="outputGraphData.length > 0">
      <ngx-charts-line-chart
        [scheme]="colorScheme"
        [legend]="false"
        [showXAxisLabel]="false"
        [showYAxisLabel]="false"
        [animations]="false"
        [timeline]="false"
        [autoScale]="false"
        [xAxis]="true"
        [yAxis]="true"
        xAxisLabel="Time"
        yAxisLabel="Output (kb/s)"
        [yScaleMin]="0"
        [roundDomains]="true"
        [results]="outputGraphData">
        <ng-template #seriesTooltipTemplate let-model="model">
          <div class="chart-legend">
            {{model[0].name.toLocaleString()}}
            <span *ngFor="let g of model">
                <br/>
                <span class="legend-label-color" [ngStyle]="{'background-color': g.color}"></span>
                <span class="legend-label-text">{{g.series}}: {{g.value}}</span>
              </span>
          </div>
        </ng-template>
      </ngx-charts-line-chart>
    </div>
  </div>
</div>
