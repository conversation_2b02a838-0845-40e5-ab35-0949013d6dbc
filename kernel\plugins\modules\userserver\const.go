package main

const (
	RPCID_USERSERVER_QUERY  string = "userserver.query"  // 查询基础信息
	RPCID_USERSERVER_MODIFY string = "userserver.modify" // 修改基础信息
	RPCID_USERSERVER_BAN    string = "userserver.ban"    // 封禁用户
	RPCID_USERSERVER_UNBAN  string = "userserver.unban"  // 解封用户

	// 仓库相关
	RPCID_USERSERVER_STORAGE_LIST           string = "userserver.storage.list"           // 获取仓库状态及物品详情
	RPCID_USERSERVER_STORAGE_DEPOSIT        string = "userserver.storage.deposit"        // 存入指定物品列表
	RPCID_USERSERVER_STORAGE_EXTRACT        string = "userserver.storage.extract"        // 提取指定物品至背包
	RPCID_USERSERVER_STORAGE_EXPAND         string = "userserver.storage.expand"         // 仓库容量扩展购买
	RPCID_USERSERVER_STORAGE_SEASON_INHERIT string = "userserver.storage.season.inherit" // 赛季继承物资标记
	RPCID_USERSERVER_STORAGE_DELETE         string = "userserver.storage.delete"         // 删除仓库中指定物品

	// 钱包相关
	RPCID_USERSERVER_WALLET_QUERY string = "userserver.wallet.query" // 获取钱包信息
	RPCID_USERSERVER_WALLET_ADD   string = "userserver.wallet.add"   // 添加钻石，金币
	RPCID_USERSERVER_WALLET_SUB   string = "userserver.wallet.sub"   // 扣除钻石，金币

	// 皮肤相关
	RPCID_USERSERVER_SKIN_LIST   string = "userserver.skin.list"   // 获取皮肤列表
	RPCID_USERSERVER_SKIN_ADD    string = "userserver.skin.add"    // 添加皮肤
	RPCID_USERSERVER_SKIN_DELETE string = "userserver.skin.delete" // 删除皮肤
	RPCID_USERSERVER_SKIN_QUERY  string = "userserver.skin.query"  // 查询皮肤
	RPCID_USERSERVER_SKIN_UPDATE string = "userserver.skin.update" // 更新皮肤

	// 坐骑相关
	RPCID_USERSERVER_MOUNT_LIST   string = "userserver.mount.list"   // 获取坐骑列表
	RPCID_USERSERVER_MOUNT_ADD    string = "userserver.mount.add"    // 添加坐骑
	RPCID_USERSERVER_MOUNT_DELETE string = "userserver.mount.delete" // 删除坐骑
	RPCID_USERSERVER_MOUNT_QUERY  string = "userserver.mount.query"  // 查询坐骑
	RPCID_USERSERVER_MOUNT_UPDATE string = "userserver.mount.update" // 更新坐骑

	// avatar相关
	RPCID_USERSERVER_AVATAR_LIST   string = "userserver.avatar.list"   // 获取avatar列表
	RPCID_USERSERVER_AVATAR_ADD    string = "userserver.avatar.add"    // 添加avatar
	RPCID_USERSERVER_AVATAR_DELETE string = "userserver.avatar.delete" // 删除avatar
	RPCID_USERSERVER_AVATAR_QUERY  string = "userserver.avatar.query"  // 查询avatar
	RPCID_USERSERVER_AVATAR_UPDATE string = "userserver.avatar.update" // 更新avatar
)
