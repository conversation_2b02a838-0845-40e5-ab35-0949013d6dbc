package main

import (
	"kernel/plugins/models"
)

const (
	RPCID_IMSERVER_PARTY_LIST   string = "imserver.party.list"   // 获取组队列表
	RPCID_IMSERVER_PARTY_DETAIL string = "imserver.party.detail" // 获取组队详情
	RPCID_IMSERVER_PARTY_UPDATE string = "imserver.party.update" // 更新组队信息
	RPCID_IMSERVER_PARTY_INVITE string = "imserver.party.invite" // 邀请加入组队
	RPCID_IMSERVER_PARTY_QUERY  string = "imserver.party.query"  // 查询指定玩家所在队伍
)

var (
	ErrMsgPartyClosed                   = models.NewErrorMsg(101, "party closed")                      // 队伍已经关闭
	ErrMsgPartyFull                     = models.NewErrorMsg(102, "party full")                        // 队伍已满
	ErrMsgPartyJoinRequestDuplicate     = models.NewErrorMsg(103, "party join request duplicate")      // 加入请求重复
	ErrMsgPartyJoinRequestAlreadyMember = models.NewErrorMsg(104, "party join request already member") // 加入请求已经成员
	ErrMsgPartyJoinRequestsFull         = models.NewErrorMsg(105, "party join requests full")          // 加入请求已满
	ErrMsgPartyNotLeader                = models.NewErrorMsg(106, "party not leader")                  // 不是队长
	ErrMsgPartyNotMember                = models.NewErrorMsg(107, "party not member")                  // 成员不存在
	ErrMsgPartyNotRequest               = models.NewErrorMsg(108, "party not request")                 // 加入请求不存在
	ErrMsgPartyAcceptRequest            = models.NewErrorMsg(109, "party accept request")              // 无法接受请求
	ErrMsgPartyRemove                   = models.NewErrorMsg(110, "party remove")                      // 无法移除
	ErrMsgPartyRemoveSelf               = models.NewErrorMsg(111, "party remove self")                 // 无法移除自己
	ErrMsgPartyAlreadyInParty           = models.NewErrorMsg(112, "already in party")                  // 已经在队伍中
	ErrMsgPartyAlreadyInvited           = models.NewErrorMsg(113, "already invited")                   // 已经邀请过
	ErrMsgPartyInviteTooMany            = models.NewErrorMsg(114, "party invite too many")             // 邀请过多
)
