package obs

import (
	"crypto/md5"
	"encoding/hex"
	"io"
	"os"
)

func getFileMd5(filename string) (string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return "", err
	}
	defer file.Close()

	md5 := md5.New()
	_, err = io.Copy(md5, file)
	if err != nil {
		return "", err
	}
	md5str := hex.EncodeToString(md5.Sum(nil))
	return md5str, nil
}

func getStringMd5(s string) string {
	md5 := md5.New()
	md5.Write([]byte(s))
	md5str := hex.EncodeToString(md5.Sum(nil))
	return md5str
}

func getBytesMd5(s []byte) string {
	md5 := md5.New()
	md5.Write(s)
	md5str := hex.EncodeToString(md5.Sum(nil))
	return md5str
}
