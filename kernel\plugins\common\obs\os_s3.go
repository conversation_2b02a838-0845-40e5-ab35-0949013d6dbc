package obs

import (
	"bytes"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net/url"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/huaweicloud/huaweicloud-sdk-go-obs/obs"
)

type OSS3 struct {
	OSClientBase
	Region   string
	s3Client *s3.S3
}

func NewS3Client(name string, osconfig *OSConfig) IOSClient {
	creds := credentials.NewStaticCredentials(osconfig.AccessKey, osconfig.Secretkey, "")
	//region := "eu-frankfurt"
	//endpoint := "http://de-tx-cloud-maps-1252226338.cos.eu-frankfurt.myqcloud.com"
	region := ""
	// 正则表达式匹配
	re := regexp.MustCompile(`(?i)(?:\w+\.)?([a-z\-0-9]+)\.\w+\.\w+$`)
	match := re.FindStringSubmatch(osconfig.Endpoint)
	if len(match) > 1 {
		fmt.Println("region:", match[1])
		region = match[1]
	} else {
		log.Panicln("region not found in endpoint")
	}

	config := &aws.Config{
		Region:           aws.String(region),
		Endpoint:         &osconfig.Endpoint,
		S3ForcePathStyle: aws.Bool(false),
		Credentials:      creds,
		//DisableSSL:       aws.Bool(true),
	}
	osSession, err := session.NewSession(config)
	if err != nil {
		log.Panicln(err.Error())
	}
	s3Client := s3.New(osSession)
	client := &OSS3{
		s3Client: s3Client,
	}
	client.Name = name
	client.OSConfig = *osconfig
	client.Region = region
	return client
}

func (O *OSS3) PutObject(key string, data []byte) error {
	return O.PutObjectWithMetadata(key, data, make(map[string]string, 0))
}

func (O *OSS3) PutObjectWithMetadata(key string, data []byte, metaData map[string]string) error {

	s3Metadata := make(map[string]*string, len(metaData)+1)
	s3Metadata[Base64MetaKey] = aws.String(obs.Base64Md5(data))
	for key, value := range metaData {
		s3Metadata[key] = aws.String(value)
	}

	_, err := O.s3Client.PutObject(&s3.PutObjectInput{
		Bucket:   aws.String(O.Bucket),
		Key:      aws.String(key),
		Body:     bytes.NewReader(data),
		Metadata: s3Metadata,
	})
	if err != nil {
		log.Printf("[%s]put object fail, key=%s, err=%s", O.Name, key, err.Error())
		return err
	}
	return nil
}

func (O *OSS3) CopyObject(originKey, targetKey string) error {
	parse, err := url.Parse(O.Endpoint)
	if err != nil {
		log.Printf("[%s]copy object fail, originKey=%s, targetKey=%s, err=%s", O.Name, originKey, targetKey, err.Error())
		return err
	}
	host := parse.Host
	if !strings.HasPrefix(parse.Host, O.Region) {
		host = O.Bucket + "." + parse.Host
	}
	copySource := host + "/" + originKey
	_, err = O.s3Client.CopyObject(&s3.CopyObjectInput{
		Bucket:     aws.String(O.Bucket),
		CopySource: aws.String(copySource),
		Key:        aws.String(targetKey),
	})
	if err != nil {
		log.Printf("[%s]copy object fail, originKey=%s, copySource=%s, targetKey=%s, err=%s", O.Name, originKey, copySource, targetKey, err.Error())
		return err
	}
	return nil
}

func (O *OSS3) GetObject(key string) ([]byte, error) {
	output, err := O.s3Client.GetObject(&s3.GetObjectInput{
		Bucket: aws.String(O.Bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		log.Printf("[%s]get object fail, key=%s, err=%s", O.Name, key, err.Error())
		return nil, err
	}
	defer output.Body.Close()
	buf := new(bytes.Buffer)
	buf.ReadFrom(output.Body)
	return buf.Bytes(), nil
}

func (O *OSS3) DelObject(key string) error {
	_, err := O.s3Client.DeleteObject(&s3.DeleteObjectInput{
		Bucket: aws.String(O.Bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		log.Printf("[%s]del object fail, key=%s, err=%s", O.Name, key, err.Error())
		return err
	}
	return nil
}

func (O *OSS3) DelObjects(keys ...string) error {
	var objects []*s3.ObjectIdentifier
	for _, key := range keys {
		objects = append(objects, &s3.ObjectIdentifier{
			Key: aws.String(key),
		})
	}
	_, err := O.s3Client.DeleteObjects(&s3.DeleteObjectsInput{
		Bucket: aws.String(O.Bucket),
		Delete: &s3.Delete{
			Objects: objects,
		},
	})
	if err != nil {
		log.Printf("[%s]del objects fail, keys=%s, err=%s", O.Name, keys, err.Error())
		return err
	}
	return nil
}

func (O *OSS3) PutFile(path string, key string) error {
	data, err := ioutil.ReadFile(path)
	if err != nil {
		log.Printf("[huawei]read file fail, path=%s, err=%s", path, err.Error())
		return err
	}

	s3Metadata := make(map[string]*string, 1)
	s3Metadata[Base64MetaKey] = aws.String(obs.Base64Md5(data))

	_, err = O.s3Client.PutObject(&s3.PutObjectInput{
		Bucket:   aws.String(O.Bucket),
		Key:      aws.String(key),
		Body:     bytes.NewReader(data),
		Metadata: s3Metadata,
	})
	if err != nil {
		log.Printf("[%s]put file fail, path=%s, err=%s", O.Name, path, err.Error())
		return err
	}
	return nil
}

func (O *OSS3) GetFile(key string, file string) error {
	output, err := O.s3Client.GetObject(&s3.GetObjectInput{
		Bucket: aws.String(O.Bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		log.Printf("[%s]get file fail, key=%s, err=%s", O.Name, key, err.Error())
		return err
	}
	defer output.Body.Close()
	out, err := os.Create(file)
	if err != nil {
		log.Printf("[%s]create file fail, file=%s, err=%s", O.Name, file, err.Error())
		return err
	}
	defer out.Close()
	_, err = io.Copy(out, output.Body)
	if err != nil {
		log.Printf("[%s]copy file fail, file=%s, err=%s", O.Name, file, err.Error())
		return err
	}
	return nil
}

func (O *OSS3) GetObjectMetadata(key string) (map[string]string, error) {
	resp, err := O.s3Client.HeadObject(&s3.HeadObjectInput{
		Bucket: aws.String(O.Bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		log.Printf("[%s]get object metadata fail, key=%s, err=%s", O.Name, key, err.Error())
		return nil, err
	}

	metadata := make(map[string]string, len(resp.Metadata))
	for k, v := range resp.Metadata {
		if len(k) == 0 {
			continue
		}
		k := strings.ToLower(k[:1]) + k[1:]
		if v != nil {
			metadata[k] = *v
		} else {
			metadata[k] = ""
		}
	}

	return metadata, nil
}

func (O *OSS3) SetObjectMetadata(key string, metaData map[string]string) error {
	s3Metadata := make(map[string]*string, len(metaData))
	for k, v := range metaData {
		s3Metadata[k] = aws.String(v)
	}
	//parse, err := url.Parse(O.Endpoint)
	//if err != nil {
	//	log.Printf("[%s]endpoint parse err, endpoint=%s, err=%s", O.Name, O.Endpoint, err.Error())
	//	return err
	//}
	//host := parse.Host
	//if !strings.HasPrefix(parse.Host, O.Region) {
	//host = O.Bucket + "." + parse.Host
	//}
	copySource := O.Bucket + "/" + key
	_, err := O.s3Client.CopyObject(&s3.CopyObjectInput{
		Bucket:            aws.String(O.Bucket),
		CopySource:        aws.String(copySource),
		Key:               aws.String(key),
		Metadata:          s3Metadata,
		MetadataDirective: aws.String("REPLACE"),
	})
	if err != nil {
		log.Printf("[%s]set object metadata fail, key=%s, err=%s", O.Name, key, err.Error())
		return err
	}
	return nil
}

func (O *OSS3) GetObjectMD5(key string) string {
	metadata, err := O.GetObjectMetadata(key)
	if err != nil {
		return ""
	}

	decodeBytes, err := base64.StdEncoding.DecodeString(metadata[Base64MetaKey])
	if err != nil {
		log.Printf("[%v]object metadata decode fail, key=%v, err=%s", O.Name, key, err.Error())
		return ""
	}

	metaMd5 := hex.EncodeToString(decodeBytes)
	return metaMd5
}

func (O *OSS3) GetObjectDownloadURL(key string) string {
	req, _ := O.s3Client.GetObjectRequest(&s3.GetObjectInput{
		Bucket: aws.String(O.Bucket),
		Key:    aws.String(key),
	})
	urlStr, err := req.Presign(24 * time.Hour)
	if err != nil {
		log.Printf("[%s]get object download url fail, key=%s, err=%s", O.Name, key, err.Error())
		return ""
	}
	return urlStr
}

func (O *OSS3) ListObjects(prefix string) ([]string, error) {
	var keys []string
	resp, err := O.s3Client.ListObjects(&s3.ListObjectsInput{
		Bucket: aws.String(O.Bucket),
		Prefix: aws.String(prefix),
	})
	if err != nil {
		log.Printf("[%s]list objects fail, prefix=%s, err=%s", O.Name, prefix, err.Error())
		return nil, err
	}
	for _, content := range resp.Contents {
		keys = append(keys, *content.Key)
	}
	return keys, nil
}

func (O *OSS3) GetObjectSize(key string) (int64, error) {
	resp, err := O.s3Client.HeadObject(&s3.HeadObjectInput{
		Bucket: aws.String(O.Bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		log.Printf("[%s]get object size fail, key=%s, err=%s", O.Name, key, err.Error())
		return 0, err
	}
	return *resp.ContentLength, nil
}

func (O *OSS3) Close() {
	O.s3Client = nil
}

func (O *OSS3) GetSafeUploadURL(key string, expiresSeconds int, maxSize int64, expectedMD5 string) string {
	expires := time.Duration(expiresSeconds) * time.Second

	// 创建预签名请求
	req, _ := O.s3Client.PutObjectRequest(&s3.PutObjectInput{
		Bucket: aws.String(O.Bucket),
		Key:    aws.String(key),
		// 设置Content-Type为二进制流，防止XSS攻击
		ContentType: aws.String("application/octet-stream"),
		// 设置Cache-Control为不缓存
		CacheControl: aws.String("no-cache"),
		// 设置Content-Disposition为attachment，强制下载
		ContentDisposition: aws.String("attachment"),
		// 设置ACL为私有访问
		ACL: aws.String("private"),
	})

	// 如果设置了文件大小限制
	if maxSize > 0 {
		req.HTTPRequest.Header.Set("Content-Length", fmt.Sprintf("%d", maxSize))
	}

	// 如果设置了MD5校验
	if expectedMD5 != "" {
		req.HTTPRequest.Header.Set("Content-MD5", expectedMD5)
	}

	// 生成带签名的URL
	urlStr, err := req.Presign(expires)
	if err != nil {
		log.Printf("[%s]create signed url fail, key=%s, err=%s", O.Name, key, err.Error())
		return ""
	}

	return urlStr
}
