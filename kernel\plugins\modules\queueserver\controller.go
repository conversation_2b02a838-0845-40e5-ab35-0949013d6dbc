package main

import (
	"context"
	"database/sql"
	"fmt"
	"kernel/kernel-common/runtime"
	"kernel/plugins/common"
	"kernel/plugins/models"
	"time"

	json "github.com/json-iterator/go"
	"github.com/redis/go-redis/v9"
)

type Controller struct {
	server  *QueueServerModule
	service *Service
}

// 利用 List 或 Sorted Set 实现排队功能
func NewController(s *QueueServerModule, service *Service) *Controller {
	c := &Controller{
		server:  s,
		service: service,
	}
	return c
}

func (c *Controller) Init() {

}

// @Summary 加入排队
// @Description 加入排队
// @Tags queueserver
// @Accept json
// @Produce json
// @Param payload body ReqJoinQueue true "加入排队请求"
// @Success 200 {object} RespJoinQueue "成功"
// @Router /v2/rpc/queueserver.joinqueue [post]
func (c *Controller) JoinQueue(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		return json.MarshalToString(RespJoinQueue{Code: models.EXCEPTION, Msg: "user_id is empty"})
	}
	uin := c.server.common.GetUin(ctx)
	if uin == 0 {
		return json.MarshalToString(RespJoinQueue{Code: models.EXCEPTION, Msg: "uin is empty"})
	}

	cli := c.server.common.Redis

	var req ReqJoinQueue
	err := json.Unmarshal([]byte(payload), &req)
	if err != nil {
		logger.Error("JoinQueue Unmarshal failed, err: %v", err)
		return json.MarshalToString(RespJoinQueue{Code: models.EXCEPTION, Msg: err.Error()})
	}
	if req.RoomID == "" {
		return json.MarshalToString(RespJoinQueue{Code: models.EXCEPTION, Msg: "roomid is empty"})
	}

	// 桶检查
	bucket_id := c.service.GetBucketID(req.RoomID)
	if !c.service.IsExistBucket(bucket_id) {
		other_cfg, err := c.service.GetRoomFromRedisBucket(bucket_id, req.RoomID)
		if err != nil {
			logger.Error("JoinQueue GetRoomFromRedisBucket failed, %s err: %v", req.RoomID, err)
			return json.MarshalToString(RespJoinQueue{Code: models.EXCEPTION, Msg: err.Error()})
		}
		if other_cfg != nil {
			// 转发到其他节点
			svcname, group := common.ParseWatchSvrKey(other_cfg.SvcGroup)
			result, err := c.server.send.RpcToNodeId(ctx, other_cfg.NodeId, svcname, group, RPCID_QUEUESERVER_JOIN_QUEUE, []byte(payload), nil, time.Second*3)
			return string(result), err
		}
		logger.Error("JoinQueue GetRoomFromRedisBucket failed, %s err: %v", req.RoomID, err)
		return json.MarshalToString(RespJoinQueue{Code: models.EXCEPTION, Msg: "bucket_id is not supported"})
	}

	// 获取玩家当前排队信息
	key_userqueue := fmt.Sprintf("user:queue:%s", user_id)
	userqueue, err := cli.Get(ctx, key_userqueue).Result()
	if err != nil && err != redis.Nil {
		logger.Error("JoinQueue Get failed, %s err: %v", key_userqueue, err)
		return json.MarshalToString(RespJoinQueue{Code: models.EXCEPTION, Msg: err.Error()})
	}
	if userqueue == req.RoomID {
		return json.MarshalToString(RespJoinQueue{Code: models.FAILED, Msg: "already in queue"})
	}

	// 加入队列
	member := fmt.Sprintf("%s:%d", user_id, uin)
	key_roomqueue := fmt.Sprintf("room:queue:%s", req.RoomID)
	pipe := cli.Pipeline()
	pipe.ZAddNX(ctx, key_roomqueue, redis.Z{Score: float64(time.Now().UnixMilli()), Member: member})
	pipe.Set(ctx, key_userqueue, req.RoomID, 0)
	_, err = pipe.Exec(ctx)
	if err != nil {
		logger.Error("JoinQueue pipeline failed, err: %v", err)
		return json.MarshalToString(RespJoinQueue{Code: models.EXCEPTION, Msg: err.Error()})
	}

	// 添加到队列
	if err = c.service.AddUserToWatchRoom(req.RoomID, user_id, uin); err != nil {
		pipe := cli.Pipeline()
		pipe.Del(ctx, key_userqueue)
		pipe.ZRem(ctx, key_roomqueue, user_id)
		pipe.Exec(ctx)
		logger.Error("JoinQueue AddUserToWatchRoom failed, roomid:%s, userid:%s, uin:%d err: %v", req.RoomID, user_id, uin, err)
		return json.MarshalToString(RespJoinQueue{Code: models.EXCEPTION, Msg: err.Error()})
	}

	return json.MarshalToString(RespJoinQueue{Code: models.OK, Msg: "success"})
}

// @Summary 离开排队
// @Description 离开排队
// @Tags queueserver
// @Accept json
// @Produce json
// @Param payload body ReqLeaveQueue true "离开排队请求"
// @Success 200 {object} ReqLeaveQueue "成功"
// @Router /v2/rpc/queueserver.leavequeue [post]
func (c *Controller) LeaveQueue(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		return json.MarshalToString(models.CommonResp{Code: models.EXCEPTION, Msg: "user_id is empty"})
	}
	uin := c.server.common.GetUin(ctx)
	if uin == 0 {
		return json.MarshalToString(models.CommonResp{Code: models.EXCEPTION, Msg: "uin is empty"})
	}

	cli := c.server.common.Redis

	var req ReqLeaveQueue
	err := json.Unmarshal([]byte(payload), &req)
	if err != nil {
		logger.Error("LeaveQueue Unmarshal failed, err: %v", err)
		return json.MarshalToString(models.CommonResp{Code: models.EXCEPTION, Msg: err.Error()})
	}
	if req.RoomID == "" {
		return json.MarshalToString(models.CommonResp{Code: models.EXCEPTION, Msg: "roomid is empty"})
	}

	// 桶检查
	bucket_id := c.service.GetBucketID(req.RoomID)
	if !c.service.IsExistBucket(bucket_id) {
		other_cfg, err := c.service.GetRoomFromRedisBucket(bucket_id, req.RoomID)
		if err != nil {
			logger.Error("LeaveQueue GetRoomFromRedisBucket failed, %s err: %v", req.RoomID, err)
			return json.MarshalToString(models.CommonResp{Code: models.EXCEPTION, Msg: err.Error()})
		}
		if other_cfg != nil {
			// 转发到其他节点
			svcname, group := common.ParseWatchSvrKey(other_cfg.SvcGroup)
			result, err := c.server.send.RpcToNodeId(ctx, other_cfg.NodeId, svcname, group, RPCID_QUEUESERVER_LEAVE_QUEUE, []byte(payload), nil, time.Second*3)
			return string(result), err
		}
		logger.Error("LeaveQueue GetRoomFromRedisBucket failed, %s err: %v", req.RoomID, err)
		return json.MarshalToString(models.CommonResp{Code: models.EXCEPTION, Msg: "bucket_id is not supported"})
	}

	// 获取玩家当前排队信息
	key_userqueue := fmt.Sprintf("user:queue:%s", user_id)
	userqueue, err := cli.Get(ctx, key_userqueue).Result()
	if err != nil {
		if err == redis.Nil {
			return json.MarshalToString(models.CommonResp{Code: models.FAILED, Msg: "not in queue " + req.RoomID})
		}
		logger.Error("LeaveQueue Get failed, %s err: %v", key_userqueue, err)
		return json.MarshalToString(models.CommonResp{Code: models.EXCEPTION, Msg: err.Error()})
	}

	// 检查玩家是否在指定房间队列中
	if userqueue != req.RoomID {
		logger.Error("LeaveQueue room not same, current: %s, request: %s", userqueue, req.RoomID)
		return json.MarshalToString(models.CommonResp{Code: models.FAILED, Msg: "not in queue " + req.RoomID})
	}

	// 使用pipeline批量执行Redis操作
	member := fmt.Sprintf("%s:%d", user_id, uin)
	pipe := cli.Pipeline()
	key_roomqueue := fmt.Sprintf("room:queue:%s", req.RoomID)
	pipe.ZRem(ctx, key_roomqueue, member)
	pipe.Del(ctx, key_userqueue)
	_, err = pipe.Exec(ctx)
	if err != nil {
		logger.Error("LeaveQueue pipeline failed, err: %v", err)
		return json.MarshalToString(models.CommonResp{Code: models.EXCEPTION, Msg: err.Error()})
	}

	// 从内存队列中移除用户
	c.service.RemoveUserFromWatchRoom(req.RoomID, user_id, uin)

	return json.MarshalToString(models.CommonResp{Code: models.OK, Msg: "success"})
}

// @Summary 获取排队状态
// @Description 获取排队状态
// @Tags queueserver
// @Accept json
// @Produce json
// @Param payload body ReqGetQueueStatus true "获取排队状态请求"
// @Success 200 {object} RespGetQueueStatus "成功"
// @Router /v2/rpc/queueserver.getqueuestatus [post]
func (c *Controller) GetQueueStatus(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	user_id := c.server.common.GetUserID(ctx)
	if user_id == "" {
		return json.MarshalToString(RespGetQueueStatus{Code: models.EXCEPTION, Msg: "user_id is empty"})
	}
	uin := c.server.common.GetUin(ctx)
	if uin == 0 {
		return json.MarshalToString(RespGetQueueStatus{Code: models.EXCEPTION, Msg: "uin is empty"})
	}

	cli := c.server.common.Redis

	var req ReqGetQueueStatus
	err := json.Unmarshal([]byte(payload), &req)
	if err != nil {
		logger.Error("GetQueueStatus Unmarshal failed, err: %v", err)
		return json.MarshalToString(RespGetQueueStatus{Code: models.EXCEPTION, Msg: err.Error()})
	}

	if req.RoomID == "" {
		return json.MarshalToString(RespGetQueueStatus{Code: models.EXCEPTION, Msg: "roomid is empty"})
	}

	// 桶检查
	bucket_id := c.service.GetBucketID(req.RoomID)
	if !c.service.IsExistBucket(bucket_id) {
		other_cfg, err := c.service.GetRoomFromRedisBucket(bucket_id, req.RoomID)
		if err != nil {
			logger.Error("GetQueueStatus GetRoomFromRedisBucket failed, %s err: %v", req.RoomID, err)
			return json.MarshalToString(RespGetQueueStatus{Code: models.EXCEPTION, Msg: err.Error()})
		}
		if other_cfg != nil {
			// 转发到其他节点
			svcname, group := common.ParseWatchSvrKey(other_cfg.SvcGroup)
			result, err := c.server.send.RpcToNodeId(ctx, other_cfg.NodeId, svcname, group, RPCID_QUEUESERVER_GET_QUEUE_STATUS, []byte(payload), nil, time.Second*3)
			return string(result), err
		}
		logger.Error("GetQueueStatus GetRoomFromRedisBucket failed, %s err: %v", req.RoomID, err)
		return json.MarshalToString(RespGetQueueStatus{Code: models.EXCEPTION, Msg: "bucket_id is not supported"})
	}

	// 获取玩家当前排队信息
	key_userqueue := fmt.Sprintf("user:queue:%s", user_id)
	userqueue, err := cli.Get(ctx, key_userqueue).Result()
	if err != nil {
		if err == redis.Nil {
			return json.MarshalToString(RespGetQueueStatus{Code: models.FAILED, Msg: "not in queue " + req.RoomID})
		}
		logger.Error("GetQueueStatus Get failed, %s err: %v", key_userqueue, err)
		return json.MarshalToString(RespGetQueueStatus{Code: models.EXCEPTION, Msg: err.Error()})
	}

	// 检查是否在指定房间队列中
	if userqueue != req.RoomID {
		logger.Error("GetQueueStatus room not same, current: %s, request: %s", userqueue, req.RoomID)
		return json.MarshalToString(RespGetQueueStatus{Code: models.FAILED, Msg: "not in queue " + req.RoomID})
	}

	// 获取房间队列信息
	member := fmt.Sprintf("%s:%d", user_id, uin)
	key_roomqueue := fmt.Sprintf("room:queue:%s", req.RoomID)
	pipe := cli.Pipeline()
	totalCmd := pipe.ZCard(ctx, key_roomqueue)
	rankCmd := pipe.ZRank(ctx, key_roomqueue, member)
	_, err = pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		logger.Error("GetQueueStatus pipeline failed, key %s err: %v", key_roomqueue, err)
		return json.MarshalToString(RespGetQueueStatus{Code: models.EXCEPTION, Msg: err.Error()})
	}

	total := totalCmd.Val()
	rank := rankCmd.Val()

	return json.MarshalToString(RespGetQueueStatus{Code: models.OK, Msg: "success", Data: &QueueStatus{Rank: rank, Total: total}})
}

func (c *Controller) GetQueueList(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	return payload, nil
}

func (c *Controller) GetQueueInfo(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	return payload, nil
}
