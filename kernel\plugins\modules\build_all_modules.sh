#!/bin/bash

# 获取参数，默认为default
BUILD_TYPE=${1:-default}

#cd 到当前文件目录
cd $(dirname $0)
# 获取CPU核心数
CORES=$(nproc)
# 设置最大并行任务数，使用核心数或自定义数值
MAX_JOBS=8  # 可以根据实际情况调整，通常设置为CPU核心数或核心数+1

# 用于跟踪后台进程
declare -a pids

# 当前运行的任务数
current_jobs=0

# 遍历当前目录下的所有子目录
for dir in $(ls -l | grep "^d" | awk '{print $9}')  
do
    # 跳过 exampleserver 目录 和 testserver 目录
    if [ -d "$dir" ] && [ "$(basename "$dir")" != "exampleserver" ] && [ "$(basename "$dir")" != "testserver" ]; then
        # 如果当前运行的任务达到最大值，等待任意一个任务完成
        while [ $current_jobs -ge $MAX_JOBS ]; do
            for pid in ${pids[@]}; do
                if ! kill -0 $pid 2>/dev/null; then
                    current_jobs=$((current_jobs - 1))
                    break
                fi
            done
            sleep 0.1
        done

        echo "Building $dir with type: $BUILD_TYPE"
        cd $dir
        ./build.sh $BUILD_TYPE &
        pids+=($!)
        current_jobs=$((current_jobs + 1))
        cd ..
    fi
done

# 等待所有后台任务完成
for pid in ${pids[@]}; do
    wait $pid
    # 检查编译是否成功
    if [ $? -ne 0 ]; then
        echo "Error: Building module failed"
        exit 1
    fi
done

echo "All modules build success with type: $BUILD_TYPE"